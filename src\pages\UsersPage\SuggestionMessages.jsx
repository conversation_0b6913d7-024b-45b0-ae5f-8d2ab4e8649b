import React from "react";
// import { AuthContext, tokenExpireError } from "../authContext";

import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
// import { GlobalContext } from "../globalContext";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
// import { getNonNullValue } from "../utils/utils";
// import PaginationBar from "../components/PaginationBar";
// import AddButton from "../components/AddButton";
// import ExportButton from "../components/ExportButton";
import MkdSDK from "Utils/MkdSDK";
import { tokenExpireError } from "Src/authContext";

let sdk = new MkdSDK();

const SuggestionMessages = async () => {
  const schema = yup.object({
    update_at: yup.string(),
    link: yup.string(),
  });
  const {
    register,
    handleSubmit,
    setError,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  async function getData(pageNum, limitNum, currentTableData) {
    try {
     

      const result = await sdk.getSuggestedQuestions();
      // const result = await sdk.callRestAPI(
      //   {
      //     payload: { ...currentTableData },
      //     page: pageNum,
      //     limit: limitNum,
      //     sortId: "",
      //     direction: "",
      //   },
      //   "PAGINATE"
      // );
     

      return result;
    } catch (error) {
      console.log("ERROR", error);
    }
  }


  return  getData();
};

export default SuggestionMessages;
