import{p as k,i as D}from"./fontawesome-svg-core-6d3776b6.js";import{P as n}from"../@ckeditor/ckeditor5-react-48fc30c1.js";import{a as C}from"../vendor-b0222800.js";function T(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,a)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?T(Object(r),!0).forEach(function(a){b(e,a,r[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):T(Object(r)).forEach(function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(r,a))})}return e}function h(e){"@babel/helpers - typeof";return h=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},h(e)}function b(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function E(e,t){if(e==null)return{};var r={},a=Object.keys(e),i,o;for(o=0;o<a.length;o++)i=a[o],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function L(e,t){if(e==null)return{};var r=E(e,t),a,i;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)a=o[i],!(t.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(e,a)&&(r[a]=e[a])}return r}function S(e){return U(e)||$(e)||K(e)||H()}function U(e){if(Array.isArray(e))return _(e)}function $(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function K(e,t){if(e){if(typeof e=="string")return _(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return _(e,t)}}function _(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,a=new Array(t);r<t;r++)a[r]=e[r];return a}function H(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function M(e){var t,r=e.beat,a=e.fade,i=e.beatFade,o=e.bounce,y=e.shake,m=e.flash,l=e.spin,s=e.spinPulse,u=e.spinReverse,x=e.pulse,P=e.fixedWidth,d=e.inverse,w=e.border,p=e.listItem,f=e.flip,I=e.size,v=e.rotation,A=e.pull,N=(t={"fa-beat":r,"fa-fade":a,"fa-beat-fade":i,"fa-bounce":o,"fa-shake":y,"fa-flash":m,"fa-spin":l,"fa-spin-reverse":u,"fa-spin-pulse":s,"fa-pulse":x,"fa-fw":P,"fa-inverse":d,"fa-border":w,"fa-li":p,"fa-flip":f===!0,"fa-flip-horizontal":f==="horizontal"||f==="both","fa-flip-vertical":f==="vertical"||f==="both"},b(t,"fa-".concat(I),typeof I<"u"&&I!==null),b(t,"fa-rotate-".concat(v),typeof v<"u"&&v!==null&&v!==0),b(t,"fa-pull-".concat(A),typeof A<"u"&&A!==null),b(t,"fa-swap-opacity",e.swapOpacity),t);return Object.keys(N).map(function(O){return N[O]?O:null}).filter(function(O){return O})}function q(e){return e=e-0,e===e}function W(e){return q(e)?e:(e=e.replace(/[\-_\s]+(.)?/g,function(t,r){return r?r.toUpperCase():""}),e.substr(0,1).toLowerCase()+e.substr(1))}var B=["style"];function G(e){return e.charAt(0).toUpperCase()+e.slice(1)}function J(e){return e.split(";").map(function(t){return t.trim()}).filter(function(t){return t}).reduce(function(t,r){var a=r.indexOf(":"),i=W(r.slice(0,a)),o=r.slice(a+1).trim();return i.startsWith("webkit")?t[G(i)]=o:t[i]=o,t},{})}function F(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(typeof t=="string")return t;var a=(t.children||[]).map(function(l){return F(e,l)}),i=Object.keys(t.attributes||{}).reduce(function(l,s){var u=t.attributes[s];switch(s){case"class":l.attrs.className=u,delete t.attributes.class;break;case"style":l.attrs.style=J(u);break;default:s.indexOf("aria-")===0||s.indexOf("data-")===0?l.attrs[s.toLowerCase()]=u:l.attrs[W(s)]=u}return l},{attrs:{}}),o=r.style,y=o===void 0?{}:o,m=L(r,B);return i.attrs.style=c(c({},i.attrs.style),y),e.apply(void 0,[t.tag,c(c({},i.attrs),m)].concat(S(a)))}var R=!1;try{R=!0}catch{}function Q(){if(!R&&console&&typeof console.error=="function"){var e;(e=console).error.apply(e,arguments)}}function z(e){if(e&&h(e)==="object"&&e.prefix&&e.iconName&&e.icon)return e;if(k.icon)return k.icon(e);if(e===null)return null;if(e&&h(e)==="object"&&e.prefix&&e.iconName)return e;if(Array.isArray(e)&&e.length===2)return{prefix:e[0],iconName:e[1]};if(typeof e=="string")return{prefix:"fas",iconName:e}}function j(e,t){return Array.isArray(t)&&t.length>0||!Array.isArray(t)&&t?b({},e,t):{}}var g=C.forwardRef(function(e,t){var r=e.icon,a=e.mask,i=e.symbol,o=e.className,y=e.title,m=e.titleId,l=e.maskId,s=z(r),u=j("classes",[].concat(S(M(e)),S(o.split(" ")))),x=j("transform",typeof e.transform=="string"?k.transform(e.transform):e.transform),P=j("mask",z(a)),d=D(s,c(c(c(c({},u),x),P),{},{symbol:i,title:y,titleId:m,maskId:l}));if(!d)return Q("Could not find icon",s),null;var w=d.abstract,p={ref:t};return Object.keys(e).forEach(function(f){g.defaultProps.hasOwnProperty(f)||(p[f]=e[f])}),V(w[0],p)});g.displayName="FontAwesomeIcon";g.propTypes={beat:n.bool,border:n.bool,beatFade:n.bool,bounce:n.bool,className:n.string,fade:n.bool,flash:n.bool,mask:n.oneOfType([n.object,n.array,n.string]),maskId:n.string,fixedWidth:n.bool,inverse:n.bool,flip:n.oneOf([!0,!1,"horizontal","vertical","both"]),icon:n.oneOfType([n.object,n.array,n.string]),listItem:n.bool,pull:n.oneOf(["right","left"]),pulse:n.bool,rotation:n.oneOf([0,90,180,270]),shake:n.bool,size:n.oneOf(["2xs","xs","sm","lg","xl","2xl","1x","2x","3x","4x","5x","6x","7x","8x","9x","10x"]),spin:n.bool,spinPulse:n.bool,spinReverse:n.bool,symbol:n.oneOfType([n.bool,n.string]),title:n.string,titleId:n.string,transform:n.oneOfType([n.string,n.object]),swapOpacity:n.bool};g.defaultProps={border:!1,className:"",mask:null,maskId:null,fixedWidth:!1,inverse:!1,flip:!1,icon:null,listItem:!1,pull:null,pulse:!1,rotation:null,size:null,spin:!1,spinPulse:!1,spinReverse:!1,beat:!1,fade:!1,beatFade:!1,bounce:!1,shake:!1,symbol:!1,title:"",titleId:null,transform:null,swapOpacity:!1};var V=F.bind(null,C.createElement);export{g as F};
