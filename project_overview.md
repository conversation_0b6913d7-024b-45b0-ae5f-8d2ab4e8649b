# Project Overview

## Current Setup Overview

### Systems in Use

1. **React Frontend Application**
   - Built with React 
   - Uses Vite as the build tool
   - Running on port 3000

2. **Backend API Services**
   - Primary API: `https://api.kaizenwin.com`
   - Authentication and data services

3. **Payment Processing**
   - Stripe integration for payments

### System Responsibilities

#### Frontend (React)
- User interface and experience
- Authentication flows (login, signup, password reset)
- Admin and user dashboards
- Content management
- Chat functionality
- Payment processing UI

#### Backend API
- Data storage and retrieval
- Authentication and authorization
- Content management system (CMS) data
- User management
- Payment processing with Stripe

### Technologies & Platforms

1. **Frontend**
   - React 
   - React Router for navigation
   - TailwindCSS for styling
   - NextUI components
   - Vite for building and development
   - React DnD for drag-and-drop functionality

2. **State Management**
   - Context API with multiple providers (Auth, Global, Data, Age, Page)

3. **Styling**
   - TailwindCSS
   - NextUI components
   - Custom CSS

4. **Authentication**
   - Custom auth with JWT tokens
   - Social login capabilities
   - Magic link authentication

5. **Payment Processing**
   - Stripe integration

### Key Components

#### Context Providers
- `AuthProvider`: Manages authentication state and user sessions
- `GlobalProvider`: Provides global application state
- `DataProvider`: Handles CMS and other data fetching
- `AgeProvider`: Age verification functionality
- `PageProvider`: Page-specific state management

#### SDK Utilities
- `MkdSDK`: Core SDK for API interactions
- `TreeSDK`: Handles hierarchical data structures
- `EcomSDK`: E-commerce functionality

#### Routing
- Uses React Router with separate routes for admin and user interfaces
- Age verification wrapper for certain routes

#### UI Components
- Admin and user headers
- Public header for unauthenticated users
- Various page components for different functionality
- Chat components
- Form components for data entry

#### Authentication Flow
- Multiple authentication methods (standard, magic link, social)
- Session management with token expiration handling
- Different flows for admin and regular users

## Page Overview and Relationships

### Admin Pages

#### Authentication Pages
- **AdminLoginPage**: Admin login interface
- **AdminConfirmLoginPage**: Confirmation step after login
- **AdminForgotPage**: Password recovery request
- **AdminResetPage**: Password reset form
- **SignUpPage**: Admin registration

#### Dashboard & Profile
- **AdminDashboardPage**: Central admin control panel with metrics and quick access
- **AdminProfilePage**: Admin profile management

#### Content Management
- **AdminCmsListPage**: Lists all CMS content
- **AddAdminCmsPage**: Create new CMS content
- **EditAdminCmsPage**: Modify existing CMS content
- **AdminPhotoListPage**: Manage photo assets
- **AddAdminPhotoPage**: Upload new photos
- **AdminEmailListPage**: Email template management
- **AddAdminEmailPage**: Create email templates
- **EditAdminEmailPage**: Modify email templates

#### User Management
- **AdminUserListPage**: View and manage all users
- **AddAdminUserPage**: Create new user accounts
- **EditAdminUserPage**: Modify user details

#### Product & Pricing
- **ListAdminPlansTablePage**: View all subscription plans
- **AddAdminPlansTablePage**: Create new plans
- **EditAdminPlansTablePage**: Modify existing plans
- **ViewAdminPlansTablePage**: Detailed plan view
- **ListAdminPricesTablePage**: Manage pricing
- **AdminStripeDiscountListPage**: Manage discount codes

#### Payments & Transactions
- **ListAdminPaymentsTablePage**: View all payment transactions
- **ViewAdminPaymentPage**: Detailed transaction view

#### Chat & Communication
- **AdminChatPage**: Admin chat interface
- **ListAdminUserChatsPage**: View all user conversations
- **ViewAdminUserChatPage**: Detailed view of specific conversations
- **AdminNewsLetterListPage**: Manage newsletter subscriptions

#### Quiz & Rewards
- **AdminQuizListPage**: Manage quizzes
- **AddAdminQuizPage**: Create new quizzes
- **EditAdminQuizPage**: Modify existing quizzes
- **AdminRewardListPage**: Manage rewards
- **AddAdminRewardPage**: Create new rewards
- **EditAdminRewardPage**: Modify existing rewards

#### SEO & Analytics
- **AdminSEOListPage**: Manage SEO settings
- **AdminGoogleAnalyticsListPage**: Analytics configuration
- **AdminDynamicHeaderListPage**: Manage dynamic headers

### User Pages

#### Authentication Pages
- **UserLoginPage**: User login interface
- **UserConfirmLoginPage**: Confirmation after login
- **UserForgotPage**: Password recovery
- **UserResetPage**: Password reset form
- **UserSignUpPage**: New user registration
- **UserConfirmSignUpPage**: Signup confirmation
- **UserVerifyEmailPage**: Email verification
- **MagicLoginVerifyPage**: Magic link verification

#### Home & Profile
- **UserHomePage**: Main landing page with service offerings
- **UserProfilePage**: User profile management

#### Subscription & Payments
- **UserPlanPage**: View available subscription plans
- **ListUserPaymentsTablePage**: User payment history
- **Payment**: Checkout process
- **UserOrderPage**: Order details and status
- **ThankYouPage**: Post-purchase confirmation

#### Content & Interaction
- **Quiz**: Interactive quiz component
- **ChatComponent**: User chat interface
- **ReportIssuesTemplate**: Problem reporting form
- **CollaborationPage**: Collaboration features

#### Information Pages
- **AboutUsUserPage**: Company information
- **PrivacyPolicy**: Privacy terms
- **UserTerms**: Terms of service
- **Disclaimer**: Legal disclaimers
- **TermsAndConditions**: Detailed terms

### Public Pages
- **NotFoundPage**: 404 error page
- **CountdownPage/CountdownPage2**: Special event countdown timers
- **AdsVid**: Video advertisements

### Page Relationships & Flow

#### Admin Flow
1. **Authentication**: AdminLoginPage → AdminConfirmLoginPage → AdminDashboardPage
2. **Content Management**: AdminDashboardPage → AdminCmsListPage → AddAdminCmsPage/EditAdminCmsPage
3. **User Management**: AdminDashboardPage → AdminUserListPage → AddAdminUserPage/EditAdminUserPage
4. **Communication**: AdminDashboardPage → AdminChatPage/ListAdminUserChatsPage → ViewAdminUserChatPage

#### User Flow
1. **Acquisition**: UserHomePage → UserSignUpPage → UserConfirmSignUpPage → UserVerifyEmailPage
2. **Authentication**: UserLoginPage → UserConfirmLoginPage → UserHomePage (authenticated)
3. **Subscription**: UserHomePage → UserPlanPage → Payment → ThankYouPage
4. **Engagement**: UserHomePage → ChatComponent/Quiz
5. **Account Management**: UserHomePage → UserProfilePage → ListUserPaymentsTablePage

#### Cross-Cutting Components
- **AgeCheckWrapper**: Age verification wrapper around user-facing pages
- **SessionExpiredModal**: Handles expired sessions for both admin and users
- **SnackBar**: Notification system used throughout the application
- **CookieConsent**: Cookie policy acceptance for all visitors