import React from "react";
import { AuthContext } from "../authContext";
import { NavLink } from "react-router-dom";
import { GlobalContext } from "../globalContext";
export const UserHeader = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { state } = React.useContext(GlobalContext);

  return (
    <>
      <div
        className={`sidebar-holder sticky h-screen top-0  ${
          !state.isOpen ? "open-nav" : ""
        }`}
      >
        <div className="sticky top-0 h-screen flex flex-col items-center ">
          <div className="w-full p-4 ">
            <div className="text-white font-bold text-center text-2xl">
              User
            </div>
          </div>
          <button className="text-white flex justify-center  text-center text-l border border-red-600 w-[90%] rounded-full p-2 m-2 bg-red-600">
            + New Chat
          </button>

          <div className="w-full sidebar-list  absolute bottom-0">
            <ul className="flex flex-wrap">
              <li className="list-none block w-full">
                <NavLink
                  to="/user/dashboard"
                  className={`${
                    state.path == "user" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  Dashboard
                </NavLink>
              </li>

              <li className="list-none block w-full">
                <NavLink
                  to="/user/payments"
                  className={`${
                    state.path == "payments" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  Payments
                </NavLink>
              </li>
              {/* <li className="list-none block w-full">
                <NavLink
                  to="/user/plans"
                  className={`${
                    state.path == "plans" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  Plans
                </NavLink>
              </li> */}

              {/* <li className="list-none block w-full">
                <NavLink
                  to="/user/prices"
                  className={`${
                    state.path == "prices" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  Prices
                </NavLink>
              </li> */}

              {/* <li className="list-none block w-full">
                <NavLink
                  to="/user/users"
                  className={`${
                    state.path == "users" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  Users
                </NavLink>
              </li> */}

              <li className="list-none block w-full">
                <NavLink
                  to="/user/profile"
                  className={`${
                    state.path == "profile" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  Profile
                </NavLink>
              </li>
              <li className="list-none block w-full">
                <NavLink
                  to="/user/login"
                  onClick={() =>
                    dispatch({
                      type: "LOGOUT",
                    })
                  }
                >
                  Logout
                </NavLink>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </>
  );
};

export default UserHeader;
