import { yupResolver } from "@hookform/resolvers/yup";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { Link, useNavigate } from "react-router-dom";
import { AuthContext, tokenExpireError } from "Src/authContext";
import { GlobalContext, showToast } from "Src/globalContext";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import {  getNonNullValue } from "Utils/utils";
import { InteractiveButton } from "./InteractiveButton";

let sdk = new MkdSDK();

const EventPlan = ({ plan, index, themeLength }) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { dispatch } = React.useContext(AuthContext);
  const [addEventModal, setAddEventModal] = React.useState(false);
  const [themes, setThemes] = React.useState([]);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [id, setId] = React.useState(null);
  const [qty, setQty] = React.useState(null);
  const [price, setPrice] = React.useState(null);
  const [isBulkPlan, setIsBulkPlan] = React.useState(null);
  const [noOfPacks, setNoOfPacks] = React.useState(null);

  const urlParams = new URLSearchParams(window.location.search);
  const currentPlan = urlParams.get("currentPlan") ?? null;
  const ev = urlParams.get("ev") ?? null;

  const navigate = useNavigate();

  const schema = yup.object({
    event_name: yup.string().required("Event Name is required"),
    theme: yup.string().required("Theme is required"),
  });

  const {
    register,
    handleSubmit,
    setError,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const onSubmit = async (_data) => {
    let event_name = getNonNullValue(_data.event_name);
    let theme = getNonNullValue(_data.theme);

    let sdk = new MkdSDK();
    localStorage.setItem("event_name", event_name);
    localStorage.setItem("theme", theme);
    try {
      let result;
      setSubmitLoading(true);
      let eventDetail = [];

      sdk.setTable("event_management");
      if (noOfPacks) {
        let i = 0;
        while (i < noOfPacks) {
          result = await sdk.callRestAPI(
            {
              event_name: event_name,
              status: 0,
              plan: 0,
              photos_remaining: 0,
              expiration_date: "N/A",
              theme: theme,
              number_of_images: "2",
              is_age_restricted: false,
              no_of_free_trials: 2,
              sharing_option: "email",
              user_id: Number(localStorage.getItem("user")),
            },
            "POST"
          );
          i++;
          if (!result.error) {
            eventDetail.push(result?.data);
          }
        }
      } else {
        result = await sdk.callRestAPI(
          {
            event_name: event_name,
            status: 0,
            plan: 0,
            photos_remaining: 0,
            expiration_date: "N/A",
            theme: theme,
            number_of_images: "2",
            is_age_restricted: false,
            no_of_free_trials: 2,
            sharing_option: "email",
            user_id: Number(localStorage.getItem("user")),
          },
          "POST"
        );
      }
      if (!result.error) {
        localStorage.setItem("bulkEvents", JSON.stringify(eventDetail));
        localStorage.setItem("price", btoa(price));
        localStorage.setItem("qty", btoa(qty));
        // go to stripe checkout page
        navigate(`/brand/checkout?id=${id}&&ev=${result.data}`);
      } else {
        setSubmitLoading(false);
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error) {
      setSubmitLoading(false);
      console.log("Error", error);
      setError("event_name", {
        type: "manual",
        message: error.message,
      });
      tokenExpireError(dispatch, error.message);
    }
  };

  async function getThemes() {
    try {
      sdk.setTable("theme");
      const result = await sdk.callRestAPI({}, "GETALL");

      const { list } = result;
      setThemes(list);
    } catch (error) {
      console.log("ERROR", error);
      // tokenExpireError(dispatch, error.message);
    }
  }

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "event_plan",
      },
    });
    getThemes();
  }, []);
  let discount_details = plan?.discount_details&&JSON.parse(plan?.discount_details);
  return (
    <>
      <div
        className={`${index == 0 ? "bg-[#fff9d6]" : ""}  ${
          index == 1 ? "bg-[#e09f3e]" : ""
        } ${index == 2 ? "bg-[#99a88c]" : ""} ${
          index == 3 ? "bg-[#335c67]" : ""
        } w-80  shadow-2xl hover:shadow-xl pb-10`}
      >
        <div className="card-body w-[90vw] md:w-auto max-h-[350px] items-center text-center">
          <h2 className="card-title">{plan?.nick_name}</h2>
          <p className="text-base mb-2 tracking-10">
            {plan?.no_of_photos} Photos
          </p>
          <p className="text-base mb-2 tracking-10">Select 1 Theme</p>
          <p className="text-base mb-2 tracking-10">
            ({themeLength} to choose from)
          </p>
          <p className="text-base mb-2 tracking-10">+</p>
          <p className="text-base mb-2 tracking-10">2 test photos</p>
          <p className="text-base mb-2 tracking-10">
            Expires 7 days after event is launched
          </p>
          <p className="text-base mb-2 tracking-10">{plan?.description}</p>

          <h2 className="card-title">${plan?.price}</h2>
        </div>
        <div className="flex justify-center ">
          <div className="transition ease-in-out delay-150 hover:-translate-y-1 duration-300">
            <button
              onClick={() => {
                setId(plan.id);
                setQty(plan.no_of_photos);
                setPrice(plan.price);
                if (currentPlan) {
                  navigate(
                    `/brand/checkout?id=${plan.id}&&ev=${ev}&&price=${plan.price}&&qty=${plan.no_of_photos}`
                  );
                } else {
                  setAddEventModal(true);
                }
              }}
              className={`flex w-full justify-center items-center bg-gradient-to-l from-[#33d4b7_9.11%] to-[#0d9895_69.45%] px-2 h-8 text-white tracking-wide outline-none focus:outline-none rounded  py-2`}
            >
              Buy
            </button>
          </div>
        </div>
        <hr className="w-[70%] grid mx-auto my-10" />
        {plan?.discount ? (
          <>
            {discount_details.map((item) => {
              return (
                <div className="grid mb-4 justify-center">
                  <span
                    onClick={() => {
                      setId(plan.id);
                      setIsBulkPlan(true);
                      setNoOfPacks(item?.no_of_picks);
                      // setQty(item?.no_of_picks * plan?.no_of_photos);
                      setQty(plan?.no_of_photos);

                      if (plan.discount_type === "%") {
                        let original_price = plan.price;
                        let percent_in_fraction = item?.discount / 100;
                        let discount_amt = original_price * percent_in_fraction;
                        let discount_price = original_price - discount_amt;
                        setPrice(discount_price);
                        setAddEventModal(true);
                      }

                      if (plan.discount_type === "$") {
                        let original_price = plan.price;
                        let discount_amt = item?.discount;
                        let discount_price = original_price - discount_amt;
                        setPrice(discount_price);
                        setAddEventModal(true);
                      }
                    }}
                    className="px-2 cursor-pointer text-sm border border-gray-500 w-fit py-1"
                  >
                    {item?.no_of_picks} Events Pack {item?.discount}
                    {plan?.discount_type} Off Buy Now
                  </span>
                </div>
              );
            })}
          </>
        ) : null}
      </div>

      {addEventModal && (
        <>
          <div className="fixed inset-0 z-10 overflow-y-auto">
            <div
              className="fixed inset-0 w-full h-full bg-black opacity-40"
              onClick={() => setAddEventModal(false)}
            ></div>
            <div className="flex items-center min-h-screen px-4 py-8">
              <div className="relative w-full sm:max-w-lg p-8 mx-auto bg-white rounded-md shadow-lg">
                <form
                  onSubmit={handleSubmit(onSubmit)}
                  className="mt-3 sm:flex flex-col"
                >
                  <div className="text-center">
                    <h3 className="capitalize special-color pb-1 leading-7 font-bold sm:leading-8 text-lg sm:text-xl">
                      Purchase {plan?.nick_name} plan
                    </h3>
                    <div className="w-full grid">
                      <div className="">
                        <label className="text-start block mb-1">
                          Event Name
                        </label>
                        <input
                          autoComplete="off"
                          type="text"
                          {...register("event_name")}
                          placeholder="Enter event name"
                          className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
                            errors.event_name?.message ? "border-red-500" : ""
                          }`}
                        />
                        <p className="text-red-500 text-xs italic block mt-1 text-start">
                          {errors.event_name?.message}
                        </p>
                      </div>
                      <div className="mt-3">
                        <label className="text-start block mb-1">
                          Select Theme
                        </label>
                        <select
                          {...register("theme")}
                          className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
                            errors.theme?.message ? "border-red-500" : ""
                          }`}
                        >
                          <option value="">Please select theme</option>
                          {themes.map((theme, index) => (
                            <option key={index} value={theme.theme_name}>
                              {theme.theme_name}
                            </option>
                          ))}
                        </select>
                        <p className="text-red-500 text-xs italic block mt-1 text-start">
                          {errors.theme?.message}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="mt-2 text-center sm:text-left">
                    <h4 className="text-lg font-medium text-gray-800"></h4>
                    <div className="items-center gap-2 mt-3 flex justify-center">
                      <InteractiveButton
                        type="submit"
                        className={`flex w-full justify-center items-center bg-gradient-to-l from-[#33d4b7_9.11%] to-[#0d9895_69.45%] text-white tracking-wide outline-none focus:outline-none rounded  py-2`}
                        loading={submitLoading}
                        disabled={submitLoading}
                      >
                        <span>Purchase Event</span>
                      </InteractiveButton>

                      <button
                        type="button"
                        onClick={() => setAddEventModal(false)}
                        className={`flex w-full justify-center items-center bg-gray-500 text-white tracking-wide outline-none focus:outline-none rounded  py-2`}
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </>
      )}
      {/* modal */}
    </>
  );
};

export default EventPlan;