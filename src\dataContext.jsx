import MkdSDK from 'Utils/MkdSDK';
import React, { createContext, useContext, useState } from 'react'
import { AuthContext, tokenExpireError } from "Src/authContext";

export const  DataContext = createContext()


const DataProvider = ({children}) => {
  const { dispatch } = React.useContext(AuthContext);
    const [cms, setCms] = useState();
    let sdk = new MkdSDK();

    React.useEffect(() => {
        async function getallcms() {
          try {
            const result = await sdk.getallcms();
    
            if (result) {
              setCms(result?.list);
            }
          } catch (err) {
            console.log("Error:", err);
            tokenExpireError(dispatch, err.message);
          }
        }
        getallcms();
      }, []);
  return (
    <DataContext.Provider value={{cms}}>
        {children}
      
    </DataContext.Provider>
  )
}

export default DataProvider

export const useData = ()=> useContext(DataContext)
