import React from "react";

export const BankTansferIcon = ({ className = "" }) => {
  return (
    <svg
      className={`${className}`}
      width="48"
      height="48"
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_313_3621)">
        <rect width="48" height="48" rx="11.52" fill="#FC1154" />
        <path
          d="M43.8854 37.3859H4.41552C3.07832 37.3859 2 36.3076 2 34.9704V12.4155C2 11.0783 3.08046 10 4.41552 10H43.8833C45.2205 10 46.2988 11.0783 46.2988 12.4155V34.9682C46.301 36.3076 45.2141 37.3859 43.8854 37.3859Z"
          fill="#FC1154"
        />
        <path
          d="M22.9327 18.7295V23.659H20.9023V19.5361C20.9023 18.522 20.1449 17.9743 19.4367 17.9743C18.8162 17.9743 18.3413 18.2717 17.8749 18.9328V23.6611H15.8444V20.0261C15.6348 20.0988 15.4251 20.1545 15.224 20.2037C15.1106 22.2983 13.63 23.7959 11.6232 23.7959C9.52858 23.7959 8.01379 22.1763 8.01379 19.9619V16.0958H10.0442V19.9533C10.0442 20.7835 10.455 21.757 11.6232 21.757C12.6951 21.757 13.1294 20.9354 13.1936 20.1545C12.3485 19.7993 11.8243 18.9456 11.8243 17.8031C11.8243 16.6264 12.7015 15.9353 13.5487 15.9353C14.6271 15.9353 15.2325 17.0136 15.224 18.0855C15.224 18.0855 15.5214 17.9807 15.8444 17.7946V16.0958H17.8749V16.7483C18.5681 16.1835 19.2121 15.9268 19.9523 15.9268C20.7739 15.9268 21.5227 16.2177 22.079 16.7334C22.6353 17.249 22.9327 17.9572 22.9327 18.7295ZM23.7478 17.8844H27.1882L23.5638 22.1934V23.6504H30.0081V21.8618H26.3602L30.0081 17.4972V16.0872H23.75V17.8844H23.7478ZM36.5935 16.9815C37.2782 17.6747 37.6334 18.6568 37.6334 19.8164V20.5182H32.1733C32.3188 21.4531 32.9863 22.0073 33.9705 22.0073C34.7921 22.0073 35.5002 21.6778 35.8148 21.1451L35.8233 21.1365H35.8319L37.4258 21.8532L37.4151 21.8639C36.7797 23.0877 35.5067 23.7895 33.9363 23.7895C31.6726 23.7895 30.1579 22.2105 30.1579 19.8678C30.1579 18.7638 30.5601 17.7496 31.2939 17.0243C31.9936 16.3247 32.9371 15.946 33.9363 15.946C35.0146 15.9524 35.926 16.3055 36.5935 16.9815ZM35.6522 18.9306C35.4831 18.1497 34.8477 17.6812 33.9363 17.6812C33.1061 17.6812 32.4707 18.1476 32.2289 18.9306H35.6522ZM42.7318 16.0402C42.458 15.9589 42.152 15.9589 42.152 15.9589C41.4845 15.9589 40.8384 16.2156 40.2906 16.6991V16.0872H38.2602V23.6504H40.2906V18.9649C40.6929 18.2075 41.3048 17.7881 42.0215 17.7881C42.0215 17.7881 42.3446 17.7881 42.5457 17.8523L42.7318 16.0402ZM7.09808 30.9804V31.2864H6.30859V27.1721H7.09808V28.7425C7.30775 28.5649 7.58161 28.413 7.98384 28.413C8.66849 28.413 9.3296 28.888 9.3296 29.8786C9.3296 30.8692 8.62998 31.3442 8.00096 31.3442C7.58161 31.3442 7.29063 31.1837 7.09808 30.9804ZM8.523 29.87C8.523 29.3394 8.20849 29.057 7.7977 29.057C7.41045 29.057 7.09594 29.3138 7.09594 29.7331V30.007C7.09594 30.4349 7.42543 30.683 7.78059 30.683C8.20849 30.683 8.523 30.4006 8.523 29.87ZM9.73183 29.87C9.73183 28.8794 10.44 28.4045 11.1332 28.4045C11.4884 28.4045 11.7537 28.5243 11.9634 28.6954V28.4622H12.7528V31.2821H11.9634V30.989C11.7772 31.1901 11.5034 31.3356 11.0861 31.3356C10.4571 31.3442 9.73183 30.8692 9.73183 29.87ZM11.9548 30.007V29.7331C11.9548 29.3138 11.6403 29.057 11.2616 29.057C10.8422 29.057 10.5277 29.3394 10.5277 29.87C10.5277 30.4006 10.8422 30.683 11.268 30.683C11.6488 30.683 11.9548 30.4327 11.9548 30.007ZM13.4439 31.2864V28.4686H14.2334V28.8388C14.4516 28.6056 14.764 28.4109 15.1684 28.4109C15.7974 28.4109 16.144 28.8131 16.144 29.3608V31.295H15.3545V29.5962C15.3545 29.2667 15.194 29.0977 14.8945 29.0977C14.6206 29.0977 14.3639 29.2902 14.227 29.5256V31.3057H13.446V31.2864H13.4439ZM16.8201 31.2864V27.1721H17.6096V29.3865H17.6416L18.4482 28.4686H19.3832L18.3049 29.7096L19.4495 31.2885H18.5146L17.6523 30.1054H17.6203V31.2885H16.8201V31.2864ZM21.3302 30.4819V29.1126H20.8317V28.4601H21.3302V27.6556H21.435L22.1197 27.8888V28.4601H22.7166V29.1126H22.1197V30.4092C22.1197 30.5953 22.2331 30.6916 22.4256 30.6916C22.5133 30.6916 22.5711 30.6745 22.6503 30.651L22.6909 30.6745V31.2778C22.5219 31.3185 22.3935 31.3335 22.1924 31.3335C21.6939 31.3442 21.3302 31.0789 21.3302 30.4819ZM23.2707 31.2864V28.4686H24.0602V28.8067C24.2464 28.582 24.5117 28.413 24.8411 28.413C24.9545 28.413 25.0508 28.4301 25.115 28.4622L25.0102 29.0827L24.9695 29.1233C24.9203 29.1148 24.8561 29.0912 24.777 29.0912C24.471 29.0912 24.2121 29.2603 24.0517 29.5983V31.2907H23.2707V31.2864ZM25.3418 29.87C25.3418 28.8794 26.05 28.4045 26.7432 28.4045C27.0983 28.4045 27.3636 28.5243 27.5733 28.6954V28.4622H28.3628V31.2821H27.5733V30.989C27.3872 31.1901 27.1133 31.3356 26.6961 31.3356C26.0756 31.3442 25.3418 30.8692 25.3418 29.87ZM27.5733 30.007V29.7331C27.5733 29.3138 27.2674 29.057 26.8801 29.057C26.4608 29.057 26.1463 29.3394 26.1463 29.87C26.1463 30.4006 26.4608 30.683 26.8865 30.683C27.2588 30.683 27.5733 30.4327 27.5733 30.007ZM29.0624 31.2864V28.4686H29.8519V28.8388C30.0701 28.6056 30.3825 28.4109 30.7869 28.4109C31.4159 28.4109 31.7625 28.8131 31.7625 29.3608V31.295H30.973V29.5962C30.973 29.2667 30.8126 29.0977 30.513 29.0977C30.2392 29.0977 29.9824 29.2902 29.8455 29.5256V31.3057H29.0646V31.2864H29.0624ZM32.2439 30.514L32.2674 30.4413L32.8708 30.2637C32.935 30.5461 33.104 30.7387 33.4827 30.7387C33.7159 30.7387 33.8849 30.6338 33.8849 30.4648C33.8849 30.2872 33.7565 30.2081 33.2644 30.0776C32.6932 29.9406 32.3701 29.6839 32.3701 29.2239C32.3701 28.7725 32.7724 28.4173 33.3843 28.4173C33.9812 28.4173 34.3749 28.6912 34.4883 29.1576L34.4562 29.2218L33.8592 29.3672C33.8036 29.1019 33.6175 28.965 33.3757 28.965C33.1896 28.965 33.0462 29.0527 33.0462 29.1832C33.0462 29.3608 33.2644 29.4336 33.7801 29.5769C34.3363 29.731 34.6252 29.9556 34.6252 30.4156C34.6252 30.9398 34.1823 31.3506 33.4977 31.3506C32.8002 31.3442 32.3894 31.0211 32.2439 30.514ZM35.3291 31.2864V29.1212H34.8541V28.4686H35.3291V28.0086C35.3291 27.3796 35.8041 27.1143 36.3047 27.1143C36.4652 27.1143 36.5957 27.1379 36.7069 27.17V27.7819L36.6663 27.8139C36.6021 27.7819 36.5058 27.7647 36.4095 27.7647C36.2405 27.7647 36.1186 27.8525 36.1186 28.0557V28.4751H36.6984V29.1276H36.1186V31.295H35.3291V31.2864ZM36.9637 29.87C36.9637 28.9928 37.6483 28.4045 38.4143 28.4045C39.253 28.4045 39.8328 28.9436 39.8328 29.8551V30.0647H37.7147C37.7553 30.452 38.0292 30.7002 38.4399 30.7002C38.8015 30.7002 38.9877 30.5226 39.0839 30.2658L39.7451 30.33L39.7857 30.3942C39.5675 31.0232 39.1011 31.3292 38.425 31.3292C37.5777 31.3442 36.9637 30.7558 36.9637 29.87ZM39.0839 29.5555C39.0433 29.2175 38.7694 28.9992 38.4079 28.9992C38.0784 28.9992 37.796 29.2175 37.7318 29.5555H39.0839ZM40.3548 31.2864V28.4686H41.1443V28.8067C41.3304 28.582 41.5957 28.413 41.9252 28.413C42.0386 28.413 42.1349 28.4301 42.1991 28.4622L42.0943 29.0827L42.0536 29.1233C42.0044 29.1148 41.9402 29.0912 41.861 29.0912C41.5551 29.0912 41.2962 29.2603 41.1357 29.5983V31.2907H40.3548V31.2864Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_313_3621">
          <rect width="48" height="48" rx="12" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
