// DynamicHeadLinks.js
import React, { useEffect, useState } from "react";
import { AuthContext, tokenExpireError } from "./authContext";
import { GlobalContext } from "./globalContext";
import MkdSDK from "Utils/MkdSDK";
import { Helmet } from "react-helmet";

let sdk = new MkdSDK();

const DynamicSEO = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [seoData, setSeoData] = useState({});
  useEffect(() => {
    const fetchDynamicSEO = async () => {
      try {
        const result = await sdk.getDynamicSEO();
        if (!result.error) {
          setSeoData(result?.list[0]);
        }
      } catch (error) {
        console.log("error", error);
        tokenExpireError(dispatch, error.message);
      }
    };

    fetchDynamicSEO();
  }, []);

  return (
    <Helmet>
      <title>{seoData.title}</title>
      <meta name="description" content={seoData.description} />
      <meta name="keywords" content={seoData.keywords} />
      {/* Add other SEO tags as needed */}
    </Helmet>
  );
};

export default DynamicSEO;
