
import {createContext, useState, useEffect, useContext } from "react"

export const AgeContext = createContext()


const AgeProvider = ({children})=>{

    const [age, setAge]= useState(20)
    const [isEligible, setIsEligible]= useState(false)

    const handleAgeChange =(val)=>{
        setAge(val)
        sessionStorage.setItem("userAge",val)
    }


useEffect(()=>{
    setIsEligible(Number(age)>18)
},[age])

useEffect(()=>{
    if(sessionStorage.getItem("userAge")){


        setAge(sessionStorage.getItem("userAge"))
    }
},[])


    return <AgeContext.Provider
    value={{setAge,handleAgeChange,age, isEligible}}
    >
        {children}
    </AgeContext.Provider>

}

export default AgeProvider;

export const useIsEligible = ()=> useContext(AgeContext)