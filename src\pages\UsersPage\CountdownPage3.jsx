import React, { useEffect, useState } from "react";
import loginBgImage from "../../assets/images/loginbg.png";
import filledinnerbg from "../../assets/images/filledinnerbg.png";
import { TypeAnimation } from "react-type-animation";
import { Shake } from "reshake";
import MkdSDK from "Utils/MkdSDK";
import moment from "moment";
import { GlobalContext, showToast } from "Src/globalContext";
let sdk = new MkdSDK();

const CountdownPage3 = () => {
  const { dispatch } = React.useContext(GlobalContext);
  const [cms, setCms] = useState();
  const [input, setInput] = useState();
  const [countdown, setCountdown] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
  });

  const handleSubmit = async () => {
    try {
      const result = await sdk.postwaitlist(input);
      if (!result.error) {
        showToast(dispatch, "Registration successful");
      } else {
        showToast(dispatch, result?.message);
      }
    } catch (error) {
      // setSubmitLoading(false);
      console.log("Error", error);

      // console.log(err);
      // showToast(dispatch, err?.Error)
    }
  };

  React.useEffect(() => {
    async function getallcms() {
      try {
        const result = await sdk.getallcms();
        // console.log("wre", result);
        if (result) {
          setCms(result?.list);
        }
      } catch (err) {
        console.log("Error:", err);
        // tokenExpireError(dispatch, err.message);
      }
    }
    getallcms();
  }, []);

  const calculateTimeLeft = () => {
    // console.log(cms?.find((item) => item.content_key === "Coming_soon_date")?.content_value);

    const inputDate = cms?.find(
      (item) => item.content_key === "Coming_soon_date"
    )?.content_value;

    const parsedDate = moment(inputDate);
    const endOfDay = parsedDate.endOf("day");
    const formattedResult = endOfDay.format("YYYY-MM-DDTHH:mm:ss");

    // console.log(formattedResult);

    const eventDate = new Date(formattedResult); // Set your event date here
    const currentDate = new Date();
    const difference = eventDate - currentDate;

    if (difference > 0) {
      const days = Math.floor(difference / (1000 * 60 * 60 * 24));
      const hours = Math.floor(
        (difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
      );
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((difference % (1000 * 60)) / 1000);

      setCountdown({ days, hours, minutes, seconds });
    }
  };

  useEffect(() => {
    const interval = setInterval(() => {
      calculateTimeLeft();
    }, 1000);

    return () => {
      clearInterval(interval);
    };
  }, [cms]);

  return (
    <div
      className=" min-h-[95vh] h-[95vh] flex gap-4 items-center justify-center text-center rounded-3xl md:p-12 p-2 relative m-4"
      style={{
        backgroundImage: `url(${loginBgImage})`,
        backgroundSize: "cover",
        backgroundPosition: "center center",
      }}
    >
      {/* texts */}
      {/* top-1/2 bottom-1/2 right-0 left-0 */}
      <div className="  flex flex-col justify-around items-center gap-8 bg-red-600 p-4 py-10 z-100  bg-opacity-20 h-full w-full rounded-3xl">
        <div className="  w-full flex ">
          {/* LOGO */}
          <div className="flex flex-col w-full relativemd:items-center items-center ">
            <svg
              width="164"
              height="50"
              viewBox="0 0 164 50"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M54.9686 24.2362C52.702 24.5078 50.6973 26.2963 50.27 28.6204C50.033 29.9135 49.4642 30.9167 48.2663 31.9325C48.7089 30.5562 48.9728 29.4286 49.1334 28.278C49.5425 25.3447 49.1821 22.3472 49.9412 19.433C50.1572 18.6019 50.4067 17.8588 50.9669 17.2323C52.9524 15.0115 52.3034 12.8338 51.1141 10.3777C50.6734 10.5776 50.5339 11.0434 50.2366 11.3657C48.6 13.1398 48.1765 15.1234 49.1802 17.3777C49.6161 18.3571 49.2185 19.3183 48.9489 20.2938C48.4078 18.2461 47.6536 16.3008 46.5982 14.4855C45.9271 13.3311 45.2044 12.213 44.7972 10.9209C44.2924 9.31606 43.3814 7.82022 43.8766 5.99634C44.0506 5.35458 43.9263 4.63918 43.7217 3.92187C43.2657 2.32465 41.8031 1.68481 40.8567 0.566756C40.7745 0.469201 40.5929 0.377385 40.481 0.399383C40.2325 0.44816 40.3042 0.692046 40.3089 0.862288C40.3214 1.3491 40.2019 1.81966 40.1483 2.29022C39.9466 4.08829 40.1034 5.97912 41.7543 7.00249C43.4779 8.07081 43.3288 9.87078 44.0764 11.3312C42.9598 10.6072 41.9226 9.8354 40.958 8.98227C39.4725 7.66816 37.8043 6.56923 37.0988 4.49094C36.5989 3.01901 33.84 1.98991 32.0552 2.28353C32.3143 2.90137 32.5724 3.51539 32.8276 4.12941C33.4519 5.63481 34.4738 6.66774 36.1467 6.93841C36.8876 7.05892 37.5806 7.34393 38.1523 7.81162C39.6981 9.07696 41.22 10.3748 42.7294 11.6842C43.6644 12.4952 44.4329 13.4468 45.1126 14.8269C45.1117 14.8279 45.1107 14.8298 45.1098 14.8308C45.0954 14.8461 45.0801 14.8556 45.0648 14.8681C43.8708 14.3191 42.9178 13.8265 41.9245 13.4526C41.0058 13.1073 40.2267 12.5736 39.7191 11.7855C38.4611 9.83444 36.1066 9.76749 34.0273 10.305C34.2797 10.6732 34.5264 11.0405 34.7806 11.403C35.6783 12.6817 36.8713 13.641 38.4592 13.509C41.3137 13.2718 43.3699 15.1253 45.7866 16.0253C45.9023 16.0684 45.9902 16.2424 46.0485 16.3735C47.2559 19.0916 47.9547 21.9178 47.9518 25.2452C47.9461 25.2471 47.9413 25.25 47.9356 25.2519C47.1565 24.1549 46.5619 23.3199 45.9701 22.484C45.4329 21.7246 44.8469 21.0188 44.7981 19.9945C44.6891 17.6694 43.0306 16.3142 40.5584 15.6877C40.6818 16.4911 40.7812 17.2687 40.9246 18.0386C41.133 19.1624 41.6932 20.1169 42.7227 20.6104C43.9932 21.2187 44.9281 22.1101 45.648 23.2922C46.0571 23.9636 46.4835 24.6398 47.0035 25.2242C47.731 26.04 47.9767 26.8721 47.7702 27.9815C47.2884 30.5696 46.5819 33.0543 45.1107 35.2904C45.0801 33.216 44.1175 31.1625 45.1079 29.0986C46.0801 27.071 45.0342 25.0883 43.4999 23.5839C42.4551 25.5771 41.7333 27.5875 43.3068 29.6543C43.7628 30.252 43.9301 30.9091 43.975 31.6398C44.0381 32.6526 44.0381 33.6904 44.2771 34.6659C44.6566 36.2153 44.0592 37.3649 43.0611 38.4131C42.3614 39.1477 41.6741 39.9052 40.9026 40.5593C39.9256 41.3876 38.9696 42.2752 37.6284 42.6606C38.6934 41.0529 38.6895 38.8627 40.4351 37.6767C42.6138 36.1971 42.4675 34.0863 42.2132 31.7182C41.4762 32.2978 40.8768 32.7875 40.2573 33.2523C39.3128 33.9601 38.8129 35.0054 38.8865 36.0671C38.9859 37.4921 38.6637 38.7259 38.0443 39.9501C37.6542 40.721 37.2097 41.4813 36.9545 42.2981C36.6275 43.3463 35.9326 43.8992 34.9594 44.2368C33.4643 44.7542 31.9558 45.2142 30.3871 45.4533C29.8976 45.5279 29.4244 45.5528 28.9837 45.2286C28.5679 44.9235 28.3901 44.7083 28.9493 44.3382C30.2207 43.4955 31.3497 42.3918 33.0398 42.4186C33.5733 42.4272 34.013 42.0973 34.4785 41.8362C35.859 41.0596 36.1199 39.5781 36.9392 38.2429C35.8599 38.4055 34.9126 38.4543 34.0254 38.7087C33.1278 38.9669 32.1795 39.4049 31.8296 40.325C31.1585 42.0887 29.7016 42.9753 28.2419 43.9001C28.1836 43.9804 28.1147 44.0464 28.0421 44.0981C27.8585 44.2282 27.6444 44.2569 27.4618 44.1038C26.3252 43.1531 24.7048 42.7046 24.0997 41.2059C23.0787 38.679 20.9269 38.483 18.5829 38.3223C19.0006 39.1639 19.2979 39.9482 19.7511 40.6282C20.3189 41.4794 21.0837 42.2675 22.1476 42.3316C23.7479 42.4282 24.9849 43.1952 26.1875 44.1057C26.4447 44.3008 26.9112 44.4223 26.8548 44.7771C26.784 45.2209 26.2889 45.3586 25.9017 45.4658C25.6264 45.5423 25.3013 45.4925 25.0079 45.4447C23.2241 45.153 21.4919 44.6767 19.8333 43.9479C19.4136 43.7633 19.0637 43.5329 18.8658 43.068C18.5274 42.2742 18.0485 41.5358 17.7569 40.7277C17.2933 39.4461 16.3756 38.3252 16.5515 36.8102C16.8402 34.3245 15.3632 33.0323 13.2649 31.9439C12.7907 34.3943 13.1865 36.4372 15.4081 38.0583C16.8478 39.1085 16.9252 41.1064 17.7292 42.6453C15.3326 41.4928 13.4972 39.7569 11.9055 37.7484C11.6092 37.3745 11.0633 37.0522 11.1226 36.55C11.4199 34.0175 10.9008 31.3223 12.5135 29.0403C12.8347 28.586 12.8959 28.0953 13.001 27.576C13.3146 26.0342 12.393 24.8693 11.9533 23.4328C11.2688 24.0296 11.0337 24.7679 10.5987 25.3619C9.84828 26.3852 9.68673 27.642 10.2096 28.6806C11.3099 30.8689 10.418 32.9797 10.4362 35.1556C9.98212 35.0781 9.92094 34.7883 9.81005 34.5731C8.82541 32.6565 8.1983 30.6212 7.76238 28.5161C7.48037 27.1513 7.67443 25.9883 8.66481 24.9085C9.42001 24.0851 9.98499 23.0846 10.6169 22.1512C10.8759 21.7686 11.1599 21.4339 11.6053 21.2655C14.2505 20.268 14.7982 18.0941 14.8317 15.5701C13.9503 15.769 13.3089 16.318 12.5957 16.7139C11.5939 17.2696 10.8195 18.162 10.7698 19.2733C10.6599 21.7237 8.81776 23.1105 7.64097 24.9277C7.47846 23.2482 7.78724 21.6558 8.11895 20.0471C8.65907 17.4284 9.71731 15.4046 12.5804 14.6194C13.9589 14.2416 15.1491 13.3598 16.7321 13.5061C18.7081 13.6888 20.5885 12.1538 21.1946 10.0353C20.3026 10.0353 19.4365 9.9693 18.5857 10.0515C17.517 10.1558 16.4119 10.5221 15.8536 11.4221C15.1061 12.6262 14.0555 13.2383 12.8184 13.6926C12.6951 13.7519 12.5699 13.8112 12.4456 13.8705C11.7956 14.1814 11.1207 14.5027 10.289 14.8996C10.7192 13.8983 11.2163 13.1666 11.7841 12.5019C11.852 12.4225 11.9198 12.3441 11.9896 12.2666C13.4379 11.0214 14.8986 9.7914 16.3297 8.52606C17.3287 7.64233 18.3621 6.88581 19.7654 6.78251C20.1067 6.75765 20.4393 6.50898 20.7672 6.32152C22.3723 5.40432 22.6715 3.73632 23.3263 2.19745C22.5568 2.06929 21.9412 2.39925 21.294 2.50733C19.5532 2.79808 18.2904 3.66268 17.7435 5.43014C17.581 5.95617 17.3516 6.48602 16.8966 6.85711C16.1232 7.4893 15.3422 8.11385 14.5707 8.74891C13.5297 9.60586 12.4896 10.4724 11.2124 11.5311C11.1924 11.5177 11.1809 11.5034 11.1627 11.49C11.4189 10.7928 11.6015 10.3786 11.7114 9.95017C12.0441 8.64561 12.611 7.52278 13.7907 6.75669C14.217 6.48028 14.5143 6.05659 14.7791 5.57169C15.7322 3.82909 15.0105 2.11902 14.9053 0.258789C14.2046 0.623184 13.9006 1.23242 13.4016 1.5796C11.5269 2.8832 11.1264 4.64779 11.5795 6.76338C11.6942 7.29802 11.6608 7.86231 11.4371 8.41512C11.005 9.48535 10.7373 10.6302 10.2316 11.6602C8.85122 14.4683 7.11424 17.1156 6.49669 20.2785C6.12483 19.3986 5.8323 18.5063 6.20512 17.613C7.15535 15.3396 6.91923 13.3148 5.23005 11.446C4.41748 10.546 4.276 10.3251 3.97201 10.9276C3.88788 11.0931 3.79324 11.3178 3.66706 11.6124C3.66132 11.7616 3.69765 11.928 3.64316 12.0572C2.80669 14.0322 3.18334 15.7891 4.58955 17.3567C5.10864 17.9353 5.30843 18.6163 5.50632 19.3594C6.58368 23.4089 5.55029 27.707 7.03872 31.7173C6.21564 30.9674 5.41646 30.2291 5.30079 29.0718C4.9901 25.9682 2.73309 24.9631 0 24.1654C0.163469 24.877 0.335539 25.4441 0.418707 26.0237C0.715054 28.0724 1.77521 29.4448 3.78559 30.0971C4.40505 30.2979 4.97672 30.6193 5.44992 31.1759C6.90967 32.8908 8.31206 34.619 9.39038 36.6218C10.3616 38.4246 11.7927 39.9147 13.4465 41.467C11.8501 41.1906 10.7354 40.5976 9.92763 39.4719C8.48508 37.4606 5.6803 37.6069 3.46439 38.4323C3.90509 38.9181 4.36777 39.359 4.74729 39.8621C5.90973 41.401 7.45169 41.883 9.30912 41.6372C10.0022 41.5454 10.6828 41.312 11.4122 41.6191C13.4092 42.4607 15.5353 42.9781 17.3554 44.2569C18.299 44.9197 19.3954 45.3701 20.5168 45.7135C21.4689 46.0052 22.4124 46.3237 23.5185 46.6814C22.1094 47.1089 20.9183 47.1998 19.7071 46.6536C18.9452 46.3103 18.1298 46.3065 17.2799 46.4002C15.8517 46.557 15.0248 47.6531 13.9245 48.3207C13.2247 48.7453 13.457 48.9729 13.8624 49.1843C13.9312 49.2207 14.0048 49.256 14.0813 49.2924C16.5123 50.3091 18.7588 50.2517 20.7453 48.2623C21.0598 47.9467 21.5827 47.8272 22.0721 47.7229C25.2717 47.0439 28.4684 46.7904 31.7063 47.4838C33.1918 47.8023 34.6936 48.0261 35.7882 49.2293C36.0549 49.5229 36.4258 49.5965 36.7681 49.7381C38.5108 50.4611 40.0288 49.5009 41.6186 49.0935C41.935 49.0122 42.0459 48.8257 41.7993 48.5234C40.3013 46.6862 37.8932 45.9947 35.5311 46.7321C33.9642 47.2208 33.6287 47.2112 32.2932 46.6422C32.3946 46.3705 32.6479 46.4097 32.8611 46.3629C34.8485 45.9296 36.6553 45.0316 38.3932 44.0378C41.023 42.5334 43.7131 41.3599 46.8314 41.6468C49.4288 41.8859 50.5922 39.9711 52.0644 38.3299C49.2194 37.5533 46.7416 37.5285 44.8756 40.1213C44.2312 41.0165 43.4311 41.2413 42.2486 41.2575C43.7705 39.8286 45.1547 38.3232 46.1135 36.5261C47.1517 34.5798 48.5321 32.9128 49.9345 31.2438C50.3111 30.7962 50.7079 30.4156 51.2747 30.2693C54.1694 29.5223 54.9313 27.2307 55.351 24.6838C55.414 24.3567 55.371 24.1874 54.9686 24.2362Z"
                fill="#C1272D"
              />
              <path
                d="M73.5369 14.3883L75.5502 12.047L82.9187 22.1736H86.2923L77.377 9.92188L85.9071 0H82.3088L75.728 7.65517L73.9012 9.78032L73.5369 10.204V9.28108V4.64437V0.0918153H70.5391V0.524118V5.16083V13.6911V17.8754V22.1736H73.5369V14.3883Z"
                fill="white"
              />
              <path
                d="M130.619 0.363281H115.084V3.36165H125.102L114.255 19.2917L112.213 22.2911H129.495V19.2917H117.731L128.577 3.36165L130.619 0.363281Z"
                fill="white"
              />
              <path
                d="M96.3214 0H94.9678L94.1896 2.09168L86.7227 22.1736H89.6326L95.6446 6.0063L101.656 22.1736H104.567L97.0996 2.09264L96.3214 0Z"
                fill="white"
              />
              <path
                d="M110.008 0.0917969H107.01V22.2855H110.008V0.0917969Z"
                fill="white"
              />
              <path
                d="M161.003 0.0917969V15.3849L151.399 2.23896L149.831 0.0917969H148.402V22.2855H151.399V6.99617L161.003 20.1431L162.568 22.2855H164.001V0.0917969H161.003Z"
                fill="white"
              />
              <path
                d="M133.066 0.363281V3.36165V9.68931V12.6877V19.2917V22.2853V22.2901H145.06V19.2917H136.064V12.6877H144.25V9.68931H136.064V3.36165H145.06V0.363281H136.064H133.066Z"
                fill="white"
              />
              <path
                d="M88.8323 47.2866L83.5745 28.4395H82.6539L76.791 47.2933L71.6289 28.4395H70.5391L76.3073 49.5112H77.2021L83.0851 30.5962L88.361 49.5112H89.2558L95.6511 28.4395H94.5527L88.8323 47.2866Z"
                fill="white"
              />
              <path
                d="M102.388 28.4395H101.336V49.5112H102.388V28.4395Z"
                fill="white"
              />
              <path
                d="M122.267 47.3096L109.815 28.4395H109.098V49.5112H110.149V30.8563L122.46 49.5112H123.318V28.4395H122.267V47.3096Z"
                fill="white"
              />
              <path
                d="M125.688 31.5755C125.688 29.9629 127.025 28.6211 128.634 28.6211C129.37 28.6278 130.085 28.9664 130.605 29.4895L130.228 29.9983C129.815 29.5746 129.239 29.2791 128.61 29.2791C127.457 29.2791 126.343 30.2757 126.343 31.5745C126.343 32.8705 127.446 33.8632 128.62 33.8632C129.268 33.8632 129.819 33.5572 130.232 33.1516L130.606 33.671C130.129 34.1444 129.486 34.5145 128.639 34.5145C127.043 34.5165 125.688 33.2023 125.688 31.5755Z"
                fill="white"
              />
              <path
                d="M136.173 28.7051V34.4426H135.529V31.8909H132.622V34.4426H131.975V28.7051H132.622V31.2501H135.529V28.7051H136.173Z"
                fill="white"
              />
              <path
                d="M141.706 33.4987H138.575L138.156 34.4417H137.455L140.106 28.6152H140.178L142.831 34.4417H142.127L141.706 33.4987ZM141.457 32.9363L140.144 29.9858L138.828 32.9363H141.457Z"
                fill="white"
              />
              <path
                d="M148.717 28.6898V34.5307H148.686L144.755 30.1637V34.4494H144.107V28.6152H144.143L148.071 32.9794V28.6898H148.717Z"
                fill="white"
              />
              <path
                d="M155.896 31.8128C155.896 33.4215 154.658 34.5213 153.178 34.5213C151.531 34.5213 150.207 33.1977 150.207 31.5775C150.207 29.9573 151.534 28.627 153.153 28.627C153.858 28.6308 154.715 28.9866 155.231 29.4887L154.867 30.0262C154.423 29.5853 153.725 29.2754 153.149 29.2754C151.939 29.2754 150.865 30.2796 150.865 31.5785C150.865 32.8381 151.879 33.8672 153.202 33.8672C154.322 33.8672 155.205 33.1164 155.205 32.1016C155.205 32.0586 155.205 32.0194 155.201 31.9878H153.145V31.4044H155.87C155.882 31.4962 155.896 31.6492 155.896 31.8128Z"
                fill="white"
              />
              <path
                d="M158.036 29.3421V31.1573H160.394V31.8048H158.036V33.798H160.757V34.4426H157.389V28.7051H160.757V29.3421H158.036Z"
                fill="white"
              />
              <path
                d="M127.503 39.2664L125.479 35.9668H126.204L127.826 38.5434L129.449 35.9668H130.178L128.147 39.2664V41.7043H127.502V39.2664H127.503Z"
                fill="white"
              />
              <path
                d="M130.514 38.8391C130.514 37.2123 131.841 35.8848 133.46 35.8848C135.082 35.8848 136.409 37.2123 136.409 38.8391C136.409 40.4555 135.082 41.7792 133.456 41.7792C131.841 41.7792 130.514 40.4555 130.514 38.8391ZM135.754 38.8353C135.754 37.5719 134.723 36.5361 133.456 36.5361C132.197 36.5361 131.169 37.5719 131.169 38.8353C131.169 40.0949 132.197 41.124 133.456 41.124C134.722 41.124 135.754 40.0959 135.754 38.8353Z"
                fill="white"
              />
              <path
                d="M137.838 39.4749V35.9648H138.489V39.4711C138.489 40.4361 139.115 41.1371 139.962 41.1371C140.809 41.1371 141.435 40.4323 141.435 39.4711V35.9648H142.086V39.4749C142.086 40.8129 141.183 41.7923 139.962 41.7923C138.745 41.7923 137.838 40.8129 137.838 39.4749Z"
                fill="white"
              />
              <path
                d="M147.141 41.7043L145.54 39.4654H144.469V41.7043H143.822L143.818 35.9668H145.511C146.575 35.9668 147.443 36.7855 147.443 37.7964C147.443 38.5405 146.935 39.1813 146.237 39.3908L147.941 41.7043H147.141ZM145.533 38.9212C146.217 38.9212 146.779 38.4018 146.779 37.7821C146.779 37.1202 146.209 36.6076 145.533 36.6076H144.467V38.9173L145.533 38.9212Z"
                fill="white"
              />
              <path
                d="M130.77 43.2275L128.158 49.054H128.08L125.479 43.2275H126.165L128.114 47.673L130.071 43.2275H130.77Z"
                fill="white"
              />
              <path
                d="M132.047 43.2275H132.694V48.9651H132.047V43.2275Z"
                fill="white"
              />
              <path
                d="M135.135 43.8645V45.6798H137.494V46.3273H135.135V48.3205H137.857V48.9651H134.488V43.2275H137.857V43.8645H135.135Z"
                fill="white"
              />
              <path
                d="M138.953 43.2275H139.647L140.893 47.4166L142.294 43.6933H142.344L143.739 47.4166L144.984 43.2275H145.681L143.902 49.0005H143.803L142.312 45.1958L140.829 49.0005H140.733L138.953 43.2275Z"
                fill="white"
              />
            </svg>
            <p className=" md:text-7xl text-4xl text-white">Kaizenwin</p>
          </div>
        </div>
        <div className="  w-full">
          {/* <Shake
            h={33}
            v={51}
            r={59}
            dur={960}
            int={18.6}
            max={27}
            fixed={true}
            fixedStop={false}
            freez={false}
          >
            <h1>&lt;Shake /&gt;</h1>
          </Shake> */}
          <p className="text-companyRed md:text-7xl text-4xl font-bold ">
            <TypeAnimation
              sequence={[
                // Same substring at the start will only be typed out once, initially
                " Coming soon !!",
                5000, // wait 1s before replacing "Mice" with "Hamsters"
                " ",
                1000,
                // "We produce food for Guinea Pigs",
                // 1000,
                // "We produce food for Chinchillas",
                // 1000,
              ]}
              wrapper="span"
              speed={10}
              style={{ fontSize: "1em", display: "inline-block" }}
              repeat={Infinity}
            />
          </p>
        </div>
        <div className=" bg-white skew-x-6 bg-opacity-25 flex flex-col justify-center items-center w-full">
          <div className="text-center w-full flex justify-center md:gap-10 gap-4 flex-1">
            {/* <p className=" md:text-6xl text-4xl font-bold">20, 40, 12</p> */}
            <div className="">
              <p className=" md:text-6xl text-4xl font-bold">{`${countdown.days}  `}</p>
              <p className=" md:text-6xl text-4xl">days</p>
            </div>
            <div>
              <p className=" md:text-6xl text-4xl font-bold">{`${countdown.hours}  `}</p>
              <p className=" md:text-6xl text-4xl">hrs</p>
            </div>
            <div>
              <p className=" md:text-6xl text-4xl font-bold">{`${countdown.minutes}  `}</p>
              <p className=" md:text-6xl text-4xl">min</p>
            </div>
            <div>
              <p className=" md:text-6xl text-4xl font-bold">{`${countdown.seconds}  `}</p>
              <p className=" md:text-6xl text-4xl">sec</p>
            </div>
          </div>
          <p className=" self-end md:text-4xl text-2xl">
            until KaizenWin launch
          </p>
        </div>
        <div className=" w-full mb-2 text-white md:text-lg text-base ">
          <p className="mb-2 text-white md:text-lg text-base">
            Enter your email and be the first to find out
          </p>
          <div className=" bg-black py-4 w-full px-2 md:flex md:flex-row flex flex-col items-center justify-center gap-4">
            <input
              className=" rounded-full md:w-1/2 w-full text-black"
              type="text"
              placeholder="enter your email"
              onChange={(e) => {
                setInput(e.target.value);
              }}
            />
            <button
              type="submit"
              className=" bg-red-700 hover:bg-companyRed hover:scale-105 transition-all md:px-4  px-2 py-2 rounded-full  w-fit text-white"
              onClick={handleSubmit}
            >
              {" "}
              Submit
            </button>
          </div>
        </div>

        <div className="flex justify-between items-center gap-4 self-end">
          <p className="text-white text-s cursor-pointer hover:scale-105 shadow-lg rounded-full shadow-white">
            <svg
              width="48"
              height="48"
              viewBox="0 0 48 48"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle cx="24" cy="24" r="24" fill="#353A41" />
              <g clip-path="url(#clip0_322_3310)">
                <path
                  d="M29.2462 12.668H20.0846C16.3612 12.668 13.332 15.6971 13.332 19.4205V28.5821C13.332 32.3055 16.3612 35.3346 20.0846 35.3346H29.2462C32.9695 35.3346 35.9987 32.3055 35.9987 28.5821V19.4205C35.9987 15.6971 32.9695 12.668 29.2462 12.668ZM33.7184 28.5821C33.7184 31.052 31.7161 33.0544 29.2462 33.0544H20.0846C17.6146 33.0544 15.6123 31.052 15.6123 28.5821V19.4205C15.6123 16.9505 17.6146 14.9482 20.0846 14.9482H29.2462C31.7161 14.9482 33.7184 16.9505 33.7184 19.4205V28.5821Z"
                  fill="white"
                />
                <path
                  d="M24.6632 18.1387C21.4307 18.1387 18.8008 20.7685 18.8008 24.001C18.8008 27.2335 21.4307 29.8634 24.6632 29.8634C27.8957 29.8634 30.5256 27.2336 30.5256 24.001C30.5256 20.7685 27.8957 18.1387 24.6632 18.1387ZM24.6632 27.5832C22.6848 27.5832 21.081 25.9795 21.081 24.0011C21.081 22.0227 22.6849 20.4189 24.6632 20.4189C26.6416 20.4189 28.2453 22.0227 28.2453 24.0011C28.2453 25.9794 26.6415 27.5832 24.6632 27.5832Z"
                  fill="white"
                />
                <path
                  d="M30.5415 19.5888C31.3173 19.5888 31.9462 18.9599 31.9462 18.1841C31.9462 17.4082 31.3173 16.7793 30.5415 16.7793C29.7656 16.7793 29.1367 17.4082 29.1367 18.1841C29.1367 18.9599 29.7656 19.5888 30.5415 19.5888Z"
                  fill="white"
                />
              </g>
              <defs>
                <clipPath id="clip0_322_3310">
                  <rect
                    width="22.6667"
                    height="22.6667"
                    fill="white"
                    transform="translate(13.334 12.667)"
                  />
                </clipPath>
              </defs>
            </svg>
          </p>
          <p className="text-white text-s cursor-pointer hover:scale-105 shadow-lg rounded-full shadow-white">
            <svg
              width="48"
              height="48"
              viewBox="0 0 48 48"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle cx="24" cy="24" r="24" fill="#353A41" />
              <path
                d="M28.5725 25.9652L29.1969 21.8984H25.2991V19.2642C25.2991 18.1549 25.8461 17.0688 27.5868 17.0688H29.3583V13.6103C29.3583 13.6103 27.7483 13.333 26.208 13.333C22.9961 13.333 20.9006 15.2819 20.9006 18.8018V21.8984H17.334V25.9652H20.9006V35.7865C21.6172 35.9018 22.3487 35.9561 23.096 35.9561C23.8434 35.9561 24.5748 35.8946 25.2915 35.7865V25.9652H28.5725Z"
                fill="white"
              />
            </svg>
          </p>
          <p className="text-white text-s cursor-pointer hover:scale-105 shadow-lg rounded-full shadow-white">
            <svg
              width="48"
              height="48"
              viewBox="0 0 48 48"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M24 48C37.2548 48 48 37.2548 48 24C48 10.7452 37.2548 0 24 0C10.7452 0 0 10.7452 0 24C0 37.2548 10.7452 48 24 48Z"
                fill="#353A41"
              />
              <g clip-path="url(#clip0_348_3281)">
                <path
                  d="M34.0001 22.4305C32.0346 22.4353 30.1174 21.8185 28.5193 20.6673V28.6958C28.5187 30.1827 28.067 31.6341 27.2246 32.8558C26.3822 34.0774 25.1893 35.0112 23.8052 35.5322C22.4212 36.0532 20.9121 36.1367 19.4796 35.7713C18.0472 35.406 16.7598 34.6093 15.7895 33.4878C14.8192 32.3664 14.2122 30.9735 14.0498 29.4956C13.8874 28.0176 14.1773 26.525 14.8807 25.2173C15.5841 23.9096 16.6675 22.8492 17.986 22.1778C19.3045 21.5064 20.7953 21.256 22.259 21.4602V25.4982C21.5892 25.2862 20.8699 25.2926 20.204 25.5165C19.538 25.7404 18.9593 26.1702 18.5506 26.7448C18.1419 27.3193 17.9241 28.0091 17.9282 28.7156C17.9324 29.4221 18.1583 30.1093 18.5737 30.6789C18.9891 31.2486 19.5727 31.6716 20.2413 31.8875C20.9098 32.1035 21.6291 32.1013 22.2964 31.8814C22.9636 31.6615 23.5448 31.235 23.9568 30.6629C24.3689 30.0908 24.5907 29.4023 24.5907 28.6958V13.001H28.5193C28.5165 13.3348 28.5443 13.6682 28.6023 13.9969C28.7388 14.7307 29.0227 15.4287 29.4365 16.0484C29.8503 16.668 30.3854 17.1961 31.009 17.6005C31.8962 18.1908 32.9364 18.5055 34.0001 18.5053V22.4305Z"
                  fill="white"
                />
              </g>
              <defs>
                <clipPath id="clip0_348_3281">
                  <rect
                    width="20"
                    height="23"
                    fill="white"
                    transform="translate(14 13)"
                  />
                </clipPath>
              </defs>
            </svg>
          </p>
        </div>
      </div>
    </div>
  );
};

export default CountdownPage3;
