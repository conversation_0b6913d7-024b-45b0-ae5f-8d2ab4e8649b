
import { AuthContext, tokenExpireError } from "Src/authContext";
import { GlobalContext } from "Src/globalContext";
import MkdSDK from "Utils/MkdSDK";
import React, { useEffect, useState } from "react";
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  makeStyles,
} from "@material-ui/core";

const useStyles = makeStyles((theme) => ({
  formControl: {
    // margin: theme.spacing(1),
    minWidth: 120,
    paddingInline: 10,
    borderRadius: '9999px', // Rounded border
    // border: '2px solid red', // Red border
    backgroundColor: '#F8F8F8', // Gray background
    '& .MuiOutlinedInput-root': {
      '& fieldset': {
        border: 'none', // Remove default border
      },
      '&:hover fieldset': {
        border: 'none', // Remove border on hover
      },
      '&.Mui-focused fieldset': {
        border: 'none', // Remove border when focused
      },
    },
    '& .MuiOutlinedInput-input': {
      padding: '10px 14px', // Adjust input padding if needed
    },
    '& .MuiInput-underline:before': {
      borderBottom: 'none', // Remove underline before selection
    },
    '& .MuiInput-underline:after': {
      borderBottom: 'none', // Remove underline after selection
    },
  },
  select: {
    '&.MuiSelect-select': {
      backgroundColor: 'transparent', // Transparent background
    },
  },
  menuItem: {
    '&:hover': {
      backgroundColor: 'rgba(193,39,44,0.1)', // Light red background on hover
      '& .MuiTypography-body1': {
        color: '#8b0000', // Deep red text color on hover
      },
    },
  },
  selectedMenuItem: {
    color: 'red', // Red text color for selected item
    backgroundColor: 'rgba(193,39,44,0.1) !important', // Light red background for selected item
    '&:hover': {
      backgroundColor: '#ffcccc', // Maintain light red background on hover for selected item
    },
    '& .MuiTypography-body1': {
      color: '#8b0000 !important', // Deep red text color on hover
    },
  },
}));
let sdk = new MkdSDK();

const SelectLang = ({ toggle, isOpen, isMessageSent }) => {
  const [selectedLang, setSelectedLang] = useState("en");
  const [open, setOpen] = React.useState(false);
  const [ageGroup, setAgeGroup] = useState("");
  const anchorRef = React.useRef(null);
  const classes = useStyles();

  const langList = {
    en: "English",
    de: "German",
    es: "Spanish",
    ru: "Russian",
    it: "Italian",
    fr: "French",
    nl: "Dutch",
    zh: "Mandarin",
    ar: "Arabic",
    pt: "Portuguese",
    hi: "Hindi",
    ja: "Japanese",
    pl: "Polish",
    sv: "Swedish",
    el: "Greek",
  };

  const { state: states, dispatch } = React.useContext(AuthContext);
  const { state } = React.useContext(GlobalContext);

 

  const prevOpen = React.useRef(open);
  React.useEffect(() => {
    if (prevOpen.current === true && open === false) {
      anchorRef?.current?.focus();
    }

    prevOpen.current = open;
  }, [open]);

  // ............SET LANGUAGE..............
  const handleSelectLang = async (e) => {
    setSelectedLang(e.target.value);

    let filter = {
      user_id: localStorage.getItem("user"),
    };

    sdk.setTable("profile");
    const results = await sdk.callRestAPI(
      {
        payload: { ...filter },
        page: 1,
        limit: 10,
        sortId: "",
        direction: "",
      },
      "PAGINATE"
    );

    const { list, total, limit, num_pages, page } = results;

    if (results) {
      sdk.setTable("profile");
      const result = await sdk.callRestAPI(
        {
          language: e.target.value,
          id: list[0]?.id,
        },
        "PUT"
      );
    }
    setSelectedLang(e.target.value);
    localStorage.setItem("selectedlanguage", e.target.value);
    dispatch({
      type: "SELECTEDLANG",
      payload: e.target.value,
    });
  };

  //............GET SELECTED LANGUAGE..............
  useEffect(() => {
    let filter = {
      user_id: localStorage.getItem("user"),
    };
    let pageSize = 10;

    async function getData(pageNum, limitNum, currentTableData) {
      try {
        if (states?.user) {
          sdk.setTable("profile");
          const result = await sdk.callRestAPI(
            {
              payload: { ...currentTableData },
              page: pageNum,
              limit: limitNum,
              sortId: "",
              direction: "",
            },
            "PAGINATE"
          );

          const { list, total, limit, num_pages, page } = result;

          setSelectedLang(list[0]?.language ?? "en");
          localStorage.setItem("selectedlanguage", list[0]?.language ?? "en");
        }
      } catch (error) {
        console.log("ERROR", error);
        tokenExpireError(dispatch, error.message);
      }
    }

    // getData()
    getData(1, pageSize, filter);
  }, []);

  const handleChange = (e) => {
    setAgeGroup(e.target.value);
    
    handleSelectLang(e);
  };

  const customStyles = {
    option: (base, { data, isDisabled, isFocused, isSelected }) => {
      return {
        ...base,
        backgroundColor: "red",
      };
    },
  };

  return (
    <nav className="bg-zinc-800  text-white  py-1 border-b-1 mb-0 flex flex-col justify-between relative">
      <FormControl className={classes.formControl}>
        <Select
          labelId="demo-simple-select-label"
          id="demo-simple-select"
          value={selectedLang}
          onChange={handleChange}
          // displayEmpty
          classes={{ select: classes.select }} // Apply custom styles to select
          MenuProps={{
            anchorOrigin: {
              vertical: 'bottom',
              horizontal: 'left',
            },
            transformOrigin: {
              vertical: 'top',
              horizontal: 'left',
            },
            getContentAnchorEl: null,
          }}
        >
          {langList &&
            Object.entries(langList)?.map(([key, value], i) => (

                <MenuItem className={`${classes.menuItem} ${selectedLang == key ? classes.selectedMenuItem : ''}`} value={key}> <span className={` text-[#999999] md:text-[14px] text-[12px]  hover:text-companyRed ${selectedLang == key ? "text-companyBlack" : ''}`}>{value} </span></MenuItem>
            ))}
        </Select>
      </FormControl>
      <p className="text-white md:text-sm text-[12px] mt-1">Select preferred language</p>
    </nav>
  );
};

export default SelectLang;
