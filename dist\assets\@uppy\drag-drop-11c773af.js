import{g,i as u,t as d}from"./dashboard-54af8551.js";import{U as D,y as s}from"./core-f5368a8f.js";const c={strings:{dropHereOr:"Drop here or %{browse}",browse:"browse"}},v={version:"3.0.3"};let m=class l extends D{constructor(t,e){super(t,e),this.handleDrop=async i=>{var a,o;i.preventDefault(),i.stopPropagation(),clearTimeout(this.removeDragOverClassTimeout),this.setPluginState({isDraggingOver:!1});const n=h=>{this.uppy.log(h,"error")},p=await g(i.dataTransfer,{logDropError:n});p.length>0&&(this.uppy.log("[DragDrop] Files dropped"),this.addFiles(p)),(a=(o=this.opts).onDrop)==null||a.call(o,i)},this.type="acquirer",this.id=this.opts.id||"DragDrop",this.title="Drag & Drop",this.defaultLocale=c;const r={target:null,inputName:"files[]",width:"100%",height:"100%",note:null};this.opts={...r,...e},this.i18nInit(),this.isDragDropSupported=u(),this.removeDragOverClassTimeout=null,this.onInputChange=this.onInputChange.bind(this),this.handleDragOver=this.handleDragOver.bind(this),this.handleDragLeave=this.handleDragLeave.bind(this),this.handleDrop=this.handleDrop.bind(this),this.addFiles=this.addFiles.bind(this),this.render=this.render.bind(this)}addFiles(t){const e=t.map(r=>({source:this.id,name:r.name,type:r.type,data:r,meta:{relativePath:r.relativePath||null}}));try{this.uppy.addFiles(e)}catch(r){this.uppy.log(r)}}onInputChange(t){const e=d(t.target.files);e.length>0&&(this.uppy.log("[DragDrop] Files selected through input"),this.addFiles(e)),t.target.value=null}handleDragOver(t){var e,r;t.preventDefault(),t.stopPropagation();const{types:i}=t.dataTransfer,a=i.some(n=>n==="Files"),{allowNewUpload:o}=this.uppy.getState();if(!a||!o){t.dataTransfer.dropEffect="none",clearTimeout(this.removeDragOverClassTimeout);return}t.dataTransfer.dropEffect="copy",clearTimeout(this.removeDragOverClassTimeout),this.setPluginState({isDraggingOver:!0}),(e=(r=this.opts).onDragOver)==null||e.call(r,t)}handleDragLeave(t){var e,r;t.preventDefault(),t.stopPropagation(),clearTimeout(this.removeDragOverClassTimeout),this.removeDragOverClassTimeout=setTimeout(()=>{this.setPluginState({isDraggingOver:!1})},50),(e=(r=this.opts).onDragLeave)==null||e.call(r,t)}renderHiddenFileInput(){const{restrictions:t}=this.uppy.opts;return s("input",{className:"uppy-DragDrop-input",type:"file",hidden:!0,ref:e=>{this.fileInputRef=e},name:this.opts.inputName,multiple:t.maxNumberOfFiles!==1,accept:t.allowedFileTypes,onChange:this.onInputChange})}static renderArrowSvg(){return s("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon uppy-DragDrop-arrow",width:"16",height:"16",viewBox:"0 0 16 16"},s("path",{d:"M11 10V0H5v10H2l6 6 6-6h-3zm0 0",fillRule:"evenodd"}))}renderLabel(){return s("div",{className:"uppy-DragDrop-label"},this.i18nArray("dropHereOr",{browse:s("span",{className:"uppy-DragDrop-browse"},this.i18n("browse"))}))}renderNote(){return s("span",{className:"uppy-DragDrop-note"},this.opts.note)}render(){const t=`uppy-u-reset
      uppy-DragDrop-container
      ${this.isDragDropSupported?"uppy-DragDrop--isDragDropSupported":""}
      ${this.getPluginState().isDraggingOver?"uppy-DragDrop--isDraggingOver":""}
    `,e={width:this.opts.width,height:this.opts.height};return s("button",{type:"button",className:t,style:e,onClick:()=>this.fileInputRef.click(),onDragOver:this.handleDragOver,onDragLeave:this.handleDragLeave,onDrop:this.handleDrop},this.renderHiddenFileInput(),s("div",{className:"uppy-DragDrop-inner"},l.renderArrowSvg(),this.renderLabel(),this.renderNote()))}install(){const{target:t}=this.opts;this.setPluginState({isDraggingOver:!1}),t&&this.mount(t,this)}uninstall(){this.unmount()}};m.VERSION=v.version;export{m as D};
