import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "../utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { AuthContext, tokenExpireError } from "../authContext";
import { GlobalContext, showToast } from "../globalContext";
import "react-quill/dist/quill.snow.css";
import Papa from "papaparse";
import { BackButton } from "Components/BackButton";

let sdk = new MkdSDK();

const AddAdminSEOPage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const schema = yup
    .object({
      quiz: yup.string(),
    })
    .required();

  const [fileObj, setFileObj] = React.useState({});
  const [uploadStatus, setUploadStatus] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [data, setData] = useState([]);

  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });


  const [inputFields, setInputFields] = useState([""]);

  

  const onSubmit = async (_data) => {
    try {
      for (let item in fileObj) {
        let formData = new FormData();
        formData.append("file", fileObj[item].file);
        let uploadResult = await sdk.uploadImage(formData);
        _data[item] = uploadResult.url;
      }

      sdk.setTable("seo");

      const result = await sdk.callRestAPI(
        {
          title: _data.title,
          keywords: _data.keywords,
          description: _data.description,
        },
        "POST"
      );
      // console.log("result", result);
      if (!result.error) {
        showToast(globalDispatch, "Added");
        navigate("/admin/seo");
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error) {
      console.log("Error", error);
      setError("name", {
        type: "manual",
        message: error.message,
      });
      tokenExpireError(dispatch, error.message);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "seo",
      },
    });
  }, []);

  const handleFileChange = (event) => {
    setIsDownloading(true);
    const file = event.target.files[0];

    if (file) {
      Papa.parse(file, {
        header: true,
        complete: (results) => {
          const filteredData = results.data.filter((row) => {
            // Filter out empty rows
            return Object.values(row).some(
              (value) => value !== null && value !== ""
            );
          });
          setData(filteredData);
          // sendToBackend(filteredData);
        },
        error: (error) => {
          console.error("Error parsing the file: ", error);
        },
      });
    }
    setIsDownloading(true);
  };


  return (
    <div className=" shadow-md rounded  mx-auto p-5">
      <div className=" flex flex-col gap-4 mb-4">
        <BackButton />
        <h4 className="text-2xl font-medium">Add SEO</h4>
      </div>
      <form className=" w-full max-w-lg" onSubmit={handleSubmit(onSubmit)}>
        <div className="mb-4  ">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="title"
          >
            Title
          </label>
          <input
            placeholder="Title"
            {...register("title")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
              errors.title?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-red-500 text-xs italic">{errors.title?.message}</p>
        </div>
        <div className="mb-4  ">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="description"
          >
            Description
          </label>
          <input
            placeholder="Description"
            {...register("description")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
              errors.description?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-red-500 text-xs italic">{errors.description?.message}</p>
        </div>

        <div className="mb-4  ">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="keywords"
          >
            Keywords
          </label>
          <textarea
            placeholder="Keywords"
            {...register("keywords")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
              errors.keywords?.message ? "border-red-500" : ""
            }`}
            row={15}
          ></textarea>
          <p className="text-red-500 text-xs italic">
            {errors.keywords?.message}
          </p>
        </div>
      

        
      

        <button
          type="submit"
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Submit
        </button>
      </form>

     
    </div>
  );
};

export default AddAdminSEOPage;
