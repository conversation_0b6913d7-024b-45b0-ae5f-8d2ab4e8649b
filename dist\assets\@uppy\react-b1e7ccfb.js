import{r as a}from"../vendor-b0222800.js";import{c as b,U as P,y as u}from"./core-f5368a8f.js";import{D,S as le,t as re}from"./dashboard-54af8551.js";import{P as t}from"../@ckeditor/ckeditor5-react-48fc30c1.js";import{D as ue}from"./drag-drop-11c773af.js";const p=t.instanceOf(b),B=t.arrayOf(t.string),y=t.shape({strings:t.object,pluralize:t.func}),pe=t.shape({id:t.string.isRequired,name:t.string.isRequired,placeholder:t.string}),w=t.oneOfType([t.arrayOf(pe),t.func]),m=t.oneOfType([t.string,t.number]),de={uppy:p,inline:t.bool,plugins:B,width:m,height:m,showProgressDetails:t.bool,hideUploadButton:t.bool,hideProgressAfterFinish:t.bool,note:t.string,metaFields:w,proudlyDisplayPoweredByUppy:t.bool,disableStatusBar:t.bool,disableInformer:t.bool,disableThumbnailGenerator:t.bool,thumbnailWidth:t.number,locale:y},he=["defaultChecked","defaultValue","suppressContentEditableWarning","suppressHydrationWarning","dangerouslySetInnerHTML","accessKey","className","contentEditable","contextMenu","dir","draggable","hidden","id","lang","placeholder","slot","spellCheck","style","tabIndex","title","translate","radioGroup","role","about","datatype","inlist","prefix","property","resource","typeof","vocab","autoCapitalize","autoCorrect","autoSave","color","itemProp","itemScope","itemType","itemID","itemRef","results","security","unselectable","inputMode","is","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],ce=/^(aria-|data-)/,g=i=>Object.fromEntries(Object.entries(i).filter(e=>{let[n]=e;return ce.test(n)||he.includes(n)}));function C(i,e){return Object.keys(i).some(n=>!Object.hasOwn(i,n)&&i[n]!==e[n])}class F extends a.Component{componentDidMount(){this.installPlugin()}componentDidUpdate(e){if(e.uppy!==this.props.uppy)this.uninstallPlugin(e),this.installPlugin();else if(C(this.props,e)){const n={...this.props,target:this.container};delete n.uppy,this.plugin.setOptions(n)}}componentWillUnmount(){this.uninstallPlugin()}installPlugin(){const{uppy:e}=this.props,n={id:"react:Dashboard",...this.props,target:this.container};delete n.uppy,e.use(D,n),this.plugin=e.getPlugin(n.id)}uninstallPlugin(e){e===void 0&&(e=this.props);const{uppy:n}=e;n.removePlugin(this.plugin)}render(){return a.createElement("div",{className:"uppy-Container",ref:e=>{this.container=e},...g(this.props)})}}F.propTypes=de;F.defaultProps={inline:!0};class M extends a.Component{componentDidMount(){this.installPlugin()}componentDidUpdate(e){const{uppy:n,open:o,onRequestClose:s}=this.props;if(e.uppy!==n)this.uninstallPlugin(e),this.installPlugin();else if(C(this.props,e)){const l={...this.props,onRequestCloseModal:s};delete l.uppy,this.plugin.setOptions(l)}e.open&&!o?this.plugin.closeModal():!e.open&&o&&this.plugin.openModal()}componentWillUnmount(){this.uninstallPlugin()}installPlugin(){const{id:e="react:DashboardModal",uppy:n,target:o,open:s,onRequestClose:l,closeModalOnClickOutside:d,disablePageScrollWhenModalOpen:r,inline:f,plugins:h,width:R,height:I,showProgressDetails:A,note:x,metaFields:k,proudlyDisplayPoweredByUppy:L,autoOpenFileEditor:N,animateOpenClose:W,browserBackButtonClose:q,closeAfterFinish:H,disableStatusBar:$,disableInformer:j,disableThumbnailGenerator:G,disableLocalFiles:K,disabled:V,hideCancelButton:z,hidePauseResumeButton:J,hideProgressAfterFinish:_,hideRetryButton:Q,hideUploadButton:X,showLinkToFileUploadResult:Y,showRemoveButtonAfterComplete:Z,showSelectedFiles:ee,waitForThumbnailsBeforeUpload:te,fileManagerSelectionType:ne,theme:oe,thumbnailType:ie,thumbnailWidth:se,locale:ae}=this.props,c={id:e,target:o,closeModalOnClickOutside:d,disablePageScrollWhenModalOpen:r,inline:f,plugins:h,width:R,height:I,showProgressDetails:A,note:x,metaFields:k,proudlyDisplayPoweredByUppy:L,autoOpenFileEditor:N,animateOpenClose:W,browserBackButtonClose:q,closeAfterFinish:H,disableStatusBar:$,disableInformer:j,disableThumbnailGenerator:G,disableLocalFiles:K,disabled:V,hideCancelButton:z,hidePauseResumeButton:J,hideProgressAfterFinish:_,hideRetryButton:Q,hideUploadButton:X,showLinkToFileUploadResult:Y,showRemoveButtonAfterComplete:Z,showSelectedFiles:ee,waitForThumbnailsBeforeUpload:te,fileManagerSelectionType:ne,theme:oe,thumbnailType:ie,thumbnailWidth:se,locale:ae,onRequestCloseModal:l};c.target||(c.target=this.container),delete c.uppy,n.use(D,c),this.plugin=n.getPlugin(c.id),s&&this.plugin.openModal()}uninstallPlugin(e){e===void 0&&(e=this.props);const{uppy:n}=e;n.removePlugin(this.plugin)}render(){return a.createElement("div",{className:"uppy-Container",ref:e=>{this.container=e},...g(this.props)})}}M.propTypes={uppy:p.isRequired,target:typeof window<"u"?t.instanceOf(window.HTMLElement):t.any,open:t.bool,onRequestClose:t.func,closeModalOnClickOutside:t.bool,disablePageScrollWhenModalOpen:t.bool,inline:t.bool,plugins:B,width:m,height:m,showProgressDetails:t.bool,note:t.string,metaFields:w,proudlyDisplayPoweredByUppy:t.bool,autoOpenFileEditor:t.bool,animateOpenClose:t.bool,browserBackButtonClose:t.bool,closeAfterFinish:t.bool,disableStatusBar:t.bool,disableInformer:t.bool,disableThumbnailGenerator:t.bool,disableLocalFiles:t.bool,disabled:t.bool,hideCancelButton:t.bool,hidePauseResumeButton:t.bool,hideProgressAfterFinish:t.bool,hideRetryButton:t.bool,hideUploadButton:t.bool,showLinkToFileUploadResult:t.bool,showRemoveButtonAfterComplete:t.bool,showSelectedFiles:t.bool,waitForThumbnailsBeforeUpload:t.bool,fileManagerSelectionType:t.string,theme:t.string,thumbnailType:t.string,thumbnailWidth:t.number,locale:y};M.defaultProps={metaFields:[],plugins:[],inline:!1,width:750,height:550,thumbnailWidth:280,thumbnailType:"image/jpeg",waitForThumbnailsBeforeUpload:!1,showLinkToFileUploadResult:!1,showProgressDetails:!1,hideUploadButton:!1,hideCancelButton:!1,hideRetryButton:!1,hidePauseResumeButton:!1,hideProgressAfterFinish:!1,note:null,closeModalOnClickOutside:!1,closeAfterFinish:!1,disableStatusBar:!1,disableInformer:!1,disableThumbnailGenerator:!1,disablePageScrollWhenModalOpen:!0,animateOpenClose:!0,fileManagerSelectionType:"files",proudlyDisplayPoweredByUppy:!0,showSelectedFiles:!0,showRemoveButtonAfterComplete:!1,browserBackButtonClose:!1,theme:"light",autoOpenFileEditor:!1,disabled:!1,disableLocalFiles:!1,open:void 0,target:void 0,locale:null,onRequestClose:void 0};class S extends a.Component{componentDidMount(){this.installPlugin()}componentDidUpdate(e){if(e.uppy!==this.props.uppy)this.uninstallPlugin(e),this.installPlugin();else if(C(this.props,e)){const n={...this.props,target:this.container};delete n.uppy,this.plugin.setOptions(n)}}componentWillUnmount(){this.uninstallPlugin()}installPlugin(){const{uppy:e,locale:n,inputName:o,width:s,height:l,note:d}=this.props,r={id:"react:DragDrop",locale:n,inputName:o,width:s,height:l,note:d,target:this.container};delete r.uppy,e.use(ue,r),this.plugin=e.getPlugin(r.id)}uninstallPlugin(e){e===void 0&&(e=this.props);const{uppy:n}=e;n.removePlugin(this.plugin)}render(){return a.createElement("div",{className:"uppy-Container",ref:e=>{this.container=e},...g(this.props)})}}S.propTypes={uppy:p.isRequired,locale:y,inputName:t.string,width:t.string,height:t.string,note:t.string};S.defaultProps={locale:null,inputName:"files[]",width:"100%",height:"100%",note:null};const ge={version:"3.1.0"},Ce={target:"body",fixed:!1,hideAfterFinish:!0};let T=class extends P{constructor(e,n){super(e,{...Ce,...n}),this.id=this.opts.id||"ProgressBar",this.title="Progress Bar",this.type="progressindicator",this.render=this.render.bind(this)}render(e){const n=e.totalProgress||0,o=(n===0||n===100)&&this.opts.hideAfterFinish;return u("div",{className:"uppy uppy-ProgressBar",style:{position:this.opts.fixed?"fixed":"initial"},"aria-hidden":o},u("div",{className:"uppy-ProgressBar-inner",style:{width:`${n}%`}}),u("div",{className:"uppy-ProgressBar-percentage"},n))}install(){const{target:e}=this.opts;e&&this.mount(e,this)}uninstall(){this.unmount()}};T.VERSION=ge.version;class U extends a.Component{componentDidMount(){this.installPlugin()}componentDidUpdate(e){if(e.uppy!==this.props.uppy)this.uninstallPlugin(e),this.installPlugin();else if(C(this.props,e)){const n={...this.props,target:this.container};delete n.uppy,this.plugin.setOptions(n)}}componentWillUnmount(){this.uninstallPlugin()}installPlugin(){const{uppy:e,fixed:n,hideAfterFinish:o}=this.props,s={id:"react:ProgressBar",fixed:n,hideAfterFinish:o,target:this.container};delete s.uppy,e.use(T,s),this.plugin=e.getPlugin(s.id)}uninstallPlugin(e){e===void 0&&(e=this.props);const{uppy:n}=e;n.removePlugin(this.plugin)}render(){return a.createElement("div",{className:"uppy-Container",ref:e=>{this.container=e},...g(this.props)})}}U.propTypes={uppy:p.isRequired,fixed:t.bool,hideAfterFinish:t.bool};U.defaultProps={fixed:!1,hideAfterFinish:!0};class O extends a.Component{componentDidMount(){this.installPlugin()}componentDidUpdate(e){if(e.uppy!==this.props.uppy)this.uninstallPlugin(e),this.installPlugin();else if(C(this.props,e)){const n={...this.props,target:this.container};delete n.uppy,this.plugin.setOptions(n)}}componentWillUnmount(){this.uninstallPlugin()}installPlugin(){const{uppy:e,hideUploadButton:n,hideRetryButton:o,hidePauseResumeButton:s,hideCancelButton:l,showProgressDetails:d,hideAfterFinish:r,doneButtonHandler:f}=this.props,h={id:"react:StatusBar",hideUploadButton:n,hideRetryButton:o,hidePauseResumeButton:s,hideCancelButton:l,showProgressDetails:d,hideAfterFinish:r,doneButtonHandler:f,target:this.container};delete h.uppy,e.use(le,h),this.plugin=e.getPlugin(h.id)}uninstallPlugin(e){e===void 0&&(e=this.props);const{uppy:n}=e;n.removePlugin(this.plugin)}render(){return a.createElement("div",{className:"uppy-Container",ref:e=>{this.container=e},...g(this.props)})}}O.propTypes={uppy:p.isRequired,hideUploadButton:t.bool,hideRetryButton:t.bool,hidePauseResumeButton:t.bool,hideCancelButton:t.bool,showProgressDetails:t.bool,hideAfterFinish:t.bool,doneButtonHandler:t.func};O.defaultProps={hideUploadButton:!1,hideRetryButton:!1,hidePauseResumeButton:!1,hideCancelButton:!1,showProgressDetails:!1,hideAfterFinish:!0,doneButtonHandler:null};const me={strings:{chooseFiles:"Choose files"}},ye={version:"3.1.0"},fe={pretty:!0,inputName:"files[]"};let E=class extends P{constructor(e,n){super(e,{...fe,...n}),this.id=this.opts.id||"FileInput",this.title="File Input",this.type="acquirer",this.defaultLocale=me,this.i18nInit(),this.render=this.render.bind(this),this.handleInputChange=this.handleInputChange.bind(this),this.handleClick=this.handleClick.bind(this)}addFiles(e){const n=e.map(o=>({source:this.id,name:o.name,type:o.type,data:o}));try{this.uppy.addFiles(n)}catch(o){this.uppy.log(o)}}handleInputChange(e){this.uppy.log("[FileInput] Something selected through input...");const n=re(e.target.files);this.addFiles(n),e.target.value=null}handleClick(){this.input.click()}render(){const e={width:"0.1px",height:"0.1px",opacity:0,overflow:"hidden",position:"absolute",zIndex:-1},{restrictions:n}=this.uppy.opts,o=n.allowedFileTypes?n.allowedFileTypes.join(","):void 0;return u("div",{className:"uppy-FileInput-container"},u("input",{className:"uppy-FileInput-input",style:this.opts.pretty?e:void 0,type:"file",name:this.opts.inputName,onChange:this.handleInputChange,multiple:n.maxNumberOfFiles!==1,accept:o,ref:s=>{this.input=s}}),this.opts.pretty&&u("button",{className:"uppy-FileInput-btn",type:"button",onClick:this.handleClick},this.i18n("chooseFiles")))}install(){const{target:e}=this.opts;e&&this.mount(e,this)}uninstall(){this.unmount()}};E.VERSION=ye.version;class v extends a.Component{componentDidMount(){this.installPlugin()}componentDidUpdate(e){e.uppy!==this.props.uppy&&(this.uninstallPlugin(e),this.installPlugin())}componentWillUnmount(){this.uninstallPlugin()}installPlugin(){const{uppy:e,locale:n,pretty:o,inputName:s}=this.props,l={id:"react:FileInput",locale:n,pretty:o,inputName:s,target:this.container};delete l.uppy,e.use(E,l),this.plugin=e.getPlugin(l.id)}uninstallPlugin(e){e===void 0&&(e=this.props);const{uppy:n}=e;n.removePlugin(this.plugin)}render(){return a.createElement("div",{className:"uppy-Container",ref:e=>{this.container=e}})}}v.propTypes={uppy:p.isRequired,locale:y,pretty:t.bool,inputName:t.string};v.defaultProps={locale:void 0,pretty:!0,inputName:"files[]"};function Se(i){if(typeof i!="function")throw new TypeError("useUppy: expected a function that returns a new Uppy instance");const e=a.useRef(void 0);if(e.current===void 0&&(e.current=i(),!(e.current instanceof b)))throw new TypeError(`useUppy: factory function must return an Uppy instance, got ${typeof e.current}`);return a.useEffect(()=>()=>{var n;(n=e.current)==null||n.close({reason:"unmount"}),e.current=void 0},[e]),e.current}export{F as D,Se as u};
