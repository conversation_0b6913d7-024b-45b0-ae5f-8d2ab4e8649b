import React, { useState } from "react";
import { useCookies } from "react-cookie";
import { Modal } from "./Modal";
import { useIsEligible } from "Src/ageContext";

const CookieConsent = ({setIsAgeModalOpen}) => {
  const [cookies, setCookie] = useCookies(["cookieConsent"]);
  const giveCookieConsent = () => {
    setIsAgeModalOpen(true)
    localStorage.setItem('hasCookieConsent', 'true');
    setCookie("cookieConsent", true, { path: "/" });
  };

 



  return (
    <>
      <div
        

        className=" border-4 border-black md:flex md:flex-row  flex-col  font-semibold  md:rounded-full p-2 md:px-4 flex items-center gap-2 fixed bottom-0 bg-white z-9 justify-between w-full mx-auto"
      >
        <p className="text-sm md:text-base  ">
          We use cookies to enhance your user experience. By using our website,
          you agree to our use of cookies.{" "}
          <a href={"/user/privacy"} className="font-semibold">
            Learn more.
          </a>
        </p>
        <button
          className="md:p-4 p-2 bg-black text-white rounded-full"
          onClick={giveCookieConsent}
        >
          Accept
        </button>
      </div>

      
    </>
  );
};

export default CookieConsent;
