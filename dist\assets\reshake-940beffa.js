import"./@mui/base-0e613ae5.js";import"./vendor-b0222800.js";import{u as f}from"./styled-components-de78ff2f.js";var s=globalThis&&globalThis.__makeTemplateObject||function(n,e){return Object.defineProperty?Object.defineProperty(n,"raw",{value:e}):n.raw=e,n},i=globalThis&&globalThis.__assign||function(){return i=Object.assign||function(n){for(var e,a=1,t=arguments.length;a<t;a++){e=arguments[a];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r])}return n},i.apply(this,arguments)};globalThis&&globalThis.__rest;f.div(o||(o=s([`
  animation-name: `,`;
  animation-duration: `,`ms;
  animation-iteration-count: `,`;
  display: 'inline-block';
  transform-origin: `,`;

  &`,` {
    animation-name: `,`;
    animation-play-state: `,`;
    animation: `,`;
  }

  animation-play-state: `,`;
`],[`
  animation-name: `,`;
  animation-duration: `,`ms;
  animation-iteration-count: `,`;
  display: 'inline-block';
  transform-origin: `,`;

  &`,` {
    animation-name: `,`;
    animation-play-state: `,`;
    animation: `,`;
  }

  animation-play-state: `,`;
`])),function(n){return n.shouldShakeDefault&&n.shakeKeyframes},function(n){return n.dur},function(n){return n.q},function(n){return n.orig},function(n){return n.trigger},function(n){return n.shouldShakeWhenTriggered&&n.shakeKeyframes},function(n){return n.freez&&(n.fixed?"paused":"running")},function(n){return n.fixed&&n.fixedStop&&"initial"},function(n){return n.active?n.freez&&!n.fixed?"paused":"running":"paused"});var o,l=globalThis&&globalThis.__assign||function(){return l=Object.assign||function(n){for(var e,a=1,t=arguments.length;a<t;a++){e=arguments[a];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r])}return n},l.apply(this,arguments)};globalThis&&globalThis.__rest;var u=globalThis&&globalThis.__assign||function(){return u=Object.assign||function(n){for(var e,a=1,t=arguments.length;a<t;a++){e=arguments[a];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r])}return n},u.apply(this,arguments)};
