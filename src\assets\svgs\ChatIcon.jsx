import React from "react";

export const ChatIcon = ({ className = "" }) => {
  return (
    <svg
    className={`${className}`}
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M15.4561 15.4362C13.9102 16.9823 11.8754 17.7309 9.85329 17.6872C7.10726 17.6278 2.29102 17.671 2.29102 17.671L3.97879 14.9803C3.97879 14.9803 2.33117 12.533 2.33117 10.0045C2.32971 8.03575 3.07931 6.0674 4.58403 4.56301C7.58348 1.56246 12.4566 1.56246 15.4561 4.56224C18.4609 7.56743 18.4555 12.4365 15.4561 15.4362Z"
      stroke="white"
      stroke-width="1.25"
      stroke-linecap="round"
    />
    <path
      d="M6.92896 10.3172H6.84667"
      stroke="white"
      stroke-width="1.25"
      stroke-linecap="square"
    />
    <path
      d="M10.0403 10.3182H9.958"
      stroke="white"
      stroke-width="1.25"
      stroke-linecap="square"
    />
    <path
      d="M13.1536 10.3172H13.0713"
      stroke="white"
      stroke-width="1.25"
      stroke-linecap="square"
    />
  </svg>
  );
};
