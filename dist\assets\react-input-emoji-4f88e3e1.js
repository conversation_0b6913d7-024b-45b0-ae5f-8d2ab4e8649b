import{r as y,a as E,$ as Jt}from"./vendor-b0222800.js";function Yt(e,t){t===void 0&&(t={});var n=t.insertAt;if(!(!e||typeof document>"u")){var r=document.head||document.getElementsByTagName("head")[0],o=document.createElement("style");o.type="text/css",n==="top"&&r.firstChild?r.insertBefore(o,r.firstChild):r.appendChild(o),o.styleSheet?o.styleSheet.cssText=e:o.appendChild(document.createTextNode(e))}}var Zt=`.react-input-emoji--container {
  color: #4b4b4b;
  text-rendering: optimizeLegibility;
  background-color: #fff;
  border: 1px solid #fff;
  border-radius: 21px;
  margin: 5px 10px;
  box-sizing: border-box;
  flex: 1 1 auto;
  font-size: 15px;
  font-family: sans-serif;
  font-weight: 400;
  line-height: 20px;
  min-height: 20px;
  min-width: 0;
  outline: none;
  width: inherit;
  will-change: width;
  vertical-align: baseline;
  border: 1px solid #eaeaea;
  margin-right: 0;
}

.react-input-emoji--wrapper {
  display: flex;
  overflow: hidden;
  flex: 1;
  position: relative;
  padding-right: 0;
  vertical-align: baseline;
  outline: none;
  margin: 0;
  padding: 0;
  border: 0;
}

.react-input-emoji--input {
  font-weight: 400;
  max-height: 100px;
  min-height: 20px;
  outline: none;
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;
  white-space: pre-wrap;
  word-wrap: break-word;
  z-index: 1;
  width: 100%;
  user-select: text;
  padding: 9px 12px 11px;
  text-align: left;
}

.react-input-emoji--input img {
  vertical-align: middle;
  width: 18px !important;
  height: 18px !important;
  display: inline !important;
  margin-left: 1px;
  margin-right: 1px;
}

.react-input-emoji--overlay {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 9;
}

.react-input-emoji--placeholder {
  color: #a0a0a0;
  pointer-events: none;
  position: absolute;
  user-select: none;
  z-index: 2;
  left: 16px;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  width: calc(100% - 22px);
}

.react-input-emoji--button {
  position: relative;
  display: block;
  text-align: center;
  padding: 0 10px;
  overflow: hidden;
  transition: color 0.1s ease-out;
  margin: 0;
  box-shadow: none;
  background: none;
  border: none;
  outline: none;
  cursor: pointer;
  flex-shrink: 0;
}

.react-input-emoji--button svg {
  fill: #858585;
}

.react-input-emoji--button__show svg {
  fill: #128b7e;
}

.react-emoji {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
}

.react-emoji-picker--container {
  position: absolute;
  top: 0;
  width: 100%;
}

.react-emoji-picker--wrapper {
  position: absolute;
  bottom: 0;
  right: 0;
  height: 435px;
  width: 352px;
  overflow: hidden;
  z-index: 10;
}

.react-emoji-picker {
  position: absolute;
  top: 0;
  left: 0;
  animation: slidein 0.1s ease-in-out;
}

.react-emoji-picker__show {
  top: 0;
}

.react-input-emoji--mention--container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 10;
}

.react-input-emoji--mention--list {
  background-color: #fafafa;
  border: 1px solid #eaeaea;
  border-radius: 4px;
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  gap: 5px;
  flex-direction: column;
  position: absolute;
  bottom: 0;
  width: 100%;
  left: 0;
}

.react-input-emoji--mention--item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 5px 10px;
  background-color: transparent;
  width: 100%;
  margin: 0;
  border: 0;
}

.react-input-emoji--mention--item__selected {
  background-color: #eeeeee;
}

.react-input-emoji--mention--item--img {
  width: 34px;
  height: 34px;
  border-radius: 50%;
}

.react-input-emoji--mention--item--name {
  font-size: 16px;
  color: #333;
}

.react-input-emoji--mention--item--name__selected {
  color: green;
}

.react-input-emoji--mention--text {
  color: #039be5;
}

.react-input-emoji--mention--loading {
  background-color: #fafafa;
  border: 1px solid #eaeaea;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
}

.react-input-emoji--mention--loading--spinner,
.react-input-emoji--mention--loading--spinner::after {
  border-radius: 50%;
  width: 10em;
  height: 10em;
}

.react-input-emoji--mention--loading--spinner {
  margin: 1px auto;
  font-size: 2px;
  position: relative;
  text-indent: -9999em;
  border-top: 1.1em solid rgba(0, 0, 0, 0.1);
  border-right: 1.1em solid rgba(0, 0, 0, 0.1);
  border-bottom: 1.1em solid rgba(0, 0, 0, 0.1);
  border-left: 1.1em solid rgba(0, 0, 0, 0.4);
  transform: translateZ(0);
  animation: load8 1.1s infinite linear;
}

@keyframes load8 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes slidein {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}
`;Yt(Zt);function Qt(e,t){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var r,o,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,t===0){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(s){u=!0,o=s}finally{try{if(!c&&n.return!=null&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}function Be(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function Fe(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Be(Object(n),!0).forEach(function(r){en(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Be(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function de(){de=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(m,p,b){m[p]=b.value},i=typeof Symbol=="function"?Symbol:{},a=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function u(m,p,b){return Object.defineProperty(m,p,{value:b,enumerable:!0,configurable:!0,writable:!0}),m[p]}try{u({},"")}catch{u=function(p,b,$){return p[b]=$}}function s(m,p,b,$){var k=p&&p.prototype instanceof S?p:S,M=Object.create(k.prototype),A=new V($||[]);return o(M,"_invoke",{value:ae(m,b,A)}),M}function v(m,p,b){try{return{type:"normal",arg:m.call(p,b)}}catch($){return{type:"throw",arg:$}}}t.wrap=s;var h="suspendedStart",g="suspendedYield",f="executing",_="completed",w={};function S(){}function j(){}function C(){}var R={};u(R,a,function(){return this});var T=Object.getPrototypeOf,z=T&&T(T(N([])));z&&z!==n&&r.call(z,a)&&(R=z);var W=C.prototype=S.prototype=Object.create(R);function U(m){["next","throw","return"].forEach(function(p){u(m,p,function(b){return this._invoke(p,b)})})}function te(m,p){function b(k,M,A,O){var I=v(m[k],m,M);if(I.type!=="throw"){var q=I.arg,ie=q.value;return ie&&typeof ie=="object"&&r.call(ie,"__await")?p.resolve(ie.__await).then(function(X){b("next",X,A,O)},function(X){b("throw",X,A,O)}):p.resolve(ie).then(function(X){q.value=X,A(q)},function(X){return b("throw",X,A,O)})}O(I.arg)}var $;o(this,"_invoke",{value:function(k,M){function A(){return new p(function(O,I){b(k,M,O,I)})}return $=$?$.then(A,A):A()}})}function ae(m,p,b){var $=h;return function(k,M){if($===f)throw new Error("Generator is already running");if($===_){if(k==="throw")throw M;return{value:e,done:!0}}for(b.method=k,b.arg=M;;){var A=b.delegate;if(A){var O=D(A,b);if(O){if(O===w)continue;return O}}if(b.method==="next")b.sent=b._sent=b.arg;else if(b.method==="throw"){if($===h)throw $=_,b.arg;b.dispatchException(b.arg)}else b.method==="return"&&b.abrupt("return",b.arg);$=f;var I=v(m,p,b);if(I.type==="normal"){if($=b.done?_:g,I.arg===w)continue;return{value:I.arg,done:b.done}}I.type==="throw"&&($=_,b.method="throw",b.arg=I.arg)}}}function D(m,p){var b=p.method,$=m.iterator[b];if($===e)return p.delegate=null,b==="throw"&&m.iterator.return&&(p.method="return",p.arg=e,D(m,p),p.method==="throw")||b!=="return"&&(p.method="throw",p.arg=new TypeError("The iterator does not provide a '"+b+"' method")),w;var k=v($,m.iterator,p.arg);if(k.type==="throw")return p.method="throw",p.arg=k.arg,p.delegate=null,w;var M=k.arg;return M?M.done?(p[m.resultName]=M.value,p.next=m.nextLoc,p.method!=="return"&&(p.method="next",p.arg=e),p.delegate=null,w):M:(p.method="throw",p.arg=new TypeError("iterator result is not an object"),p.delegate=null,w)}function P(m){var p={tryLoc:m[0]};1 in m&&(p.catchLoc=m[1]),2 in m&&(p.finallyLoc=m[2],p.afterLoc=m[3]),this.tryEntries.push(p)}function K(m){var p=m.completion||{};p.type="normal",delete p.arg,m.completion=p}function V(m){this.tryEntries=[{tryLoc:"root"}],m.forEach(P,this),this.reset(!0)}function N(m){if(m||m===""){var p=m[a];if(p)return p.call(m);if(typeof m.next=="function")return m;if(!isNaN(m.length)){var b=-1,$=function k(){for(;++b<m.length;)if(r.call(m,b))return k.value=m[b],k.done=!1,k;return k.value=e,k.done=!0,k};return $.next=$}}throw new TypeError(typeof m+" is not iterable")}return j.prototype=C,o(W,"constructor",{value:C,configurable:!0}),o(C,"constructor",{value:j,configurable:!0}),j.displayName=u(C,c,"GeneratorFunction"),t.isGeneratorFunction=function(m){var p=typeof m=="function"&&m.constructor;return!!p&&(p===j||(p.displayName||p.name)==="GeneratorFunction")},t.mark=function(m){return Object.setPrototypeOf?Object.setPrototypeOf(m,C):(m.__proto__=C,u(m,c,"GeneratorFunction")),m.prototype=Object.create(W),m},t.awrap=function(m){return{__await:m}},U(te.prototype),u(te.prototype,l,function(){return this}),t.AsyncIterator=te,t.async=function(m,p,b,$,k){k===void 0&&(k=Promise);var M=new te(s(m,p,b,$),k);return t.isGeneratorFunction(p)?M:M.next().then(function(A){return A.done?A.value:M.next()})},U(W),u(W,c,"Generator"),u(W,a,function(){return this}),u(W,"toString",function(){return"[object Generator]"}),t.keys=function(m){var p=Object(m),b=[];for(var $ in p)b.push($);return b.reverse(),function k(){for(;b.length;){var M=b.pop();if(M in p)return k.value=M,k.done=!1,k}return k.done=!0,k}},t.values=N,V.prototype={constructor:V,reset:function(m){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(K),!m)for(var p in this)p.charAt(0)==="t"&&r.call(this,p)&&!isNaN(+p.slice(1))&&(this[p]=e)},stop:function(){this.done=!0;var m=this.tryEntries[0].completion;if(m.type==="throw")throw m.arg;return this.rval},dispatchException:function(m){if(this.done)throw m;var p=this;function b(I,q){return M.type="throw",M.arg=m,p.next=I,q&&(p.method="next",p.arg=e),!!q}for(var $=this.tryEntries.length-1;$>=0;--$){var k=this.tryEntries[$],M=k.completion;if(k.tryLoc==="root")return b("end");if(k.tryLoc<=this.prev){var A=r.call(k,"catchLoc"),O=r.call(k,"finallyLoc");if(A&&O){if(this.prev<k.catchLoc)return b(k.catchLoc,!0);if(this.prev<k.finallyLoc)return b(k.finallyLoc)}else if(A){if(this.prev<k.catchLoc)return b(k.catchLoc,!0)}else{if(!O)throw new Error("try statement without catch or finally");if(this.prev<k.finallyLoc)return b(k.finallyLoc)}}}},abrupt:function(m,p){for(var b=this.tryEntries.length-1;b>=0;--b){var $=this.tryEntries[b];if($.tryLoc<=this.prev&&r.call($,"finallyLoc")&&this.prev<$.finallyLoc){var k=$;break}}k&&(m==="break"||m==="continue")&&k.tryLoc<=p&&p<=k.finallyLoc&&(k=null);var M=k?k.completion:{};return M.type=m,M.arg=p,k?(this.method="next",this.next=k.finallyLoc,w):this.complete(M)},complete:function(m,p){if(m.type==="throw")throw m.arg;return m.type==="break"||m.type==="continue"?this.next=m.arg:m.type==="return"?(this.rval=this.arg=m.arg,this.method="return",this.next="end"):m.type==="normal"&&p&&(this.next=p),w},finish:function(m){for(var p=this.tryEntries.length-1;p>=0;--p){var b=this.tryEntries[p];if(b.finallyLoc===m)return this.complete(b.completion,b.afterLoc),K(b),w}},catch:function(m){for(var p=this.tryEntries.length-1;p>=0;--p){var b=this.tryEntries[p];if(b.tryLoc===m){var $=b.completion;if($.type==="throw"){var k=$.arg;K(b)}return k}}throw new Error("illegal catch attempt")},delegateYield:function(m,p,b){return this.delegate={iterator:N(m),resultName:p,nextLoc:b},this.method==="next"&&(this.arg=e),w}},t}function Ne(e,t,n,r,o,i,a){try{var l=e[i](a),c=l.value}catch(u){n(u);return}l.done?t(c):Promise.resolve(c).then(r,o)}function Ue(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(c){Ne(i,r,o,a,l,"next",c)}function l(c){Ne(i,r,o,a,l,"throw",c)}a(void 0)})}}function en(e,t,n){return t=un(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function tn(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function nn(e,t){if(e==null)return{};var n=tn(e,t),r,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function ee(e,t){return on(e)||Qt(e,t)||gt(e,t)||cn()}function vt(e){return rn(e)||an(e)||gt(e)||sn()}function rn(e){if(Array.isArray(e))return Re(e)}function on(e){if(Array.isArray(e))return e}function an(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function gt(e,t){if(e){if(typeof e=="string")return Re(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Re(e,t)}}function Re(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function sn(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function cn(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ln(e,t){if(typeof e!="object"||e===null)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var r=n.call(e,t||"default");if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function un(e){var t=ln(e,"string");return typeof t=="symbol"?t:String(t)}var dn="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7";function bt(e){var t=fn(e);return t&&(t=vt(new Set(t)),t.forEach(function(n){e=_t(e,n,yt("",n))})),e}function _t(e,t,n){return e.replace(new RegExp(t,"g"),n)}function fn(e){return e.match(/(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe23\u20d0-\u20f0]|\ud83c[\udffb-\udfff])?(?:\u200d(?:[^\ud800-\udfff]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe23\u20d0-\u20f0]|\ud83c[\udffb-\udfff])?)*/g)}function hn(e){var t,n=document.querySelector("em-emoji-picker");if(!n)return Ve(e.native);var r=n==null||(t=n.shadowRoot)===null||t===void 0?void 0:t.querySelector('[title="'.concat(e.name,'"] > span > span'));if(!r)return Ve(e.native);var o=_t(r.style.cssText,'"',"'");return yt(o,e.native)}function yt(e,t){return'<img style="'.concat(e,'; display: inline-block" data-emoji="').concat(t,'" src="').concat(dn,'" />')}function Ve(e){return'<span class="width: 18px; height: 18px; display: inline-block; margin: 0 1px;">'.concat(e,"</span>")}function me(e){var t=document.createElement("div");t.innerHTML=e;var n=Array.prototype.slice.call(t.querySelectorAll("img"));return n.forEach(function(r){t.innerHTML=t.innerHTML.replace(r.outerHTML,r.dataset.emoji)}),t.innerHTML}function pn(e){var t=window.getSelection();if(t!==null){for(var n=document.createElement("div"),r=0,o=t.rangeCount;r<o;++r)n.appendChild(t.getRangeAt(r).cloneContents());n=vn(n),e.clipboardData.setData("text",n.innerText),e.preventDefault()}}function mn(e){var t,n;if(window.getSelection){if(t=window.getSelection(),t===null)return;if(t.getRangeAt&&t.rangeCount){n=t.getRangeAt(0),n.deleteContents();var r=document.createElement("div");r.innerHTML=e;for(var o=document.createDocumentFragment(),i,a;i=r.firstChild;)a=o.appendChild(i);n.insertNode(o),a&&(n=n.cloneRange(),n.setStartAfter(a),n.collapse(!0),t.removeAllRanges(),t.addRange(n))}}}function vn(e){var t=Array.prototype.slice.call(e.querySelectorAll("img"));return t.forEach(function(n){n.outerHTML=n.dataset.emoji}),e}function We(e){var t=e.text,n=e.html,r=t.length,o=(n.match(/<img/g)||[]).length;return r+o}function gn(e){var t=e.innerHTML.replace(/<br\s*\/?>/gi,"[BR]"),n=t.replace(/<[^>]+>/g,""),r=n.replace(/\[BR\]/gi,"</br>");return r}function bn(e){var t=e.startContainer,n=e.startOffset;if(t.nodeType!==Node.TEXT_NODE){for(;t.nodeType!==Node.TEXT_NODE&&(t=t.nextSibling,!!t););if(!t)for(t=e.commonAncestorContainer;t.nodeType!==Node.TEXT_NODE;)t=t.firstChild;n=0}return{node:t,offset:n}}function _n(){var e=window.getSelection(),t=e.getRangeAt(0),n=bn(t);return{selection:e,range:t,selectionStart:n}}function yn(){var e=_n(),t=e.selection,n=e.range,r=e.selectionStart;if(t.isCollapsed&&r.offset===r.node.textContent.length){var o=document.createElement("br");n.insertNode(o),n.setStartAfter(o),n.setEndAfter(o),t.removeAllRanges(),t.addRange(n);var i=document.createElement("br");n.insertNode(i),n.setStartAfter(i),n.setEndAfter(i),t.removeAllRanges(),t.addRange(n)}else{var a=document.createElement("br");n.insertNode(a),n.setStartAfter(a),n.setEndAfter(a),t.removeAllRanges(),t.addRange(n),r.node.nextSibling&&r.node.nextSibling.nodeType===Node.TEXT_NODE&&(n.setStart(r.node.nextSibling,1),n.setEnd(r.node.nextSibling,1)),t.removeAllRanges(),t.addRange(n)}}function wt(e){var t=y.useRef([]),n=y.useRef(""),r=y.useCallback(function(i){t.current.push(i)},[]),o=y.useCallback(function(i){var a=t.current.reduce(function(l,c){return c(l)},i);return a=wn(a,e),n.current=a,a},[]);return{addSanitizeFn:r,sanitize:o,sanitizedTextRef:n}}function wn(e,t){var n=document.createElement("div");n.innerHTML=e;var r;return t?r=gn(n):r=n.innerText||"",r=r.replace(/\n/gi,""),r}function kn(e){var t=e.ref,n=e.textInputRef,r=e.setValue,o=e.emitChange,i=wt(!1),a=i.sanitize,l=i.sanitizedTextRef;y.useImperativeHandle(t,function(){return{get value(){return l.current},set value(c){r(c)},focus:function(){n.current!==null&&n.current.focus()},blur:function(){n.current!==null&&a(n.current.html),o()}}})}function xn(e,t,n){var r=y.useRef(null),o=y.useRef(n),i=y.useCallback(function(){if(e.current!==null){var l=r.current,c=e.current.size;(!l||l.width!==c.width||l.height!==c.height)&&typeof t=="function"&&t(c),r.current=c}},[t,e]),a=y.useCallback(function(l){typeof o.current=="function"&&o.current(l),typeof t=="function"&&i()},[i,t]);return y.useEffect(function(){e.current&&i()},[i,e]),a}var $n=["placeholder","style","tabIndex","className","onChange"],Cn=function(t,n){var r=t.placeholder,o=t.style,i=t.tabIndex,a=t.className,l=t.onChange,c=nn(t,$n);y.useImperativeHandle(n,function(){return{appendContent:function(_){s.current&&s.current.focus(),mn(_),s.current&&s.current.focus(),s.current&&u.current&&me(s.current.innerHTML)===""?u.current.style.visibility="visible":u.current&&(u.current.style.visibility="hidden"),s.current&&typeof l=="function"&&l(s.current.innerHTML)},set html(f){if(s.current&&(s.current.innerHTML=f),u.current){var _=me(f);_===""?u.current.style.visibility="visible":u.current.style.visibility="hidden"}typeof l=="function"&&s.current&&l(s.current.innerHTML)},get html(){return s.current?s.current.innerHTML:""},get text(){return s.current?s.current.innerText:""},get size(){return s.current?{width:s.current.offsetWidth,height:s.current.offsetHeight}:{width:0,height:0}},focus:function(){s.current&&s.current.focus()}}});var u=y.useRef(null),s=y.useRef(null);function v(f){if(f.key==="Enter"&&(f.shiftKey===!0||f.ctrlKey===!0)&&c.shouldReturn&&(f.preventDefault(),s.current)){yn();return}f.key==="Enter"?c.onEnter(f):f.key==="ArrowUp"?c.onArrowUp(f):f.key==="ArrowDown"?c.onArrowDown(f):f.key.length===1&&u.current&&(u.current.style.visibility="hidden"),c.onKeyDown(f)}function h(){c.onFocus()}function g(f){c.onKeyUp(f);var _=s.current;if(u.current&&_){var w=me(_.innerHTML);w===""?u.current.style.visibility="visible":u.current.style.visibility="hidden"}typeof l=="function"&&s.current&&l(s.current.innerHTML)}return E.createElement("div",{className:"react-input-emoji--container",style:o},E.createElement("div",{className:"react-input-emoji--wrapper",onClick:h},E.createElement("div",{ref:u,className:"react-input-emoji--placeholder"},r),E.createElement("div",{ref:s,onKeyDown:v,onKeyUp:g,tabIndex:i,contentEditable:!0,className:"react-input-emoji--input".concat(a?" ".concat(a):""),onBlur:c.onBlur,onCopy:c.onCopy,onPaste:c.onPaste,"data-testid":"react-input-emoji--input"})))},Sn=y.forwardRef(Cn);function Ke(e){var t=e.showPicker,n=e.toggleShowPicker,r=e.buttonElement,o=e.buttonRef,i=y.useRef(null),a=y.useState(!1),l=ee(a,2),c=l[0],u=l[1];return y.useEffect(function(){var s,v,h,g;((s=o==null||(v=o.current)===null||v===void 0||(v=v.childNodes)===null||v===void 0?void 0:v.length)!==null&&s!==void 0?s:0)>2?(i.current.appendChild(o.current.childNodes[0]),u(!0)):((h=r==null||(g=r.childNodes)===null||g===void 0?void 0:g.length)!==null&&h!==void 0?h:0)>2&&(i.current.appendChild(r==null?void 0:r.childNodes[0]),u(!0))},[r==null?void 0:r.childNodes]),E.createElement("button",{ref:i,type:"button",className:"react-input-emoji--button".concat(t?" react-input-emoji--button__show":""),onClick:n},!c&&E.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24",className:"react-input-emoji--button--icon"},E.createElement("path",{d:"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0m0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10"}),E.createElement("path",{d:"M8 7a2 2 0 1 0-.001 3.999A2 2 0 0 0 8 7M16 7a2 2 0 1 0-.001 3.999A2 2 0 0 0 16 7M15.232 15c-.693 1.195-1.87 2-3.349 2-1.477 0-2.655-.805-3.347-2H15m3-2H6a6 6 0 1 0 12 0"})))}function kt(e){return e&&e.__esModule?e.default:e}function G(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var $e,x,xt,fe,$t,qe,be={},Ct=[],En=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function ne(e,t){for(var n in t)e[n]=t[n];return e}function St(e){var t=e.parentNode;t&&t.removeChild(e)}function Me(e,t,n){var r,o,i,a={};for(i in t)i=="key"?r=t[i]:i=="ref"?o=t[i]:a[i]=t[i];if(arguments.length>2&&(a.children=arguments.length>3?$e.call(arguments,2):n),typeof e=="function"&&e.defaultProps!=null)for(i in e.defaultProps)a[i]===void 0&&(a[i]=e.defaultProps[i]);return ve(e,a,r,o,null)}function ve(e,t,n,r,o){var i={type:e,props:t,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:o??++xt};return o==null&&x.vnode!=null&&x.vnode(i),i}function Z(){return{current:null}}function le(e){return e.children}function Y(e,t){this.props=e,this.context=t}function ue(e,t){if(t==null)return e.__?ue(e.__,e.__.__k.indexOf(e)+1):null;for(var n;t<e.__k.length;t++)if((n=e.__k[t])!=null&&n.__e!=null)return n.__e;return typeof e.type=="function"?ue(e):null}function Et(e){var t,n;if((e=e.__)!=null&&e.__c!=null){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if((n=e.__k[t])!=null&&n.__e!=null){e.__e=e.__c.base=n.__e;break}return Et(e)}}function Ge(e){(!e.__d&&(e.__d=!0)&&fe.push(e)&&!_e.__r++||qe!==x.debounceRendering)&&((qe=x.debounceRendering)||$t)(_e)}function _e(){for(var e;_e.__r=fe.length;)e=fe.sort(function(t,n){return t.__v.__b-n.__v.__b}),fe=[],e.some(function(t){var n,r,o,i,a,l;t.__d&&(a=(i=(n=t).__v).__e,(l=n.__P)&&(r=[],(o=ne({},i)).__v=i.__v+1,He(l,i,o,n.__n,l.ownerSVGElement!==void 0,i.__h!=null?[a]:null,r,a??ue(i),i.__h),Mt(r,i),i.__e!=a&&Et(i)))})}function jt(e,t,n,r,o,i,a,l,c,u){var s,v,h,g,f,_,w,S=r&&r.__k||Ct,j=S.length;for(n.__k=[],s=0;s<t.length;s++)if((g=n.__k[s]=(g=t[s])==null||typeof g=="boolean"?null:typeof g=="string"||typeof g=="number"||typeof g=="bigint"?ve(null,g,null,null,g):Array.isArray(g)?ve(le,{children:g},null,null,null):g.__b>0?ve(g.type,g.props,g.key,null,g.__v):g)!=null){if(g.__=n,g.__b=n.__b+1,(h=S[s])===null||h&&g.key==h.key&&g.type===h.type)S[s]=void 0;else for(v=0;v<j;v++){if((h=S[v])&&g.key==h.key&&g.type===h.type){S[v]=void 0;break}h=null}He(e,g,h=h||be,o,i,a,l,c,u),f=g.__e,(v=g.ref)&&h.ref!=v&&(w||(w=[]),h.ref&&w.push(h.ref,null,g),w.push(v,g.__c||f,g)),f!=null?(_==null&&(_=f),typeof g.type=="function"&&g.__k===h.__k?g.__d=c=Lt(g,c,e):c=Rt(e,g,h,S,f,c),typeof n.type=="function"&&(n.__d=c)):c&&h.__e==c&&c.parentNode!=e&&(c=ue(h))}for(n.__e=_,s=j;s--;)S[s]!=null&&(typeof n.type=="function"&&S[s].__e!=null&&S[s].__e==n.__d&&(n.__d=ue(r,s+1)),zt(S[s],S[s]));if(w)for(s=0;s<w.length;s++)Tt(w[s],w[++s],w[++s])}function Lt(e,t,n){for(var r,o=e.__k,i=0;o&&i<o.length;i++)(r=o[i])&&(r.__=e,t=typeof r.type=="function"?Lt(r,t,n):Rt(n,r,r,o,r.__e,t));return t}function ye(e,t){return t=t||[],e==null||typeof e=="boolean"||(Array.isArray(e)?e.some(function(n){ye(n,t)}):t.push(e)),t}function Rt(e,t,n,r,o,i){var a,l,c;if(t.__d!==void 0)a=t.__d,t.__d=void 0;else if(n==null||o!=i||o.parentNode==null)e:if(i==null||i.parentNode!==e)e.appendChild(o),a=null;else{for(l=i,c=0;(l=l.nextSibling)&&c<r.length;c+=2)if(l==o)break e;e.insertBefore(o,i),a=i}return a!==void 0?a:o.nextSibling}function jn(e,t,n,r,o){var i;for(i in n)i==="children"||i==="key"||i in t||we(e,i,null,n[i],r);for(i in t)o&&typeof t[i]!="function"||i==="children"||i==="key"||i==="value"||i==="checked"||n[i]===t[i]||we(e,i,t[i],n[i],r)}function Xe(e,t,n){t[0]==="-"?e.setProperty(t,n):e[t]=n==null?"":typeof n!="number"||En.test(t)?n:n+"px"}function we(e,t,n,r,o){var i;e:if(t==="style")if(typeof n=="string")e.style.cssText=n;else{if(typeof r=="string"&&(e.style.cssText=r=""),r)for(t in r)n&&t in n||Xe(e.style,t,"");if(n)for(t in n)r&&n[t]===r[t]||Xe(e.style,t,n[t])}else if(t[0]==="o"&&t[1]==="n")i=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=n,n?r||e.addEventListener(t,i?Ye:Je,i):e.removeEventListener(t,i?Ye:Je,i);else if(t!=="dangerouslySetInnerHTML"){if(o)t=t.replace(/xlink[H:h]/,"h").replace(/sName$/,"s");else if(t!=="href"&&t!=="list"&&t!=="form"&&t!=="tabIndex"&&t!=="download"&&t in e)try{e[t]=n??"";break e}catch{}typeof n=="function"||(n!=null&&(n!==!1||t[0]==="a"&&t[1]==="r")?e.setAttribute(t,n):e.removeAttribute(t))}}function Je(e){this.l[e.type+!1](x.event?x.event(e):e)}function Ye(e){this.l[e.type+!0](x.event?x.event(e):e)}function He(e,t,n,r,o,i,a,l,c){var u,s,v,h,g,f,_,w,S,j,C,R=t.type;if(t.constructor!==void 0)return null;n.__h!=null&&(c=n.__h,l=t.__e=n.__e,t.__h=null,i=[l]),(u=x.__b)&&u(t);try{e:if(typeof R=="function"){if(w=t.props,S=(u=R.contextType)&&r[u.__c],j=u?S?S.props.value:u.__:r,n.__c?_=(s=t.__c=n.__c).__=s.__E:("prototype"in R&&R.prototype.render?t.__c=s=new R(w,j):(t.__c=s=new Y(w,j),s.constructor=R,s.render=Rn),S&&S.sub(s),s.props=w,s.state||(s.state={}),s.context=j,s.__n=r,v=s.__d=!0,s.__h=[]),s.__s==null&&(s.__s=s.state),R.getDerivedStateFromProps!=null&&(s.__s==s.state&&(s.__s=ne({},s.__s)),ne(s.__s,R.getDerivedStateFromProps(w,s.__s))),h=s.props,g=s.state,v)R.getDerivedStateFromProps==null&&s.componentWillMount!=null&&s.componentWillMount(),s.componentDidMount!=null&&s.__h.push(s.componentDidMount);else{if(R.getDerivedStateFromProps==null&&w!==h&&s.componentWillReceiveProps!=null&&s.componentWillReceiveProps(w,j),!s.__e&&s.shouldComponentUpdate!=null&&s.shouldComponentUpdate(w,s.__s,j)===!1||t.__v===n.__v){s.props=w,s.state=s.__s,t.__v!==n.__v&&(s.__d=!1),s.__v=t,t.__e=n.__e,t.__k=n.__k,t.__k.forEach(function(T){T&&(T.__=t)}),s.__h.length&&a.push(s);break e}s.componentWillUpdate!=null&&s.componentWillUpdate(w,s.__s,j),s.componentDidUpdate!=null&&s.__h.push(function(){s.componentDidUpdate(h,g,f)})}s.context=j,s.props=w,s.state=s.__s,(u=x.__r)&&u(t),s.__d=!1,s.__v=t,s.__P=e,u=s.render(s.props,s.state,s.context),s.state=s.__s,s.getChildContext!=null&&(r=ne(ne({},r),s.getChildContext())),v||s.getSnapshotBeforeUpdate==null||(f=s.getSnapshotBeforeUpdate(h,g)),C=u!=null&&u.type===le&&u.key==null?u.props.children:u,jt(e,Array.isArray(C)?C:[C],t,n,r,o,i,a,l,c),s.base=t.__e,t.__h=null,s.__h.length&&a.push(s),_&&(s.__E=s.__=null),s.__e=!1}else i==null&&t.__v===n.__v?(t.__k=n.__k,t.__e=n.__e):t.__e=Ln(n.__e,t,n,r,o,i,a,c);(u=x.diffed)&&u(t)}catch(T){t.__v=null,(c||i!=null)&&(t.__e=l,t.__h=!!c,i[i.indexOf(l)]=null),x.__e(T,t,n)}}function Mt(e,t){x.__c&&x.__c(t,e),e.some(function(n){try{e=n.__h,n.__h=[],e.some(function(r){r.call(n)})}catch(r){x.__e(r,n.__v)}})}function Ln(e,t,n,r,o,i,a,l){var c,u,s,v=n.props,h=t.props,g=t.type,f=0;if(g==="svg"&&(o=!0),i!=null){for(;f<i.length;f++)if((c=i[f])&&"setAttribute"in c==!!g&&(g?c.localName===g:c.nodeType===3)){e=c,i[f]=null;break}}if(e==null){if(g===null)return document.createTextNode(h);e=o?document.createElementNS("http://www.w3.org/2000/svg",g):document.createElement(g,h.is&&h),i=null,l=!1}if(g===null)v===h||l&&e.data===h||(e.data=h);else{if(i=i&&$e.call(e.childNodes),u=(v=n.props||be).dangerouslySetInnerHTML,s=h.dangerouslySetInnerHTML,!l){if(i!=null)for(v={},f=0;f<e.attributes.length;f++)v[e.attributes[f].name]=e.attributes[f].value;(s||u)&&(s&&(u&&s.__html==u.__html||s.__html===e.innerHTML)||(e.innerHTML=s&&s.__html||""))}if(jn(e,h,v,o,l),s)t.__k=[];else if(f=t.props.children,jt(e,Array.isArray(f)?f:[f],t,n,r,o&&g!=="foreignObject",i,a,i?i[0]:n.__k&&ue(n,0),l),i!=null)for(f=i.length;f--;)i[f]!=null&&St(i[f]);l||("value"in h&&(f=h.value)!==void 0&&(f!==v.value||f!==e.value||g==="progress"&&!f)&&we(e,"value",f,v.value,!1),"checked"in h&&(f=h.checked)!==void 0&&f!==e.checked&&we(e,"checked",f,v.checked,!1))}return e}function Tt(e,t,n){try{typeof e=="function"?e(t):e.current=t}catch(r){x.__e(r,n)}}function zt(e,t,n){var r,o;if(x.unmount&&x.unmount(e),(r=e.ref)&&(r.current&&r.current!==e.__e||Tt(r,null,t)),(r=e.__c)!=null){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(i){x.__e(i,t)}r.base=r.__P=null}if(r=e.__k)for(o=0;o<r.length;o++)r[o]&&zt(r[o],t,typeof e.type!="function");n||e.__e==null||St(e.__e),e.__e=e.__d=void 0}function Rn(e,t,n){return this.constructor(e,n)}function Pt(e,t,n){var r,o,i;x.__&&x.__(e,t),o=(r=typeof n=="function")?null:n&&n.__k||t.__k,i=[],He(t,e=(!r&&n||t).__k=Me(le,null,[e]),o||be,be,t.ownerSVGElement!==void 0,!r&&n?[n]:o?null:t.firstChild?$e.call(t.childNodes):null,i,!r&&n?n:o?o.__e:t.firstChild,r),Mt(i,e)}$e=Ct.slice,x={__e:function(e,t){for(var n,r,o;t=t.__;)if((n=t.__c)&&!n.__)try{if((r=n.constructor)&&r.getDerivedStateFromError!=null&&(n.setState(r.getDerivedStateFromError(e)),o=n.__d),n.componentDidCatch!=null&&(n.componentDidCatch(e),o=n.__d),o)return n.__E=n}catch(i){e=i}throw e}},xt=0,Y.prototype.setState=function(e,t){var n;n=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=ne({},this.state),typeof e=="function"&&(e=e(ne({},n),this.props)),e&&ne(n,e),e!=null&&this.__v&&(t&&this.__h.push(t),Ge(this))},Y.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),Ge(this))},Y.prototype.render=le,fe=[],$t=typeof Promise=="function"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,_e.__r=0;var Mn=0;function d(e,t,n,r,o){var i,a,l={};for(a in t)a=="ref"?i=t[a]:l[a]=t[a];var c={type:e,props:l,key:n,ref:i,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:--Mn,__source:r,__self:o};if(typeof e=="function"&&(i=e.defaultProps))for(a in i)l[a]===void 0&&(l[a]=i[a]);return x.vnode&&x.vnode(c),c}function Tn(e,t){try{window.localStorage[`emoji-mart.${e}`]=JSON.stringify(t)}catch{}}function zn(e){try{const t=window.localStorage[`emoji-mart.${e}`];if(t)return JSON.parse(t)}catch{}}var re={set:Tn,get:zn};const Se=new Map,Pn=[{v:14,emoji:"🫠"},{v:13.1,emoji:"😶‍🌫️"},{v:13,emoji:"🥸"},{v:12.1,emoji:"🧑‍🦰"},{v:12,emoji:"🥱"},{v:11,emoji:"🥰"},{v:5,emoji:"🤩"},{v:4,emoji:"👱‍♀️"},{v:3,emoji:"🤣"},{v:2,emoji:"👋🏻"},{v:1,emoji:"🙃"}];function An(){for(const{v:e,emoji:t}of Pn)if(At(t))return e}function Dn(){return!At("🇨🇦")}function At(e){if(Se.has(e))return Se.get(e);const t=On(e);return Se.set(e,t),t}const On=(()=>{let e=null;try{navigator.userAgent.includes("jsdom")||(e=document.createElement("canvas").getContext("2d",{willReadFrequently:!0}))}catch{}if(!e)return()=>!1;const t=25,n=20,r=Math.floor(t/2);return e.font=r+"px Arial, Sans-Serif",e.textBaseline="top",e.canvas.width=n*2,e.canvas.height=t,o=>{e.clearRect(0,0,n*2,t),e.fillStyle="#FF0000",e.fillText(o,0,22),e.fillStyle="#0000FF",e.fillText(o,n,22);const i=e.getImageData(0,0,n,t).data,a=i.length;let l=0;for(;l<a&&!i[l+3];l+=4);if(l>=a)return!1;const c=n+l/4%n,u=Math.floor(l/4/n),s=e.getImageData(c,u,1,1).data;return!(i[l]!==s[0]||i[l+2]!==s[2]||e.measureText(o).width>=n)}})();var Ze={latestVersion:An,noCountryFlags:Dn};const Te=["+1","grinning","kissing_heart","heart_eyes","laughing","stuck_out_tongue_winking_eye","sweat_smile","joy","scream","disappointed","unamused","weary","sob","sunglasses","heart"];let B=null;function Hn(e){B||(B=re.get("frequently")||{});const t=e.id||e;t&&(B[t]||(B[t]=0),B[t]+=1,re.set("last",t),re.set("frequently",B))}function In({maxFrequentRows:e,perLine:t}){if(!e)return[];B||(B=re.get("frequently"));let n=[];if(!B){B={};for(let i in Te.slice(0,t)){const a=Te[i];B[a]=t-i,n.push(a)}return n}const r=e*t,o=re.get("last");for(let i in B)n.push(i);if(n.sort((i,a)=>{const l=B[a],c=B[i];return l==c?i.localeCompare(a):l-c}),n.length>r){const i=n.slice(r);n=n.slice(0,r);for(let a of i)a!=o&&delete B[a];o&&n.indexOf(o)==-1&&(delete B[n[n.length-1]],n.splice(-1,1,o)),re.set("frequently",B)}return n}var Dt={add:Hn,get:In,DEFAULTS:Te},Ot={};Ot=JSON.parse('{"search":"Search","search_no_results_1":"Oh no!","search_no_results_2":"That emoji couldn’t be found","pick":"Pick an emoji…","add_custom":"Add custom emoji","categories":{"activity":"Activity","custom":"Custom","flags":"Flags","foods":"Food & Drink","frequent":"Frequently used","nature":"Animals & Nature","objects":"Objects","people":"Smileys & People","places":"Travel & Places","search":"Search Results","symbols":"Symbols"},"skins":{"1":"Default","2":"Light","3":"Medium-Light","4":"Medium","5":"Medium-Dark","6":"Dark","choose":"Choose default skin tone"}}');var Q={autoFocus:{value:!1},dynamicWidth:{value:!1},emojiButtonColors:{value:null},emojiButtonRadius:{value:"100%"},emojiButtonSize:{value:36},emojiSize:{value:24},emojiVersion:{value:14,choices:[1,2,3,4,5,11,12,12.1,13,13.1,14]},exceptEmojis:{value:[]},icons:{value:"auto",choices:["auto","outline","solid"]},locale:{value:"en",choices:["en","ar","be","cs","de","es","fa","fi","fr","hi","it","ja","kr","nl","pl","pt","ru","sa","tr","uk","vi","zh"]},maxFrequentRows:{value:4},navPosition:{value:"top",choices:["top","bottom","none"]},noCountryFlags:{value:!1},noResultsEmoji:{value:null},perLine:{value:9},previewEmoji:{value:null},previewPosition:{value:"bottom",choices:["top","bottom","none"]},searchPosition:{value:"sticky",choices:["sticky","static","none"]},set:{value:"native",choices:["native","apple","facebook","google","twitter"]},skin:{value:1,choices:[1,2,3,4,5,6]},skinTonePosition:{value:"preview",choices:["preview","search","none"]},theme:{value:"auto",choices:["auto","light","dark"]},categories:null,categoryIcons:null,custom:null,data:null,i18n:null,getImageURL:null,getSpritesheetURL:null,onAddCustomEmoji:null,onClickOutside:null,onEmojiSelect:null,stickySearch:{deprecated:!0,value:!0}};let F=null,L=null;const Ee={};async function Qe(e){if(Ee[e])return Ee[e];const n=await(await fetch(e)).json();return Ee[e]=n,n}let je=null,Ht=null,It=!1;function Ce(e,{caller:t}={}){return je||(je=new Promise(n=>{Ht=n})),e?Bn(e):t&&!It&&console.warn(`\`${t}\` requires data to be initialized first. Promise will be pending until \`init\` is called.`),je}async function Bn(e){It=!0;let{emojiVersion:t,set:n,locale:r}=e;if(t||(t=Q.emojiVersion.value),n||(n=Q.set.value),r||(r=Q.locale.value),L)L.categories=L.categories.filter(c=>!c.name);else{L=(typeof e.data=="function"?await e.data():e.data)||await Qe(`https://cdn.jsdelivr.net/npm/@emoji-mart/data@latest/sets/${t}/${n}.json`),L.emoticons={},L.natives={},L.categories.unshift({id:"frequent",emojis:[]});for(const c in L.aliases){const u=L.aliases[c],s=L.emojis[u];s&&(s.aliases||(s.aliases=[]),s.aliases.push(c))}L.originalCategories=L.categories}if(F=(typeof e.i18n=="function"?await e.i18n():e.i18n)||(r=="en"?kt(Ot):await Qe(`https://cdn.jsdelivr.net/npm/@emoji-mart/data@latest/i18n/${r}.json`)),e.custom)for(let c in e.custom){c=parseInt(c);const u=e.custom[c],s=e.custom[c-1];if(!(!u.emojis||!u.emojis.length)){u.id||(u.id=`custom_${c+1}`),u.name||(u.name=F.categories.custom),s&&!u.icon&&(u.target=s.target||s),L.categories.push(u);for(const v of u.emojis)L.emojis[v.id]=v}}e.categories&&(L.categories=L.originalCategories.filter(c=>e.categories.indexOf(c.id)!=-1).sort((c,u)=>{const s=e.categories.indexOf(c.id),v=e.categories.indexOf(u.id);return s-v}));let o=null,i=null;n=="native"&&(o=Ze.latestVersion(),i=e.noCountryFlags||Ze.noCountryFlags());let a=L.categories.length,l=!1;for(;a--;){const c=L.categories[a];if(c.id=="frequent"){let{maxFrequentRows:v,perLine:h}=e;v=v>=0?v:Q.maxFrequentRows.value,h||(h=Q.perLine.value),c.emojis=Dt.get({maxFrequentRows:v,perLine:h})}if(!c.emojis||!c.emojis.length){L.categories.splice(a,1);continue}const{categoryIcons:u}=e;if(u){const v=u[c.id];v&&!c.icon&&(c.icon=v)}let s=c.emojis.length;for(;s--;){const v=c.emojis[s],h=v.id?v:L.emojis[v],g=()=>{c.emojis.splice(s,1)};if(!h||e.exceptEmojis&&e.exceptEmojis.includes(h.id)){g();continue}if(o&&h.version>o){g();continue}if(i&&c.id=="flags"&&!Wn.includes(h.id)){g();continue}if(!h.search){if(l=!0,h.search=","+[[h.id,!1],[h.name,!0],[h.keywords,!1],[h.emoticons,!1]].map(([_,w])=>{if(_)return(Array.isArray(_)?_:[_]).map(S=>(w?S.split(/[-|_|\s]+/):[S]).map(j=>j.toLowerCase())).flat()}).flat().filter(_=>_&&_.trim()).join(","),h.emoticons)for(const _ of h.emoticons)L.emoticons[_]||(L.emoticons[_]=h.id);let f=0;for(const _ of h.skins){if(!_)continue;f++;const{native:w}=_;w&&(L.natives[w]=h.id,h.search+=`,${w}`);const S=f==1?"":`:skin-tone-${f}:`;_.shortcodes=`:${h.id}:${S}`}}}}l&&ce.reset(),Ht()}function Bt(e,t,n){e||(e={});const r={};for(let o in t)r[o]=Ft(o,e,t,n);return r}function Ft(e,t,n,r){const o=n[e];let i=r&&r.getAttribute(e)||(t[e]!=null&&t[e]!=null?t[e]:null);return o&&(i!=null&&o.value&&typeof o.value!=typeof i&&(typeof o.value=="boolean"?i=i!="false":i=o.value.constructor(i)),o.transform&&i&&(i=o.transform(i)),(i==null||o.choices&&o.choices.indexOf(i)==-1)&&(i=o.value)),i}const Fn=/^(?:\:([^\:]+)\:)(?:\:skin-tone-(\d)\:)?$/;let ze=null;function Nn(e){return e.id?e:L.emojis[e]||L.emojis[L.aliases[e]]||L.emojis[L.natives[e]]}function Un(){ze=null}async function Vn(e,{maxResults:t,caller:n}={}){if(!e||!e.trim().length)return null;t||(t=90),await Ce(null,{caller:n||"SearchIndex.search"});const r=e.toLowerCase().replace(/(\w)-/,"$1 ").split(/[\s|,]+/).filter((l,c,u)=>l.trim()&&u.indexOf(l)==c);if(!r.length)return;let o=ze||(ze=Object.values(L.emojis)),i,a;for(const l of r){if(!o.length)break;i=[],a={};for(const c of o){if(!c.search)continue;const u=c.search.indexOf(`,${l}`);u!=-1&&(i.push(c),a[c.id]||(a[c.id]=0),a[c.id]+=c.id==l?0:u+1)}o=i}return i.length<2||(i.sort((l,c)=>{const u=a[l.id],s=a[c.id];return u==s?l.id.localeCompare(c.id):u-s}),i.length>t&&(i=i.slice(0,t))),i}var ce={search:Vn,get:Nn,reset:Un,SHORTCODES_REGEX:Fn};const Wn=["checkered_flag","crossed_flags","pirate_flag","rainbow-flag","transgender_flag","triangular_flag_on_post","waving_black_flag","waving_white_flag"];function Kn(e,t){return Array.isArray(e)&&Array.isArray(t)&&e.length===t.length&&e.every((n,r)=>n==t[r])}async function qn(e=1){for(let t in[...Array(e).keys()])await new Promise(requestAnimationFrame)}function Gn(e,{skinIndex:t=0}={}){const n=e.skins[t]||(()=>(t=0,e.skins[t]))(),r={id:e.id,name:e.name,native:n.native,unified:n.unified,keywords:e.keywords,shortcodes:n.shortcodes||e.shortcodes};return e.skins.length>1&&(r.skin=t+1),n.src&&(r.src=n.src),e.aliases&&e.aliases.length&&(r.aliases=e.aliases),e.emoticons&&e.emoticons.length&&(r.emoticons=e.emoticons),r}const Xn={activity:{outline:d("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:d("path",{d:"M12 0C5.373 0 0 5.372 0 12c0 6.627 5.373 12 12 12 6.628 0 12-5.373 12-12 0-6.628-5.372-12-12-12m9.949 11H17.05c.224-2.527 1.232-4.773 1.968-6.113A9.966 9.966 0 0 1 21.949 11M13 11V2.051a9.945 9.945 0 0 1 4.432 1.564c-.858 1.491-2.156 4.22-2.392 7.385H13zm-2 0H8.961c-.238-3.165-1.536-5.894-2.393-7.385A9.95 9.95 0 0 1 11 2.051V11zm0 2v8.949a9.937 9.937 0 0 1-4.432-1.564c.857-1.492 2.155-4.221 2.393-7.385H11zm4.04 0c.236 3.164 1.534 5.893 2.392 7.385A9.92 9.92 0 0 1 13 21.949V13h2.04zM4.982 4.887C5.718 6.227 6.726 8.473 6.951 11h-4.9a9.977 9.977 0 0 1 2.931-6.113M2.051 13h4.9c-.226 2.527-1.233 4.771-1.969 6.113A9.972 9.972 0 0 1 2.051 13m16.967 6.113c-.735-1.342-1.744-3.586-1.968-6.113h4.899a9.961 9.961 0 0 1-2.931 6.113"})}),solid:d("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",children:d("path",{d:"M16.17 337.5c0 44.98 7.565 83.54 13.98 107.9C35.22 464.3 50.46 496 174.9 496c9.566 0 19.59-.4707 29.84-1.271L17.33 307.3C16.53 317.6 16.17 327.7 16.17 337.5zM495.8 174.5c0-44.98-7.565-83.53-13.98-107.9c-4.688-17.54-18.34-31.23-36.04-35.95C435.5 27.91 392.9 16 337 16c-9.564 0-19.59 .4707-29.84 1.271l187.5 187.5C495.5 194.4 495.8 184.3 495.8 174.5zM26.77 248.8l236.3 236.3c142-36.1 203.9-150.4 222.2-221.1L248.9 26.87C106.9 62.96 45.07 177.2 26.77 248.8zM256 335.1c0 9.141-7.474 16-16 16c-4.094 0-8.188-1.564-11.31-4.689L164.7 283.3C161.6 280.2 160 276.1 160 271.1c0-8.529 6.865-16 16-16c4.095 0 8.189 1.562 11.31 4.688l64.01 64C254.4 327.8 256 331.9 256 335.1zM304 287.1c0 9.141-7.474 16-16 16c-4.094 0-8.188-1.564-11.31-4.689L212.7 235.3C209.6 232.2 208 228.1 208 223.1c0-9.141 7.473-16 16-16c4.094 0 8.188 1.562 11.31 4.688l64.01 64.01C302.5 279.8 304 283.9 304 287.1zM256 175.1c0-9.141 7.473-16 16-16c4.094 0 8.188 1.562 11.31 4.688l64.01 64.01c3.125 3.125 4.688 7.219 4.688 11.31c0 9.133-7.468 16-16 16c-4.094 0-8.189-1.562-11.31-4.688l-64.01-64.01C257.6 184.2 256 180.1 256 175.1z"})})},custom:d("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",children:d("path",{d:"M417.1 368c-5.937 10.27-16.69 16-27.75 16c-5.422 0-10.92-1.375-15.97-4.281L256 311.4V448c0 17.67-14.33 32-31.1 32S192 465.7 192 448V311.4l-118.3 68.29C68.67 382.6 63.17 384 57.75 384c-11.06 0-21.81-5.734-27.75-16c-8.828-15.31-3.594-34.88 11.72-43.72L159.1 256L41.72 187.7C26.41 178.9 21.17 159.3 29.1 144C36.63 132.5 49.26 126.7 61.65 128.2C65.78 128.7 69.88 130.1 73.72 132.3L192 200.6V64c0-17.67 14.33-32 32-32S256 46.33 256 64v136.6l118.3-68.29c3.838-2.213 7.939-3.539 12.07-4.051C398.7 126.7 411.4 132.5 417.1 144c8.828 15.31 3.594 34.88-11.72 43.72L288 256l118.3 68.28C421.6 333.1 426.8 352.7 417.1 368z"})}),flags:{outline:d("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:d("path",{d:"M0 0l6.084 24H8L1.916 0zM21 5h-4l-1-4H4l3 12h3l1 4h13L21 5zM6.563 3h7.875l2 8H8.563l-2-8zm8.832 10l-2.856 1.904L12.063 13h3.332zM19 13l-1.5-6h1.938l2 8H16l3-2z"})}),solid:d("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",children:d("path",{d:"M64 496C64 504.8 56.75 512 48 512h-32C7.25 512 0 504.8 0 496V32c0-17.75 14.25-32 32-32s32 14.25 32 32V496zM476.3 0c-6.365 0-13.01 1.35-19.34 4.233c-45.69 20.86-79.56 27.94-107.8 27.94c-59.96 0-94.81-31.86-163.9-31.87C160.9 .3055 131.6 4.867 96 15.75v350.5c32-9.984 59.87-14.1 84.85-14.1c73.63 0 124.9 31.78 198.6 31.78c31.91 0 68.02-5.971 111.1-23.09C504.1 355.9 512 344.4 512 332.1V30.73C512 11.1 495.3 0 476.3 0z"})})},foods:{outline:d("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:d("path",{d:"M17 4.978c-1.838 0-2.876.396-3.68.934.513-1.172 1.768-2.934 4.68-2.934a1 1 0 0 0 0-2c-2.921 0-4.629 1.365-5.547 2.512-.064.078-.119.162-.18.244C11.73 1.838 10.798.023 9.207.023 8.579.022 7.85.306 7 .978 5.027 2.54 5.329 3.902 6.492 4.999 3.609 5.222 0 7.352 0 12.969c0 4.582 4.961 11.009 9 11.009 1.975 0 2.371-.486 3-1 .629.514 1.025 1 3 1 4.039 0 9-6.418 9-11 0-5.953-4.055-8-7-8M8.242 2.546c.641-.508.943-.523.965-.523.426.169.975 1.405 1.357 3.055-1.527-.629-2.741-1.352-2.98-1.846.059-.112.241-.356.658-.686M15 21.978c-1.08 0-1.21-.109-1.559-.402l-.176-.146c-.367-.302-.816-.452-1.266-.452s-.898.15-1.266.452l-.176.146c-.347.292-.477.402-1.557.402-2.813 0-7-5.389-7-9.009 0-5.823 4.488-5.991 5-5.991 1.939 0 2.484.471 3.387 1.251l.323.276a1.995 1.995 0 0 0 2.58 0l.323-.276c.902-.78 1.447-1.251 3.387-1.251.512 0 5 .168 5 6 0 3.617-4.187 9-7 9"})}),solid:d("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",children:d("path",{d:"M481.9 270.1C490.9 279.1 496 291.3 496 304C496 316.7 490.9 328.9 481.9 337.9C472.9 346.9 460.7 352 448 352H64C51.27 352 39.06 346.9 30.06 337.9C21.06 328.9 16 316.7 16 304C16 291.3 21.06 279.1 30.06 270.1C39.06 261.1 51.27 256 64 256H448C460.7 256 472.9 261.1 481.9 270.1zM475.3 388.7C478.3 391.7 480 395.8 480 400V416C480 432.1 473.3 449.3 461.3 461.3C449.3 473.3 432.1 480 416 480H96C79.03 480 62.75 473.3 50.75 461.3C38.74 449.3 32 432.1 32 416V400C32 395.8 33.69 391.7 36.69 388.7C39.69 385.7 43.76 384 48 384H464C468.2 384 472.3 385.7 475.3 388.7zM50.39 220.8C45.93 218.6 42.03 215.5 38.97 211.6C35.91 207.7 33.79 203.2 32.75 198.4C31.71 193.5 31.8 188.5 32.99 183.7C54.98 97.02 146.5 32 256 32C365.5 32 457 97.02 479 183.7C480.2 188.5 480.3 193.5 479.2 198.4C478.2 203.2 476.1 207.7 473 211.6C469.1 215.5 466.1 218.6 461.6 220.8C457.2 222.9 452.3 224 447.3 224H64.67C59.73 224 54.84 222.9 50.39 220.8zM372.7 116.7C369.7 119.7 368 123.8 368 128C368 131.2 368.9 134.3 370.7 136.9C372.5 139.5 374.1 141.6 377.9 142.8C380.8 143.1 384 144.3 387.1 143.7C390.2 143.1 393.1 141.6 395.3 139.3C397.6 137.1 399.1 134.2 399.7 131.1C400.3 128 399.1 124.8 398.8 121.9C397.6 118.1 395.5 116.5 392.9 114.7C390.3 112.9 387.2 111.1 384 111.1C379.8 111.1 375.7 113.7 372.7 116.7V116.7zM244.7 84.69C241.7 87.69 240 91.76 240 96C240 99.16 240.9 102.3 242.7 104.9C244.5 107.5 246.1 109.6 249.9 110.8C252.8 111.1 256 112.3 259.1 111.7C262.2 111.1 265.1 109.6 267.3 107.3C269.6 105.1 271.1 102.2 271.7 99.12C272.3 96.02 271.1 92.8 270.8 89.88C269.6 86.95 267.5 84.45 264.9 82.7C262.3 80.94 259.2 79.1 256 79.1C251.8 79.1 247.7 81.69 244.7 84.69V84.69zM116.7 116.7C113.7 119.7 112 123.8 112 128C112 131.2 112.9 134.3 114.7 136.9C116.5 139.5 118.1 141.6 121.9 142.8C124.8 143.1 128 144.3 131.1 143.7C134.2 143.1 137.1 141.6 139.3 139.3C141.6 137.1 143.1 134.2 143.7 131.1C144.3 128 143.1 124.8 142.8 121.9C141.6 118.1 139.5 116.5 136.9 114.7C134.3 112.9 131.2 111.1 128 111.1C123.8 111.1 119.7 113.7 116.7 116.7L116.7 116.7z"})})},frequent:{outline:d("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[d("path",{d:"M13 4h-2l-.001 7H9v2h2v2h2v-2h4v-2h-4z"}),d("path",{d:"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0m0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10"})]}),solid:d("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",children:d("path",{d:"M256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512zM232 256C232 264 236 271.5 242.7 275.1L338.7 339.1C349.7 347.3 364.6 344.3 371.1 333.3C379.3 322.3 376.3 307.4 365.3 300L280 243.2V120C280 106.7 269.3 96 255.1 96C242.7 96 231.1 106.7 231.1 120L232 256z"})})},nature:{outline:d("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[d("path",{d:"M15.5 8a1.5 1.5 0 1 0 .001 3.001A1.5 1.5 0 0 0 15.5 8M8.5 8a1.5 1.5 0 1 0 .001 3.001A1.5 1.5 0 0 0 8.5 8"}),d("path",{d:"M18.933 0h-.027c-.97 0-2.138.787-3.018 1.497-1.274-.374-2.612-.51-3.887-.51-1.285 0-2.616.133-3.874.517C7.245.79 6.069 0 5.093 0h-.027C3.352 0 .07 2.67.002 7.026c-.039 2.479.276 4.238 1.04 5.013.254.258.882.677 1.295.882.191 3.177.922 5.238 2.536 6.38.897.637 2.187.949 3.2 1.102C8.04 20.6 8 20.795 8 21c0 1.773 2.35 3 4 3 1.648 0 4-1.227 4-3 0-.201-.038-.393-.072-.586 2.573-.385 5.435-1.877 5.925-7.587.396-.22.887-.568 1.104-.788.763-.774 1.079-2.534 1.04-5.013C23.929 2.67 20.646 0 18.933 0M3.223 9.135c-.237.281-.837 1.155-.884 1.238-.15-.41-.368-1.349-.337-3.291.051-3.281 2.478-4.972 3.091-5.031.256.015.731.27 1.265.646-1.11 1.171-2.275 2.915-2.352 5.125-.133.546-.398.858-.783 1.313M12 22c-.901 0-1.954-.693-2-1 0-.654.475-1.236 1-1.602V20a1 1 0 1 0 2 0v-.602c.524.365 1 .947 1 1.602-.046.307-1.099 1-2 1m3-3.48v.02a4.752 4.752 0 0 0-1.262-1.02c1.092-.516 2.239-1.334 2.239-2.217 0-1.842-1.781-2.195-3.977-2.195-2.196 0-3.978.354-3.978 2.195 0 .883 1.148 1.701 2.238 2.217A4.8 4.8 0 0 0 9 18.539v-.025c-1-.076-2.182-.281-2.973-.842-1.301-.92-1.838-3.045-1.853-6.478l.023-.041c.496-.826 1.49-1.45 1.804-3.102 0-2.047 1.357-3.631 2.362-4.522C9.37 3.178 10.555 3 11.948 3c1.447 0 2.685.192 3.733.57 1 .9 2.316 2.465 2.316 4.48.313 1.651 1.307 2.275 1.803 3.102.035.058.068.117.102.178-.059 5.967-1.949 7.01-4.902 7.19m6.628-8.202c-.037-.065-.074-.13-.113-.195a7.587 7.587 0 0 0-.739-.987c-.385-.455-.648-.768-.782-1.313-.076-2.209-1.241-3.954-2.353-5.124.531-.376 1.004-.63 1.261-.647.636.071 3.044 1.764 3.096 5.031.027 1.81-.347 3.218-.37 3.235"})]}),solid:d("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 576 512",children:d("path",{d:"M332.7 19.85C334.6 8.395 344.5 0 356.1 0C363.6 0 370.6 3.52 375.1 9.502L392 32H444.1C456.8 32 469.1 37.06 478.1 46.06L496 64H552C565.3 64 576 74.75 576 88V112C576 156.2 540.2 192 496 192H426.7L421.6 222.5L309.6 158.5L332.7 19.85zM448 64C439.2 64 432 71.16 432 80C432 88.84 439.2 96 448 96C456.8 96 464 88.84 464 80C464 71.16 456.8 64 448 64zM416 256.1V480C416 497.7 401.7 512 384 512H352C334.3 512 320 497.7 320 480V364.8C295.1 377.1 268.8 384 240 384C211.2 384 184 377.1 160 364.8V480C160 497.7 145.7 512 128 512H96C78.33 512 64 497.7 64 480V249.8C35.23 238.9 12.64 214.5 4.836 183.3L.9558 167.8C-3.331 150.6 7.094 133.2 24.24 128.1C41.38 124.7 58.76 135.1 63.05 152.2L66.93 167.8C70.49 182 83.29 191.1 97.97 191.1H303.8L416 256.1z"})})},objects:{outline:d("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[d("path",{d:"M12 0a9 9 0 0 0-5 16.482V21s2.035 3 5 3 5-3 5-3v-4.518A9 9 0 0 0 12 0zm0 2c3.86 0 7 3.141 7 7s-3.14 7-7 7-7-3.141-7-7 3.14-7 7-7zM9 17.477c.94.332 1.946.523 3 .523s2.06-.19 3-.523v.834c-.91.436-1.925.689-3 .689a6.924 6.924 0 0 1-3-.69v-.833zm.236 3.07A8.854 8.854 0 0 0 12 21c.965 0 1.888-.167 2.758-.451C14.155 21.173 13.153 22 12 22c-1.102 0-2.117-.789-2.764-1.453z"}),d("path",{d:"M14.745 12.449h-.004c-.852-.024-1.188-.858-1.577-1.824-.421-1.061-.703-1.561-1.182-1.566h-.009c-.481 0-.783.497-1.235 1.537-.436.982-.801 1.811-1.636 1.791l-.276-.043c-.565-.171-.853-.691-1.284-1.794-.125-.313-.202-.632-.27-.913-.051-.213-.127-.53-.195-.634C7.067 9.004 7.039 9 6.99 9A1 1 0 0 1 7 7h.01c1.662.017 2.015 1.373 2.198 2.134.486-.981 1.304-2.058 2.797-2.075 1.531.018 2.28 1.153 2.731 2.141l.002-.008C14.944 8.424 15.327 7 16.979 7h.032A1 1 0 1 1 17 9h-.011c-.149.076-.256.474-.319.709a6.484 6.484 0 0 1-.311.951c-.429.973-.79 1.789-1.614 1.789"})]}),solid:d("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 384 512",children:d("path",{d:"M112.1 454.3c0 6.297 1.816 12.44 5.284 17.69l17.14 25.69c5.25 7.875 17.17 14.28 26.64 14.28h61.67c9.438 0 21.36-6.401 26.61-14.28l17.08-25.68c2.938-4.438 5.348-12.37 5.348-17.7L272 415.1h-160L112.1 454.3zM191.4 .0132C89.44 .3257 16 82.97 16 175.1c0 44.38 16.44 84.84 43.56 115.8c16.53 18.84 42.34 58.23 52.22 91.45c.0313 .25 .0938 .5166 .125 .7823h160.2c.0313-.2656 .0938-.5166 .125-.7823c9.875-33.22 35.69-72.61 52.22-91.45C351.6 260.8 368 220.4 368 175.1C368 78.61 288.9-.2837 191.4 .0132zM192 96.01c-44.13 0-80 35.89-80 79.1C112 184.8 104.8 192 96 192S80 184.8 80 176c0-61.76 50.25-111.1 112-111.1c8.844 0 16 7.159 16 16S200.8 96.01 192 96.01z"})})},people:{outline:d("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[d("path",{d:"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0m0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10"}),d("path",{d:"M8 7a2 2 0 1 0-.001 3.999A2 2 0 0 0 8 7M16 7a2 2 0 1 0-.001 3.999A2 2 0 0 0 16 7M15.232 15c-.693 1.195-1.87 2-3.349 2-1.477 0-2.655-.805-3.347-2H15m3-2H6a6 6 0 1 0 12 0"})]}),solid:d("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",children:d("path",{d:"M0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256zM256 432C332.1 432 396.2 382 415.2 314.1C419.1 300.4 407.8 288 393.6 288H118.4C104.2 288 92.92 300.4 96.76 314.1C115.8 382 179.9 432 256 432V432zM176.4 160C158.7 160 144.4 174.3 144.4 192C144.4 209.7 158.7 224 176.4 224C194 224 208.4 209.7 208.4 192C208.4 174.3 194 160 176.4 160zM336.4 224C354 224 368.4 209.7 368.4 192C368.4 174.3 354 160 336.4 160C318.7 160 304.4 174.3 304.4 192C304.4 209.7 318.7 224 336.4 224z"})})},places:{outline:d("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[d("path",{d:"M6.5 12C5.122 12 4 13.121 4 14.5S5.122 17 6.5 17 9 15.879 9 14.5 7.878 12 6.5 12m0 3c-.275 0-.5-.225-.5-.5s.225-.5.5-.5.5.225.5.5-.225.5-.5.5M17.5 12c-1.378 0-2.5 1.121-2.5 2.5s1.122 2.5 2.5 2.5 2.5-1.121 2.5-2.5-1.122-2.5-2.5-2.5m0 3c-.275 0-.5-.225-.5-.5s.225-.5.5-.5.5.225.5.5-.225.5-.5.5"}),d("path",{d:"M22.482 9.494l-1.039-.346L21.4 9h.6c.552 0 1-.439 1-.992 0-.006-.003-.008-.003-.008H23c0-1-.889-2-1.984-2h-.642l-.731-1.717C19.262 3.012 18.091 2 16.764 2H7.236C5.909 2 4.738 3.012 4.357 4.283L3.626 6h-.642C1.889 6 1 7 1 8h.003S1 8.002 1 8.008C1 8.561 1.448 9 2 9h.6l-.043.148-1.039.346a2.001 2.001 0 0 0-1.359 2.097l.751 7.508a1 1 0 0 0 .994.901H3v1c0 1.103.896 2 2 2h2c1.104 0 2-.897 2-2v-1h6v1c0 1.103.896 2 2 2h2c1.104 0 2-.897 2-2v-1h1.096a.999.999 0 0 0 .994-.901l.751-7.508a2.001 2.001 0 0 0-1.359-2.097M6.273 4.857C6.402 4.43 6.788 4 7.236 4h9.527c.448 0 .834.43.963.857L19.313 9H4.688l1.585-4.143zM7 21H5v-1h2v1zm12 0h-2v-1h2v1zm2.189-3H2.811l-.662-6.607L3 11h18l.852.393L21.189 18z"})]}),solid:d("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",children:d("path",{d:"M39.61 196.8L74.8 96.29C88.27 57.78 124.6 32 165.4 32H346.6C387.4 32 423.7 57.78 437.2 96.29L472.4 196.8C495.6 206.4 512 229.3 512 256V448C512 465.7 497.7 480 480 480H448C430.3 480 416 465.7 416 448V400H96V448C96 465.7 81.67 480 64 480H32C14.33 480 0 465.7 0 448V256C0 229.3 16.36 206.4 39.61 196.8V196.8zM109.1 192H402.9L376.8 117.4C372.3 104.6 360.2 96 346.6 96H165.4C151.8 96 139.7 104.6 135.2 117.4L109.1 192zM96 256C78.33 256 64 270.3 64 288C64 305.7 78.33 320 96 320C113.7 320 128 305.7 128 288C128 270.3 113.7 256 96 256zM416 320C433.7 320 448 305.7 448 288C448 270.3 433.7 256 416 256C398.3 256 384 270.3 384 288C384 305.7 398.3 320 416 320z"})})},symbols:{outline:d("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:d("path",{d:"M0 0h11v2H0zM4 11h3V6h4V4H0v2h4zM15.5 17c1.381 0 2.5-1.116 2.5-2.493s-1.119-2.493-2.5-2.493S13 13.13 13 14.507 14.119 17 15.5 17m0-2.986c.276 0 .5.222.5.493 0 .272-.224.493-.5.493s-.5-.221-.5-.493.224-.493.5-.493M21.5 19.014c-1.381 0-2.5 1.116-2.5 2.493S20.119 24 21.5 24s2.5-1.116 2.5-2.493-1.119-2.493-2.5-2.493m0 2.986a.497.497 0 0 1-.5-.493c0-.271.224-.493.5-.493s.5.222.5.493a.497.497 0 0 1-.5.493M22 13l-9 9 1.513 1.5 8.99-9.009zM17 11c2.209 0 4-1.119 4-2.5V2s.985-.161 1.498.949C23.01 4.055 23 6 23 6s1-1.119 1-3.135C24-.02 21 0 21 0h-2v6.347A5.853 5.853 0 0 0 17 6c-2.209 0-4 1.119-4 2.5s1.791 2.5 4 2.5M10.297 20.482l-1.475-1.585a47.54 47.54 0 0 1-1.442 1.129c-.307-.288-.989-1.016-2.045-2.183.902-.836 1.479-1.466 1.729-1.892s.376-.871.376-1.336c0-.592-.273-1.178-.818-1.759-.546-.581-1.329-.871-2.349-.871-1.008 0-1.79.293-2.344.879-.556.587-.832 1.181-.832 1.784 0 .813.419 1.748 1.256 2.805-.847.614-1.444 1.208-1.794 1.784a3.465 3.465 0 0 0-.523 1.833c0 .857.308 1.56.924 2.107.616.549 1.423.823 2.42.823 1.173 0 2.444-.379 3.813-1.137L8.235 24h2.819l-2.09-2.383 1.333-1.135zm-6.736-6.389a1.02 1.02 0 0 1 .73-.286c.31 0 .559.085.747.254a.849.849 0 0 1 .283.659c0 .518-.419 1.112-1.257 1.784-.536-.651-.805-1.231-.805-1.742a.901.901 0 0 1 .302-.669M3.74 22c-.427 0-.778-.116-1.057-.349-.279-.232-.418-.487-.418-.766 0-.594.509-1.288 1.527-2.083.968 1.134 1.717 1.946 2.248 2.438-.921.507-1.686.76-2.3.76"})}),solid:d("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",children:d("path",{d:"M500.3 7.251C507.7 13.33 512 22.41 512 31.1V175.1C512 202.5 483.3 223.1 447.1 223.1C412.7 223.1 383.1 202.5 383.1 175.1C383.1 149.5 412.7 127.1 447.1 127.1V71.03L351.1 90.23V207.1C351.1 234.5 323.3 255.1 287.1 255.1C252.7 255.1 223.1 234.5 223.1 207.1C223.1 181.5 252.7 159.1 287.1 159.1V63.1C287.1 48.74 298.8 35.61 313.7 32.62L473.7 .6198C483.1-1.261 492.9 1.173 500.3 7.251H500.3zM74.66 303.1L86.5 286.2C92.43 277.3 102.4 271.1 113.1 271.1H174.9C185.6 271.1 195.6 277.3 201.5 286.2L213.3 303.1H239.1C266.5 303.1 287.1 325.5 287.1 351.1V463.1C287.1 490.5 266.5 511.1 239.1 511.1H47.1C21.49 511.1-.0019 490.5-.0019 463.1V351.1C-.0019 325.5 21.49 303.1 47.1 303.1H74.66zM143.1 359.1C117.5 359.1 95.1 381.5 95.1 407.1C95.1 434.5 117.5 455.1 143.1 455.1C170.5 455.1 191.1 434.5 191.1 407.1C191.1 381.5 170.5 359.1 143.1 359.1zM440.3 367.1H496C502.7 367.1 508.6 372.1 510.1 378.4C513.3 384.6 511.6 391.7 506.5 396L378.5 508C372.9 512.1 364.6 513.3 358.6 508.9C352.6 504.6 350.3 496.6 353.3 489.7L391.7 399.1H336C329.3 399.1 323.4 395.9 321 389.6C318.7 383.4 320.4 376.3 325.5 371.1L453.5 259.1C459.1 255 467.4 254.7 473.4 259.1C479.4 263.4 481.6 271.4 478.7 278.3L440.3 367.1zM116.7 219.1L19.85 119.2C-8.112 90.26-6.614 42.31 24.85 15.34C51.82-8.137 93.26-3.642 118.2 21.83L128.2 32.32L137.7 21.83C162.7-3.642 203.6-8.137 231.6 15.34C262.6 42.31 264.1 90.26 236.1 119.2L139.7 219.1C133.2 225.6 122.7 225.6 116.7 219.1H116.7z"})})}},Jn={loupe:d("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",children:d("path",{d:"M12.9 14.32a8 8 0 1 1 1.41-1.41l5.35 5.33-1.42 1.42-5.33-5.34zM8 14A6 6 0 1 0 8 2a6 6 0 0 0 0 12z"})}),delete:d("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",children:d("path",{d:"M10 8.586L2.929 1.515 1.515 2.929 8.586 10l-7.071 7.071 1.414 1.414L10 11.414l7.071 7.071 1.414-1.414L11.414 10l7.071-7.071-1.414-1.414L10 8.586z"})})};var ke={categories:Xn,search:Jn};function Pe(e){let{id:t,skin:n,emoji:r}=e;if(e.shortcodes){const l=e.shortcodes.match(ce.SHORTCODES_REGEX);l&&(t=l[1],l[2]&&(n=l[2]))}if(r||(r=ce.get(t||e.native)),!r)return e.fallback;const o=r.skins[n-1]||r.skins[0],i=o.src||(e.set!="native"&&!e.spritesheet?typeof e.getImageURL=="function"?e.getImageURL(e.set,o.unified):`https://cdn.jsdelivr.net/npm/emoji-datasource-${e.set}@14.0.0/img/${e.set}/64/${o.unified}.png`:void 0),a=typeof e.getSpritesheetURL=="function"?e.getSpritesheetURL(e.set):`https://cdn.jsdelivr.net/npm/emoji-datasource-${e.set}@14.0.0/img/${e.set}/sheets-256/64.png`;return d("span",{class:"emoji-mart-emoji","data-emoji-set":e.set,children:i?d("img",{style:{maxWidth:e.size||"1em",maxHeight:e.size||"1em",display:"inline-block"},alt:o.native||o.shortcodes,src:i}):e.set=="native"?d("span",{style:{fontSize:e.size,fontFamily:'"EmojiMart", "Segoe UI Emoji", "Segoe UI Symbol", "Segoe UI", "Apple Color Emoji", "Twemoji Mozilla", "Noto Color Emoji", "Android Emoji"'},children:o.native}):d("span",{style:{display:"block",width:e.size,height:e.size,backgroundImage:`url(${a})`,backgroundSize:`${100*L.sheet.cols}% ${100*L.sheet.rows}%`,backgroundPosition:`${100/(L.sheet.cols-1)*o.x}% ${100/(L.sheet.rows-1)*o.y}%`}})})}const Yn=typeof window<"u"&&window.HTMLElement?window.HTMLElement:Object;class Nt extends Yn{static get observedAttributes(){return Object.keys(this.Props)}update(t={}){for(let n in t)this.attributeChangedCallback(n,null,t[n])}attributeChangedCallback(t,n,r){if(!this.component)return;const o=Ft(t,{[t]:r},this.constructor.Props,this);this.component.componentWillReceiveProps?this.component.componentWillReceiveProps({[t]:o}):(this.component.props[t]=o,this.component.forceUpdate())}disconnectedCallback(){this.disconnected=!0,this.component&&this.component.unregister&&this.component.unregister()}constructor(t={}){if(super(),this.props=t,t.parent||t.ref){let n=null;const r=t.parent||(n=t.ref&&t.ref.current);n&&(n.innerHTML=""),r&&r.appendChild(this)}}}class Zn extends Nt{setShadow(){this.attachShadow({mode:"open"})}injectStyles(t){if(!t)return;const n=document.createElement("style");n.textContent=t,this.shadowRoot.insertBefore(n,this.shadowRoot.firstChild)}constructor(t,{styles:n}={}){super(t),this.setShadow(),this.injectStyles(n)}}var Ut={fallback:"",id:"",native:"",shortcodes:"",size:{value:"",transform:e=>/\D/.test(e)?e:`${e}px`},set:Q.set,skin:Q.skin};class Vt extends Nt{async connectedCallback(){const t=Bt(this.props,Ut,this);t.element=this,t.ref=n=>{this.component=n},await Ce(),!this.disconnected&&Pt(d(Pe,{...t}),this)}constructor(t){super(t)}}G(Vt,"Props",Ut);typeof customElements<"u"&&!customElements.get("em-emoji")&&customElements.define("em-emoji",Vt);var et,Ae=[],tt=x.__b,nt=x.__r,rt=x.diffed,it=x.__c,ot=x.unmount;function Qn(){var e;for(Ae.sort(function(t,n){return t.__v.__b-n.__v.__b});e=Ae.pop();)if(e.__P)try{e.__H.__h.forEach(ge),e.__H.__h.forEach(De),e.__H.__h=[]}catch(t){e.__H.__h=[],x.__e(t,e.__v)}}x.__b=function(e){tt&&tt(e)},x.__r=function(e){nt&&nt(e);var t=e.__c.__H;t&&(t.__h.forEach(ge),t.__h.forEach(De),t.__h=[])},x.diffed=function(e){rt&&rt(e);var t=e.__c;t&&t.__H&&t.__H.__h.length&&(Ae.push(t)!==1&&et===x.requestAnimationFrame||((et=x.requestAnimationFrame)||function(n){var r,o=function(){clearTimeout(i),at&&cancelAnimationFrame(r),setTimeout(n)},i=setTimeout(o,100);at&&(r=requestAnimationFrame(o))})(Qn))},x.__c=function(e,t){t.some(function(n){try{n.__h.forEach(ge),n.__h=n.__h.filter(function(r){return!r.__||De(r)})}catch(r){t.some(function(o){o.__h&&(o.__h=[])}),t=[],x.__e(r,n.__v)}}),it&&it(e,t)},x.unmount=function(e){ot&&ot(e);var t,n=e.__c;n&&n.__H&&(n.__H.__.forEach(function(r){try{ge(r)}catch(o){t=o}}),t&&x.__e(t,n.__v))};var at=typeof requestAnimationFrame=="function";function ge(e){var t=e.__c;typeof t=="function"&&(e.__c=void 0,t())}function De(e){e.__c=e.__()}function er(e,t){for(var n in t)e[n]=t[n];return e}function st(e,t){for(var n in e)if(n!=="__source"&&!(n in t))return!0;for(var r in t)if(r!=="__source"&&e[r]!==t[r])return!0;return!1}function xe(e){this.props=e}(xe.prototype=new Y).isPureReactComponent=!0,xe.prototype.shouldComponentUpdate=function(e,t){return st(this.props,e)||st(this.state,t)};var ct=x.__b;x.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),ct&&ct(e)};var tr=x.__e;x.__e=function(e,t,n){if(e.then){for(var r,o=t;o=o.__;)if((r=o.__c)&&r.__c)return t.__e==null&&(t.__e=n.__e,t.__k=n.__k),r.__c(e,t)}tr(e,t,n)};var lt=x.unmount;function Le(){this.__u=0,this.t=null,this.__b=null}function Wt(e){var t=e.__.__c;return t&&t.__e&&t.__e(e)}function he(){this.u=null,this.o=null}x.unmount=function(e){var t=e.__c;t&&t.__R&&t.__R(),t&&e.__h===!0&&(e.type=null),lt&&lt(e)},(Le.prototype=new Y).__c=function(e,t){var n=t.__c,r=this;r.t==null&&(r.t=[]),r.t.push(n);var o=Wt(r.__v),i=!1,a=function(){i||(i=!0,n.__R=null,o?o(l):l())};n.__R=a;var l=function(){if(!--r.__u){if(r.state.__e){var u=r.state.__e;r.__v.__k[0]=function v(h,g,f){return h&&(h.__v=null,h.__k=h.__k&&h.__k.map(function(_){return v(_,g,f)}),h.__c&&h.__c.__P===g&&(h.__e&&f.insertBefore(h.__e,h.__d),h.__c.__e=!0,h.__c.__P=f)),h}(u,u.__c.__P,u.__c.__O)}var s;for(r.setState({__e:r.__b=null});s=r.t.pop();)s.forceUpdate()}},c=t.__h===!0;r.__u++||c||r.setState({__e:r.__b=r.__v.__k[0]}),e.then(a,a)},Le.prototype.componentWillUnmount=function(){this.t=[]},Le.prototype.render=function(e,t){if(this.__b){if(this.__v.__k){var n=document.createElement("div"),r=this.__v.__k[0].__c;this.__v.__k[0]=function i(a,l,c){return a&&(a.__c&&a.__c.__H&&(a.__c.__H.__.forEach(function(u){typeof u.__c=="function"&&u.__c()}),a.__c.__H=null),(a=er({},a)).__c!=null&&(a.__c.__P===c&&(a.__c.__P=l),a.__c=null),a.__k=a.__k&&a.__k.map(function(u){return i(u,l,c)})),a}(this.__b,n,r.__O=r.__P)}this.__b=null}var o=t.__e&&Me(le,null,e.fallback);return o&&(o.__h=null),[Me(le,null,t.__e?null:e.children),o]};var ut=function(e,t,n){if(++n[1]===n[0]&&e.o.delete(t),e.props.revealOrder&&(e.props.revealOrder[0]!=="t"||!e.o.size))for(n=e.u;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;e.u=n=n[2]}};(he.prototype=new Y).__e=function(e){var t=this,n=Wt(t.__v),r=t.o.get(e);return r[0]++,function(o){var i=function(){t.props.revealOrder?(r.push(o),ut(t,e,r)):o()};n?n(i):i()}},he.prototype.render=function(e){this.u=null,this.o=new Map;var t=ye(e.children);e.revealOrder&&e.revealOrder[0]==="b"&&t.reverse();for(var n=t.length;n--;)this.o.set(t[n],this.u=[1,0,this.u]);return e.children},he.prototype.componentDidUpdate=he.prototype.componentDidMount=function(){var e=this;this.o.forEach(function(t,n){ut(e,n,t)})};var nr=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.element")||60103,rr=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|marker(?!H|W|U)|overline|paint|stop|strikethrough|stroke|text(?!L)|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,ir=typeof document<"u",or=function(e){return(typeof Symbol<"u"&&typeof Symbol()=="symbol"?/fil|che|rad/i:/fil|che|ra/i).test(e)};Y.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(e){Object.defineProperty(Y.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(t){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:t})}})});var dt=x.event;function ar(){}function sr(){return this.cancelBubble}function cr(){return this.defaultPrevented}x.event=function(e){return dt&&(e=dt(e)),e.persist=ar,e.isPropagationStopped=sr,e.isDefaultPrevented=cr,e.nativeEvent=e};var ft={configurable:!0,get:function(){return this.class}},ht=x.vnode;x.vnode=function(e){var t=e.type,n=e.props,r=n;if(typeof t=="string"){var o=t.indexOf("-")===-1;for(var i in r={},n){var a=n[i];ir&&i==="children"&&t==="noscript"||i==="value"&&"defaultValue"in n&&a==null||(i==="defaultValue"&&"value"in n&&n.value==null?i="value":i==="download"&&a===!0?a="":/ondoubleclick/i.test(i)?i="ondblclick":/^onchange(textarea|input)/i.test(i+t)&&!or(n.type)?i="oninput":/^onfocus$/i.test(i)?i="onfocusin":/^onblur$/i.test(i)?i="onfocusout":/^on(Ani|Tra|Tou|BeforeInp)/.test(i)?i=i.toLowerCase():o&&rr.test(i)?i=i.replace(/[A-Z0-9]/,"-$&").toLowerCase():a===null&&(a=void 0),r[i]=a)}t=="select"&&r.multiple&&Array.isArray(r.value)&&(r.value=ye(n.children).forEach(function(l){l.props.selected=r.value.indexOf(l.props.value)!=-1})),t=="select"&&r.defaultValue!=null&&(r.value=ye(n.children).forEach(function(l){l.props.selected=r.multiple?r.defaultValue.indexOf(l.props.value)!=-1:r.defaultValue==l.props.value})),e.props=r,n.class!=n.className&&(ft.enumerable="className"in n,n.className!=null&&(r.class=n.className),Object.defineProperty(r,"className",ft))}e.$$typeof=nr,ht&&ht(e)};var pt=x.__r;x.__r=function(e){pt&&pt(e),e.__c};const lr={light:"outline",dark:"solid"};class ur extends xe{renderIcon(t){const{icon:n}=t;if(n){if(n.svg)return d("span",{class:"flex",dangerouslySetInnerHTML:{__html:n.svg}});if(n.src)return d("img",{src:n.src})}const r=ke.categories[t.id]||ke.categories.custom,o=this.props.icons=="auto"?lr[this.props.theme]:this.props.icons;return r[o]||r}render(){let t=null;return d("nav",{id:"nav",class:"padding","data-position":this.props.position,dir:this.props.dir,children:d("div",{class:"flex relative",children:[this.categories.map((n,r)=>{const o=n.name||F.categories[n.id],i=!this.props.unfocused&&n.id==this.state.categoryId;return i&&(t=r),d("button",{"aria-label":o,"aria-selected":i||void 0,title:o,type:"button",class:"flex flex-grow flex-center",onMouseDown:a=>a.preventDefault(),onClick:()=>{this.props.onClick({category:n,i:r})},children:this.renderIcon(n)})}),d("div",{class:"bar",style:{width:`${100/this.categories.length}%`,opacity:t==null?0:1,transform:this.props.dir==="rtl"?`scaleX(-1) translateX(${t*100}%)`:`translateX(${t*100}%)`}})]})})}constructor(){super(),this.categories=L.categories.filter(t=>!t.target),this.state={categoryId:this.categories[0].id}}}class dr extends xe{shouldComponentUpdate(t){for(let n in t)if(n!="children"&&t[n]!=this.props[n])return!0;return!1}render(){return this.props.children}}const pe={rowsPerRender:10};class fr extends Y{getInitialState(t=this.props){return{skin:re.get("skin")||t.skin,theme:this.initTheme(t.theme)}}componentWillMount(){this.dir=F.rtl?"rtl":"ltr",this.refs={menu:Z(),navigation:Z(),scroll:Z(),search:Z(),searchInput:Z(),skinToneButton:Z(),skinToneRadio:Z()},this.initGrid(),this.props.stickySearch==!1&&this.props.searchPosition=="sticky"&&(console.warn("[EmojiMart] Deprecation warning: `stickySearch` has been renamed `searchPosition`."),this.props.searchPosition="static")}componentDidMount(){if(this.register(),this.shadowRoot=this.base.parentNode,this.props.autoFocus){const{searchInput:t}=this.refs;t.current&&t.current.focus()}}componentWillReceiveProps(t){this.nextState||(this.nextState={});for(const n in t)this.nextState[n]=t[n];clearTimeout(this.nextStateTimer),this.nextStateTimer=setTimeout(()=>{let n=!1;for(const o in this.nextState)this.props[o]=this.nextState[o],(o==="custom"||o==="categories")&&(n=!0);delete this.nextState;const r=this.getInitialState();if(n)return this.reset(r);this.setState(r)})}componentWillUnmount(){this.unregister()}async reset(t={}){await Ce(this.props),this.initGrid(),this.unobserve(),this.setState(t,()=>{this.observeCategories(),this.observeRows()})}register(){document.addEventListener("click",this.handleClickOutside),this.observe()}unregister(){document.removeEventListener("click",this.handleClickOutside),this.unobserve()}observe(){this.observeCategories(),this.observeRows()}unobserve({except:t=[]}={}){Array.isArray(t)||(t=[t]);for(const n of this.observers)t.includes(n)||n.disconnect();this.observers=[].concat(t)}initGrid(){const{categories:t}=L;this.refs.categories=new Map;const n=L.categories.map(o=>o.id).join(",");this.navKey&&this.navKey!=n&&this.refs.scroll.current&&(this.refs.scroll.current.scrollTop=0),this.navKey=n,this.grid=[],this.grid.setsize=0;const r=(o,i)=>{const a=[];a.__categoryId=i.id,a.__index=o.length,this.grid.push(a);const l=this.grid.length-1,c=l%pe.rowsPerRender?{}:Z();return c.index=l,c.posinset=this.grid.setsize+1,o.push(c),a};for(let o of t){const i=[];let a=r(i,o);for(let l of o.emojis)a.length==this.getPerLine()&&(a=r(i,o)),this.grid.setsize+=1,a.push(l);this.refs.categories.set(o.id,{root:Z(),rows:i})}}initTheme(t){if(t!="auto")return t;if(!this.darkMedia){if(this.darkMedia=matchMedia("(prefers-color-scheme: dark)"),this.darkMedia.media.match(/^not/))return"light";this.darkMedia.addListener(()=>{this.props.theme=="auto"&&this.setState({theme:this.darkMedia.matches?"dark":"light"})})}return this.darkMedia.matches?"dark":"light"}initDynamicPerLine(t=this.props){if(!t.dynamicWidth)return;const{element:n,emojiButtonSize:r}=t,o=()=>{const{width:a}=n.getBoundingClientRect();return Math.floor(a/r)},i=new ResizeObserver(()=>{this.unobserve({except:i}),this.setState({perLine:o()},()=>{this.initGrid(),this.forceUpdate(()=>{this.observeCategories(),this.observeRows()})})});return i.observe(n),this.observers.push(i),o()}getPerLine(){return this.state.perLine||this.props.perLine}getEmojiByPos([t,n]){const r=this.state.searchResults||this.grid,o=r[t]&&r[t][n];if(o)return ce.get(o)}observeCategories(){const t=this.refs.navigation.current;if(!t)return;const n=new Map,r=a=>{a!=t.state.categoryId&&t.setState({categoryId:a})},o={root:this.refs.scroll.current,threshold:[0,1]},i=new IntersectionObserver(a=>{for(const c of a){const u=c.target.dataset.id;n.set(u,c.intersectionRatio)}const l=[...n];for(const[c,u]of l)if(u){r(c);break}},o);for(const{root:a}of this.refs.categories.values())i.observe(a.current);this.observers.push(i)}observeRows(){const t={...this.state.visibleRows},n=new IntersectionObserver(r=>{for(const o of r){const i=parseInt(o.target.dataset.index);o.isIntersecting?t[i]=!0:delete t[i]}this.setState({visibleRows:t})},{root:this.refs.scroll.current,rootMargin:`${this.props.emojiButtonSize*(pe.rowsPerRender+5)}px 0px ${this.props.emojiButtonSize*pe.rowsPerRender}px`});for(const{rows:r}of this.refs.categories.values())for(const o of r)o.current&&n.observe(o.current);this.observers.push(n)}preventDefault(t){t.preventDefault()}unfocusSearch(){const t=this.refs.searchInput.current;t&&t.blur()}navigate({e:t,input:n,left:r,right:o,up:i,down:a}){const l=this.state.searchResults||this.grid;if(!l.length)return;let[c,u]=this.state.pos;const s=(()=>{if(c==0&&u==0&&!t.repeat&&(r||i))return null;if(c==-1)return!t.repeat&&(o||a)&&n.selectionStart==n.value.length?[0,0]:null;if(r||o){let v=l[c];const h=r?-1:1;if(u+=h,!v[u]){if(c+=h,v=l[c],!v)return c=r?0:l.length-1,u=r?0:l[c].length-1,[c,u];u=r?v.length-1:0}return[c,u]}if(i||a){c+=i?-1:1;const v=l[c];return v?(v[u]||(u=v.length-1),[c,u]):(c=i?0:l.length-1,u=i?0:l[c].length-1,[c,u])}})();if(s)t.preventDefault();else{this.state.pos[0]>-1&&this.setState({pos:[-1,-1]});return}this.setState({pos:s,keyboard:!0},()=>{this.scrollTo({row:s[0]})})}scrollTo({categoryId:t,row:n}){const r=this.state.searchResults||this.grid;if(!r.length)return;const o=this.refs.scroll.current,i=o.getBoundingClientRect();let a=0;if(n>=0&&(t=r[n].__categoryId),t&&(a=(this.refs[t]||this.refs.categories.get(t).root).current.getBoundingClientRect().top-(i.top-o.scrollTop)+1),n>=0)if(!n)a=0;else{const l=r[n].__index,c=a+l*this.props.emojiButtonSize,u=c+this.props.emojiButtonSize+this.props.emojiButtonSize*.88;if(c<o.scrollTop)a=c;else if(u>o.scrollTop+i.height)a=u-i.height;else return}this.ignoreMouse(),o.scrollTop=a}ignoreMouse(){this.mouseIsIgnored=!0,clearTimeout(this.ignoreMouseTimer),this.ignoreMouseTimer=setTimeout(()=>{delete this.mouseIsIgnored},100)}handleEmojiOver(t){this.mouseIsIgnored||this.state.showSkins||this.setState({pos:t||[-1,-1],keyboard:!1})}handleEmojiClick({e:t,emoji:n,pos:r}){if(this.props.onEmojiSelect&&(!n&&r&&(n=this.getEmojiByPos(r)),n)){const o=Gn(n,{skinIndex:this.state.skin-1});this.props.maxFrequentRows&&Dt.add(o,this.props),this.props.onEmojiSelect(o,t)}}closeSkins(){this.state.showSkins&&(this.setState({showSkins:null,tempSkin:null}),this.base.removeEventListener("click",this.handleBaseClick),this.base.removeEventListener("keydown",this.handleBaseKeydown))}handleSkinMouseOver(t){this.setState({tempSkin:t})}handleSkinClick(t){this.ignoreMouse(),this.closeSkins(),this.setState({skin:t,tempSkin:null}),re.set("skin",t)}renderNav(){return d(ur,{ref:this.refs.navigation,icons:this.props.icons,theme:this.state.theme,dir:this.dir,unfocused:!!this.state.searchResults,position:this.props.navPosition,onClick:this.handleCategoryClick},this.navKey)}renderPreview(){const t=this.getEmojiByPos(this.state.pos),n=this.state.searchResults&&!this.state.searchResults.length;return d("div",{id:"preview",class:"flex flex-middle",dir:this.dir,"data-position":this.props.previewPosition,children:[d("div",{class:"flex flex-middle flex-grow",children:[d("div",{class:"flex flex-auto flex-middle flex-center",style:{height:this.props.emojiButtonSize,fontSize:this.props.emojiButtonSize},children:d(Pe,{emoji:t,id:n?this.props.noResultsEmoji||"cry":this.props.previewEmoji||(this.props.previewPosition=="top"?"point_down":"point_up"),set:this.props.set,size:this.props.emojiButtonSize,skin:this.state.tempSkin||this.state.skin,spritesheet:!0,getSpritesheetURL:this.props.getSpritesheetURL})}),d("div",{class:`margin-${this.dir[0]}`,children:t||n?d("div",{class:`padding-${this.dir[2]} align-${this.dir[0]}`,children:[d("div",{class:"preview-title ellipsis",children:t?t.name:F.search_no_results_1}),d("div",{class:"preview-subtitle ellipsis color-c",children:t?t.skins[0].shortcodes:F.search_no_results_2})]}):d("div",{class:"preview-placeholder color-c",children:F.pick})})]}),!t&&this.props.skinTonePosition=="preview"&&this.renderSkinToneButton()]})}renderEmojiButton(t,{pos:n,posinset:r,grid:o}){const i=this.props.emojiButtonSize,a=this.state.tempSkin||this.state.skin,c=(t.skins[a-1]||t.skins[0]).native,u=Kn(this.state.pos,n),s=n.concat(t.id).join("");return d(dr,{selected:u,skin:a,size:i,children:d("button",{"aria-label":c,"aria-selected":u||void 0,"aria-posinset":r,"aria-setsize":o.setsize,"data-keyboard":this.state.keyboard,title:this.props.previewPosition=="none"?t.name:void 0,type:"button",class:"flex flex-center flex-middle",tabindex:"-1",onClick:v=>this.handleEmojiClick({e:v,emoji:t}),onMouseEnter:()=>this.handleEmojiOver(n),onMouseLeave:()=>this.handleEmojiOver(),style:{width:this.props.emojiButtonSize,height:this.props.emojiButtonSize,fontSize:this.props.emojiSize,lineHeight:0},children:[d("div",{"aria-hidden":"true",class:"background",style:{borderRadius:this.props.emojiButtonRadius,backgroundColor:this.props.emojiButtonColors?this.props.emojiButtonColors[(r-1)%this.props.emojiButtonColors.length]:void 0}}),d(Pe,{emoji:t,set:this.props.set,size:this.props.emojiSize,skin:a,spritesheet:!0,getSpritesheetURL:this.props.getSpritesheetURL})]})},s)}renderSearch(){const t=this.props.previewPosition=="none"||this.props.skinTonePosition=="search";return d("div",{children:[d("div",{class:"spacer"}),d("div",{class:"flex flex-middle",children:[d("div",{class:"search relative flex-grow",children:[d("input",{type:"search",ref:this.refs.searchInput,placeholder:F.search,onClick:this.handleSearchClick,onInput:this.handleSearchInput,onKeyDown:this.handleSearchKeyDown,autoComplete:"off"}),d("span",{class:"icon loupe flex",children:ke.search.loupe}),this.state.searchResults&&d("button",{title:"Clear","aria-label":"Clear",type:"button",class:"icon delete flex",onClick:this.clearSearch,onMouseDown:this.preventDefault,children:ke.search.delete})]}),t&&this.renderSkinToneButton()]})]})}renderSearchResults(){const{searchResults:t}=this.state;return t?d("div",{class:"category",ref:this.refs.search,children:[d("div",{class:`sticky padding-small align-${this.dir[0]}`,children:F.categories.search}),d("div",{children:t.length?t.map((n,r)=>d("div",{class:"flex",children:n.map((o,i)=>this.renderEmojiButton(o,{pos:[r,i],posinset:r*this.props.perLine+i+1,grid:t}))})):d("div",{class:`padding-small align-${this.dir[0]}`,children:this.props.onAddCustomEmoji&&d("a",{onClick:this.props.onAddCustomEmoji,children:F.add_custom})})})]}):null}renderCategories(){const{categories:t}=L,n=!!this.state.searchResults,r=this.getPerLine();return d("div",{style:{visibility:n?"hidden":void 0,display:n?"none":void 0,height:"100%"},children:t.map(o=>{const{root:i,rows:a}=this.refs.categories.get(o.id);return d("div",{"data-id":o.target?o.target.id:o.id,class:"category",ref:i,children:[d("div",{class:`sticky padding-small align-${this.dir[0]}`,children:o.name||F.categories[o.id]}),d("div",{class:"relative",style:{height:a.length*this.props.emojiButtonSize},children:a.map((l,c)=>{const u=l.index-l.index%pe.rowsPerRender,s=this.state.visibleRows[u],v="current"in l?l:void 0;if(!s&&!v)return null;const h=c*r,g=h+r,f=o.emojis.slice(h,g);return f.length<r&&f.push(...new Array(r-f.length)),d("div",{"data-index":l.index,ref:v,class:"flex row",style:{top:c*this.props.emojiButtonSize},children:s&&f.map((_,w)=>{if(!_)return d("div",{style:{width:this.props.emojiButtonSize,height:this.props.emojiButtonSize}});const S=ce.get(_);return this.renderEmojiButton(S,{pos:[l.index,w],posinset:l.posinset+w,grid:this.grid})})},l.index)})})]})})})}renderSkinToneButton(){return this.props.skinTonePosition=="none"?null:d("div",{class:"flex flex-auto flex-center flex-middle",style:{position:"relative",width:this.props.emojiButtonSize,height:this.props.emojiButtonSize},children:d("button",{type:"button",ref:this.refs.skinToneButton,class:"skin-tone-button flex flex-auto flex-center flex-middle","aria-selected":this.state.showSkins?"":void 0,"aria-label":F.skins.choose,title:F.skins.choose,onClick:this.openSkins,style:{width:this.props.emojiSize,height:this.props.emojiSize},children:d("span",{class:`skin-tone skin-tone-${this.state.skin}`})})})}renderLiveRegion(){const t=this.getEmojiByPos(this.state.pos),n=t?t.name:"";return d("div",{"aria-live":"polite",class:"sr-only",children:n})}renderSkins(){const n=this.refs.skinToneButton.current.getBoundingClientRect(),r=this.base.getBoundingClientRect(),o={};return this.dir=="ltr"?o.right=r.right-n.right-3:o.left=n.left-r.left-3,this.props.previewPosition=="bottom"&&this.props.skinTonePosition=="preview"?o.bottom=r.bottom-n.top+6:(o.top=n.bottom-r.top+3,o.bottom="auto"),d("div",{ref:this.refs.menu,role:"radiogroup",dir:this.dir,"aria-label":F.skins.choose,class:"menu hidden","data-position":o.top?"top":"bottom",style:o,children:[...Array(6).keys()].map(i=>{const a=i+1,l=this.state.skin==a;return d("div",{children:[d("input",{type:"radio",name:"skin-tone",value:a,"aria-label":F.skins[a],ref:l?this.refs.skinToneRadio:null,defaultChecked:l,onChange:()=>this.handleSkinMouseOver(a),onKeyDown:c=>{(c.code=="Enter"||c.code=="Space"||c.code=="Tab")&&(c.preventDefault(),this.handleSkinClick(a))}}),d("button",{"aria-hidden":"true",tabindex:"-1",onClick:()=>this.handleSkinClick(a),onMouseEnter:()=>this.handleSkinMouseOver(a),onMouseLeave:()=>this.handleSkinMouseOver(),class:"option flex flex-grow flex-middle",children:[d("span",{class:`skin-tone skin-tone-${a}`}),d("span",{class:"margin-small-lr",children:F.skins[a]})]})]})})})}render(){const t=this.props.perLine*this.props.emojiButtonSize;return d("section",{id:"root",class:"flex flex-column",dir:this.dir,style:{width:this.props.dynamicWidth?"100%":`calc(${t}px + (var(--padding) + var(--sidebar-width)))`},"data-emoji-set":this.props.set,"data-theme":this.state.theme,"data-menu":this.state.showSkins?"":void 0,children:[this.props.previewPosition=="top"&&this.renderPreview(),this.props.navPosition=="top"&&this.renderNav(),this.props.searchPosition=="sticky"&&d("div",{class:"padding-lr",children:this.renderSearch()}),d("div",{ref:this.refs.scroll,class:"scroll flex-grow padding-lr",children:d("div",{style:{width:this.props.dynamicWidth?"100%":t,height:"100%"},children:[this.props.searchPosition=="static"&&this.renderSearch(),this.renderSearchResults(),this.renderCategories()]})}),this.props.navPosition=="bottom"&&this.renderNav(),this.props.previewPosition=="bottom"&&this.renderPreview(),this.state.showSkins&&this.renderSkins(),this.renderLiveRegion()]})}constructor(t){super(),G(this,"handleClickOutside",n=>{const{element:r}=this.props;n.target!=r&&(this.state.showSkins&&this.closeSkins(),this.props.onClickOutside&&this.props.onClickOutside(n))}),G(this,"handleBaseClick",n=>{this.state.showSkins&&(n.target.closest(".menu")||(n.preventDefault(),n.stopImmediatePropagation(),this.closeSkins()))}),G(this,"handleBaseKeydown",n=>{this.state.showSkins&&n.key=="Escape"&&(n.preventDefault(),n.stopImmediatePropagation(),this.closeSkins())}),G(this,"handleSearchClick",()=>{this.getEmojiByPos(this.state.pos)&&this.setState({pos:[-1,-1]})}),G(this,"handleSearchInput",async()=>{const n=this.refs.searchInput.current;if(!n)return;const{value:r}=n,o=await ce.search(r),i=()=>{this.refs.scroll.current&&(this.refs.scroll.current.scrollTop=0)};if(!o)return this.setState({searchResults:o,pos:[-1,-1]},i);const a=n.selectionStart==n.value.length?[0,0]:[-1,-1],l=[];l.setsize=o.length;let c=null;for(let u of o)(!l.length||c.length==this.getPerLine())&&(c=[],c.__categoryId="search",c.__index=l.length,l.push(c)),c.push(u);this.ignoreMouse(),this.setState({searchResults:l,pos:a},i)}),G(this,"handleSearchKeyDown",n=>{const r=n.currentTarget;switch(n.stopImmediatePropagation(),n.key){case"ArrowLeft":this.navigate({e:n,input:r,left:!0});break;case"ArrowRight":this.navigate({e:n,input:r,right:!0});break;case"ArrowUp":this.navigate({e:n,input:r,up:!0});break;case"ArrowDown":this.navigate({e:n,input:r,down:!0});break;case"Enter":n.preventDefault(),this.handleEmojiClick({e:n,pos:this.state.pos});break;case"Escape":n.preventDefault(),this.state.searchResults?this.clearSearch():this.unfocusSearch();break}}),G(this,"clearSearch",()=>{const n=this.refs.searchInput.current;n&&(n.value="",n.focus(),this.handleSearchInput())}),G(this,"handleCategoryClick",({category:n,i:r})=>{this.scrollTo(r==0?{row:-1}:{categoryId:n.id})}),G(this,"openSkins",n=>{const{currentTarget:r}=n,o=r.getBoundingClientRect();this.setState({showSkins:o},async()=>{await qn(2);const i=this.refs.menu.current;i&&(i.classList.remove("hidden"),this.refs.skinToneRadio.current.focus(),this.base.addEventListener("click",this.handleBaseClick,!0),this.base.addEventListener("keydown",this.handleBaseKeydown,!0))})}),this.observers=[],this.state={pos:[-1,-1],perLine:this.initDynamicPerLine(t),visibleRows:{0:!0},...this.getInitialState(t)}}}class Ie extends Zn{async connectedCallback(){const t=Bt(this.props,Q,this);t.element=this,t.ref=n=>{this.component=n},await Ce(t),!this.disconnected&&Pt(d(fr,{...t}),this.shadowRoot)}constructor(t){super(t,{styles:kt(Kt)})}}G(Ie,"Props",Q);typeof customElements<"u"&&!customElements.get("em-emoji-picker")&&customElements.define("em-emoji-picker",Ie);var Kt={};Kt=`:host {
  width: min-content;
  height: 435px;
  min-height: 230px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  --border-radius: 10px;
  --category-icon-size: 18px;
  --font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", sans-serif;
  --font-size: 15px;
  --preview-placeholder-size: 21px;
  --preview-title-size: 1.1em;
  --preview-subtitle-size: .9em;
  --shadow-color: 0deg 0% 0%;
  --shadow: .3px .5px 2.7px hsl(var(--shadow-color) / .14), .4px .8px 1px -3.2px hsl(var(--shadow-color) / .14), 1px 2px 2.5px -4.5px hsl(var(--shadow-color) / .14);
  display: flex;
}

[data-theme="light"] {
  --em-rgb-color: var(--rgb-color, 34, 36, 39);
  --em-rgb-accent: var(--rgb-accent, 34, 102, 237);
  --em-rgb-background: var(--rgb-background, 255, 255, 255);
  --em-rgb-input: var(--rgb-input, 255, 255, 255);
  --em-color-border: var(--color-border, rgba(0, 0, 0, .05));
  --em-color-border-over: var(--color-border-over, rgba(0, 0, 0, .1));
}

[data-theme="dark"] {
  --em-rgb-color: var(--rgb-color, 222, 222, 221);
  --em-rgb-accent: var(--rgb-accent, 58, 130, 247);
  --em-rgb-background: var(--rgb-background, 21, 22, 23);
  --em-rgb-input: var(--rgb-input, 0, 0, 0);
  --em-color-border: var(--color-border, rgba(255, 255, 255, .1));
  --em-color-border-over: var(--color-border-over, rgba(255, 255, 255, .2));
}

#root {
  --color-a: rgb(var(--em-rgb-color));
  --color-b: rgba(var(--em-rgb-color), .65);
  --color-c: rgba(var(--em-rgb-color), .45);
  --padding: 12px;
  --padding-small: calc(var(--padding) / 2);
  --sidebar-width: 16px;
  --duration: 225ms;
  --duration-fast: 125ms;
  --duration-instant: 50ms;
  --easing: cubic-bezier(.4, 0, .2, 1);
  width: 100%;
  text-align: left;
  border-radius: var(--border-radius);
  background-color: rgb(var(--em-rgb-background));
  position: relative;
}

@media (prefers-reduced-motion) {
  #root {
    --duration: 0;
    --duration-fast: 0;
    --duration-instant: 0;
  }
}

#root[data-menu] button {
  cursor: auto;
}

#root[data-menu] .menu button {
  cursor: pointer;
}

:host, #root, input, button {
  color: rgb(var(--em-rgb-color));
  font-family: var(--font-family);
  font-size: var(--font-size);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: normal;
}

*, :before, :after {
  box-sizing: border-box;
  min-width: 0;
  margin: 0;
  padding: 0;
}

.relative {
  position: relative;
}

.flex {
  display: flex;
}

.flex-auto {
  flex: none;
}

.flex-center {
  justify-content: center;
}

.flex-column {
  flex-direction: column;
}

.flex-grow {
  flex: auto;
}

.flex-middle {
  align-items: center;
}

.flex-wrap {
  flex-wrap: wrap;
}

.padding {
  padding: var(--padding);
}

.padding-t {
  padding-top: var(--padding);
}

.padding-lr {
  padding-left: var(--padding);
  padding-right: var(--padding);
}

.padding-r {
  padding-right: var(--padding);
}

.padding-small {
  padding: var(--padding-small);
}

.padding-small-b {
  padding-bottom: var(--padding-small);
}

.padding-small-lr {
  padding-left: var(--padding-small);
  padding-right: var(--padding-small);
}

.margin {
  margin: var(--padding);
}

.margin-r {
  margin-right: var(--padding);
}

.margin-l {
  margin-left: var(--padding);
}

.margin-small-l {
  margin-left: var(--padding-small);
}

.margin-small-lr {
  margin-left: var(--padding-small);
  margin-right: var(--padding-small);
}

.align-l {
  text-align: left;
}

.align-r {
  text-align: right;
}

.color-a {
  color: var(--color-a);
}

.color-b {
  color: var(--color-b);
}

.color-c {
  color: var(--color-c);
}

.ellipsis {
  white-space: nowrap;
  max-width: 100%;
  width: auto;
  text-overflow: ellipsis;
  overflow: hidden;
}

.sr-only {
  width: 1px;
  height: 1px;
  position: absolute;
  top: auto;
  left: -10000px;
  overflow: hidden;
}

a {
  cursor: pointer;
  color: rgb(var(--em-rgb-accent));
}

a:hover {
  text-decoration: underline;
}

.spacer {
  height: 10px;
}

[dir="rtl"] .scroll {
  padding-left: 0;
  padding-right: var(--padding);
}

.scroll {
  padding-right: 0;
  overflow-x: hidden;
  overflow-y: auto;
}

.scroll::-webkit-scrollbar {
  width: var(--sidebar-width);
  height: var(--sidebar-width);
}

.scroll::-webkit-scrollbar-track {
  border: 0;
}

.scroll::-webkit-scrollbar-button {
  width: 0;
  height: 0;
  display: none;
}

.scroll::-webkit-scrollbar-corner {
  background-color: rgba(0, 0, 0, 0);
}

.scroll::-webkit-scrollbar-thumb {
  min-height: 20%;
  min-height: 65px;
  border: 4px solid rgb(var(--em-rgb-background));
  border-radius: 8px;
}

.scroll::-webkit-scrollbar-thumb:hover {
  background-color: var(--em-color-border-over) !important;
}

.scroll:hover::-webkit-scrollbar-thumb {
  background-color: var(--em-color-border);
}

.sticky {
  z-index: 1;
  background-color: rgba(var(--em-rgb-background), .9);
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  font-weight: 500;
  position: sticky;
  top: -1px;
}

[dir="rtl"] .search input[type="search"] {
  padding: 10px 2.2em 10px 2em;
}

[dir="rtl"] .search .loupe {
  left: auto;
  right: .7em;
}

[dir="rtl"] .search .delete {
  left: .7em;
  right: auto;
}

.search {
  z-index: 2;
  position: relative;
}

.search input, .search button {
  font-size: calc(var(--font-size)  - 1px);
}

.search input[type="search"] {
  width: 100%;
  background-color: var(--em-color-border);
  transition-duration: var(--duration);
  transition-property: background-color, box-shadow;
  transition-timing-function: var(--easing);
  border: 0;
  border-radius: 10px;
  outline: 0;
  padding: 10px 2em 10px 2.2em;
  display: block;
}

.search input[type="search"]::-ms-input-placeholder {
  color: inherit;
  opacity: .6;
}

.search input[type="search"]::placeholder {
  color: inherit;
  opacity: .6;
}

.search input[type="search"], .search input[type="search"]::-webkit-search-decoration, .search input[type="search"]::-webkit-search-cancel-button, .search input[type="search"]::-webkit-search-results-button, .search input[type="search"]::-webkit-search-results-decoration {
  -webkit-appearance: none;
  -ms-appearance: none;
  appearance: none;
}

.search input[type="search"]:focus {
  background-color: rgb(var(--em-rgb-input));
  box-shadow: inset 0 0 0 1px rgb(var(--em-rgb-accent)), 0 1px 3px rgba(65, 69, 73, .2);
}

.search .icon {
  z-index: 1;
  color: rgba(var(--em-rgb-color), .7);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.search .loupe {
  pointer-events: none;
  left: .7em;
}

.search .delete {
  right: .7em;
}

svg {
  fill: currentColor;
  width: 1em;
  height: 1em;
}

button {
  -webkit-appearance: none;
  -ms-appearance: none;
  appearance: none;
  cursor: pointer;
  color: currentColor;
  background-color: rgba(0, 0, 0, 0);
  border: 0;
}

#nav {
  z-index: 2;
  padding-top: 12px;
  padding-bottom: 12px;
  padding-right: var(--sidebar-width);
  position: relative;
}

#nav button {
  color: var(--color-b);
  transition: color var(--duration) var(--easing);
}

#nav button:hover {
  color: var(--color-a);
}

#nav svg, #nav img {
  width: var(--category-icon-size);
  height: var(--category-icon-size);
}

#nav[dir="rtl"] .bar {
  left: auto;
  right: 0;
}

#nav .bar {
  width: 100%;
  height: 3px;
  background-color: rgb(var(--em-rgb-accent));
  transition: transform var(--duration) var(--easing);
  border-radius: 3px 3px 0 0;
  position: absolute;
  bottom: -12px;
  left: 0;
}

#nav button[aria-selected] {
  color: rgb(var(--em-rgb-accent));
}

#preview {
  z-index: 2;
  padding: calc(var(--padding)  + 4px) var(--padding);
  padding-right: var(--sidebar-width);
  position: relative;
}

#preview .preview-placeholder {
  font-size: var(--preview-placeholder-size);
}

#preview .preview-title {
  font-size: var(--preview-title-size);
}

#preview .preview-subtitle {
  font-size: var(--preview-subtitle-size);
}

#nav:before, #preview:before {
  content: "";
  height: 2px;
  position: absolute;
  left: 0;
  right: 0;
}

#nav[data-position="top"]:before, #preview[data-position="top"]:before {
  background: linear-gradient(to bottom, var(--em-color-border), transparent);
  top: 100%;
}

#nav[data-position="bottom"]:before, #preview[data-position="bottom"]:before {
  background: linear-gradient(to top, var(--em-color-border), transparent);
  bottom: 100%;
}

.category:last-child {
  min-height: calc(100% + 1px);
}

.category button {
  font-family: -apple-system, BlinkMacSystemFont, Helvetica Neue, sans-serif;
  position: relative;
}

.category button > * {
  position: relative;
}

.category button .background {
  opacity: 0;
  background-color: var(--em-color-border);
  transition: opacity var(--duration-fast) var(--easing) var(--duration-instant);
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.category button:hover .background {
  transition-duration: var(--duration-instant);
  transition-delay: 0s;
}

.category button[aria-selected] .background {
  opacity: 1;
}

.category button[data-keyboard] .background {
  transition: none;
}

.row {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.skin-tone-button {
  border: 1px solid rgba(0, 0, 0, 0);
  border-radius: 100%;
}

.skin-tone-button:hover {
  border-color: var(--em-color-border);
}

.skin-tone-button:active .skin-tone {
  transform: scale(.85) !important;
}

.skin-tone-button .skin-tone {
  transition: transform var(--duration) var(--easing);
}

.skin-tone-button[aria-selected] {
  background-color: var(--em-color-border);
  border-top-color: rgba(0, 0, 0, .05);
  border-bottom-color: rgba(0, 0, 0, 0);
  border-left-width: 0;
  border-right-width: 0;
}

.skin-tone-button[aria-selected] .skin-tone {
  transform: scale(.9);
}

.menu {
  z-index: 2;
  white-space: nowrap;
  border: 1px solid var(--em-color-border);
  background-color: rgba(var(--em-rgb-background), .9);
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  transition-property: opacity, transform;
  transition-duration: var(--duration);
  transition-timing-function: var(--easing);
  border-radius: 10px;
  padding: 4px;
  position: absolute;
  box-shadow: 1px 1px 5px rgba(0, 0, 0, .05);
}

.menu.hidden {
  opacity: 0;
}

.menu[data-position="bottom"] {
  transform-origin: 100% 100%;
}

.menu[data-position="bottom"].hidden {
  transform: scale(.9)rotate(-3deg)translateY(5%);
}

.menu[data-position="top"] {
  transform-origin: 100% 0;
}

.menu[data-position="top"].hidden {
  transform: scale(.9)rotate(3deg)translateY(-5%);
}

.menu input[type="radio"] {
  clip: rect(0 0 0 0);
  width: 1px;
  height: 1px;
  border: 0;
  margin: 0;
  padding: 0;
  position: absolute;
  overflow: hidden;
}

.menu input[type="radio"]:checked + .option {
  box-shadow: 0 0 0 2px rgb(var(--em-rgb-accent));
}

.option {
  width: 100%;
  border-radius: 6px;
  padding: 4px 6px;
}

.option:hover {
  color: #fff;
  background-color: rgb(var(--em-rgb-accent));
}

.skin-tone {
  width: 16px;
  height: 16px;
  border-radius: 100%;
  display: inline-block;
  position: relative;
  overflow: hidden;
}

.skin-tone:after {
  content: "";
  mix-blend-mode: overlay;
  background: linear-gradient(rgba(255, 255, 255, .2), rgba(0, 0, 0, 0));
  border: 1px solid rgba(0, 0, 0, .8);
  border-radius: 100%;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  box-shadow: inset 0 -2px 3px #000, inset 0 1px 2px #fff;
}

.skin-tone-1 {
  background-color: #ffc93a;
}

.skin-tone-2 {
  background-color: #ffdab7;
}

.skin-tone-3 {
  background-color: #e7b98f;
}

.skin-tone-4 {
  background-color: #c88c61;
}

.skin-tone-5 {
  background-color: #a46134;
}

.skin-tone-6 {
  background-color: #5d4437;
}

[data-index] {
  justify-content: space-between;
}

[data-emoji-set="twitter"] .skin-tone:after {
  box-shadow: none;
  border-color: rgba(0, 0, 0, .5);
}

[data-emoji-set="twitter"] .skin-tone-1 {
  background-color: #fade72;
}

[data-emoji-set="twitter"] .skin-tone-2 {
  background-color: #f3dfd0;
}

[data-emoji-set="twitter"] .skin-tone-3 {
  background-color: #eed3a8;
}

[data-emoji-set="twitter"] .skin-tone-4 {
  background-color: #cfad8d;
}

[data-emoji-set="twitter"] .skin-tone-5 {
  background-color: #a8805d;
}

[data-emoji-set="twitter"] .skin-tone-6 {
  background-color: #765542;
}

[data-emoji-set="google"] .skin-tone:after {
  box-shadow: inset 0 0 2px 2px rgba(0, 0, 0, .4);
}

[data-emoji-set="google"] .skin-tone-1 {
  background-color: #f5c748;
}

[data-emoji-set="google"] .skin-tone-2 {
  background-color: #f1d5aa;
}

[data-emoji-set="google"] .skin-tone-3 {
  background-color: #d4b48d;
}

[data-emoji-set="google"] .skin-tone-4 {
  background-color: #aa876b;
}

[data-emoji-set="google"] .skin-tone-5 {
  background-color: #916544;
}

[data-emoji-set="google"] .skin-tone-6 {
  background-color: #61493f;
}

[data-emoji-set="facebook"] .skin-tone:after {
  border-color: rgba(0, 0, 0, .4);
  box-shadow: inset 0 -2px 3px #000, inset 0 1px 4px #fff;
}

[data-emoji-set="facebook"] .skin-tone-1 {
  background-color: #f5c748;
}

[data-emoji-set="facebook"] .skin-tone-2 {
  background-color: #f1d5aa;
}

[data-emoji-set="facebook"] .skin-tone-3 {
  background-color: #d4b48d;
}

[data-emoji-set="facebook"] .skin-tone-4 {
  background-color: #aa876b;
}

[data-emoji-set="facebook"] .skin-tone-5 {
  background-color: #916544;
}

[data-emoji-set="facebook"] .skin-tone-6 {
  background-color: #61493f;
}

`;function hr(e){const t=y.useRef(null),n=y.useRef(null);return n.current&&n.current.update(e),y.useEffect(()=>(n.current=new Ie({...e,ref:t}),()=>{n.current=null}),[]),E.createElement("div",{ref:t})}function pr(e){var t=e.theme,n=e.onSelectEmoji,r=e.disableRecent,o=e.customEmojis,i=e.language,a=y.useMemo(function(){var c=[];return r||c.push("frequent"),c=[].concat(vt(c),["people","nature","foods","activity","places","objects","symbols","flags"]),c},[r]),l=y.useMemo(function(){if(i)return require("@emoji-mart/data/i18n/".concat(i??"en",".json"))},[i]);return E.createElement(hr,{data:void 0,theme:t,previewPosition:"none",onEmojiSelect:n,custom:o,categories:a,set:"apple",i18n:l})}var mr=y.memo(pr);function mt(e){var t=e.showPicker,n=e.theme,r=e.handleSelectEmoji,o=e.disableRecent,i=e.customEmojis,a=e.position,l=e.language;return E.createElement("div",{className:"react-emoji-picker--container"},t&&E.createElement("div",{className:"react-emoji-picker--wrapper",onClick:function(u){return u.stopPropagation()},style:a==="below"?{top:"40px"}:{}},E.createElement("div",{className:"react-emoji-picker"},E.createElement(mr,{theme:n,onSelectEmoji:r,disableRecent:o,customEmojis:i,language:l}))))}var vr=435,gr=function(t){var n=t.theme,r=t.keepOpened,o=t.disableRecent,i=t.customEmojis,a=t.addSanitizeFn,l=t.addPolluteFn,c=t.appendContent,u=t.buttonElement,s=t.buttonRef,v=t.language,h=y.useState(!1),g=ee(h,2),f=g[0],_=g[1],w=y.useState(),S=ee(w,2),j=S[0],C=S[1],R=y.useState(),T=ee(R,2),z=T[0],W=T[1];y.useEffect(function(){a(me)},[a]),y.useEffect(function(){l(bt)},[l]),y.useEffect(function(){function D(P){var K=P.target;K.classList.contains("react-input-emoji--button")||K.classList.contains("react-input-emoji--button--icon")||_(!1)}return document.addEventListener("click",D),function(){document.removeEventListener("click",D)}},[]);function U(D){D.stopPropagation(),D.preventDefault(),W(te(D)),_(function(P){return!P})}function te(D){var P=D.currentTarget,K=P.getBoundingClientRect(),V=vr;return K.top>=V?"above":"below"}function ae(D){c(hn(D)),r||_(function(P){return!P})}return y.useEffect(function(){var D;s!=null&&(D=s.current)!==null&&D!==void 0&&D.style?(s.current.style.position="relative",C(s.current)):u!=null&&u.style&&(u.style.position="relative",C(u))},[s,u]),j?Jt.createPortal(E.createElement(E.Fragment,null,E.createElement(mt,{showPicker:f,theme:n,handleSelectEmoji:ae,disableRecent:o,customEmojis:i,position:z,language:v}),E.createElement(Ke,{showPicker:f,toggleShowPicker:U,buttonElement:j,buttonRef:s})),j):E.createElement(E.Fragment,null,E.createElement(mt,{showPicker:f,theme:n,handleSelectEmoji:ae,disableRecent:o,customEmojis:i,position:z,language:v}),E.createElement(Ke,{showPicker:f,toggleShowPicker:U}))};function br(){var e=qt();if(!e)return null;var t=e.text.substring(e.begin,e.end);return t||null}function _r(){var e=qt();e&&e.element.deleteData(e.begin,e.end-e.begin)}function qt(){var e=Oe();if(!e)return null;var t=e.element,n=e.caretOffset,r=t.textContent,o=r.lastIndexOf("@");return o===-1||o>=n||o!==0&&r[o-1]!==" "?null:{begin:o,end:n,text:r,element:t}}function Oe(){var e=yr();if(e===null)return null;var t=0;if(typeof window.getSelection<"u"){var n=window.getSelection().getRangeAt(0),r=n.cloneRange();r.selectNodeContents(e),r.setEnd(n.endContainer,n.endOffset),t=r.toString().length}else if(typeof document.selection<"u"&&document.selection.type!="Control"){var o=document.selection.createRange(),i=document.body.createTextRange();i.moveToElementText(e),i.setEndPoint("EndToEnd",o),t=i.text.length}return{element:e,caretOffset:t}}function yr(){var e=document.getSelection().anchorNode;return(e==null?void 0:e.nodeType)==3?e:null}function wr(e){var t,n=y.useState(!1),r=ee(n,2),o=r[0],i=r[1],a=y.useState([]),l=ee(a,2),c=l[0],u=l[1],s=y.useState(null),v=ee(s,2),h=v[0],g=v[1],f=y.useCallback(function(){_r(),u([])},[]),_=y.useCallback(Ue(de().mark(function j(){var C,R;return de().wrap(function(z){for(;;)switch(z.prev=z.next){case 0:if(C=br(),g(C),C!==null){z.next=6;break}u([]),z.next=12;break;case 6:return i(!0),z.next=9,e(C);case 9:R=z.sent,i(!1),u(R);case 12:case"end":return z.stop()}},j)})),[e]),w=y.useCallback(function(j){return(t=t||Ue(de().mark(function C(R){var T,z;return de().wrap(function(U){for(;;)switch(U.prev=U.next){case 0:if(typeof e=="function"){U.next=2;break}return U.abrupt("return");case 2:R.key==="Backspace"&&(T=Oe())!==null&&T!==void 0&&T.element.parentElement.hasAttribute("data-mention-id")?(z=Oe(),z.element.parentElement.remove()):["ArrowUp","ArrowDown","Esc","Escape"].includes(R.key)||_();case 3:case"end":return U.stop()}},C)}))).apply(this,arguments)},[_,e]),S=y.useCallback(function(){_()},[_]);return{mentionSearchText:h,mentionUsers:c,onKeyUp:w,onFocus:S,onSelectUser:f,loading:o}}var kr=function(t,n){var r=t.users,o=t.mentionSearchText,i=t.onSelect,a=t.addEventListener,l=y.useState(0),c=ee(l,2),u=c[0],s=c[1];y.useImperativeHandle(n,function(){return{prevUser:function(){s(function(_){return _===0?0:_-1})},nextUser:function(){s(function(_){return _===r.length-1?r.length-1:_+1})}}}),y.useEffect(function(){s(0)},[r]);function v(f,_){return'<span class="react-input-emoji--mention--item--name__selected" data-testid="metion-selected-word">'.concat(f,"</span>").concat(_)}var h=y.useMemo(function(){var f=o?o.substring(1).toLocaleLowerCase():"";return r.map(function(_){var w=_.name;if(o&&o.length>1)if(_.name.toLowerCase().startsWith(f))w=v(_.name.substring(0,f.length),_.name.substring(f.length));else{var S=_.name.split(" ");w=S.map(function(j){return j.toLocaleLowerCase().startsWith(f)?v(j.substring(0,f.length),j.substring(f.length)):j}).join(" ")}return Fe(Fe({},_),{},{nameHtml:w})})},[o,r]);function g(f){return function(_){_.stopPropagation(),_.preventDefault(),i(f)}}return y.useEffect(function(){var f=a("enter",function(_){_.stopPropagation(),_.preventDefault(),i(h[u])});return function(){f()}},[a,i,u,h]),E.createElement("ul",{className:"react-input-emoji--mention--list","data-testid":"mention-user-list"},h.map(function(f,_){return E.createElement("li",{key:f.id},E.createElement("button",{type:"button",onClick:g(f),className:"react-input-emoji--mention--item".concat(u===_?" react-input-emoji--mention--item__selected":""),onMouseOver:function(){return s(_)}},E.createElement("img",{className:"react-input-emoji--mention--item--img",src:f.image}),E.createElement("div",{className:"react-input-emoji--mention--item--name",dangerouslySetInnerHTML:{__html:f.nameHtml}})))}))},xr=y.forwardRef(kr),$r=function(t){var n=t.searchMention,r=t.addEventListener,o=t.appendContent,i=t.addSanitizeFn,a=y.useRef(null),l=y.useState(!1),c=ee(l,2),u=c[0],s=c[1],v=wr(n),h=v.mentionSearchText,g=v.mentionUsers,f=v.loading,_=v.onKeyUp,w=v.onFocus,S=v.onSelectUser;y.useEffect(function(){i(function(C){var R=document.createElement("div");R.innerHTML=C;var T=Array.prototype.slice.call(R.querySelectorAll(".react-input-emoji--mention--text"));return T.forEach(function(z){R.innerHTML=R.innerHTML.replace(z.outerHTML,"@[".concat(z.dataset.mentionName,"](userId:").concat(z.dataset.mentionId,")"))}),R.innerHTML})},[i]),y.useEffect(function(){s(g.length>0)},[g]),y.useEffect(function(){function C(){s(!1)}return document.addEventListener("click",C),function(){document.removeEventListener("click",C)}},[]),y.useEffect(function(){var C=r("keyUp",_);return function(){C()}},[r,_]),y.useEffect(function(){function C(T){switch(T.key){case"Esc":case"Escape":s(!1);break}}var R=r("keyDown",C);return function(){R()}},[r]),y.useEffect(function(){var C=r("focus",w);return function(){C()}},[r,w]),y.useEffect(function(){if(u){var C=r("arrowUp",function(T){T.stopPropagation(),T.preventDefault(),a.current.prevUser()}),R=r("arrowDown",function(T){T.stopPropagation(),T.preventDefault(),a.current.nextUser()});return function(){C(),R()}}},[r,u]);function j(C){S(),o('<span class="react-input-emoji--mention--text" data-mention-id="'.concat(C.id,'" data-mention-name="').concat(C.name,'">@').concat(C.name,"</span> "))}return E.createElement(E.Fragment,null,f?E.createElement("div",{className:"react-input-emoji--mention--container"},E.createElement("div",{className:"react-input-emoji--mention--loading"},E.createElement("div",{className:"react-input-emoji--mention--loading--spinner"},"Loading..."))):u&&E.createElement("div",{className:"react-input-emoji--mention--container",onClick:function(R){return R.stopPropagation()}},E.createElement(xr,{ref:a,mentionSearchText:h,users:g,onSelect:j,addEventListener:r})))};function oe(){var e=[];return{subscribe:function(n){return e.push(n),function(){e=e.filter(function(r){return r!==n})}},publish:function(n){e.forEach(function(r){return r(n)})},get currentListerners(){return e}}}function Cr(){var e=y.useMemo(function(){return{keyDown:oe(),keyUp:oe(),arrowUp:oe(),arrowDown:oe(),enter:oe(),focus:oe(),blur:oe()}},[]),t=y.useCallback(function(n,r){return e[n].subscribe(r)},[e]);return{addEventListener:t,listeners:e}}function Sr(){var e=y.useRef([]),t=y.useCallback(function(r){e.current.push(r)},[]),n=y.useCallback(function(r){var o=e.current.reduce(function(i,a){return a(i)},r);return o},[]);return{addPolluteFn:t,pollute:n}}function Er(e,t){var n=e.onChange,r=e.onEnter,o=e.shouldReturn,i=e.onResize,a=e.onClick,l=e.onFocus,c=e.onBlur,u=e.onKeyDown,s=e.theme,v=e.cleanOnEnter,h=e.placeholder,g=e.maxLength,f=e.keepOpened,_=e.inputClass,w=e.disableRecent,S=e.tabIndex,j=e.value,C=e.customEmojis,R=e.language,T=e.searchMention,z=e.buttonElement,W=e.buttonRef,U=e.borderRadius,te=e.borderColor,ae=e.fontSize,D=e.fontFamily,P=y.useRef(null),K=Cr(),V=K.addEventListener,N=K.listeners,m=wt(e.shouldReturn),p=m.addSanitizeFn,b=m.sanitize,$=m.sanitizedTextRef,k=Sr(),M=k.addPolluteFn,A=k.pollute,O=y.useCallback(function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";P.current!==null&&(P.current.html=bt(H),$.current=H)},[$]),I=y.useCallback(function(H){O(H)},[O]),q=xn(P,i,n);kn({ref:t,setValue:I,textInputRef:P,emitChange:q}),y.useEffect(function(){$.current!==j&&I(j)},[$,I,j]),y.useEffect(function(){function H(se){if(typeof g<"u"&&se.key!=="Backspace"&&P.current!==null&&We(P.current)>=g&&se.preventDefault(),se.key==="Enter"&&P.current){se.preventDefault();var Xt=b(P.current.html);return q($.current),typeof r=="function"&&N.enter.currentListerners.length===0&&r(Xt),v&&N.enter.currentListerners.length===0&&O(""),typeof u=="function"&&u(se.nativeEvent),!1}return typeof u=="function"&&u(se.nativeEvent),!0}var J=V("keyDown",H);return function(){J()}},[V,v,q,N.enter.currentListerners.length,g,r,u,b,$,O]),y.useEffect(function(){function H(){typeof a=="function"&&a(),typeof l=="function"&&l()}var J=V("focus",H);return function(){J()}},[V,a,l]),y.useEffect(function(){function H(){typeof c=="function"&&c()}var J=V("blur",H);return function(){J()}},[V,c]);function ie(H){b(H),j!==$.current&&q($.current)}function X(H){typeof g<"u"&&P.current!==null&&We(P.current)>=g||P.current!==null&&P.current.appendContent(H)}function Gt(H){H.preventDefault();var J;H.clipboardData&&(J=H.clipboardData.getData("text/plain"),J=A(J),document.execCommand("insertHTML",!1,J))}return E.createElement("div",{className:"react-emoji"},E.createElement($r,{searchMention:T,addEventListener:V,appendContent:X,addSanitizeFn:p}),E.createElement(Sn,{ref:P,onCopy:pn,onPaste:Gt,shouldReturn:o,onBlur:N.blur.publish,onFocus:N.focus.publish,onArrowUp:N.arrowUp.publish,onArrowDown:N.arrowDown.publish,onKeyUp:N.keyUp.publish,onKeyDown:N.keyDown.publish,onEnter:N.enter.publish,placeholder:h,style:{borderRadius:U,borderColor:te,fontSize:ae,fontFamily:D},tabIndex:S,className:_,onChange:ie}),E.createElement(gr,{theme:s,keepOpened:f,disableRecent:w,customEmojis:C,addSanitizeFn:p,addPolluteFn:M,appendContent:X,buttonElement:z,buttonRef:W,language:R}))}var jr=y.forwardRef(Er);jr.defaultProps={theme:"auto",height:30,placeholder:"Type a message",borderRadius:21,borderColor:"#EAEAEA",fontSize:15,fontFamily:"sans-serif",tabIndex:0,shouldReturn:!1,customEmojis:[],language:void 0};export{jr as I};
