import AdSense from "react-adsense";
import { AuthContext, tokenExpireError } from "Src/authContext";
import { GlobalContext, showToast } from "Src/globalContext";
import MkdSDK from "Utils/MkdSDK";
import React, { useEffect, useRef, useState } from "react";
import {
  Routes,
  Route,
  useParams,
  useLocation,
  useNavigate,
} from "react-router-dom";
import { CountdownCircleTimer } from "react-countdown-circle-timer";
import SkeletonLoading from "./SkeletonLoading";
import { CompanyLogoMid } from "Assets/svgs/CompanyLogoMid";
import { useIsEligible } from "Src/ageContext";
import DisplayAdsByAge from "./DisplayAdsByAge";
import PlayCircleIcon from "@mui/icons-material/PlayCircle";
import PauseCircleOutlineIcon from "@mui/icons-material/PauseCircleOutline";
import { useData } from "Src/dataContext";
// import homeVid from "Assets/vid/homeVid2.mp4";
let sdk = new MkdSDK();

const AdsVid = () => {
  const [message, setMessage] = useState("");
  const [verified, setVerified] = useState(false);
  const [videoDuration, setVideoDuration] = useState(30); // Default duration
  // const [cms, setCms] = useState();
  const [showButton, setShowButton] = useState(false);
  const { state, dispatch } = React.useContext(AuthContext);
  const { state: globalState, dispatch: globalDispatch } =
    React.useContext(GlobalContext);
  const { isEligible } = useIsEligible();
  const [playing, setPlaying] = useState(false);

  const videoRef = useRef(null);

  const location = useLocation();
  const prevRoute = location?.state?.from?.pathname;
  const navigate = useNavigate();
  const { cms } = useData();

  // to automatically redirect to homepage after 30s....
  useEffect(() => {
    if (videoDuration > 0) {
      // Ensure duration is set
      const timer = setTimeout(() => {
        setShowButton(true);
      }, videoDuration * 1000);
      return () => clearTimeout(timer);
    }
  }, [videoDuration]); // Runs only when videoDuration updates

  const handleVideoEnd = async () => {
    try {
      if (localStorage.getItem("watchVideo")) {
        const result = await sdk.getfreemessage();
        if (result.error) {
          showToast(globalDispatch, `${result.message}`, 4000, "error");
          result.message.includes("has no value")
            ? setMessage("try again")
            : setMessage(result.message);

          navigate(prevRoute ?? "/user/chat", { replace: true });
        } else {
          showToast(globalDispatch, "Free message received", 4000);
          setMessage("verified! click here to continue");
          setVerified(true);
          localStorage.getItem("newUser")
            ? navigate(prevRoute ?? "/user/chat", { replace: true })
            : // ? navigate(prevRoute ?? "/user/privacy", { replace: true })
              navigate(prevRoute ?? "/user/chat", { replace: true });
        }
        // if (result) {
        //   window.location.href = `${result.data.paymentUrl}`;
        // }
        // if successful, redirect to privacy or chat bot
        // if successful, display the continue text or show invalid coupon code text
      }
    } catch (err) {
      tokenExpireError(
        dispatch,
        err.response?.data?.message ? err.response?.data?.message : err.message
      );
    }
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        if (entry.isIntersecting) {
          videoRef.current?.play();
          setPlaying(true);
        } else {
          videoRef.current?.pause();
          setPlaying(false);
        }
      },
      { threshold: 0.1 } // Adjust threshold to control when the video should play (e.g., 0.5 means half the video should be in view)
    );

    if (videoRef.current) {
      observer.observe(videoRef.current);
    }

    return () => {
      if (videoRef.current) {
        observer.unobserve(videoRef.current);
      }
    };
  }, []);

  const togglePlay = () => {
    const newPlayingState = !playing;
    setPlaying(newPlayingState);
    if (newPlayingState) {
      videoRef.current?.play();
    } else {
      videoRef.current?.pause();
    }
  };


  return (
    <div className="flex flex-col justify-center items-center mx-auto  py-6 px-2 ">
      <div
        className="w-full "
        // ref={videoRef}
      >
        <div className=" w-full">
          <div className=" pb-4 md:flex md:flex-row flex flex-col items-center justify-between md:gap-8 gap-4">
            <div
              className=" "
              onClick={() => {
                navigate("/", { replace: true });
              }}
            >
              <CompanyLogoMid />
            </div>

            <div className=" flex-1">
              <h2 className=" font-bold md:text-center text-center md:text-xl text-base">
                Elevate Your Sports IQ with KaizenWin - Where Insights Rule!
              </h2>
            </div>
          </div>
          <div className="flex items-center justify-center pb-4 mt-4">
            <p className=" text-center max-w-4xl">
              KaizenWin is your go-to source for unadulterated sports
              statistics. We're not about gambling or guesswork – we're all
              about pure, data-backed insights that'll transform the way you
              view your favorite sports.
            </p>
          </div>

          <div className="flex items-end justify-center pb-4 w-full">
            <CountdownCircleTimer
              isPlaying
              // duration={30}
              duration={videoDuration + 2}
              colors="#f93434"
              colorsTime={[7, 5, 2, 0]}
              size={30}
              strokeWidth={1}
            >
              {({ remainingTime }) => remainingTime}
            </CountdownCircleTimer>
          </div>

          {/* .........banner or space for ads........ */}

          <div className=" flex justify-between items-center md:flex-nowrap flex-wrap">
            {[1, 2, 3]?.map((val, idx) => (
              <span key={idx} className="w-full">

                <DisplayAdsByAge />
              </span>
            ))}
          </div>

          {!cms && <SkeletonLoading padding="py-[10rem]" />}
          <div className=" group w-full my-4 flex justify-center h-full max-h-[700px]">
            <video
              ref={videoRef}
              src={`${
                cms?.find((item) => item.content_key === "Ads_video")
                  ?.content_value
              }`}
              autoPlay={false}
              onLoadedMetadata={() => {
                if (videoRef.current?.duration) {
                  setVideoDuration(videoRef.current.duration); // Update video duration dynamically
                }
              }}
              onEnded={() => {
                videoRef.current.pause(); // Ensures the video stops after playing once
              }}
            />

            <div className="group-hover:opacity-100 absolute inset-0 rounded-full opacity-0 transition-opacity duration-300 flex items-center justify-center"></div>
          </div>

          <div className="flex items-center justify-center pb-4">
            <p className=" text-center max-w-4xl">
              {
                cms?.find((item) => item.content_key === "Ads_text")
                  ?.content_value
              }
            </p>
          </div>

          <div className=" flex justify-between items-center md:flex-nowrap flex-wrap">
            {[1, 2, 3]?.map((val, idx) => (
              <span key={idx} className="w-full">

                <DisplayAdsByAge />
              </span>
            ))}
          </div>

          <div className=" flex items-center justify-center">
            {/* ....show continue button after 30s..... */}
            {/* {showButton && (
              <button className=" border px-2 rounded-full bg-companyRed text-white" onClick={handleVideoEnd}>
                Continue to Chat
              </button>
            )} */}

            <button
              disabled={!showButton}
              className={`border px-2 rounded-full mt-4 ${
                !showButton ? "bg-[#c3bebf]" : "bg-companyRed"
              }  text-white`}
              onClick={handleVideoEnd}
            >
              Click to continue
            </button>
            {showButton}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdsVid;
