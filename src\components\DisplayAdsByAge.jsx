import { height } from "@mui/system";
import { useIsEligible } from "Src/ageContext";
import React from "react";
import AdSense from "react-adsense";

const DisplayAdsByAge = () => {
  const { isEligible } = useIsEligible();
  return (
    <>
      {isEligible ? (
        <div
          id="ad-above18"
          className="flex items-center justify-center w-full"
        >
          <AdSense.Google
            client={import.meta.env.VITE_GOOGLE_ADS_API_KEY}
            slot="9974196971"
            style={{ display: "block", minWidth: 250, height:"fit-content" }}
            layout="in-article"
            format="fluid"
          />
        </div>
      ) : (
        <div
          id="ad-under18"
          className="flex items-center justify-center w-full"
        >
          <AdSense.Google
            client={import.meta.env.VITE_GOOGLE_ADS_API_KEY}
            slot="9974196971"
            style={{ display: "block", minWidth: 250,height:"fit-content" }}
            layout="in-article"
            format="fluid"
          />
        </div>
      )}
    </>
  );
};

export default DisplayAdsByAge;
