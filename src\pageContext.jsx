import React, { createContext, useReducer, useContext, useEffect } from "react";
import { AuthContext, tokenExpireError } from "./authContext";
import MkdSDK from "Utils/MkdSDK";

let sdk = new MkdSDK();

// Create a Context for the page layout
const PageContext = createContext();

// Define the reducer to handle state changes
const pageReducer = (state, action) => {
  switch (action.type) {
    case "ADD_ELEMENT":
      return [...state, action.payload];
    case "REMOVE_ELEMENT":
      return state.filter((item) => item.id !== action.payload);
    case "UPDATE_ELEMENT_POSITION":
      const { fromIndex, toIndex } = action.payload;
      const updatedState = [...state];
      const [movedElement] = updatedState.splice(fromIndex, 1);
      updatedState.splice(toIndex, 0, movedElement);
      return updatedState;
    case "SET_PAGE_LAYOUT":
      return action.payload;
    default:
      return state;
  }
};

// Context Provider component
export const PageProvider = ({ children }) => {
  const { state, dispatch: authDispatch } = React.useContext(AuthContext);
  const [pageLayout, dispatch] = useReducer(pageReducer, []);

  useEffect(() => {
    async function getData() {
      try {
        sdk.setTable("collaboration");

        const result = await sdk.callRestAPI({}, "GETALL");
        dispatch({
          type: "SET_PAGE_LAYOUT",
          payload: result?.list,
        });

        const { list, total, limit, num_pages, page } = result;
      } catch (error) {
        console.log("ERROR", error);
        tokenExpireError(authDispatch, error.message);
      }
    }
    if(state?.user){

      getData();

    }

  }, [state?.user]);

  return (
    <PageContext.Provider value={{ pageLayout, dispatch }}>
      {children}
    </PageContext.Provider>
  );
};

// Custom hook to use the PageContext
export const usePageContext = () => useContext(PageContext);
