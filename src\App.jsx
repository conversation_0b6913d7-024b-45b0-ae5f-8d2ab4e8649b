import React from "react";
import AuthProvider from "./authContext";
import GlobalProvider from "./globalContext";
import Main from "./main";
import "@uppy/core/dist/style.css";
import "@uppy/dashboard/dist/style.css";
import { BrowserRouter as Router } from "react-router-dom";
import "react-loading-skeleton/dist/skeleton.css";
import { loadStripe } from "@stripe/stripe-js";
import { Elements } from "@stripe/react-stripe-js";
import ScrollToTop from "Components/ScrollToTop";
import { SkeletonTheme } from "react-loading-skeleton";
import { NextUIProvider } from "@nextui-org/react";
import usePageViews from "Components/Hooks/usePageViews";
import DataProvider from "./dataContext";
import AgeProvider from "./ageContext";
import DynamicHeadLinks from "./DynamicHeadLinks";
import DynamicSEO from "./DynamicSEO";
import { PageProvider } from "./pageContext";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";

const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_API_KEY);

function App() {
  // usePageViews();

  return (
    <SkeletonTheme baseColor="#313131" highlightColor="#525252">
      <AuthProvider>
        <GlobalProvider>
          <AgeProvider>
            <PageProvider>
              <DataProvider>
                <NextUIProvider>
                  <DndProvider backend={HTML5Backend}>
                    <Router>
                      <ScrollToTop></ScrollToTop>
                      <DynamicHeadLinks />
                      <DynamicSEO />
                      {/* <Elements stripe={stripePromise}> */}
                      <Main />
                      {/* </Elements> */}
                    </Router>
                  </DndProvider>
                </NextUIProvider>
              </DataProvider>
            </PageProvider>
          </AgeProvider>
        </GlobalProvider>
      </AuthProvider>
    </SkeletonTheme>
  );
}

export default App;
