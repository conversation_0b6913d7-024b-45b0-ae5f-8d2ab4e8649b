import moment from 'moment';

export function formatTimestamp(timestamp) {
    const now = moment();
    const messageTime = moment(timestamp);
    const diffInMinutes = now.diff(messageTime, 'minutes');
    
    
    if (diffInMinutes < 1) {
      return 'just now';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInMinutes < 1440) { // 1440 minutes in a day
      return `${moment().startOf('day').fromNow()}`; // Displays like '5 hours ago'
    } else {
      return messageTime.format('MMM D, YYYY'); // Format for older dates
    }
  }
  
 
  