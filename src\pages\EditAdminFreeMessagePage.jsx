import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "../utils/MkdSDK";
import { useNavigate, useParams } from "react-router-dom";
import { AuthContext, tokenExpireError } from "../authContext";
import { GlobalContext, showToast } from "../globalContext";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { isImage, empty, isVideo, isPdf } from "../utils/utils";
import { useEffect } from "react";
import { InteractiveButton } from "Components/InteractiveButton";

let sdk = new MkdSDK();
const EditAdminFreeMessagePage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [isloading, setIsloading] = useState(false);
  const schema = yup
    .object({
      create_at: yup.string(),
      update_at: yup.string(),
      email: yup.string(),
    })
    .required();
  const params = useParams();

  const selectType = [
    { key: "", value: "Basic" },
    { key: "text", value: "Premium" },
    { key: "image", value: "Deluxe" },
    { key: "number", value: "Number" },
  ];
  const status = [
    { key: "0", value: "Active" },
    { key: "1", value: "Expired" },
  ];

  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  useEffect(function () {
    (async function () {
      try {
        sdk.setTable("setting");
        const result = await sdk.callRestAPI({ id: 1 }, "GET");
        if (!result.error) {
          setValue("free_message_count", result.model.free_message_count);
          // setValue("plan", result.model.plan_id);
          // setValue("status", result.model.expired);
        }
      } catch (error) {
        console.log("error", error);
        tokenExpireError(dispatch, error.message);
      }
    })();
  }, []);

  const handlegenerate = async (_data) => {
    let sdk = new MkdSDK();
    setIsloading(true);

    try {
      sdk.setTable("setting");

      const result = await sdk.callRestAPI(
        {
          id: 1,
          free_message_count: parseInt(_data.free_message_count),
        },
        "PUT"
      );
      // console.log("result", result);
      if (!result.error) {
        showToast(globalDispatch, "Successfully Updated");
        // navigate("/admin/rewards");
        setIsloading(false);
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
          setIsloading(false);
        }
      }
    } catch (error) {
      console.log("Error", error);
      setError("name", {
        type: "manual",
        message: error.message,
      });
      tokenExpireError(dispatch, error.message);
      setIsloading(false);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "free-message",
      },
    });
  }, []);

  return (
    <div>
      {/* Generate Coupon */}
      <div className=" shadow-md rounded  mx-auto p-5 mt-6">
        <h4 className="text-2xl font-medium">Edit Free Message Count</h4>
        <form
          className=" w-full max-w-lg"
          onSubmit={handleSubmit(handlegenerate)}
        >
          <div className="mb-4  ">
            <label
              className="block text-gray-700 text-sm font-bold mb-2"
              htmlFor="update_at"
            >
              Free message count
            </label>
            <input
              type="number"
              placeholder="Free message count"
              {...register("free_message_count")}
              className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
                errors.free_message_count?.message ? "border-red-500" : ""
              }`}
            />
            <p className="text-red-500 text-xs italic">
              {errors.free_message_count?.message}
            </p>
          </div>

          {/* <div className="mb-4 w-full   ">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Plan
            </label>
            <select
              className="shadow border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"
              {...register("plan")}
            >
              {plans?.map((option) => (
                <option
                  name="plan"
                  value={option.id}
                  key={option.id}
                  defaultValue=""
                >
                  {option.name}
                </option>
              ))}
            </select>
            <p className="text-red-500 text-xs italic"></p>
          </div>

          <div className="mb-4 w-full   ">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Status
            </label>
            <select
              className="shadow border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"
              {...register("status")}
            >
              <option name="status" defaultValue=""></option>

              {status?.map((option) => (
                <option
                  name="status"
                  value={option.key}
                  key={option.key}
                  defaultValue=""
                >
                  {option.value}
                </option>
              ))}
            </select>
            <p className="text-red-500 text-xs italic"></p>
          </div> */}

          {/* <input
            type="email"
            placeholder="winner email"
            {...register("email")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
              errors.email?.message ? "border-red-500" : ""
            }`}
          /> */}
          {/* <p
            className={`shadow appearance-none border rounded w-full py-4 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline mb-4`}
            placeholder="coupon code"
          >
            {" "}
          </p> */}

          <InteractiveButton
            loading={isloading}
            type="submit"
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
          >
            Update
          </InteractiveButton>
        </form>
      </div>
    </div>
  );
};

export default EditAdminFreeMessagePage;
