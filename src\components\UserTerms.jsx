import React from "react";
import { useState } from "react";
import { useLocation, useNavigate } from "react-router";
import SkeletonLoading from "./SkeletonLoading";
import { AuthContext, tokenExpireError } from "Src/authContext";
import { GlobalContext } from "Src/globalContext";
import MkdSDK from "Utils/MkdSDK";

let sdk = new MkdSDK();
const UserTerms = () => {
  const [isOpen, setIsOpen] = useState(true);
  const [accepted, setAccepted] = useState(false);
  const [rejected, setRejected] = useState(false);
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [cms, setCms] = useState();

  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const redirect_uri = searchParams.get("redirect_uri");
  const prevRoute = location?.state?.from?.pathname;

  const navigate = useNavigate();

  let from = location.state?.from?.pathname;
  // console.log("from", from);
  const handleAccept = () => {
    setAccepted(true);
    if (from == "/user/login") {
      navigate(redirect_uri ?? "/user/chat", { replace: true });
      localStorage.setItem("terms", true);
    } else if (from == "/user/sign-up") {
      navigate(redirect_uri ?? "/", { replace: true });
      localStorage.setItem("terms", true);
    } else if (from == "/user/confirm-signup") {
      navigate("/", { replace: true });
      localStorage.setItem("terms", true);
    } else {
      navigate(prevRoute ?? "/user/buy", { replace: true });
      localStorage.setItem("terms", true);
    }
  };

  const handleReject = () => {
    setRejected(true);
    navigate(redirect_uri ?? "/", { replace: true });
  };
  React.useEffect(() => {
    async function getallcms() {
      try {
        const result = await sdk.getallcms();
        if (result) {
          setCms(result?.list);
        }
      } catch (err) {
        console.log("Error:", err);
        tokenExpireError(dispatch, err.message);
      }
    }
    getallcms();
  }, []);

  let textLists;
  cms &&
    (textLists = JSON.parse(
      cms?.find((item) => item.content_key === "Terms_texts")?.content_value
    ));

  let termsSliceIndex = 6;

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white md:p-8 p-4 rounded shadow-lg max-w-5xl w-full">
        <h1 className="md:text-4xl text-2xl font-semibold mb-6">
          {!cms && <SkeletonLoading />}
          {
            cms?.find((item) => item.content_key === "Terms_header_text")
              ?.content_value
          }
        </h1>
        <div className="mb-4 md:text-justify text-justify">
          {!cms &&
            Array(9)
              .fill(0)
              .map((_, i) => (
                <div key={i} className="mb-4">
                  <h2 className="text-lg font-semibold w-1/2">
                    <SkeletonLoading counter={1} />
                  </h2>
                  <p className="text-gray-400 text-sm font-normal">
                    <SkeletonLoading counter={2} />
                  </p>
                </div>
              ))}

          {cms
            ? textLists.slice(0, termsSliceIndex).map((content, i) => {
                return (
                  <>
                    {/* <div key={i} className="md:text-base text-[15px] mb-8"> */}
                    <h1 className="md:text-2xl text-lg font-semibold mb-2">
                      {content.key}
                    </h1>
                    <p className="mb-4">
                      {content.value}
                      {/* textLists?.length === i + 1 ? ( */}
                      {termsSliceIndex === i + 1 ? (
                        <>
                          <a
                            href="/user/termsofuse"
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            ...Read full terms and conditions
                          </a>
                          {/* <a
                            className=" "
                            href={
                              cms?.find(
                                (item) => item.content_key === "Terms_document"
                              )?.content_value
                            }
                            download={true}
                          >
                            view terms
                          </a>{" "} */}
                        </>
                      ) : null}
                    </p>
                    {/* </div> */}
                  </>
                );
              })
            : null}
        </div>

        {/* Add your terms and conditions text here */}

        <div className="flex justify-between mt-6">
          <button
            onClick={handleAccept}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded focus:outline-none text-sm"
          >
            Accept and Continue
          </button>
          <button
            onClick={handleReject}
            className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded focus:outline-none text-sm"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

export default UserTerms;
