import React, { useContext, useEffect, useMemo, useState } from "react";
import { useNavigate } from "react-router";
import { useParams, useLocation } from "react-router-dom";
import {
  useStripe,
  PaymentElement,
  useElements,
  Elements,
  ExpressCheckoutElement,
} from "@stripe/react-stripe-js";
import MkdSDK from "Utils/MkdSDK";
import { InteractiveButton } from "./InteractiveButton";
import { GlobalContext, showToast } from "Src/globalContext";
import { AuthContext } from "Src/authContext";

const Checkout = ({ clientSecret, paymentIntent }) => {
  const { state, dispatch } = React.useContext(AuthContext);

  const navigate = useNavigate();
  const stripe = useStripe();
  const elements = useElements();

  // const urlParams = new URLSearchParams(window.location.search);
  // const id = urlParams.get("id");
  const { id, name, amount } = useParams();
  // console.log("id", id, name, amount);
  // const  data = useParams();

  const qty = atob(localStorage.getItem("qty"));
  const price = atob(localStorage.getItem("price"));
  // const event_id = urlParams.get("ev");
  const location = useLocation();
  const prevRoute = location?.state?.from?.pathname;

  const [loading, setLoading] = useState(false);

  const [event, setEvent] = React.useState(null);

  const { state: globalState, dispatch: globalDispatch } =
    React.useContext(GlobalContext);

  let sdk = new MkdSDK();

  // const paymentElementOptions = {
  //   layout: {
  //     type: 'accordion',
  //     defaultCollapsed: false,
  //     radios: true,
  //     spacedAccordionItems: false
  //   }
  // };

  const paymentElementOptions = {
    layout: "tabs",
    defaultCollapsed: false,
  };

  const processPayment = async (e) => {
    e.preventDefault();

    if (!stripe || !elements) {
      return;
    }
    setLoading(true);
    const { error, paymentIntent } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        // Make sure to change this to your payment completion page
        // `http://localhost:3000/user/thank-you`
        // `https://kaizenwin.com/user/thank-you`,
        return_url: `https://kaizenwin.com/user/thank-you`,
      },
      redirect: "if_required",
    });

    if (!error) {
      setLoading(false);
      // dispatch({ type: "PAYMENTINTENTID", payload: paymentIntent?.id });
      let pay_id = paymentIntent?.id;
      // const confirm = await sdk.callRawAPI(
      //   "v3/api/custom/kaizenwin/membership/verify",
      //   { payment_ref: pay_id },
      //   "post"
      // );
      const confirm = await sdk.verifypayment(pay_id);

      if (confirm?.error) {
        showToast(globalDispatch, error?.message, 4000, "error");
        setLoading(false);

        return;
      } else if (!confirm.error) {
        showToast(globalDispatch, "Payment successful", 4000);
        localStorage.getItem("newUser")
          ? navigate(prevRoute ?? "/user/privacy")
          : navigate(prevRoute ?? "/user/chat");
      }
    }

    if (error?.type === "card_error" || error?.type === "validation_error") {
      showToast(globalDispatch, error.message, 2000, "error");
      setLoading(false);
    } else {
      // console.log("error", error);
      // work on this later
      // alert("An unexpected error occurred.");
    }
  };

  if (!stripe) {
    return;
  }

  if (!clientSecret) {
    return;
  }

  return (
    <>
      {/* bg-[#212D63] */}
      <form
        className="md:w-[600px] max-w-[60rem] md:p-10 md:mt-2 mt-10 mx-auto"
        onSubmit={processPayment}
        id="stripe-payment-form"
      >
        {/* <ExpressCheckoutElement onConfirm={processPayment} /> */}
        <PaymentElement id="payment-element" options={paymentElementOptions} />
        <InteractiveButton
          loading={loading}
          type="submit"
          className="bg-blue-600 hover:bg-blue-800 text-white w-full px-16 mt-16 py-2 rounded-[5px]"
        >
          Pay
        </InteractiveButton>
      </form>
    </>
  );
};

export default Checkout;
