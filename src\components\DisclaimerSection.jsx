import React from 'react'
import SkeletonLoading from './SkeletonLoading'
import { useData } from 'Src/dataContext';

const DisclaimerSection = () => {
    const { cms } = useData();
  return (
    <section className="flex flex-col justify-between items-center max-w-[1000px] md:w-[70vw] w-[90vw] mt-[100px] font-semibold">
        <h2 className="mb-[40px] md:mb-[50px] text-center md:text-5xl text-[32px] max-w-[822px] text-companyBlack">
          {!cms && <SkeletonLoading />}
          {
            cms?.find(
              (item) => item.content_key === "Disclaimer_Section_headings"
            )?.content_value
          }
        </h2>
        <p className="text-center font-normal md:text-companyBlack text-[#999999] md:text-opacity-60 text-[15px] md:text-[22px]">
          {!cms && <SkeletonLoading counter={2} />}
          {
            cms?.find(
              (item) => item.content_key === "Disclaimer_Section_sub_headings"
            )?.content_value
          }
        </p>
        <div className="my-[100px] md:text-left text-center ">
          {!cms &&
            Array(9)
              .fill(0)
              .map((_, i) => (
                <div key={i} className="mb-4">
                  <h2 className="text-lg font-semibold w-1/2">
                    <SkeletonLoading counter={1} />
                  </h2>
                  <p className="text-gray-400 text-sm font-normal">
                    <SkeletonLoading counter={2} />
                  </p>
                </div>
              ))}
          {cms
            ? JSON.parse(
                cms?.find(
                  (item) => item.content_key === "Disclaimer_Section_texts"
                )?.content_value
              ).map((content, i) => (
                <div key={i} className="mb-10">
                  <h2 className="text-[18px] md:text-[24px] font-semibold text-companyBlack mb-4">
                    {content.key}
                  </h2>
                  <p className="text-[#999999] md:text-[16px] text-[15px] font-normal">
                    {content.value}
                  </p>
                </div>
              ))
            : null}
        </div>
      </section>
  )
}

export default DisclaimerSection
