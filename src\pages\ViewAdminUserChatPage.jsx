import React from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "../utils/MkdSDK";
import { useNavigate, useParams } from "react-router-dom";
import { AuthContext, tokenExpireError } from "../authContext";
import { GlobalContext, showToast } from "../globalContext";
import { isImage, empty, isVideo } from "../utils/utils";

let sdk = new MkdSDK();

const ViewAdminUserChatPage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  const [viewModel, setViewModel] = React.useState({});

  const params = useParams();

  React.useEffect(function () {
    (async function () {
      try {
        sdk.setTable("chat");
        const result = await sdk.callRestAPI({ id: Number(params?.id) }, "GET");
        if (!result.error) {
          setViewModel(result.model);
        }
      } catch (error) {
        console.log("error", error);
        tokenExpireError(dispatch, error.message);
      }
    })();
  }, []);
  return (
    <div className=" shadow-md rounded  mx-auto p-5">
      <h4 className="text-2xl font-medium">View chat</h4>

      {/* <div className="my-4">
            <div className="flex mb-4">
                <div className="flex-1">Update At</div>
                <div className="flex-1">{viewModel?.update_at}</div>
            </div>
        </div> */}
      <div className="my-4">
        <div className="flex mb-4">
          {/* <div className="flex">Chat</div> */}
          <div className="flex-1 flex flex-col gap-4">

            {viewModel?.chat &&
              
                <div className=" flex gap-4">
                  <div className=" capitalize font-bold">message</div>
                  <div>{JSON.parse(viewModel?.chat).message}</div>
                </div>
              }


            {/* {viewModel?.chat &&
              Object.entries(JSON.parse(viewModel?.chat)).map(([key, val]) => (
                <div className=" flex gap-4">
                  <div className=" capitalize font-bold">{key}</div>
                  <div>{val}</div>
                </div>
              ))} */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewAdminUserChatPage;
