import{r as u}from"../vendor-b0222800.js";import{j as p,n as B,D as h}from"./base-0e613ae5.js";import{_ as C}from"../@emotion/react-32889a6e.js";import{_ as N}from"../@material-ui/core-b35124d7.js";import{c as _}from"./material-a827c5f8.js";import{e as g,f as j,a as P,g as b}from"./icons-material-14196d48.js";const y=["className","component"];function R(n={}){const{themeId:s,defaultTheme:m,defaultClassName:t="MuiBox-root",generateClassName:a}=n,x=g("div",{shouldForwardProp:e=>e!=="theme"&&e!=="sx"&&e!=="as"})(j);return u.forwardRef(function(c,l){const o=P(m),r=b(c),{className:i,component:d="div"}=r,f=N(r,y);return p.jsx(x,C({as:d,ref:l,className:_(i,a?a(t):t),theme:s&&o[s]||o},f))})}const S=B("MuiBox",["root"]),v=S,w=R({defaultClassName:v.root,generateClassName:h.generate}),I=w;export{I as B};
