import React, { useState } from "react";
import { AuthContext, tokenExpireError } from "../authContext";
import { NavLink } from "react-router-dom";
import { GlobalContext } from "../globalContext";
import aboutimg from "../assets/images/aboutimg.png";
import { useNavigate } from "react-router-dom";
import Navbar from "./NavBar";
import FooterComp from "./FooterComp";
import MkdSDK from "Utils/MkdSDK";
import SkeletonLoading from "./SkeletonLoading";

let sdk = new MkdSDK();
const PrivacyPolicy = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [cms, setCms] = useState();

  const navigate = useNavigate();

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "user",
      },
    });
  }, []);

  React.useEffect(() => {
    if (localStorage.getItem("newUser")){
      localStorage.removeItem("newUser")
    }
    async function getallcms() {
      try {
        const result = await sdk.getallcms();
        // console.log("wre", result);
        if (result) {
          setCms(result?.list);
        }
      } catch (err) {
        console.log("Error:", err);
        tokenExpireError(dispatch, err.message);
      }
    }
    getallcms();
  }, []);

  let textLists;
  cms &&
    (textLists = JSON.parse(
      cms?.find((item) => item.content_key === "Privacy_texts")?.content_value
    ));

  // console.log(textLists);

  return (
    <div className="relative ">
      <Navbar />
      <div className="h-[100vh] flex flex-col justify-between w-full items-center ">
        <div
          className="bg-gradient-to-r from-red-600 to-black text-white py-20 mx-8 px-4 sm:px-6 lg:px-8 text-center flex flex-col justify-between md:max-w-7xl  w-[95%]"
          style={{
            // backgroundImage: `url('/path/to/your/background-image.jpg')`,
            backgroundImage: `url(${aboutimg})`,
            backgroundSize: "cover",
            backgroundPosition: "center",
          }}
        >
          <div className="max-w-7xl mx-auto ">
            <h1 className="text-[24px] sm:text-6xl lg:text-[64px] font-semibold leading-tight mb-6">
              {!cms && <SkeletonLoading />}
              {
                cms?.find((item) => item.content_key === "Privacy_header_text")
                  ?.content_value
              }
            </h1>

            <button
              className="bg-companyRed hover:bg-red-600 text-white py-[24px] md:px-[48px] px-2 rounded-full md:text-xl text-base transition duration-300 ease-in-out inline-block"
              onClick={() => {
                navigate("/user/chat");
                localStorage.removeItem("newUser");
              }}
            >
              Go to chatbot
            </button>
          </div>
        </div>
        <div className=" md:text-left text-center flex flex-col justify-center items-center w-full px-4">
          <div className=" md:text-left text-center max-w-[1240px] w-full pt-8 px-2">
            {!cms &&
              Array(9)
                .fill(0)
                .map((_, i) => (
                  <div key={i} className="mb-4">
                    <h2 className="text-lg font-semibold w-1/2">
                      <SkeletonLoading counter={1} />
                    </h2>
                    <p className="text-gray-400 text-sm font-normal">
                      <SkeletonLoading counter={2} />
                    </p>
                  </div>
                ))}

            {cms
              ? textLists.map((content, i) => (
                  <div key={i} className="md:text-base text-[15px] mb-8">
                    <h2 className=" md:text-[24px] text-[18px] py-2 font-semibold">
                      {content.key}
                    </h2>
                    <p className=" md:text-justify text-center md:text-[16px] text-[15px]">
                      {content.value}

                      {textLists?.length === i + 1 ? (
                        <a href="mailto:<EMAIL>">
                          <EMAIL>
                        </a>
                      ) : null}
                    </p>
                  </div>
                ))
              : null}

            <div className="md:text-base text-[15px] mb-8">
              <h2 className=" md:text-[24px] text-[18px] py-2 font-normal"></h2>
              <p className=" md:text-justify text-center"></p>
            </div>
          </div>
        </div>

        <FooterComp />
      </div>
    </div>
  );
};

export default PrivacyPolicy;
