import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "../utils/MkdSDK";
import { GlobalContext, showToast } from "../globalContext";
import { useNavigate, useParams } from "react-router-dom";
import { AuthContext, tokenExpireError } from "../authContext";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { isImage, empty, isVideo, isPdf } from "../utils/utils";

let sdk = new MkdSDK();

const EditAdminLendersPage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const schema = yup
    .object({
    //   create_at: yup.string(),
    //   update_at: yup.string(),
      link: yup.string(),
    })
    .required();
  const [fileObj, setFileObj] = React.useState({});
  const navigate = useNavigate();

  const [create_at, setCreateAt] = useState("");
  const [update_at, setUpdateAt] = useState("");
  const [id, setId] = useState(0);
  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const params = useParams();

  useEffect(function () {
    (async function () {
      try {
        sdk.setTable("lenders");
        const result = await sdk.callRestAPI({ id: Number(params?.id) }, "GET");
        if (!result.error) {
        //   setValue("create_at", result.model.create_at);
        //   setValue("update_at", result.model.update_at);
          setValue("link", result.model.link);

        //   setCreateAt(result.model.create_at);
        //   setUpdateAt(result.model.update_at);
          setId(result.model.id);
        }
      } catch (error) {
        console.log("error", error);
        tokenExpireError(dispatch, error.message);
      }
    })();
  }, []);

  const previewImage = (field, target) => {
    let tempFileObj = fileObj;
    tempFileObj[field] = {
      file: target.files[0],
      tempURL: URL.createObjectURL(target.files[0]),
    };
    setFileObj({ ...tempFileObj });
  };

  const onSubmit = async (_data) => {
    try {
      sdk.setTable("lenders");
      for (let item in fileObj) {
        let formData = new FormData();
        formData.append("file", fileObj[item].file);
        let uploadResult = await sdk.uploadImage(formData);
        _data[item] = uploadResult.url;
      }
      const result = await sdk.callRestAPI(
        {
          id: id,

        //   create_at: _data.create_at,
        //   update_at: _data.update_at,
        link: _data.link,
        },
        "PUT"
      );

      if (!result.error) {
        showToast(globalDispatch, "Updated");
        navigate("/admin/lenders");
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error) {
      console.log("Error", error);
      setError("create_at", {
        type: "manual",
        message: error.message,
      });
    }
  };
  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "lenders",
      },
    });
  }, []);

  return (
    <div className=" shadow-md rounded   mx-auto p-5">
      <h4 className="text-2xl font-medium">Edit Lenders</h4>
      <form className=" w-full max-w-lg" onSubmit={handleSubmit(onSubmit)}>
        {/* <div className="mb-4  ">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="create_at"
          >
            Create At
          </label>
          <input
            type="date"
            placeholder="Create At"
            {...register("create_at")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
              errors.create_at?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-red-500 text-xs italic">
            {errors.create_at?.message}
          </p>
        </div> */}

        {/* <div className="mb-4  ">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="update_at"
          >
            Update At
          </label>
          <input
            type="datetime-local"
            placeholder="Update At"
            {...register("update_at")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
              errors.update_at?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-red-500 text-xs italic">
            {errors.update_at?.message}
          </p>
        </div> */}
        <div className="mb-4  ">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="update_at"
          >
           Link
          </label>
          <input
            type="text"
            placeholder="link"
            {...register("link")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
              errors.link?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-red-500 text-xs italic">
            {errors.link?.message}
          </p>
        </div>

        <button
          type="submit"
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Submit
        </button>
      </form>
    </div>
  );
};

export default EditAdminLendersPage;
