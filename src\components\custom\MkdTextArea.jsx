import * as React from "react";
import TextareaAutosize from '@mui/material/TextareaAutosize';
import { Box } from '@mui/system';
export default function MkdTextArea({ onKeyDown, onChange, value }) {

  const red = {
    100: "#DAECFF",
    200: "#b6daff",
    400: "#3399FF",
    500: "#007FFF",
    600: "#e02424",
    900: "#003A75",
  };


  return (
    <Box
      component={TextareaAutosize}
      maxRows={3}
      value={value}
        onChange={onChange}
        onKeyDown={onKeyDown}
      placeholder=""
      sx={{
        width: '100%',
        paddingX: '24px',
        fontSize: '16px',
        border: '1px solid red',
        borderRadius:"50px",
        lineHeight: '1.2',
        outline: 'none',
        '&:focus': {
          border: 'none',
          boxShadow: 'none',
          
        },
        '&:hover': {
          borderColor: 'red',
        },
        '&:active': {
          borderColor: 'red',
        },
      }}
    />
 
 
  );
}
