import{a as ue,r as fe}from"./vendor-b0222800.js";import{P as m}from"./@ckeditor/ckeditor5-react-48fc30c1.js";function u(e){console.warn("[react-ga]",e)}function _(e){"@babel/helpers - typeof";return _=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_(e)}var ce=["to","target"];function q(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function x(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?q(Object(r),!0).forEach(function(n){k(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):q(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function le(e,t){if(e==null)return{};var r=se(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function se(e,t){if(e==null)return{};var r={},n=Object.keys(e),a,i;for(i=0;i<n.length;i++)a=n[i],!(t.indexOf(a)>=0)&&(r[a]=e[a]);return r}function pe(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function I(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function ge(e,t,r){return t&&I(e.prototype,t),r&&I(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function ye(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&E(e,t)}function E(e,t){return E=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,a){return n.__proto__=a,n},E(e,t)}function de(e){var t=me();return function(){var n=w(e),a;if(t){var i=w(this).constructor;a=Reflect.construct(n,arguments,i)}else a=n.apply(this,arguments);return be(this,a)}}function be(e,t){if(t&&(_(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return J(e)}function J(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function me(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function w(e){return w=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},w(e)}function k(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var $="_blank",ve=1,b=function(e){ye(r,e);var t=de(r);function r(){var n;pe(this,r);for(var a=arguments.length,i=new Array(a),c=0;c<a;c++)i[c]=arguments[c];return n=t.call.apply(t,[this].concat(i)),k(J(n),"handleClick",function(f){var o=n.props,p=o.target,g=o.eventLabel,ae=o.to,L=o.onClick,C=o.trackerNames,N={label:g},ie=p!==$,oe=!(f.ctrlKey||f.shiftKey||f.metaKey||f.button===ve);ie&&oe?(f.preventDefault(),r.trackLink(N,function(){window.location.href=ae},C)):r.trackLink(N,function(){},C),L&&L(f)}),n}return ge(r,[{key:"render",value:function(){var a=this.props,i=a.to,c=a.target,f=le(a,ce),o=x(x({},f),{},{target:c,href:i,onClick:this.handleClick});return c===$&&(o.rel="".concat(o.rel?o.rel:""," noopener noreferrer").trim()),delete o.eventLabel,delete o.trackerNames,ue.createElement("a",o)}}]),r}(fe.Component);k(b,"trackLink",function(){u("ga tracking not enabled")});b.propTypes={eventLabel:m.string.isRequired,target:m.string,to:m.string,onClick:m.func,trackerNames:m.arrayOf(m.string)};b.defaultProps={target:null,to:null,onClick:null,trackerNames:null};function he(e){return typeof e=="string"&&e.indexOf("@")!==-1}var Oe="REDACTED (Potential Email Address)";function we(e){return he(e)?(u("This arg looks like an email address, redacting."),Oe):e}function j(e){return e&&e.toString().replace(/^\s+|\s+$/g,"")}var Pe=/^(a|an|and|as|at|but|by|en|for|if|in|nor|of|on|or|per|the|to|vs?\.?|via)$/i;function je(e){return j(e).replace(/[A-Za-z0-9\u00C0-\u00FF]+[^\s-]*/g,function(t,r,n){return r>0&&r+t.length!==n.length&&t.search(Pe)>-1&&n.charAt(r-2)!==":"&&(n.charAt(r+t.length)!=="-"||n.charAt(r-1)==="-")&&n.charAt(r-1).search(/[^\s-]/)<0?t.toLowerCase():t.substr(1).search(/[A-Z]|\../)>-1?t:t.charAt(0).toUpperCase()+t.substr(1)})}function Ae(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,n=e||"";return t&&(n=je(e)),r&&(n=we(n)),n}function Se(e){return e.substring(0,1)==="/"?e.substring(1):e}var R=!1;function _e(e){if(!R){R=!0;var t="https://www.google-analytics.com/analytics.js";e&&e.gaAddress?t=e.gaAddress:e&&e.debug&&(t="https://www.google-analytics.com/analytics_debug.js");var r=e&&e.onerror;(function(n,a,i,c,f,o,p){n.GoogleAnalyticsObject=f,n[f]=n[f]||function(){(n[f].q=n[f].q||[]).push(arguments)},n[f].l=1*new Date,o=a.createElement(i),p=a.getElementsByTagName(i)[0],o.async=1,o.src=c,o.onerror=r,p.parentNode.insertBefore(o,p)})(window,document,"script",t,"ga")}}function l(e){console.info("[react-ga]",e)}var S=[];const P={calls:S,ga:function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];S.push([].concat(r))},resetCalls:function(){S.length=0}};var Ee=["category","action","label","value","nonInteraction","transport"];function Te(e,t){if(e==null)return{};var r=ke(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function ke(e,t){if(e==null)return{};var r={},n=Object.keys(e),a,i;for(i=0;i<n.length;i++)a=n[i],!(t.indexOf(a)>=0)&&(r[a]=e[a]);return r}function z(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function De(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?z(Object(r),!0).forEach(function(n){Le(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):z(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Le(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function O(e){"@babel/helpers - typeof";return O=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},O(e)}function Ce(e){return Ie(e)||xe(e)||qe(e)||Ne()}function Ne(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function qe(e,t){if(e){if(typeof e=="string")return T(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return T(e,t)}}function xe(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Ie(e){if(Array.isArray(e))return T(e)}function T(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var K=typeof window>"u"||typeof document>"u",y=!1,W=!0,F=!1,V=!0,U=!0,v=function(){var t;return F?P.ga.apply(P,arguments):K?!1:window.ga?(t=window).ga.apply(t,arguments):u("ReactGA.initialize must be called first or GoogleAnalytics should be loaded manually")};function d(e){return Ae(e,W,U)}function A(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];var a=r[0];if(typeof v=="function"){if(typeof a!="string"){u("ga command must be a string");return}(V||!Array.isArray(e))&&v.apply(void 0,r),Array.isArray(e)&&e.forEach(function(i){v.apply(void 0,Ce(["".concat(i,".").concat(a)].concat(r.slice(1))))})}}function G(e,t){if(!e){u("gaTrackingID is required in initialize()");return}t&&(t.debug&&t.debug===!0&&(y=!0),t.titleCase===!1&&(W=!1),t.redactEmail===!1&&(U=!1),t.useExistingGa)||(t&&t.gaOptions?v("create",e,t.gaOptions):v("create",e,"auto"))}function Z(e,t){return Array.isArray(e)?e.forEach(function(r){if(O(r)!=="object"){u("All configs must be an object");return}G(r.trackingId,r)}):G(e,t),!0}function H(e,t){if(t&&t.testMode===!0)F=!0;else{if(K)return;(!t||t.standardImplementation!==!0)&&_e(t)}V=t&&typeof t.alwaysSendToDefaultTracker=="boolean"?t.alwaysSendToDefaultTracker:!0,Z(e,t)}function s(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.length>0&&(v.apply(void 0,t),y&&(l("called ga('arguments');"),l("with arguments: ".concat(JSON.stringify(t))))),window.ga}function Q(e,t){if(!e){u("`fieldsObject` is required in .set()");return}if(O(e)!=="object"){u("Expected `fieldsObject` arg to be an Object");return}Object.keys(e).length===0&&u("empty `fieldsObject` given to .set()"),A(t,"set",e),y&&(l("called ga('set', fieldsObject);"),l("with fieldsObject: ".concat(JSON.stringify(e))))}function h(e,t){A(t,"send",e),y&&(l("called ga('send', fieldObject);"),l("with fieldObject: ".concat(JSON.stringify(e))),l("with trackers: ".concat(JSON.stringify(t))))}function X(e,t,r){if(!e){u("path is required in .pageview()");return}var n=j(e);if(n===""){u("path cannot be an empty string in .pageview()");return}var a={};if(r&&(a.title=r),typeof s=="function"&&(A(t,"send",De({hitType:"pageview",page:n},a)),y)){l("called ga('send', 'pageview', path);");var i="";r&&(i=" and title: ".concat(r)),l("with path: ".concat(n).concat(i))}}function Y(e,t){if(!e){u("modalName is required in .modalview(modalName)");return}var r=Se(j(e));if(r===""){u("modalName cannot be an empty string or a single / in .modalview()");return}if(typeof s=="function"){var n="/modal/".concat(r);A(t,"send","pageview",n),y&&(l("called ga('send', 'pageview', path);"),l("with path: ".concat(n)))}}function ee(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=e.category,r=e.variable,n=e.value,a=e.label,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:void 0;if(typeof s=="function"){if(!t||!r||typeof n!="number"){u("args.category, args.variable AND args.value are required in timing() AND args.value has to be a number");return}var c={hitType:"timing",timingCategory:d(t),timingVar:d(r),timingValue:n};a&&(c.timingLabel=d(a)),h(c,i)}}function te(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=e.category,r=e.action,n=e.label,a=e.value,i=e.nonInteraction,c=e.transport,f=Te(e,Ee),o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:void 0;if(typeof s=="function"){if(!t||!r){u("args.category AND args.action are required in event()");return}var p={hitType:"event",eventCategory:d(t),eventAction:d(r)};n&&(p.eventLabel=d(n)),typeof a<"u"&&(typeof a!="number"?u("Expected `args.value` arg to be a Number."):p.eventValue=a),typeof i<"u"&&(typeof i!="boolean"?u("`args.nonInteraction` must be a boolean."):p.nonInteraction=i),typeof c<"u"&&(typeof c!="string"?u("`args.transport` must be a string."):(["beacon","xhr","image"].indexOf(c)===-1&&u("`args.transport` must be either one of these values: `beacon`, `xhr` or `image`"),p.transport=c)),Object.keys(f).filter(function(g){return g.substr(0,9)==="dimension"}).forEach(function(g){p[g]=f[g]}),Object.keys(f).filter(function(g){return g.substr(0,6)==="metric"}).forEach(function(g){p[g]=f[g]}),h(p,o)}}function re(e,t){var r=e.description,n=e.fatal;if(typeof s=="function"){var a={hitType:"exception"};r&&(a.exDescription=d(r)),typeof n<"u"&&(typeof n!="boolean"?u("`args.fatal` must be a boolean."):a.exFatal=n),h(a,t)}}var ne={require:function(t,r,n){if(typeof s=="function"){if(!t){u("`name` is required in .require()");return}var a=j(t);if(a===""){u("`name` cannot be an empty string in .require()");return}var i=n?"".concat(n,".require"):"require";if(r){if(O(r)!=="object"){u("Expected `options` arg to be an Object");return}Object.keys(r).length===0&&u("Empty `options` given to .require()"),s(i,a,r),y&&l("called ga('require', '".concat(a,"', ").concat(JSON.stringify(r)))}else s(i,a),y&&l("called ga('require', '".concat(a,"');"))}},execute:function(t,r){for(var n,a,i=arguments.length,c=new Array(i>2?i-2:0),f=2;f<i;f++)c[f-2]=arguments[f];if(c.length===1?n=c[0]:(a=c[0],n=c[1]),typeof s=="function")if(typeof t!="string")u("Expected `pluginName` arg to be a String.");else if(typeof r!="string")u("Expected `action` arg to be a String.");else{var o="".concat(t,":").concat(r);n=n||null,a&&n?(s(o,a,n),y&&(l("called ga('".concat(o,"');")),l('actionType: "'.concat(a,'" with payload: ').concat(JSON.stringify(n))))):n?(s(o,n),y&&(l("called ga('".concat(o,"');")),l("with payload: ".concat(JSON.stringify(n))))):(s(o),y&&l("called ga('".concat(o,"');")))}}};function D(e,t,r){if(typeof t!="function"){u("hitCallback function is required");return}if(typeof s=="function"){if(!e||!e.label){u("args.label is required in outboundLink()");return}var n={hitType:"event",eventCategory:"Outbound",eventAction:"Click",eventLabel:d(e.label)},a=!1,i=function(){a=!0,t()},c=setTimeout(i,250),f=function(){clearTimeout(c),a||t()};n.hitCallback=f,h(n,r)}else setTimeout(t,0)}var $e=P;const Re={initialize:H,ga:s,set:Q,send:h,pageview:X,modalview:Y,timing:ee,event:te,exception:re,plugin:ne,outboundLink:D,testModeAPI:P},ze=Object.freeze(Object.defineProperty({__proto__:null,addTrackers:Z,default:Re,event:te,exception:re,ga:s,initialize:H,modalview:Y,outboundLink:D,pageview:X,plugin:ne,send:h,set:Q,testModeAPI:$e,timing:ee},Symbol.toStringTag,{value:"Module"}));function M(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function B(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?M(Object(r),!0).forEach(function(n){Ge(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ge(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}b.origTrackLink=b.trackLink;b.trackLink=D;var Me=b;B(B({},ze),{},{OutboundLink:Me});
