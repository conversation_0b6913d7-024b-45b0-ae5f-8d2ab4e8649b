import React from "react";

const Quiz = () => {
  return (
    <div>
      <div>
        <h2>gamification:</h2>

        <h2>beat the AI</h2>
        <p>
          Combine AI-driven match insights with quiz and trivia games. Users can
          answer questions related to upcoming matches, player statistics, or
          team history. Correct answers earn them free messages, adding an
          educational and competitive layer to the prediction experience this is
          important for us due to the legal positioning of the company
        </p>

        <div>
          <h2>1. Match Insight-Based Questions</h2>
          <p>
            Users will be presented with questions related to upcoming matches.
            These questions can be derived from AI-generated insights about the
            teams, their recent performance, and historical data. For example,
            users might be asked about the last time these two teams met, or
            which player has the highest goal-scoring record in the current
            season. Correct answers to these questions will earn them points.
          </p>
        </div>
        <div>
          <h2>2. Player and Team Statistics</h2>
          <p>
            Our trivia games will also delve into player statistics. Users can
            answer questions about individual player achievements, such as the
            number of goals scored, assists provided, or clean sheets kept.
            Team-specific questions can include queries about goals scored,
            matches won, or historical rivalries. Accurate answers will
            contribute to their overall score.
          </p>
        </div>
        <div>
          <h2>3. Achievements and social media</h2>
          <p>
            To make the experience more exciting and competitive, users can earn
            free messages by answering the question and doing this on there
            instagram and tagging us. by doing this we dont need to build a
            ranking system. the ai will provide a question which they can answer
            by posting a story with the question and their answer and by tagging
            us.{" "}
          </p>
        </div>
        <div>
          <h2>4. Educational and Fun</h2>
          <p>
            Our trivia games aim to be both educational and fun. Users can learn
            more about the sport they love while enjoying a unique prediction
            experience. By integrating AI-generated insights with trivia
            questions, we're creating an immersive environment that caters to
            both dedicated football aficionados and casual fans. Incorporating
            AI-driven trivia games into our platform adds an educational and
            competitive layer that transforms match predictions into an engaging
            and enriching experience for our users. Welcome to a new era of
            football prediction, where knowledge and passion unite.
          </p>
          <p>
            Users can use the AI to answer these trivia questions but they will
            get significantly less points if they do so. Every question to the
            AI will also cost a message. The AI will not give the answer to the
            question but it will give a big hint to the answer.{" "}
          </p>
        </div>
      </div>
    </div>
  );
};

export default Quiz;
