import { AuthContext, tokenExpireError } from "Src/authContext";
import MkdSDK from "Utils/MkdSDK";
import React, { useEffect, useState } from "react";
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  makeStyles,
} from "@material-ui/core";

const useStyles = makeStyles((theme) => ({
  formControl: {
    // margin: theme.spacing(1),
    minWidth: 120,
    paddingInline: 10,
    borderRadius: "9999px", // Rounded border
    // border: '2px solid red', // Red border
    backgroundColor: "#F8F8F8", // Gray background
    "& .MuiOutlinedInput-root": {
      "& fieldset": {
        border: "none", // Remove default border
      },
      "&:hover fieldset": {
        border: "none", // Remove border on hover
      },
      "&.Mui-focused fieldset": {
        border: "none", // Remove border when focused
      },
    },
    "& .MuiOutlinedInput-input": {
      padding: "10px 14px", // Adjust input padding if needed
    },
    "& .MuiInput-underline:before": {
      borderBottom: "none", // Remove underline before selection
    },
    "& .MuiInput-underline:after": {
      borderBottom: "none", // Remove underline after selection
    },
  },
  select: {
    "&.MuiSelect-select": {
      backgroundColor: "transparent", // Transparent background
    },
  },
  menuItem: {
    "&:hover": {
      backgroundColor: "rgba(193,39,44,0.1)", // Light red background on hover
      "& .MuiTypography-body1": {
        color: "#8b0000", // Deep red text color on hover
      },
    },
  },
  selectedMenuItem: {
    color: "red", // Red text color for selected item
    backgroundColor: "rgba(193,39,44,0.1) !important", // Light red background for selected item
    "&:hover": {
      backgroundColor: "#ffcccc", // Maintain light red background on hover for selected item
    },
    "& .MuiTypography-body1": {
      color: "#8b0000 !important", // Deep red text color on hover
    },
  },
}));
let sdk = new MkdSDK();

export const sportList = {
  football: [
    "Serie A (IT)",
    "Eredivisie",
    "Ligue 1",
    "Bundesliga",

    "La Liga",

    "European championship",

    "top 150 countries",
    "KKD",
    "Serie A (Brazil)",
    "Serie B(italy)",
    "Ligue 2",
    "A-League",
    "Liga MX",
    "Super ligue Turkiye",
    "premier league Russia",
    "Bundesliga austria",
    "Superliga denmark",

    "Scottish premiership",

    "Primera division argentina",
    "J1 league japan",

    "Eliteserien norway",

    "La liga 2",

    "Super league greece",
    "Championship",
    "League one england",

    "League 2 England",
    "USL USA",
    "MLS USA",
    "3e bundesliga Germany",
    "2e Bundesliga",

    "Liga Portugal",
    "Jupiler pro belgium",
  ],

  tennis: ["top 250 female", "top 250 male"],
  darts: ["top 100 dart"],
  "american football": ["NFL", "NCAA(College football)"],
  icehockey: ["NHL", "College icehockey", "KHL", "Liiga", "AHL"],
  basketball: [
    "NBA",
    "College basketball",
    "Euroleague",
    "BSL",
    "BBL",
    "NBL",
    "CBA",
    "Greek A1 league",
  ],
  baseball: ["MLB", "College baseball", "NPB", "Lidom"],
  boxing: [
    "Top 30 Heavyw",
    "Top 30 Cruiser",
    "Top 30 light heavy",
    "Top 30 middelweight",
    "Top 30 welter",
    "top 30 Lightweight",
    "top 30 Feather",
  ],
  mma: [
    "top 25 heavy",
    "top 25 light heavy",
    "top 25 middle",
    "top 25 welter",
    "top 25 lightweight",
    "top 25 feather",
    "top 25 bantam",
    "top 25 fly",
  ],
  rugby: ["top 40 rugby nations", "Super league", "NRL"],
};

const SuggestedSelectSport = ({
  toggle,
  isOpen,
  isMessageSent,
  sport,
  setSport,
  className,
  title = true,
  search = false,
}) => {
  const [selectedSport, setSelectedSport] = useState("");
  const [open, setOpen] = React.useState(false);
  const [ageGroup, setAgeGroup] = useState("");
  const anchorRef = React.useRef(null);
  const classes = useStyles();

  const { state: states, dispatch } = React.useContext(AuthContext);

  const prevOpen = React.useRef(open);
  React.useEffect(() => {
    setSport(selectedSport);
  }, [selectedSport]);
  React.useEffect(() => {
    if (prevOpen.current === true && open === false) {
      anchorRef?.current?.focus();
    }

    prevOpen.current = open;
  }, [open]);

  // ............SET SPORT..............
  const handleSelectSport = async (e) => {
    setSelectedSport(e.target.value);

    let filter = {
      user_id: localStorage.getItem("user"),
    };

    // sdk.setTable("profile");
    // const results = await sdk.callRestAPI(
    //   {
    //     payload: { ...filter },
    //     page: 1,
    //     limit: 10,
    //     sortId: "",
    //     direction: "",
    //   },
    //   "PAGINATE"
    // );

    // const { list, total, limit, num_pages, page } = results;

    // if (results) {
    //   sdk.setTable("profile");
    //   const result = await sdk.callRestAPI(
    //     {
    //       language: e.target.value,
    //       id: list[0]?.id,
    //     },
    //     "PUT"
    //   );
    // }

    setSelectedSport(e.target.value);

    // set the array of sports in the localstorage
    // localStorage.setItem("selectedSport", [sportList[e.target.value]]);
    // localStorage.setItem("selectedSportName", e.target.value);

    dispatch({
      type: "selectedSport",
      payload: e.target.value,
    });
  };

  //............GET SELECTED LANGUAGE..............
  useEffect(() => {
    setSelectedSport(sport);
  }, [sport]);

  const handleChange = (e) => {
    // setAgeGroup(e.target.value);
    handleSelectSport(e);
  };

  return (
    <nav
      className={`bg-zinc-800 text-white py-1 mb-0 flex flex-col justify-between relative ${className}`}
    >
      <FormControl className={classes.formControl}>
        <Select
          labelId="demo-simple-select-label"
          id="demo-simple-select"
          value={selectedSport || ""} // Ensure it defaults to an empty value
          onChange={handleChange}
          displayEmpty // Enable placeholder for empty value
          classes={{ select: classes.select }}
          MenuProps={{
            anchorOrigin: {
              vertical: "bottom",
              horizontal: "left",
            },
            transformOrigin: {
              vertical: "top",
              horizontal: "left",
            },
            getContentAnchorEl: null,
          }}
        >
          {/* Placeholder option */}
          <MenuItem value="" disabled>
            <span className="text-[#999999] md:text-[14px] text-[12px] capitalize">
              Select Sport
            </span>
          </MenuItem>
          {search && (
            <MenuItem value={null}>
              <span className="text-[#999999] md:text-[14px] text-[12px] capitalize">
                All
              </span>
            </MenuItem>
          )}

          {/* Dynamic sport list options */}
          {sportList &&
            Object.entries(sportList)?.map(([key, value], i) => (
              <MenuItem
                key={key}
                className={`${classes.menuItem} ${
                  selectedSport == value ? classes.selectedMenuItem : ""
                }`}
                value={key}
              >
                <span
                  className={`text-[#999999] md:text-[14px] text-[12px] hover:text-companyRed capitalize ${
                    selectedSport == value ? "text-companyBlack" : ""
                  }`}
                >
                  {key}
                </span>
              </MenuItem>
            ))}
        </Select>
      </FormControl>
      {/* {title && <p className="text-black text-[16px] mt-1">Select sport</p>} */}
    </nav>
  );
};

export default SuggestedSelectSport;
