import React, { useState, useEffect } from "react";
import { loadStripe } from "@stripe/stripe-js";
import { Elements } from "@stripe/react-stripe-js";
import { useNavigate, useParams } from "react-router";
// import Checkout from "Components/Checkout";
import MkdSDK from "Utils/MkdSDK";
import { GlobalContext, showToast } from "Src/globalContext";
import { tokenExpireError } from "Src/authContext";
import Checkout from "Components/Checkout";
import { CompanyLogoBig } from "../../assets/svgs/CompanyLogoBig";
import { CompanyLogoMid } from "Assets/svgs/CompanyLogoMid";
import { InteractiveButton } from "Components/InteractiveButton";
import { Modal } from "Components/Modal";
import { CloseIcon } from "Assets/svgs";
import { Troubleshoot } from "@mui/icons-material";

const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_API_KEY);

export default function Payment() {
  const [clientSecret, setClientSecret] = useState("");
  const [paymentIntent, setPaymentIntent] = useState("");
  const [amountWithCoupon, setAmountWithCoupon] = useState("");
  const [amount, setAmount] = useState("");
  const [planId, setPlanId] = useState("");
  const [loading, setLoading] = useState(false);
  const [isCouponOpen, setIsCouponOpen] = useState(false);
  const [name, setName] = useState("");
  const [isNewsLetterBonus, setIsNewsLetterBonus] = useState();
  const [couponCode, setCouponCode] = useState();
  const [oldPrice, setOldPrice] = useState();

  const qty = atob(localStorage.getItem("qty"));
  const data = useParams();

  let id = data?.id;

  const navigate = useNavigate();
  const { state, dispatch: globalDispatch } = React.useContext(GlobalContext);

  let sdk = new MkdSDK();

  const getPaymentIntent = async (
    planId,
    name,
    amountWithCoupon,
    couponCode
  ) => {
    try {
      setLoading(true);
      const result = await sdk.callRawAPI(
        // "https://skillgames.mkdlabs.com/v3/api/custom/skillgames/player/payment-intent/create",
        "/v3/api/custom/kaizenwin/membership/payment/intent",

        {
          // amount: 12,
          currency: "eur",
          plan_id: planId,

          coupon: couponCode,
        },
        "post"
      );
      
      

      if (!result.error) {
        setClientSecret(result.client_secret);
        setPaymentIntent(result.payment_intent_id);
        setAmountWithCoupon(result.amount);
        
        setLoading(false);
        showToast(
          globalDispatch,
          "Price updated successfully",
          7000,
          "success"
        );
        setIsCouponOpen(false);
      } else if (result.error) {
        setTimeout(() => {}, 2000);
        setLoading(false);
        showToast(globalDispatch, result?.message, 4000, "error");
      }
    } catch (error) {
      tokenExpireError(globalDispatch, error.message);
      setTimeout(() => {}, 1000);
      setLoading(false);
      showToast(globalDispatch, error?.message, 4000, "error");
    }
  };

  useEffect(() => {
    // Get the current URL
    const url = window.location.href;

    // Parse the URL to get query parameters
    const params = new URLSearchParams(url);

    // Retrieve the values of the desired parameters

    setIsNewsLetterBonus(params.get("newsletterBonus"));
    setName(params.get("name"));
    setAmount(params.get("amount"));
    setPlanId(params.get("plan_id"));
    setAmountWithCoupon(params.get("amountWithCoupon"));
    setOldPrice(params.get("amountWithCoupon"))
    setPaymentIntent(params.get("paymentIntent"));
    setClientSecret(params.get("clientSecret"));
  }, []);

  // useEffect(() => {
  //   // Show the modal after 5 seconds if it hasn't been shown yet
  //   const timer = setTimeout(() => {
  //     setIsCouponOpen(true);
  //   }, 2000);

  //   // Cleanup the timer
  //   return () => clearTimeout(timer);
  // }, []);

  const appearance = {
    theme: "stripe",

    variables: {
      colorPrimary: "#0570de",
      colorBackground: "white",
      colorText: "black",
      colorDanger: "#df1b41",
      fontFamily: "Ideal Sans, system-ui, sans-serif",
      fontWeightNormal: "500",
      spacingUnit: "5px",
      borderRadius: "4px",
      // See all possible variables below
    },
  };

  const options = {
    clientSecret,
    appearance,
  };

  return (
    // grid md:grid-cols-2
    <div className=" p-4">
      <div className="card flex flex-col justify-center items-center p-4 gap-2 max-w-[60rem] mx-auto rounded-xl">
        <button
          className=" self-end mt-5 bg-blue-300 hover:bg-blue-400 px-6 py-1 rounded-full"
          onClick={() => navigate(-1)}
        >
          Back
        </button>
        <div className=" md:flex md:flex-row flex flex-col gap-2 w-full h-fit mx-auto ">
          <div className=" md:w-[450px] w-full md:block mx-auto h-full ">
            <div className=" items-center md:text-center text-center">
              <div className=" pb-4">
                {/* <CompanyLogoBig /> */}
                <div
                  className=" pb-4 cursor-pointer w-fit"
                  onClick={() => {
                    navigate("/");
                  }}
                >
                  <CompanyLogoMid />
                </div>
              </div>
              {/* ${oldPrice !== amountWithCoupon && "p-2 bg-green-600 rounded-lg max-w-[200px] text-white"} */}
              <div className="w-full mx-auto text-justify max-w-[400px] ">
                <p className="text-xl mb-2 tracking-10">Purchase Plan </p>
                <p className="text-2xl mb-2 tracking-10 font-bold">{name}</p>
                <p className="text-xl mb-2 tracking-10 ">Price:</p>
                <p className={`text-5xl mb-2 tracking-10 font-bold  `}>
                  €
                  { isNaN(parseInt(amountWithCoupon) / 100)
                    ? " "
                    : parseInt(amountWithCoupon) / 100 }
                </p>
                {
                  parseInt(oldPrice) !== parseInt(amountWithCoupon) && oldPrice && (
                    <p className="text-xl mb-2 tracking-10 font-bold line-through text-red-500">Old Price: €{parseInt(oldPrice) / 100}</p>
                  )
                }
                {
                  isNewsLetterBonus &&parseInt(amount) !== parseInt(amountWithCoupon) && oldPrice && (
                    <>
                    <p className="text-l mb-2 tracking-10 font-bold  text-green-500">10% discount on your first purchase</p>
                    {/* <p className="text-xl mb-2 tracking-10 font-bold line-through text-red-500">Old Price: €{parseInt(amount) / 100}</p> */}
                    </>
                  )
                }

                <div className=" mt-4">
                  <InteractiveButton
                    className="bg-companyRed text-white hover:bg-[#c1272ca6] w-fit py-3 px-4 h-full text-sm  rounded-lg"
                    onClick={() => {
                      setIsCouponOpen(true);
                    }}
                  >
                    Apply coupon
                  </InteractiveButton>
                </div>
              </div>
            </div>
          </div>
          {clientSecret && (
            <Elements options={options} stripe={stripePromise}>
              <Checkout
                clientSecret={clientSecret}
                paymentIntent={paymentIntent}
              />
            </Elements>
          )}
        </div>
      </div>

      <Modal isOpen={isCouponOpen}>
        <div className=" w-full flex justify-end">
          <button onClick={() => setIsCouponOpen(false)}>
            <CloseIcon />
          </button>
        </div>

        <CouponFunction
          name={name}
          amountWithCoupon={amountWithCoupon}
          setCouponCode={setCouponCode}
          getPaymentIntent={getPaymentIntent}
          loading={loading}
          couponCode={couponCode}
          planId={planId}
        />
      </Modal>
    </div>
  );
}

export const CouponFunction = ({
  name,
  amountWithCoupon,
  setCouponCode,
  getPaymentIntent,
  loading,
  couponCode,
  planId,
}) => {
  return (
    <div className="w-full mx-auto text-justify">
      <p className="text-xl mb-4 tracking-10 w-full text-center font-semibold">Apply a coupon? </p>
      <p className="text-lg mb-2 tracking-10">Purchase Plan </p>
      <p className="text-2xl mb-2 tracking-10 font-bold">{name}</p>
      <p className="text-lg mb-2 tracking-10 ">Price:</p>
      <p className="text-5xl mb-2 tracking-10 font-bold">
        €
        {isNaN(parseInt(amountWithCoupon) / 100)
          ? " "
          : parseInt(amountWithCoupon) / 100}
      </p>

      <div className="  md:flex md:flex-row flex flex-col items-center justify-center mt-4 gap-2 max-w-[500px] w-full border  p-2">
        <input
          type="text"
          className="  shadow appearance-none border rounded w-full px-3 text-gray-700 h-full leading-tight focus:outline-none focus:shadow-outline"
          placeholder="Coupon code"
          onChange={(e) => setCouponCode(e.target.value)}
        />
        <InteractiveButton
          loading={loading}
          className="bg-companyRed text-white hover:bg-[#c1272ca6] w-full max-w-[170px] py-3 px-4 h-full text-sm  rounded-lg"
          onClick={() => {
            getPaymentIntent(planId, name, amountWithCoupon, couponCode);
          }}
        >
          Apply coupon
        </InteractiveButton>
      </div>
    </div>
  );
};
