import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import React from "react";

const StatusNotification = ({ value, name }) => {
  // Determine the class based on the value (true = green, false = red)
  const statusClass = value ? "bg-green-500" : "bg-red-500";

  return (
    <div className="flex flex-col justify-between gap-2 items-center text-base w-full max-w-xl">
      <div
        className={`text-white p-4 rounded font-bold w-full max-w-lg ${statusClass}`}
      >
      <div className=" font-bold flex justify-between">
        <span className=" text-sm">{name}</span> <FontAwesomeIcon icon="fa-solid fa-bell" />
      </div>
        <span className="text-sm">Status:</span> {value ? "All Good" : "Warning"}
      </div>
    </div>
  );
};

export default StatusNotification;
