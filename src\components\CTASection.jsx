import React from "react";
import SkeletonLoading from "./SkeletonLoading";
import { useData } from "Src/dataContext";
import { useNavigate } from "react-router";

const CTASection = () => {
  const { cms } = useData();
  const navigate = useNavigate()
  return (
    <section
      className="md:flex py-6 w-full  my-2 h-[484px] flex-col items-center text-center justify-evenly relative "
      style={{
        backgroundImage: `url(${
          cms?.find((item) => item.content_key === "CTA_Section_image")
            ?.content_value
        })`,
        // backgroundImage: `url(${cta})`,
        backgroundSize: "cover",
        backgroundPosition: "center center",
      }}
    >
      {!cms && <SkeletonLoading padding="py-[10rem]" />}
      <div className=" text-white font-light flex flex-col justify-evenly items-center  gap-6 px-6 lg:max-w-[852px]  md:px-2 md:max-w-[852px] sm:max-w-[100vw] h-full">
        <h2
          className={` text-white md:text-4xl sm:text-2xl text-2xl font-medium pt-4`}
        >
          {
            cms?.find((item) => item.content_key === "CTA_Section_headings")
              ?.content_value
          }
        </h2>
        <p className="md:text-lg sm:text-base text-[15px] font-normal max-w-[766px]">
          {
            cms?.find((item) => item.content_key === "CTA_Section_text")
              ?.content_value
          }
        </p>
        <button
          onClick={() => {
            navigate("/user/chat");
          }}
          className="border rounded-full font-inter font-medium bg-white text-companyRed px-4 py-2 cursor-pointer hover:scale-105 text-sm"
        >
          {
            cms?.find((item) => item.content_key === "CTA_Section_button_text")
              ?.content_value
          }
        </button>
        <p className="md:text-lg text-[15px] font-medium">
          {
            cms?.find((item) => item.content_key === "CTA_Section_closing_text")
              ?.content_value
          }
        </p>
      </div>
    </section>
  );
};

export default CTASection;
