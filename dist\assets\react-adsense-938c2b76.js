import{r as p}from"./vendor-b0222800.js";import{p as s}from"./@ckeditor/ckeditor5-react-48fc30c1.js";var d={},u={};Object.defineProperty(u,"__esModule",{value:!0});var v=function(){function t(e,r){for(var o=0;o<r.length;o++){var n=r[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(e,r,o){return r&&t(e.prototype,r),o&&t(e,o),e}}(),h=p,l=y(h),b=s,a=y(b);function y(t){return t&&t.__esModule?t:{default:t}}function g(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function m(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function w(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var i=function(t){w(e,t);function e(){return g(this,e),m(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return v(e,[{key:"componentDidMount",value:function(){window&&(window.adsbygoogle=window.adsbygoogle||[]).push({})}},{key:"render",value:function(){return l.default.createElement("ins",{className:this.props.className+" adsbygoogle",style:this.props.style,"data-ad-client":this.props.client,"data-ad-slot":this.props.slot,"data-ad-layout":this.props.layout,"data-ad-layout-key":this.props.layoutKey,"data-ad-format":this.props.format,"data-full-width-responsive":this.props.responsive})}}]),e}(l.default.Component);u.default=i;i.propTypes={className:a.default.string,style:a.default.object,client:a.default.string.isRequired,slot:a.default.string.isRequired,layout:a.default.string,layoutKey:a.default.string,format:a.default.string,responsive:a.default.string};i.defaultProps={className:"",style:{display:"block"},format:"auto",layout:"",layoutKey:"",responsive:"false"};var f={};Object.defineProperty(f,"__esModule",{value:!0});var O=function(){function t(e,r){for(var o=0;o<r.length;o++){var n=r[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(e,r,o){return r&&t(e.prototype,r),o&&t(e,o),e}}(),E=p,c=P(E);function P(t){return t&&t.__esModule?t:{default:t}}function k(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function R(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function T(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var M=function(t){T(e,t);function e(){return k(this,e),R(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return O(e,[{key:"render",value:function(){return c.default.createElement("div",{className:"adsbybaidu"},"TODO")}}]),e}(c.default.Component);f.default=M;Object.defineProperty(d,"__esModule",{value:!0});var $=u,j=_($),D=f,q=_(D);function _(t){return t&&t.__esModule?t:{default:t}}var x={Google:j.default,Baidu:q.default},G=d.default=x;export{G as _};
