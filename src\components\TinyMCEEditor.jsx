// import React, { useState, useEffect } from "react";
// import { Editor } from "@tinymce/tinymce-react";
// import Compressor from 'compressorjs';

// const TinyMCEEditor = ({ initialContent, onSave }) => {
//   const [editorContent, setEditorContent] = useState(initialContent);

//   useEffect(() => {
//     setEditorContent(initialContent);
//   }, [initialContent]);

//   const handleEditorChange = (content) => {
//     setEditorContent(content);
//   };

//   const handleSave = () => {
//     const cleanedContent = cleanHTML(editorContent);
//     onSave(cleanedContent);
//   };

//   const handleImageUpload = async (blobInfo, success, failure) => {
//     try {
//       const compressedImage = await compressImage(blobInfo.blob());
//       const reader = new FileReader();
//       reader.readAsDataURL(compressedImage);
//       reader.onloadend = () => {
//         success(reader.result);
//       };
//     } catch (error) {
//       failure('Image compression failed: ' + error.message);
//     }
//   };

//   const compressImage = (file) => {
//     return new Promise((resolve, reject) => {
//       new Compressor(file, {
//         quality: 0.6, // Adjust quality as needed
//         success(result) {
//           resolve(result);
//         },
//         error(err) {
//           reject(err);
//         },
//       });
//     });
//   };

//   const cleanHTML = (html) => {
//     // Remove unnecessary tags, attributes, or inline styles
//     return html
//       .replace(/style="[^"]*"/g, '') // Remove inline styles
//       .replace(/\s+/g, ' ')           // Replace multiple spaces with a single space
//       .trim();                        // Trim leading and trailing spaces
//   };

//   return (
//     <div>
//       <Editor
//         apiKey={import.meta.env.VITE_TINYMCE_EDITOR_API_KEY} // Replace with your TinyMCE API key
//         value={editorContent}
//         onEditorChange={handleEditorChange}
//         init={{
//           height: 500,
//           menubar: false,
//           plugins: [
//             'advlist autolink lists link image charmap print preview anchor',
//             'searchreplace visualblocks code fullscreen',
//             'insertdatetime media table paste code help wordcount',
//             'image' // No imagetools here since we're handling the upload
//           ],
//           toolbar:
//             "undo redo | formatselect | bold italic backcolor | \
//             alignleft aligncenter alignright alignjustify | \
//             bullist numlist outdent indent | removeformat | help | image",
//           images_upload_handler: handleImageUpload, // Custom image upload handler
//           image_dimensions: true,  // Allow image dimensions adjustment
//           image_class_list: [
//             { title: 'Responsive', value: 'img-responsive' }
//           ],
//           resize_img_proportional: true,  // Ensure proportional resizing
//         }}
//       />
//       <button
//         onClick={handleSave}
//         className="mt-4 px-4 py-2 bg-blue-500 text-white rounded"
//       >
//         Save
//       </button>
//     </div>
//   );
// };

// export default TinyMCEEditor;

import React, { useState, useEffect } from "react";
import { Editor } from "@tinymce/tinymce-react";

const TinyMCEEditor = ({ initialContent, onSave }) => {
  const [editorContent, setEditorContent] = useState(initialContent);

  useEffect(() => {
    setEditorContent(initialContent);
  }, [initialContent]);

  const handleEditorChange = (content) => {
    setEditorContent(content);
  };

  const handleSave = () => {
    onSave(editorContent);
  };

  const compressImage = (file, callback) => {
    const reader = new FileReader();
    reader.onload = () => {
      const img = new Image();
      img.src = reader.result;

      img.onload = () => {
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");
        const maxWidth = 800; // Max width for the compressed image
        const maxHeight = 800; // Max height for the compressed image
        let width = img.width;
        let height = img.height;

        if (width > height) {
          if (width > maxWidth) {
            height *= maxWidth / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width *= maxHeight / height;
            height = maxHeight;
          }
        }

        canvas.width = width;
        canvas.height = height;
        ctx.drawImage(img, 0, 0, width, height);

        canvas.toBlob(
          (blob) => {
            callback(blob);
          },
          file.type,
          0.7
        ); // 0.7 is the quality for JPEG images
      };
    };

    reader.readAsDataURL(file);
  };

  return (
    <div>
      <Editor
        apiKey={import.meta.env.VITE_TINYMCE_EDITOR_API_KEY} // Replace with your TinyMCE API key
        value={editorContent}
        onEditorChange={handleEditorChange}
        init={{
          height: 500,
          menubar: false,
          plugins: [
            "advlist autolink lists link image charmap print preview anchor",
            "searchreplace visualblocks code fullscreen",
            "insertdatetime media table paste code help wordcount",
            "image",
          ],
          toolbar:
            "undo redo | formatselect | bold italic backcolor | \
            alignleft aligncenter alignright alignjustify | \
            bullist numlist outdent indent | removeformat | help | image",
          image_advtab: true,
          file_picker_types: "image",
          file_picker_callback: (cb, value, meta) => {
            const input = document.createElement("input");
            input.setAttribute("type", "file");
            input.setAttribute("accept", "image/*");

            input.addEventListener("change", (e) => {
              const file = e.target.files[0];

              compressImage(file, (compressedBlob) => {
                const id = "blobid" + new Date().getTime();
                const blobCache = tinymce.activeEditor.editorUpload.blobCache;
                const reader = new FileReader();

                reader.onload = () => {
                  const base64 = reader.result.split(",")[1];
                  const blobInfo = blobCache.create(id, compressedBlob, base64);
                  blobCache.add(blobInfo);
                  cb(blobInfo.blobUri(), { title: file.name });
                };

                reader.readAsDataURL(compressedBlob);
              });
            });

            input.click();
          },
          imagetools_toolbar:
            "rotateleft rotateright | flipv fliph | editimage imageoptions",
          image_dimensions: true, // Allow image dimensions adjustment
          image_class_list: [{ title: "Responsive", value: "img-responsive" }],

          resize_img_proportional: true, // Ensure proportional resizing
        }}
      />
      <button
        onClick={handleSave}
        className="mt-4 px-4 py-2 bg-blue-500 text-white rounded"
      >
        Save
      </button>
    </div>
  );
};

export default TinyMCEEditor;
