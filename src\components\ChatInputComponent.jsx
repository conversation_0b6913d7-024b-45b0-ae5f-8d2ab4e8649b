import React from 'react'
import MkdTextArea from './custom/MkdTextArea'

const ChatInputComponent = ({
    handleSubmit,
    userInput,
    handleInputChange,
    handleKeyDown,
}) => {
  return (
    <div className="w-full flex flex-col justify-center items-center">
            <form
              onSubmit={handleSubmit}
              className={`mb-4 mt-4 flex border-2  focus:ring-0 rounded-full items-center justify-center bg-white md:py-1  sm:py-2 py-[0.5px] md:px-3 sm:px-3 px-2 gap-1 md:w-[80%] w-[90%]   
         `}
            >
              <MkdTextArea
                value={userInput}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
              />

              <button type="submit" className=" ">
                <svg
                  width="50"
                  height="50"
                  viewBox="0 0 58 58"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <rect width="58" height="58" rx="29" fill="#C1272D" />
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M37.5622 28.1059C38.3031 28.4734 38.3031 29.5302 37.5622 29.8976L21.4444 37.8926C20.7797 38.2223 20 37.7387 20 36.9967V31.0003L29 29.0003L20 26.7947L20 21.0068C20 20.2648 20.7797 19.7813 21.4444 20.111L37.5622 28.1059Z"
                    fill="white"
                    stroke="white"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
            </form>
          </div>
  )
}

export default ChatInputComponent
