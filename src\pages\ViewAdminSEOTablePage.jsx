import React from "react";
import MkdSDK from "../utils/MkdSDK";
import { useParams } from "react-router-dom";
import { AuthContext, tokenExpireError } from "../authContext";
import { GlobalContext } from "../globalContext";
import { BackButton } from "Components/BackButton";

let sdk = new MkdSDK();

const ViewAdminSEOTablePage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  const [viewModel, setViewModel] = React.useState({});

  const params = useParams();

  React.useEffect(function () {
    (async function () {
      try {
        sdk.setTable("seo");
        const result = await sdk.callRestAPI({ id: Number(params?.id) }, "GET");
        if (!result.error) {
          setViewModel(result.model);
        }
      } catch (error) {
        console.log("error", error);
        tokenExpireError(dispatch, error.message);
      }
    })();
  }, []);
  return (
    <div className=" shadow-md rounded  mx-auto p-5">
      <div className=" flex flex-col gap-4 mb-4">
        <BackButton />
        <h4 className="text-2xl font-medium">View SEO</h4>
      </div>

      <div className="my-4">
        <div className="flex mb-4">
          <div className="flex-1">Id</div>
          <div className="flex-1">{viewModel?.id}</div>
        </div>
      </div>

      <div className="my-4">
        <div className="flex mb-4">
          <div className="flex-1">Create At</div>
          <div className="flex-1">{viewModel?.create_at}</div>
        </div>
      </div>

      {/* <div className="my-4">
        <div className="flex mb-4">
          <div className="flex-1">Update At</div>
          <div className="flex-1">{viewModel?.update_at}</div>
        </div>
      </div> */}

      <div className="my-4">
        <div className="flex mb-4">
          <div className="flex-1">Title</div>
          <div className="flex-1">{viewModel?.title}</div>
        </div>
      </div>
      <div className="my-4">
        <div className="flex mb-4">
          <div className="flex-1">Description</div>
          <div className="flex-1">{viewModel?.description}</div>
        </div>
      </div>
      <div className="my-4">
        <div className="flex mb-4">
          <div className="flex-1">Keywords</div>
          <div className="flex-1">{viewModel?.keywords}</div>
        </div>
      </div>
    </div>
  );
};

export default ViewAdminSEOTablePage;
