import { CKEditor } from "@ckeditor/ckeditor5-react";
import ClassicEditor from "@ckeditor/ckeditor5-build-classic";

export const MyEditor = () => {
  return (
    <CKEditor
      editor={ClassicEditor}
      config={{
        ckfinder: {
          uploadUrl: "/upload/image",
        },
        toolbar: [
          "imageUpload",
          "imageStyle:inline",
          "imageStyle:block",
          "imageStyle:side",
          "resizeImage:50",
          "resizeImage:75",
          "resizeImage:original",
        ],
        image: {
          resizeUnit: "%",
          toolbar: [
            "imageTextAlternative",
            "imageStyle:inline",
            "imageStyle:block",
            "imageStyle:side",
            "resizeImage:50",
            "resizeImage:75",
            "resizeImage:original",
          ],
        },
      }}
    />
  );
};
