import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "../utils/MkdSDK";
import { GlobalContext, showToast } from "../globalContext";
import { useNavigate, useParams } from "react-router-dom";
import { AuthContext, tokenExpireError } from "../authContext";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { isImage, empty, isVideo, isPdf } from "../utils/utils";
import { BackButton } from "Components/BackButton";
import SelectSport from "Components/SelectSport";
import SuggestedSelectSport from "Components/SuggestedSelectSport ";

let sdk = new MkdSDK();

const EditAdminSuggestedQuestions = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const schema = yup
    .object({
      suggested_questions: yup.string(),
    })
    .required();
  const [fileObj, setFileObj] = React.useState({});
  const navigate = useNavigate();

  const [suggestedQuestions, setSuggestedQuestions] = useState("");
  const [description, setDescription] = useState("");
  const [amount, setAmount] = useState("");
  const [stripe_id, setStripeId] = useState("");
  const [messages, setMessages] = useState("");
  const [id, setId] = useState(0);
  const [sport, setSport] = React.useState();
  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const params = useParams();

  // ADDING NEW ATTRIBUTES..................................STARTS
  const [inputFields, setInputFields] = useState([""]);

  const handleChange = (index, event) => {
    const values = [...inputFields];
    values[index] = event.target.value;
    setInputFields(values);
  };

  const handleAddFields = () => {
    setInputFields([...inputFields, ""]);
  };

  const handleRemoveFields = (index) => {
    const values = [...inputFields];
    values.splice(index, 1);
    setInputFields(values);
  };

  // console.log("inputFields",inputFields);

  // const handleSubmit = (event) => {
  //   event.preventDefault();
  //   // Handle form submission logic here
  //   console.log('Form submitted:', inputFields);
  // };

  // ADDING NEW ATTRIBUTES..................................ENDS

  useEffect(function () {
    (async function () {
      try {
        sdk.setTable("suggested_question");
        const result = await sdk.callRestAPI({ id: Number(params?.id) }, "GET");
        if (!result.error) {
          setValue("suggested_questions", result.model.question);
          setValue("priority", result.model.priority);
          setValue("priority_expiry", result.model.priority_expiry);
          setSport(result.model.sport);

          setSuggestedQuestions(result.model.question);

          setId(result.model.id);
        }
      } catch (error) {
        console.log("error", error);
        tokenExpireError(dispatch, error.message);
      }
    })();
  }, []);

  const previewImage = (field, target) => {
    let tempFileObj = fileObj;
    tempFileObj[field] = {
      file: target.files[0],
      tempURL: URL.createObjectURL(target.files[0]),
    };
    setFileObj({ ...tempFileObj });
  };

  const onSubmit = async (_data) => {
    try {
      sdk.setTable("suggested_question");
      for (let item in fileObj) {
        let formData = new FormData();
        formData.append("file", fileObj[item].file);
        let uploadResult = await sdk.uploadImage(formData);
        _data[item] = uploadResult.url;
      }
      const result = await sdk.callRestAPI(
        {
          id: id,
          question: _data.suggested_questions,
          priority: _data.priority ? 1 : 0,
          priority_expiry: _data.priority_expiry,
          sport: sport,
        },
        "PUT"
      );

      if (!result.error) {
        showToast(globalDispatch, "Updated");
        navigate("/admin/suggested-questions");
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error) {
      console.log("Error", error);
      setError("name", {
        type: "manual",
        message: error.message,
      });
    }
  };
  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "suggested-questions",
      },
    });
  }, []);
  // console.log("log", inputFields);

  return (
    <div className=" shadow-md rounded   mx-auto p-5">
      <BackButton />
      <h4 className="text-2xl font-medium">Edit Suggested Questions</h4>
      <form className=" w-full max-w-lg" onSubmit={handleSubmit(onSubmit)}>
        <div className="mb-4  ">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="suggested_questions"
          >
            Suggested Question
          </label>
          <input
            placeholder="Suggested Question"
            {...register("suggested_questions")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
              errors.name?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-red-500 text-xs italic">{errors.name?.message}</p>
        </div>
        <div className="mb-4  ">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="suggested_questions"
          >
            Priority
          </label>
          <input
            type="checkbox"
            placeholder="priority"
            {...register("priority")}
            className={`shadow appearance-none border rounded  py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
              errors.priority?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-red-500 text-xs italic">
            {errors.priority?.message}
          </p>
        </div>
        <div className="mb-4  ">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="suggested_questions"
          >
            Priority Expiry
          </label>
          <input
            type="date"
            placeholder="priority"
            {...register("priority_expiry")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
              errors.priority?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-red-500 text-xs italic">
            {errors.priority?.message}
          </p>
        </div>
        <div className="mb-4  ">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="sport"
          >
            Sport
          </label>
          <SuggestedSelectSport
            setSport={setSport}
            sport={sport}
            title={false}
            className={" !bg-white !text-black shadow appearance-none border rounded w-full  text-gray-700 leading-tight focus:outline-none focus:shadow-outline "}
          />
         
          <p className="text-red-500 text-xs italic">{errors.name?.message}</p>
        </div>

        <button
          type="submit"
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Submit
        </button>
      </form>
    </div>
  );
};

export default EditAdminSuggestedQuestions;
