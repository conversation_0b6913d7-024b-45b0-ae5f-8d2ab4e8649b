import React, { useState } from "react";
import { AuthContext } from "Src/authContext";
import { GlobalContext, showToast } from "Src/globalContext";
import {
  Routes,
  Route,
  useParams,
  useLocation,
  useNavigate,
} from "react-router-dom";
import Spinner from "Components/Spinner";
import MkdSDK from "Utils/MkdSDK";

let sdk = new MkdSDK();

const UserOrderPage = () => {
  const { state,dispatch } = React.useContext(AuthContext);
  const { dispatch: GlobalDispatch } = React.useContext(GlobalContext);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [verified, setVerified] = useState(false);

  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const redirect_uri = searchParams.get("redirect_uri");
  const prevRoute = location?.state?.from?.pathname;
  const navigate = useNavigate();

  let { invoice_id } = useParams();
  // console.log("invoice", invoice_id);

  React.useEffect(() => {
    const verify = async () => {
      try {
        setSubmitLoading(true);
        const result = await sdk.verifypayment(invoice_id);
        if (!result.error) {
          setVerified(true);
          // dispatch({
          //   type: "LOGIN",
          //   payload: result,
          // });

          showToast(GlobalDispatch, " Payment Succesfull ", 4000, "success");
          localStorage.getItem("newUser")? navigate(prevRoute ?? "/user/privacy"): navigate(prevRoute ?? "/user/chat")
        } else {
          setSubmitLoading(false);
          // if (result.validation) {
          //   const keys = Object.keys(result.validation);
          //   for (let i = 0; i < keys.length; i++) {
          //     const field = keys[i];
          //     setError(field, {
          //       type: "manual",
          //       message: result.validation[field],
          //     });
          //   }
          // }
        }
      } catch (error) {
        setSubmitLoading(false);
        console.log("Error", error);
        // setError("payment", {
        //   type: "manual",
        //   message: error.response?.data.message
        //     ? error.response.data.message
        //     : error.message,
        // });
      }
    };

    verify();

    // const verify = async () => {
    //   const result = await sdk.verifypayment(invoice_id);

    //   console.log(result);
    //   navigate(redirect_uri ?? "/user/chat");

    // };

    // GlobalDispatch({
    //   type: "SETPATH",
    //   payload: {
    //     path: "user",
    //   },
    // });
  }, []);
  return (
    <>
      <div className="w-full flex justify-center items-center text-7xl h-screen text-gray-700 ">
        {/* Loading..... */}
        {submitLoading && !verified ? <Spinner /> : null}
        {submitLoading && verified ? (
          <div className="flex justify-center items-center h-screen">
            <div className="text-green-500 text-6xl">&#x2713; Successful</div>
          </div>
        ) : !submitLoading && !verified ? (
          <div className="flex justify-center items-center h-screen">
            <div className="text-red-500 text-6xl">&#x2716; Error</div>
          </div>
        ) : null}
      </div>
    </>
  );
};

export default UserOrderPage;
