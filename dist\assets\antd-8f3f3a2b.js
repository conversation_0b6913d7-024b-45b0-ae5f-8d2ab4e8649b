import"./vendor-b0222800.js";import"./@uppy/dashboard-54af8551.js";var f="-ms-",U="-moz-",e="-webkit-",ar="comm",v="rule",l="decl",ur="@import",er="@keyframes",br="@layer",tr=Math.abs,rr=String.fromCharCode,Q=Object.assign;function wr(r,s){return u(r,0)^45?(((s<<2^u(r,0))<<2^u(r,1))<<2^u(r,2))<<2^u(r,3):0}function ir(r){return r.trim()}function z(r,s){return(r=s.exec(r))?r[0]:r}function n(r,s,c){return r.replace(s,c)}function W(r,s,c){return r.indexOf(s,c)}function u(r,s){return r.charCodeAt(s)|0}function Y(r,s,c){return r.slice(s,c)}function O(r){return r.length}function fr(r){return r.length}function P(r,s){return s.push(r),r}function hr(r,s){return r.map(s).join("")}function sr(r,s){return r.filter(function(c){return!z(c,s)})}var q=1,B=1,or=0,g=0,o=0,D="";function G(r,s,c,a,t,w,k,p){return{value:r,root:s,parent:c,type:a,props:t,children:w,line:q,column:B,length:k,return:"",siblings:p}}function S(r,s){return Q(G("",null,null,"",null,null,0,r.siblings),r,{length:-r.length},s)}function N(r){for(;r.root;)r=S(r.root,{children:[r]});P(r,r.siblings)}function $r(){return o}function gr(){return o=g>0?u(D,--g):0,B--,o===10&&(B=1,q--),o}function E(){return o=g<or?u(D,g++):0,B++,o===10&&(B=1,q++),o}function L(){return u(D,g)}function Z(){return g}function H(r,s){return Y(D,r,s)}function V(r){switch(r){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function kr(r){return q=B=1,or=O(D=r),g=0,[]}function dr(r){return D="",r}function J(r){return ir(H(g-1,X(r===91?r+2:r===40?r+1:r)))}function xr(r){for(;(o=L())&&o<33;)E();return V(r)>2||V(o)>3?"":" "}function Er(r,s){for(;--s&&E()&&!(o<48||o>102||o>57&&o<65||o>70&&o<97););return H(r,Z()+(s<6&&L()==32&&E()==32))}function X(r){for(;E();)switch(o){case r:return g;case 34:case 39:r!==34&&r!==39&&X(o);break;case 40:r===41&&X(r);break;case 92:E();break}return g}function Mr(r,s){for(;E()&&r+o!==47+10;)if(r+o===42+42&&L()===47)break;return"/*"+H(s,g-1)+"*"+rr(r===47?r:E())}function Or(r){for(;!V(L());)E();return H(r,g)}function Cr(r){return dr(_("",null,null,null,[""],r=kr(r),0,[0],r))}function _(r,s,c,a,t,w,k,p,d){for(var x=0,A=0,h=k,K=0,C=0,j=0,$=1,F=1,R=1,b=0,T="",I=t,m=w,M=a,i=T;F;)switch(j=b,b=E()){case 40:if(j!=108&&u(i,h-1)==58){W(i+=n(J(b),"&","&\f"),"&\f",tr(x?p[x-1]:0))!=-1&&(R=-1);break}case 34:case 39:case 91:i+=J(b);break;case 9:case 10:case 13:case 32:i+=xr(j);break;case 92:i+=Er(Z()-1,7);continue;case 47:switch(L()){case 42:case 47:P(Rr(Mr(E(),Z()),s,c,d),d);break;default:i+="/"}break;case 123*$:p[x++]=O(i)*R;case 125*$:case 59:case 0:switch(b){case 0:case 125:F=0;case 59+A:R==-1&&(i=n(i,/\f/g,"")),C>0&&O(i)-h&&P(C>32?nr(i+";",a,c,h-1,d):nr(n(i," ","")+";",a,c,h-2,d),d);break;case 59:i+=";";default:if(P(M=cr(i,s,c,x,A,t,p,T,I=[],m=[],h,w),w),b===123)if(A===0)_(i,s,M,M,I,w,h,p,m);else switch(K===99&&u(i,3)===110?100:K){case 100:case 108:case 109:case 115:_(r,M,M,a&&P(cr(r,M,M,0,0,t,p,T,t,I=[],h,m),m),t,m,h,p,a?I:m);break;default:_(i,M,M,M,[""],m,0,p,m)}}x=A=C=0,$=R=1,T=i="",h=k;break;case 58:h=1+O(i),C=j;default:if($<1){if(b==123)--$;else if(b==125&&$++==0&&gr()==125)continue}switch(i+=rr(b),b*$){case 38:R=A>0?1:(i+="\f",-1);break;case 44:p[x++]=(O(i)-1)*R,R=1;break;case 64:L()===45&&(i+=J(E())),K=L(),A=h=O(T=i+=Or(Z())),b++;break;case 45:j===45&&O(i)==2&&($=0)}}return w}function cr(r,s,c,a,t,w,k,p,d,x,A,h){for(var K=t-1,C=t===0?w:[""],j=fr(C),$=0,F=0,R=0;$<a;++$)for(var b=0,T=Y(r,K+1,K=tr(F=k[$])),I=r;b<j;++b)(I=ir(F>0?C[b]+" "+T:n(T,/&\f/g,C[b])))&&(d[R++]=I);return G(r,s,c,t===0?v:p,d,x,A,h)}function Rr(r,s,c,a){return G(r,s,c,ar,rr($r()),Y(r,2,-2),0,a)}function nr(r,s,c,a,t){return G(r,s,c,l,Y(r,0,a),Y(r,a+1,-1),a,t)}function pr(r,s,c){switch(wr(r,s)){case 5103:return e+"print-"+r+r;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return e+r+r;case 4789:return U+r+r;case 5349:case 4246:case 4810:case 6968:case 2756:return e+r+U+r+f+r+r;case 5936:switch(u(r,s+11)){case 114:return e+r+f+n(r,/[svh]\w+-[tblr]{2}/,"tb")+r;case 108:return e+r+f+n(r,/[svh]\w+-[tblr]{2}/,"tb-rl")+r;case 45:return e+r+f+n(r,/[svh]\w+-[tblr]{2}/,"lr")+r}case 6828:case 4268:case 2903:return e+r+f+r+r;case 6165:return e+r+f+"flex-"+r+r;case 5187:return e+r+n(r,/(\w+).+(:[^]+)/,e+"box-$1$2"+f+"flex-$1$2")+r;case 5443:return e+r+f+"flex-item-"+n(r,/flex-|-self/g,"")+(z(r,/flex-|baseline/)?"":f+"grid-row-"+n(r,/flex-|-self/g,""))+r;case 4675:return e+r+f+"flex-line-pack"+n(r,/align-content|flex-|-self/g,"")+r;case 5548:return e+r+f+n(r,"shrink","negative")+r;case 5292:return e+r+f+n(r,"basis","preferred-size")+r;case 6060:return e+"box-"+n(r,"-grow","")+e+r+f+n(r,"grow","positive")+r;case 4554:return e+n(r,/([^-])(transform)/g,"$1"+e+"$2")+r;case 6187:return n(n(n(r,/(zoom-|grab)/,e+"$1"),/(image-set)/,e+"$1"),r,"")+r;case 5495:case 3959:return n(r,/(image-set\([^]*)/,e+"$1$`$1");case 4968:return n(n(r,/(.+:)(flex-)?(.*)/,e+"box-pack:$3"+f+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+e+r+r;case 4200:if(!z(r,/flex-|baseline/))return f+"grid-column-align"+Y(r,s)+r;break;case 2592:case 3360:return f+n(r,"template-","")+r;case 4384:case 3616:return c&&c.some(function(a,t){return s=t,z(a.props,/grid-\w+-end/)})?~W(r+(c=c[s].value),"span",0)?r:f+n(r,"-start","")+r+f+"grid-row-span:"+(~W(c,"span",0)?z(c,/\d+/):+z(c,/\d+/)-+z(r,/\d+/))+";":f+n(r,"-start","")+r;case 4896:case 4128:return c&&c.some(function(a){return z(a.props,/grid-\w+-start/)})?r:f+n(n(r,"-end","-span"),"span ","")+r;case 4095:case 3583:case 4068:case 2532:return n(r,/(.+)-inline(.+)/,e+"$1$2")+r;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(O(r)-1-s>6)switch(u(r,s+1)){case 109:if(u(r,s+4)!==45)break;case 102:return n(r,/(.+:)(.+)-([^]+)/,"$1"+e+"$2-$3$1"+U+(u(r,s+3)==108?"$3":"$2-$3"))+r;case 115:return~W(r,"stretch",0)?pr(n(r,"stretch","fill-available"),s,c)+r:r}break;case 5152:case 5920:return n(r,/(.+?):(\d+)(\s*\/\s*(span)?\s*(\d+))?(.*)/,function(a,t,w,k,p,d,x){return f+t+":"+w+x+(k?f+t+"-span:"+(p?d:+d-+w)+x:"")+r});case 4949:if(u(r,s+6)===121)return n(r,":",":"+e)+r;break;case 6444:switch(u(r,u(r,14)===45?18:11)){case 120:return n(r,/(.+:)([^;\s!]+)(;|(\s+)?!.+)?/,"$1"+e+(u(r,14)===45?"inline-":"")+"box$3$1"+e+"$2$3$1"+f+"$2box$3")+r;case 100:return n(r,":",":"+f)+r}break;case 5719:case 2647:case 2135:case 3927:case 2391:return n(r,"scroll-","scroll-snap-")+r}return r}function y(r,s){for(var c="",a=0;a<r.length;a++)c+=s(r[a],a,r,s)||"";return c}function Tr(r,s,c,a){switch(r.type){case br:if(r.children.length)break;case ur:case l:return r.return=r.return||r.value;case ar:return"";case er:return r.return=r.value+"{"+y(r.children,a)+"}";case v:if(!O(r.value=r.props.join(",")))return""}return O(c=y(r.children,a))?r.return=r.value+"{"+c+"}":""}function mr(r){var s=fr(r);return function(c,a,t,w){for(var k="",p=0;p<s;p++)k+=r[p](c,a,t,w)||"";return k}}function Sr(r){return function(s){s.root||(s=s.return)&&r(s)}}function jr(r,s,c,a){if(r.length>-1&&!r.return)switch(r.type){case l:r.return=pr(r.value,r.length,c);return;case er:return y([S(r,{value:n(r.value,"@","@"+e)})],a);case v:if(r.length)return hr(c=r.props,function(t){switch(z(t,a=/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":N(S(r,{props:[n(t,/:(read-\w+)/,":"+U+"$1")]})),N(S(r,{props:[t]})),Q(r,{props:sr(c,a)});break;case"::placeholder":N(S(r,{props:[n(t,/:(plac\w+)/,":"+e+"input-$1")]})),N(S(r,{props:[n(t,/:(plac\w+)/,":"+U+"$1")]})),N(S(r,{props:[n(t,/:(plac\w+)/,f+"input-$1")]})),N(S(r,{props:[t]})),Q(r,{props:sr(c,a)});break}return""})}}export{v as R,y as a,Cr as c,mr as m,jr as p,Sr as r,Tr as s};
