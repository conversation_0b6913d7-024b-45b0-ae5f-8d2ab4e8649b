import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "../utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { AuthContext, tokenExpireError } from "../authContext";
import { GlobalContext, showToast } from "../globalContext";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { isImage, empty, isVideo, isPdf } from "../utils/utils";
import Papa from "papaparse";
import { BackButton } from "Components/BackButton";

let sdk = new MkdSDK();

const AddAdminDynamicHeaderPage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const schema = yup
    .object({
      quiz: yup.string(),
    })
    .required();

  const [fileObj, setFileObj] = React.useState({});
  const [uploadStatus, setUploadStatus] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [data, setData] = useState([]);

  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  // ADDING NEW ATTRIBUTES..................................STARTS
  const [inputFields, setInputFields] = useState([""]);

  const handleChange = (index, event) => {
    const values = [...inputFields];
    values[index] = event.target.value;
    setInputFields(values);
  };

  const handleAddFields = () => {
    setInputFields([...inputFields, ""]);
  };

  const handleRemoveFields = (index) => {
    const values = [...inputFields];
    values.splice(index, 1);
    setInputFields(values);
  };

  

  // ADDING NEW ATTRIBUTES..................................ENDS


  const onSubmit = async (_data) => {
    try {
      for (let item in fileObj) {
        let formData = new FormData();
        formData.append("file", fileObj[item].file);
        let uploadResult = await sdk.uploadImage(formData);
        _data[item] = uploadResult.url;
      }

      sdk.setTable("dynamic_head");

      const result = await sdk.callRestAPI(
        {
          headlink: _data.headlink,
          name: _data.name,
        },
        "POST"
      );
      // console.log("result", result);
      if (!result.error) {
        showToast(globalDispatch, "Added");
        navigate("/admin/dynamic-headers");
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error) {
      console.log("Error", error);
      setError("name", {
        type: "manual",
        message: error.message,
      });
      tokenExpireError(dispatch, error.message);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "dynamicHeaders",
      },
    });
  }, []);

  const handleFileChange = (event) => {
    setIsDownloading(true);
    const file = event.target.files[0];

    if (file) {
      Papa.parse(file, {
        header: true,
        complete: (results) => {
          const filteredData = results.data.filter((row) => {
            // Filter out empty rows
            return Object.values(row).some(
              (value) => value !== null && value !== ""
            );
          });
          setData(filteredData);
          // sendToBackend(filteredData);
        },
        error: (error) => {
          console.error("Error parsing the file: ", error);
        },
      });
    }
    setIsDownloading(true);
  };

  const sendToBackend = async (parsedData) => {
    sdk.setTable("dynamic_head");
    setIsUploading(true);
    try {
      // const promises = parsedData.map((item) =>
      const promises = data.map((item) =>
        sdk.callRestAPI(
          {
            headlink: item?.headlink, 
          },
          "POST"
        )
      );
      await Promise.all(promises);
      showToast(globalDispatch, "Added");
      navigate("/admin/dynamic-headers");
      // setUploadStatus("Upload successful!");
    } catch (error) {
      console.error("Error uploading data:", error);
      setUploadStatus("Upload failed. Please try again.");
    }
    setIsUploading(false);
  };

  return (
    <div className=" shadow-md rounded  mx-auto p-5">
      <div className=" flex flex-col gap-4 mb-4">
        <BackButton />
        <h4 className="text-2xl font-medium">Add Head Link</h4>
      </div>
      <form className=" w-full max-w-lg" onSubmit={handleSubmit(onSubmit)}>
        <div className="mb-4  ">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="quiz"
          >
            Head Link
          </label>
          <textarea
            placeholder="Paste your <script> or <link> here"
            name=""
            id=""
            cols={10}
            rows={10}
            {...register("headlink")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
              errors.name?.message ? "border-red-500" : ""
            }`}
          ></textarea>
          <input
            placeholder="Name"
            {...register("name")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
              errors.name?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-red-500 text-xs italic">{errors.name?.message}</p>
        </div>

        {/* ADDING NEW ATTRIBUTES..................................ENDS */}

        <button
          type="submit"
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Submit
        </button>
      </form>

      {/* <div className="my-4  ">
        <div className=" space-y-2">
          <label
            className="block text-gray-700 text-base font-bold mb-"
            htmlFor="update_at"
          >
            Upload csv
          </label>
          <div>
            <input
              className=""
              type="file"
              accept=".csv"
              onChange={handleFileChange}
            />
            {isUploading && <p>uploading...</p>}
            {uploadStatus && <p>{uploadStatus}</p>}
          </div>
          <button
            type="button"
            disabled={!isDownloading}
            onClick={sendToBackend}
            className="disabled:bg-blue-200 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
          >
            Submit
          </button>
        </div>
      </div> */}
    </div>
  );
};

export default AddAdminDynamicHeaderPage;
