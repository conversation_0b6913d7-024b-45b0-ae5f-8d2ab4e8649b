import React from "react";
import "tailwindcss/tailwind.css"; // Import Tailwind CSS
import { Datepicker } from "flowbite-react";
// import { DatePicker } from "flowbite";
// import 'antd/dist/antd.css';
// import { Select } from "antd";

import {Select, SelectItem, Avatar} from "@nextui-org/react";
// import {users} from "./data";

// import Select from 'react-select';
const users = [
  {
    id: 1,
    name: "<PERSON>",
    role: "CEO",
    team: "Management",
    status: "active",
    age: "29",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/male/1.png",
    email: "<EMAIL>",
  },
  {
    id: 2,
    name: "<PERSON><PERSON>",
    role: "Tech Lead",
    team: "Development",
    status: "paused",
    age: "25",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/female/1.png",
    email: "<EMAIL>",
  },
]
const Test = () => {
  const food = ["B", "c", "d"];
  // const users = ["B", "c", "d"];
  // const options = [
  //   { value: 'option1', label: 'Option 1' },
  //   { value: 'option2', label: 'Option 2' },
  //   // ...other options
  // ];
  // const handleChange = selectedOption => {
  //   // Handle the selected option
  //   console.log(`Selected:`, selectedOption);
  // };

  return (
    <div>
      hey






    {/* <Select
      items={users}
      label="Assigned to"
      className="max-w-xs"
      variant="bordered"
      classNames={{
        label: "group-data-[filled=true]:-translate-y-5",
        trigger: "min-h-unit-16",
        listboxWrapper: "max-h-[400px]",
      }}
      listboxProps={{
        itemClasses: {
          base: [
            "rounded-md",
            "text-default-500",
            "transition-opacity",
            "data-[hover=true]:text-foreground",
            "data-[hover=true]:bg-default-100",
            "dark:data-[hover=true]:bg-default-50",
            "data-[selectable=true]:focus:bg-default-50",
            "data-[pressed=true]:opacity-70",
            "data-[focus-visible=true]:ring-default-500",
          ],
        },
      }}
      popoverProps={{
        classNames: {
          base: "before:bg-default-200",
          content: "p-0 border-small border-divider bg-background",
        },
      }}
      renderValue={(items,i) => {
        return items.map((item) => (
          <div key={i} className="flex items-center gap-2">
            <Avatar
              alt={item.data.name}
              className="flex-shrink-0"
              size="sm"
              src={item.data.avatar}
            />
            <div className="flex flex-col">
              <span>{item.data.name}</span>
              <span className="text-default-500 text-tiny">({item.data.email})</span>
            </div>
          </div>
        ));
      }}
    >
      {(user) => (
        <SelectItem key={item.key}  textValue={user.name}>
          <div className="flex gap-2 items-center">
            <Avatar alt={user.name} className="flex-shrink-0" size="sm" src={user.avatar} />
            <div className="flex flex-col">
              <span className="text-small">{user.name}</span>
              <span className="text-tiny text-default-400">{user.email}</span>
            </div>
          </div>
        </SelectItem>
      )}
    </Select> */}





      .................................
      {/* <Select
        placeholder="select"
        style={{ width: "50%" }}
        className="selectclass"
      >
        {food.map((foo, i) => (
          <Select.Option key={i} value={foo}>
            {foo}
          </Select.Option>
        ))}
      </Select> */}
      {/* <h1>Select an option:</h1>
      <Select
        options={options}
        onChange={handleChange}
        classNames={{
          control: (state) =>
            state.isFocused ? 'border-red-600' : 'border-grey-300',
          option: (state) =>
            state.isFocused ? 'bg-red-600' : 'bg-grey-300',
        }}
        // You can add more props and styles here as needed
      />
      <Datepicker title="Flowbite Datepicker" /> */}
    </div>
  );
  // return (
  //   <div className="mt-8">
  //     <h1 className="text-2xl font-bold mb-4">Flowbite Date Picker</h1>
  //     <Datepicker
  //       className="bg-red-500 text-darkred hover:bg-red-300" // Apply custom Tailwind CSS classes
  //       placeholder="Select a date"
  //       popupClassName=" bg-red-500 text-white"
  //     />
  //   </div>
  // );
};

export default Test;
