import{y as o,p as tt,U as Ne,g as $i,b as se,_ as Hi,$ as Q,B as Zt,i as Vi,l as qi,a as Ee}from"./core-f5368a8f.js";import{g as ei}from"../vendor-b0222800.js";import{d as ji,n as ti}from"./aws-s3-9d3fc29b.js";function gt(i,e,t,s){return t===0||i===e?i:s===0?e:i+(e-i)*2**(-s/t)}const $={STATE_ERROR:"error",STATE_WAITING:"waiting",STATE_PREPROCESSING:"preprocessing",STATE_UPLOADING:"uploading",STATE_POSTPROCESSING:"postprocessing",STATE_COMPLETE:"complete"};var ii={exports:{}};/*!
	Copyright (c) 2018 <PERSON>.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/(function(i){(function(){var e={}.hasOwnProperty;function t(){for(var n="",r=0;r<arguments.length;r++){var l=arguments[r];l&&(n=a(n,s(l)))}return n}function s(n){if(typeof n=="string"||typeof n=="number")return n;if(typeof n!="object")return"";if(Array.isArray(n))return t.apply(null,n);if(n.toString!==Object.prototype.toString&&!n.toString.toString().includes("[native code]"))return n.toString();var r="";for(var l in n)e.call(n,l)&&n[l]&&(r=a(r,l));return r}function a(n,r){return r?n?n+" "+r:n+r:n}i.exports?(t.default=t,i.exports=t):window.classNames=t})()})(ii);var Wi=ii.exports;const L=ei(Wi);function Me(i){const e=[];let t="indeterminate",s;for(const{progress:n}of Object.values(i)){const{preprocess:r,postprocess:l}=n;s==null&&(r||l)&&({mode:t,message:s}=r||l),(r==null?void 0:r.mode)==="determinate"&&e.push(r.value),(l==null?void 0:l.mode)==="determinate"&&e.push(l.value)}const a=e.reduce((n,r)=>n+r/e.length,0);return{mode:t,message:s,value:a}}function Gi(i){const e=Math.floor(i/3600)%24,t=Math.floor(i/60)%60,s=Math.floor(i%60);return{hours:e,minutes:t,seconds:s}}function Ki(i){const e=Gi(i),t=e.hours===0?"":`${e.hours}h`,s=e.minutes===0?"":`${e.hours===0?e.minutes:` ${e.minutes.toString(10).padStart(2,"0")}`}m`,a=e.hours!==0?"":`${e.minutes===0?e.seconds:` ${e.seconds.toString(10).padStart(2,"0")}`}s`;return`${t}${s}${a}`}const Xi="·",yt=()=>` ${Xi} `;function Yi(i){const{newFiles:e,isUploadStarted:t,recoveredState:s,i18n:a,uploadState:n,isSomeGhost:r,startUpload:l}=i,u=L("uppy-u-reset","uppy-c-btn","uppy-StatusBar-actionBtn","uppy-StatusBar-actionBtn--upload",{"uppy-c-btn-primary":n===$.STATE_WAITING},{"uppy-StatusBar-actionBtn--disabled":r}),d=e&&t&&!s?a("uploadXNewFiles",{smart_count:e}):a("uploadXFiles",{smart_count:e});return o("button",{type:"button",className:u,"aria-label":a("uploadXFiles",{smart_count:e}),onClick:l,disabled:r,"data-uppy-super-focusable":!0},d)}function Qi(i){const{i18n:e,uppy:t}=i;return o("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-StatusBar-actionBtn uppy-StatusBar-actionBtn--retry","aria-label":e("retryUpload"),onClick:()=>t.retryAll().catch(()=>{}),"data-uppy-super-focusable":!0,"data-cy":"retry"},o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"8",height:"10",viewBox:"0 0 8 10"},o("path",{d:"M4 2.408a2.75 2.75 0 1 0 2.75 2.75.626.626 0 0 1 1.25.018v.023a4 4 0 1 1-4-4.041V.25a.25.25 0 0 1 .389-.208l2.299 1.533a.25.25 0 0 1 0 .416l-2.3 1.533A.25.25 0 0 1 4 3.316v-.908z"})),e("retry"))}function Ji(i){const{i18n:e,uppy:t}=i;return o("button",{type:"button",className:"uppy-u-reset uppy-StatusBar-actionCircleBtn",title:e("cancel"),"aria-label":e("cancel"),onClick:()=>t.cancelAll(),"data-cy":"cancel","data-uppy-super-focusable":!0},o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"16",height:"16",viewBox:"0 0 16 16"},o("g",{fill:"none",fillRule:"evenodd"},o("circle",{fill:"#888",cx:"8",cy:"8",r:"8"}),o("path",{fill:"#FFF",d:"M9.283 8l2.567 2.567-1.283 1.283L8 9.283 5.433 11.85 4.15 10.567 6.717 8 4.15 5.433 5.433 4.15 8 6.717l2.567-2.567 1.283 1.283z"}))))}function Zi(i){const{isAllPaused:e,i18n:t,isAllComplete:s,resumableUploads:a,uppy:n}=i,r=t(e?"resume":"pause");function l(){if(!s){if(!a){n.cancelAll();return}if(e){n.resumeAll();return}n.pauseAll()}}return o("button",{title:r,"aria-label":r,className:"uppy-u-reset uppy-StatusBar-actionCircleBtn",type:"button",onClick:l,"data-cy":"togglePauseResume","data-uppy-super-focusable":!0},o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"16",height:"16",viewBox:"0 0 16 16"},o("g",{fill:"none",fillRule:"evenodd"},o("circle",{fill:"#888",cx:"8",cy:"8",r:"8"}),o("path",{fill:"#FFF",d:e?"M6 4.25L11.5 8 6 11.75z":"M5 4.5h2v7H5v-7zm4 0h2v7H9v-7z"}))))}function es(i){const{i18n:e,doneButtonHandler:t}=i;return o("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-StatusBar-actionBtn uppy-StatusBar-actionBtn--done",onClick:t,"data-uppy-super-focusable":!0},e("done"))}function si(){return o("svg",{className:"uppy-StatusBar-spinner","aria-hidden":"true",focusable:"false",width:"14",height:"14"},o("path",{d:"M13.983 6.547c-.12-2.509-1.64-4.893-3.939-5.936-2.48-1.127-5.488-.656-7.556 1.094C.524 3.367-.398 6.048.162 8.562c.556 2.495 2.46 4.52 4.94 5.183 2.932.784 5.61-.602 7.256-3.015-1.493 1.993-3.745 3.309-6.298 2.868-2.514-.434-4.578-2.349-5.153-4.84a6.226 6.226 0 0 1 2.98-6.778C6.34.586 9.74 1.1 11.373 3.493c.407.596.693 1.282.842 1.988.127.598.073 1.197.161 1.794.078.525.543 1.257 1.15.864.525-.341.49-1.05.456-1.592-.007-.15.02.3 0 0",fillRule:"evenodd"}))}function ts(i){const{progress:e}=i,{value:t,mode:s,message:a}=e,n="·";return o("div",{className:"uppy-StatusBar-content"},o(si,null),s==="determinate"?`${Math.round(t*100)}% ${n} `:"",a)}function is(i){const{numUploads:e,complete:t,totalUploadedSize:s,totalSize:a,totalETA:n,i18n:r}=i,l=e>1;return o("div",{className:"uppy-StatusBar-statusSecondary"},l&&r("filesUploadedOfTotal",{complete:t,smart_count:e}),o("span",{className:"uppy-StatusBar-additionalInfo"},l&&yt(),r("dataUploadedOfTotal",{complete:tt(s),total:tt(a)}),yt(),r("xTimeLeft",{time:Ki(n)})))}function ni(i){const{i18n:e,complete:t,numUploads:s}=i;return o("div",{className:"uppy-StatusBar-statusSecondary"},e("filesUploadedOfTotal",{complete:t,smart_count:s}))}function ss(i){const{i18n:e,newFiles:t,startUpload:s}=i,a=L("uppy-u-reset","uppy-c-btn","uppy-StatusBar-actionBtn","uppy-StatusBar-actionBtn--uploadNewlyAdded");return o("div",{className:"uppy-StatusBar-statusSecondary"},o("div",{className:"uppy-StatusBar-statusSecondaryHint"},e("xMoreFilesAdded",{smart_count:t})),o("button",{type:"button",className:a,"aria-label":e("uploadXFiles",{smart_count:t}),onClick:s},e("upload")))}function ns(i){const{i18n:e,supportsUploadProgress:t,totalProgress:s,showProgressDetails:a,isUploadStarted:n,isAllComplete:r,isAllPaused:l,newFiles:u,numUploads:d,complete:h,totalUploadedSize:c,totalSize:p,totalETA:m,startUpload:g}=i,v=u&&n;if(!n||r)return null;const f=e(l?"paused":"uploading");function w(){return!l&&!v&&a?t?o(is,{numUploads:d,complete:h,totalUploadedSize:c,totalSize:p,totalETA:m,i18n:e}):o(ni,{i18n:e,complete:h,numUploads:d}):null}return o("div",{className:"uppy-StatusBar-content","aria-label":f,title:f},l?null:o(si,null),o("div",{className:"uppy-StatusBar-status"},o("div",{className:"uppy-StatusBar-statusPrimary"},t?`${f}: ${s}%`:f),w(),v?o(ss,{i18n:e,newFiles:u,startUpload:g}):null))}function as(i){const{i18n:e}=i;return o("div",{className:"uppy-StatusBar-content",role:"status",title:e("complete")},o("div",{className:"uppy-StatusBar-status"},o("div",{className:"uppy-StatusBar-statusPrimary"},o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-StatusBar-statusIndicator uppy-c-icon",width:"15",height:"11",viewBox:"0 0 15 11"},o("path",{d:"M.414 5.843L1.627 4.63l3.472 3.472L13.202 0l1.212 1.213L5.1 10.528z"})),e("complete"))))}function rs(i){const{error:e,i18n:t,complete:s,numUploads:a}=i;function n(){const r=`${t("uploadFailed")} 

 ${e}`;alert(r)}return o("div",{className:"uppy-StatusBar-content",title:t("uploadFailed")},o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-StatusBar-statusIndicator uppy-c-icon",width:"11",height:"11",viewBox:"0 0 11 11"},o("path",{d:"M4.278 5.5L0 1.222 1.222 0 5.5 4.278 9.778 0 11 1.222 6.722 5.5 11 9.778 9.778 11 5.5 6.722 1.222 11 0 9.778z"})),o("div",{className:"uppy-StatusBar-status"},o("div",{className:"uppy-StatusBar-statusPrimary"},t("uploadFailed"),o("button",{className:"uppy-u-reset uppy-StatusBar-details","aria-label":t("showErrorDetails"),"data-microtip-position":"top-right","data-microtip-size":"medium",onClick:n,type:"button"},"?")),o(ni,{i18n:t,complete:s,numUploads:a})))}const{STATE_ERROR:bt,STATE_WAITING:vt,STATE_PREPROCESSING:Le,STATE_UPLOADING:we,STATE_POSTPROCESSING:xe,STATE_COMPLETE:Fe}=$;function ai(i){const{newFiles:e,allowNewUpload:t,isUploadInProgress:s,isAllPaused:a,resumableUploads:n,error:r,hideUploadButton:l,hidePauseResumeButton:u,hideCancelButton:d,hideRetryButton:h,recoveredState:c,uploadState:p,totalProgress:m,files:g,supportsUploadProgress:v,hideAfterFinish:f,isSomeGhost:w,doneButtonHandler:S,isUploadStarted:y,i18n:F,startUpload:T,uppy:I,isAllComplete:A,showProgressDetails:E,numUploads:H,complete:ge,totalSize:Ie,totalETA:ye,totalUploadedSize:be}=i;function Z(){switch(p){case xe:case Le:{const ve=Me(g);return ve.mode==="determinate"?ve.value*100:m}case bt:return null;case we:return v?m:null;default:return m}}function G(){switch(p){case xe:case Le:{const{mode:ve}=Me(g);return ve==="indeterminate"}case we:return!v;default:return!1}}function Ue(){if(c)return!1;switch(p){case vt:return l||e===0;case Fe:return f;default:return!1}}const ae=Z(),Ni=Ue(),Re=ae??100,Ii=!r&&e&&!s&&!a&&t&&!l,Ui=!d&&p!==vt&&p!==Fe,Ri=n&&!u&&p===we,Mi=r&&!A&&!h,Li=S&&p===Fe,xi=L("uppy-StatusBar-progress",{"is-indeterminate":G()}),zi=L("uppy-StatusBar",`is-${p}`,{"has-ghosts":w});return o("div",{className:zi,"aria-hidden":Ni},o("div",{className:xi,style:{width:`${Re}%`},role:"progressbar","aria-label":`${Re}%`,"aria-valuetext":`${Re}%`,"aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":ae}),(()=>{switch(p){case Le:case xe:return o(ts,{progress:Me(g)});case Fe:return o(as,{i18n:F});case bt:return o(rs,{error:r,i18n:F,numUploads:H,complete:ge});case we:return o(ns,{i18n:F,supportsUploadProgress:v,totalProgress:m,showProgressDetails:E,isUploadStarted:y,isAllComplete:A,isAllPaused:a,newFiles:e,numUploads:H,complete:ge,totalUploadedSize:be,totalSize:Ie,totalETA:ye,startUpload:T});default:return null}})(),o("div",{className:"uppy-StatusBar-actions"},c||Ii?o(Yi,{newFiles:e,isUploadStarted:y,recoveredState:c,i18n:F,isSomeGhost:w,startUpload:T,uploadState:p}):null,Mi?o(Qi,{i18n:F,uppy:I}):null,Ri?o(Zi,{isAllPaused:a,i18n:F,isAllComplete:A,resumableUploads:n,uppy:I}):null,Ui?o(Ji,{i18n:F,uppy:I}):null,Li?o(es,{i18n:F,doneButtonHandler:S}):null))}ai.defaultProps={doneButtonHandler:void 0,hideAfterFinish:!1,hideCancelButton:!1,hidePauseResumeButton:!1,hideRetryButton:!1,hideUploadButton:void 0,showProgressDetails:void 0};const os={strings:{uploading:"Uploading",complete:"Complete",uploadFailed:"Upload failed",paused:"Paused",retry:"Retry",cancel:"Cancel",pause:"Pause",resume:"Resume",done:"Done",filesUploadedOfTotal:{0:"%{complete} of %{smart_count} file uploaded",1:"%{complete} of %{smart_count} files uploaded"},dataUploadedOfTotal:"%{complete} of %{total}",xTimeLeft:"%{time} left",uploadXFiles:{0:"Upload %{smart_count} file",1:"Upload %{smart_count} files"},uploadXNewFiles:{0:"Upload +%{smart_count} file",1:"Upload +%{smart_count} files"},upload:"Upload",retryUpload:"Retry upload",xMoreFilesAdded:{0:"%{smart_count} more file added",1:"%{smart_count} more files added"},showErrorDetails:"Show error details"}};function C(i,e){if(!Object.prototype.hasOwnProperty.call(i,e))throw new TypeError("attempted to use private field on non-instance");return i}var ls=0;function ne(i){return"__private_"+ls+++"_"+i}const ds={version:"3.3.0"},us=2e3,hs=2e3;function cs(i,e,t,s){if(i)return $.STATE_ERROR;if(e)return $.STATE_COMPLETE;if(t)return $.STATE_WAITING;let a=$.STATE_WAITING;const n=Object.keys(s);for(let r=0;r<n.length;r++){const{progress:l}=s[n[r]];if(l.uploadStarted&&!l.uploadComplete)return $.STATE_UPLOADING;l.preprocess&&(a=$.STATE_PREPROCESSING),l.postprocess&&a!==$.STATE_PREPROCESSING&&(a=$.STATE_POSTPROCESSING)}return a}const ps={target:"body",hideUploadButton:!1,hideRetryButton:!1,hidePauseResumeButton:!1,hideCancelButton:!1,showProgressDetails:!1,hideAfterFinish:!0,doneButtonHandler:null};var U=ne("lastUpdateTime"),R=ne("previousUploadedBytes"),j=ne("previousSpeed"),N=ne("previousETA"),ze=ne("computeSmoothETA"),re=ne("onUploadStart");let ri=class extends Ne{constructor(e,t){super(e,{...ps,...t}),Object.defineProperty(this,ze,{value:fs}),Object.defineProperty(this,U,{writable:!0,value:void 0}),Object.defineProperty(this,R,{writable:!0,value:void 0}),Object.defineProperty(this,j,{writable:!0,value:void 0}),Object.defineProperty(this,N,{writable:!0,value:void 0}),this.startUpload=()=>this.uppy.upload().catch(()=>{}),Object.defineProperty(this,re,{writable:!0,value:()=>{const{recoveredState:s}=this.uppy.getState();if(C(this,j)[j]=null,C(this,N)[N]=null,s){C(this,R)[R]=Object.values(s.files).reduce((a,n)=>{let{progress:r}=n;return a+r.bytesUploaded},0),this.uppy.emit("restore-confirmed");return}C(this,U)[U]=performance.now(),C(this,R)[R]=0}}),this.id=this.opts.id||"StatusBar",this.title="StatusBar",this.type="progressindicator",this.defaultLocale=os,this.i18nInit(),this.render=this.render.bind(this),this.install=this.install.bind(this)}render(e){const{capabilities:t,files:s,allowNewUpload:a,totalProgress:n,error:r,recoveredState:l}=e,{newFiles:u,startedFiles:d,completeFiles:h,isUploadStarted:c,isAllComplete:p,isAllErrored:m,isAllPaused:g,isUploadInProgress:v,isSomeGhost:f}=this.uppy.getObjectOfFilesPerState(),w=l?Object.values(s):u,S=!!t.resumableUploads,y=t.uploadProgress!==!1;let F=0,T=0;d.forEach(A=>{F+=A.progress.bytesTotal||0,T+=A.progress.bytesUploaded||0});const I=C(this,ze)[ze]({uploaded:T,total:F,remaining:F-T});return ai({error:r,uploadState:cs(r,p,l,e.files||{}),allowNewUpload:a,totalProgress:n,totalSize:F,totalUploadedSize:T,isAllComplete:!1,isAllPaused:g,isAllErrored:m,isUploadStarted:c,isUploadInProgress:v,isSomeGhost:f,recoveredState:l,complete:h.length,newFiles:w.length,numUploads:d.length,totalETA:I,files:s,i18n:this.i18n,uppy:this.uppy,startUpload:this.startUpload,doneButtonHandler:this.opts.doneButtonHandler,resumableUploads:S,supportsUploadProgress:y,showProgressDetails:this.opts.showProgressDetails,hideUploadButton:this.opts.hideUploadButton,hideRetryButton:this.opts.hideRetryButton,hidePauseResumeButton:this.opts.hidePauseResumeButton,hideCancelButton:this.opts.hideCancelButton,hideAfterFinish:this.opts.hideAfterFinish,isTargetDOMEl:this.isTargetDOMEl})}onMount(){const e=this.el;$i(e)||(e.dir="ltr")}install(){const{target:e}=this.opts;e&&this.mount(e,this),this.uppy.on("upload",C(this,re)[re]),C(this,U)[U]=performance.now(),C(this,R)[R]=this.uppy.getFiles().reduce((t,s)=>t+s.progress.bytesUploaded,0)}uninstall(){this.unmount(),this.uppy.off("upload",C(this,re)[re])}};function fs(i){var e,t;if(i.total===0||i.remaining===0)return 0;(t=(e=C(this,U))[U])!=null||(e[U]=performance.now());const s=performance.now()-C(this,U)[U];if(s===0){var a;return Math.round(((a=C(this,N)[N])!=null?a:0)/100)/10}const n=i.uploaded-C(this,R)[R];if(C(this,R)[R]=i.uploaded,n<=0){var r;return Math.round(((r=C(this,N)[N])!=null?r:0)/100)/10}const l=n/s,u=C(this,j)[j]==null?l:gt(l,C(this,j)[j],us,s);C(this,j)[j]=u;const d=i.remaining/u,h=Math.max(C(this,N)[N]-s,0),c=C(this,N)[N]==null?d:gt(d,h,hs,s);return C(this,N)[N]=c,C(this,U)[U]=performance.now(),Math.round(c/100)/10}ri.VERSION=ds.version;const wt=300;class ms extends se{constructor(){super(...arguments),this.ref=Hi()}componentWillEnter(e){this.ref.current.style.opacity="1",this.ref.current.style.transform="none",setTimeout(e,wt)}componentWillLeave(e){this.ref.current.style.opacity="0",this.ref.current.style.transform="translateY(350%)",setTimeout(e,wt)}render(){const{children:e}=this.props;return o("div",{className:"uppy-Informer-animated",ref:this.ref},e)}}function gs(i,e){return Object.assign(i,e)}function ys(i,e){var t;return(t=i==null?void 0:i.key)!=null?t:e}function bs(i,e){const t=i._ptgLinkedRefs||(i._ptgLinkedRefs={});return t[e]||(t[e]=s=>{i.refs[e]=s})}function oe(i){const e={};for(let t=0;t<i.length;t++)if(i[t]!=null){const s=ys(i[t],t.toString(36));e[s]=i[t]}return e}function vs(i,e){i=i||{},e=e||{};const t=r=>e.hasOwnProperty(r)?e[r]:i[r],s={};let a=[];for(const r in i)e.hasOwnProperty(r)?a.length&&(s[r]=a,a=[]):a.push(r);const n={};for(const r in e){if(s.hasOwnProperty(r))for(let l=0;l<s[r].length;l++){const u=s[r][l];n[s[r][l]]=t(u)}n[r]=t(r)}for(let r=0;r<a.length;r++)n[a[r]]=t(a[r]);return n}const ws=i=>i;class oi extends se{constructor(e,t){super(e,t),this.refs={},this.state={children:oe(Q(Q(this.props.children))||[])},this.performAppear=this.performAppear.bind(this),this.performEnter=this.performEnter.bind(this),this.performLeave=this.performLeave.bind(this)}componentWillMount(){this.currentlyTransitioningKeys={},this.keysToAbortLeave=[],this.keysToEnter=[],this.keysToLeave=[]}componentDidMount(){const e=this.state.children;for(const t in e)e[t]&&this.performAppear(t)}componentWillReceiveProps(e){const t=oe(Q(e.children)||[]),s=this.state.children;this.setState(n=>({children:vs(n.children,t)}));let a;for(a in t)if(t.hasOwnProperty(a)){const n=s&&s.hasOwnProperty(a);t[a]&&n&&this.currentlyTransitioningKeys[a]?(this.keysToEnter.push(a),this.keysToAbortLeave.push(a)):t[a]&&!n&&!this.currentlyTransitioningKeys[a]&&this.keysToEnter.push(a)}for(a in s)if(s.hasOwnProperty(a)){const n=t&&t.hasOwnProperty(a);s[a]&&!n&&!this.currentlyTransitioningKeys[a]&&this.keysToLeave.push(a)}}componentDidUpdate(){const{keysToEnter:e}=this;this.keysToEnter=[],e.forEach(this.performEnter);const{keysToLeave:t}=this;this.keysToLeave=[],t.forEach(this.performLeave)}_finishAbort(e){const t=this.keysToAbortLeave.indexOf(e);t!==-1&&this.keysToAbortLeave.splice(t,1)}performAppear(e){this.currentlyTransitioningKeys[e]=!0;const t=this.refs[e];t!=null&&t.componentWillAppear?t.componentWillAppear(this._handleDoneAppearing.bind(this,e)):this._handleDoneAppearing(e)}_handleDoneAppearing(e){const t=this.refs[e];t!=null&&t.componentDidAppear&&t.componentDidAppear(),delete this.currentlyTransitioningKeys[e],this._finishAbort(e);const s=oe(Q(this.props.children)||[]);(!s||!s.hasOwnProperty(e))&&this.performLeave(e)}performEnter(e){this.currentlyTransitioningKeys[e]=!0;const t=this.refs[e];t!=null&&t.componentWillEnter?t.componentWillEnter(this._handleDoneEntering.bind(this,e)):this._handleDoneEntering(e)}_handleDoneEntering(e){const t=this.refs[e];t!=null&&t.componentDidEnter&&t.componentDidEnter(),delete this.currentlyTransitioningKeys[e],this._finishAbort(e);const s=oe(Q(this.props.children)||[]);(!s||!s.hasOwnProperty(e))&&this.performLeave(e)}performLeave(e){if(this.keysToAbortLeave.indexOf(e)!==-1)return;this.currentlyTransitioningKeys[e]=!0;const s=this.refs[e];s!=null&&s.componentWillLeave?s.componentWillLeave(this._handleDoneLeaving.bind(this,e)):this._handleDoneLeaving(e)}_handleDoneLeaving(e){if(this.keysToAbortLeave.indexOf(e)!==-1)return;const s=this.refs[e];s!=null&&s.componentDidLeave&&s.componentDidLeave(),delete this.currentlyTransitioningKeys[e];const a=oe(Q(this.props.children)||[]);if(a&&a.hasOwnProperty(e))this.performEnter(e);else{const n=gs({},this.state.children);delete n[e],this.setState({children:n})}}render(e,t){let{childFactory:s,transitionLeave:a,transitionName:n,transitionAppear:r,transitionEnter:l,transitionLeaveTimeout:u,transitionEnterTimeout:d,transitionAppearTimeout:h,component:c,...p}=e,{children:m}=t;const g=Object.entries(m).map(v=>{let[f,w]=v;if(!w)return;const S=bs(this,f);return Zt(s(w),{ref:S,key:f})}).filter(Boolean);return o(c,p,g)}}oi.defaultProps={component:"span",childFactory:ws};const Fs={version:"3.1.0"};class li extends Ne{constructor(e,t){super(e,t),this.render=s=>o("div",{className:"uppy uppy-Informer"},o(oi,null,s.info.map(a=>o(ms,{key:a.message},o("p",{role:"alert"},a.message," ",a.details&&o("span",{"aria-label":a.details,"data-microtip-position":"top-left","data-microtip-size":"medium",role:"tooltip",onClick:()=>alert(`${a.message} 

 ${a.details}`)},"?")))))),this.type="progressindicator",this.id=this.opts.id||"Informer",this.title="Informer"}install(){const{target:e}=this.opts;e&&this.mount(e,this)}}li.VERSION=Fs.version;const Ps=/^data:([^/]+\/[^,;]+(?:[^,]*?))(;base64)?,([\s\S]*)$/;function Ss(i,e,t){var s,a;const n=Ps.exec(i),r=(s=(a=e.mimeType)!=null?a:n==null?void 0:n[1])!=null?s:"plain/text";let l;if((n==null?void 0:n[2])!=null){const u=atob(decodeURIComponent(n[3])),d=new Uint8Array(u.length);for(let h=0;h<u.length;h++)d[h]=u.charCodeAt(h);l=[d]}else(n==null?void 0:n[3])!=null&&(l=[decodeURIComponent(n[3])]);return t?new File(l,e.name||"",{type:r}):new Blob(l,{type:r})}function Ft(i){return i.startsWith("blob:")}function Pt(i){return i?/^[^/]+\/(jpe?g|gif|png|svg|svg\+xml|bmp|webp|avif)$/.test(i):!1}function b(i,e,t){return e in i?Object.defineProperty(i,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):i[e]=t,i}var di=typeof self<"u"?self:global;const pe=typeof navigator<"u",Cs=pe&&typeof HTMLImageElement>"u",St=!(typeof global>"u"||typeof process>"u"||!process.versions||!process.versions.node),ui=di.Buffer,hi=!!ui,Ts=i=>i!==void 0;function ci(i){return i===void 0||(i instanceof Map?i.size===0:Object.values(i).filter(Ts).length===0)}function O(i){let e=new Error(i);throw delete e.stack,e}function Ct(i){let e=function(t){let s=0;return t.ifd0.enabled&&(s+=1024),t.exif.enabled&&(s+=2048),t.makerNote&&(s+=2048),t.userComment&&(s+=1024),t.gps.enabled&&(s+=512),t.interop.enabled&&(s+=100),t.ifd1.enabled&&(s+=1024),s+2048}(i);return i.jfif.enabled&&(e+=50),i.xmp.enabled&&(e+=2e4),i.iptc.enabled&&(e+=14e3),i.icc.enabled&&(e+=6e3),e}const $e=i=>String.fromCharCode.apply(null,i),Tt=typeof TextDecoder<"u"?new TextDecoder("utf-8"):void 0;let fe=class te{static from(e,t){return e instanceof this&&e.le===t?e:new te(e,void 0,void 0,t)}constructor(e,t=0,s,a){if(typeof a=="boolean"&&(this.le=a),Array.isArray(e)&&(e=new Uint8Array(e)),e===0)this.byteOffset=0,this.byteLength=0;else if(e instanceof ArrayBuffer){s===void 0&&(s=e.byteLength-t);let n=new DataView(e,t,s);this._swapDataView(n)}else if(e instanceof Uint8Array||e instanceof DataView||e instanceof te){s===void 0&&(s=e.byteLength-t),(t+=e.byteOffset)+s>e.byteOffset+e.byteLength&&O("Creating view outside of available memory in ArrayBuffer");let n=new DataView(e.buffer,t,s);this._swapDataView(n)}else if(typeof e=="number"){let n=new DataView(new ArrayBuffer(e));this._swapDataView(n)}else O("Invalid input argument for BufferView: "+e)}_swapArrayBuffer(e){this._swapDataView(new DataView(e))}_swapBuffer(e){this._swapDataView(new DataView(e.buffer,e.byteOffset,e.byteLength))}_swapDataView(e){this.dataView=e,this.buffer=e.buffer,this.byteOffset=e.byteOffset,this.byteLength=e.byteLength}_lengthToEnd(e){return this.byteLength-e}set(e,t,s=te){return e instanceof DataView||e instanceof te?e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength):e instanceof ArrayBuffer&&(e=new Uint8Array(e)),e instanceof Uint8Array||O("BufferView.set(): Invalid data argument."),this.toUint8().set(e,t),new s(this,t,e.byteLength)}subarray(e,t){return t=t||this._lengthToEnd(e),new te(this,e,t)}toUint8(){return new Uint8Array(this.buffer,this.byteOffset,this.byteLength)}getUint8Array(e,t){return new Uint8Array(this.buffer,this.byteOffset+e,t)}getString(e=0,t=this.byteLength){return a=this.getUint8Array(e,t),Tt?Tt.decode(a):hi?Buffer.from(a).toString("utf8"):decodeURIComponent(escape($e(a)));var a}getLatin1String(e=0,t=this.byteLength){let s=this.getUint8Array(e,t);return $e(s)}getUnicodeString(e=0,t=this.byteLength){const s=[];for(let a=0;a<t&&e+a<this.byteLength;a+=2)s.push(this.getUint16(e+a));return $e(s)}getInt8(e){return this.dataView.getInt8(e)}getUint8(e){return this.dataView.getUint8(e)}getInt16(e,t=this.le){return this.dataView.getInt16(e,t)}getInt32(e,t=this.le){return this.dataView.getInt32(e,t)}getUint16(e,t=this.le){return this.dataView.getUint16(e,t)}getUint32(e,t=this.le){return this.dataView.getUint32(e,t)}getFloat32(e,t=this.le){return this.dataView.getFloat32(e,t)}getFloat64(e,t=this.le){return this.dataView.getFloat64(e,t)}getFloat(e,t=this.le){return this.dataView.getFloat32(e,t)}getDouble(e,t=this.le){return this.dataView.getFloat64(e,t)}getUintBytes(e,t,s){switch(t){case 1:return this.getUint8(e,s);case 2:return this.getUint16(e,s);case 4:return this.getUint32(e,s);case 8:return this.getUint64&&this.getUint64(e,s)}}getUint(e,t,s){switch(t){case 8:return this.getUint8(e,s);case 16:return this.getUint16(e,s);case 32:return this.getUint32(e,s);case 64:return this.getUint64&&this.getUint64(e,s)}}toString(e){return this.dataView.toString(e,this.constructor.name)}ensureChunk(){}};function it(i,e){O(`${i} '${e}' was not loaded, try using full build of exifr.`)}class dt extends Map{constructor(e){super(),this.kind=e}get(e,t){return this.has(e)||it(this.kind,e),t&&(e in t||function(s,a){O(`Unknown ${s} '${a}'.`)}(this.kind,e),t[e].enabled||it(this.kind,e)),super.get(e)}keyList(){return Array.from(this.keys())}}var pi=new dt("file parser"),M=new dt("segment parser"),ut=new dt("file reader");let _s=di.fetch;function _t(i,e){return(t=i).startsWith("data:")||t.length>1e4?nt(i,e,"base64"):St&&i.includes("://")?st(i,e,"url",At):St?nt(i,e,"fs"):pe?st(i,e,"url",At):void O("Invalid input argument");var t}async function st(i,e,t,s){return ut.has(t)?nt(i,e,t):s?async function(a,n){let r=await n(a);return new fe(r)}(i,s):void O(`Parser ${t} is not loaded`)}async function nt(i,e,t){let s=new(ut.get(t))(i,e);return await s.read(),s}const At=i=>_s(i).then(e=>e.arrayBuffer()),at=i=>new Promise((e,t)=>{let s=new FileReader;s.onloadend=()=>e(s.result||new ArrayBuffer),s.onerror=t,s.readAsArrayBuffer(i)}),ht=new Map,As=new Map,Es=new Map,Pe=["chunked","firstChunkSize","firstChunkSizeNode","firstChunkSizeBrowser","chunkSize","chunkLimit"],fi=["jfif","xmp","icc","iptc","ihdr"],rt=["tiff",...fi],k=["ifd0","ifd1","exif","gps","interop"],Se=[...rt,...k],Ce=["makerNote","userComment"],mi=["translateKeys","translateValues","reviveValues","multiSegment"],Te=[...mi,"sanitize","mergeOutput","silentErrors"];let gi=class{get translate(){return this.translateKeys||this.translateValues||this.reviveValues}},le=class extends gi{get needed(){return this.enabled||this.deps.size>0}constructor(e,t,s,a){if(super(),b(this,"enabled",!1),b(this,"skip",new Set),b(this,"pick",new Set),b(this,"deps",new Set),b(this,"translateKeys",!1),b(this,"translateValues",!1),b(this,"reviveValues",!1),this.key=e,this.enabled=t,this.parse=this.enabled,this.applyInheritables(a),this.canBeFiltered=k.includes(e),this.canBeFiltered&&(this.dict=ht.get(e)),s!==void 0)if(Array.isArray(s))this.parse=this.enabled=!0,this.canBeFiltered&&s.length>0&&this.translateTagSet(s,this.pick);else if(typeof s=="object"){if(this.enabled=!0,this.parse=s.parse!==!1,this.canBeFiltered){let{pick:n,skip:r}=s;n&&n.length>0&&this.translateTagSet(n,this.pick),r&&r.length>0&&this.translateTagSet(r,this.skip)}this.applyInheritables(s)}else s===!0||s===!1?this.parse=this.enabled=s:O(`Invalid options argument: ${s}`)}applyInheritables(e){let t,s;for(t of mi)s=e[t],s!==void 0&&(this[t]=s)}translateTagSet(e,t){if(this.dict){let s,a,{tagKeys:n,tagValues:r}=this.dict;for(s of e)typeof s=="string"?(a=r.indexOf(s),a===-1&&(a=n.indexOf(Number(s))),a!==-1&&t.add(Number(n[a]))):t.add(s)}else for(let s of e)t.add(s)}finalizeFilters(){!this.enabled&&this.deps.size>0?(this.enabled=!0,Oe(this.pick,this.deps)):this.enabled&&this.pick.size>0&&Oe(this.pick,this.deps)}};var B={jfif:!1,tiff:!0,xmp:!1,icc:!1,iptc:!1,ifd0:!0,ifd1:!1,exif:!0,gps:!0,interop:!1,ihdr:void 0,makerNote:!1,userComment:!1,multiSegment:!1,skip:[],pick:[],translateKeys:!0,translateValues:!0,reviveValues:!0,sanitize:!0,mergeOutput:!0,silentErrors:!0,chunked:!0,firstChunkSize:void 0,firstChunkSizeNode:512,firstChunkSizeBrowser:65536,chunkSize:65536,chunkLimit:5},Et=new Map;class ct extends gi{static useCached(e){let t=Et.get(e);return t!==void 0||(t=new this(e),Et.set(e,t)),t}constructor(e){super(),e===!0?this.setupFromTrue():e===void 0?this.setupFromUndefined():Array.isArray(e)?this.setupFromArray(e):typeof e=="object"?this.setupFromObject(e):O(`Invalid options argument ${e}`),this.firstChunkSize===void 0&&(this.firstChunkSize=pe?this.firstChunkSizeBrowser:this.firstChunkSizeNode),this.mergeOutput&&(this.ifd1.enabled=!1),this.filterNestedSegmentTags(),this.traverseTiffDependencyTree(),this.checkLoadedPlugins()}setupFromUndefined(){let e;for(e of Pe)this[e]=B[e];for(e of Te)this[e]=B[e];for(e of Ce)this[e]=B[e];for(e of Se)this[e]=new le(e,B[e],void 0,this)}setupFromTrue(){let e;for(e of Pe)this[e]=B[e];for(e of Te)this[e]=B[e];for(e of Ce)this[e]=!0;for(e of Se)this[e]=new le(e,!0,void 0,this)}setupFromArray(e){let t;for(t of Pe)this[t]=B[t];for(t of Te)this[t]=B[t];for(t of Ce)this[t]=B[t];for(t of Se)this[t]=new le(t,!1,void 0,this);this.setupGlobalFilters(e,void 0,k)}setupFromObject(e){let t;for(t of(k.ifd0=k.ifd0||k.image,k.ifd1=k.ifd1||k.thumbnail,Object.assign(this,e),Pe))this[t]=He(e[t],B[t]);for(t of Te)this[t]=He(e[t],B[t]);for(t of Ce)this[t]=He(e[t],B[t]);for(t of rt)this[t]=new le(t,B[t],e[t],this);for(t of k)this[t]=new le(t,B[t],e[t],this.tiff);this.setupGlobalFilters(e.pick,e.skip,k,Se),e.tiff===!0?this.batchEnableWithBool(k,!0):e.tiff===!1?this.batchEnableWithUserValue(k,e):Array.isArray(e.tiff)?this.setupGlobalFilters(e.tiff,void 0,k):typeof e.tiff=="object"&&this.setupGlobalFilters(e.tiff.pick,e.tiff.skip,k)}batchEnableWithBool(e,t){for(let s of e)this[s].enabled=t}batchEnableWithUserValue(e,t){for(let s of e){let a=t[s];this[s].enabled=a!==!1&&a!==void 0}}setupGlobalFilters(e,t,s,a=s){if(e&&e.length){for(let r of a)this[r].enabled=!1;let n=Dt(e,s);for(let[r,l]of n)Oe(this[r].pick,l),this[r].enabled=!0}else if(t&&t.length){let n=Dt(t,s);for(let[r,l]of n)Oe(this[r].skip,l)}}filterNestedSegmentTags(){let{ifd0:e,exif:t,xmp:s,iptc:a,icc:n}=this;this.makerNote?t.deps.add(37500):t.skip.add(37500),this.userComment?t.deps.add(37510):t.skip.add(37510),s.enabled||e.skip.add(700),a.enabled||e.skip.add(33723),n.enabled||e.skip.add(34675)}traverseTiffDependencyTree(){let{ifd0:e,exif:t,gps:s,interop:a}=this;a.needed&&(t.deps.add(40965),e.deps.add(40965)),t.needed&&e.deps.add(34665),s.needed&&e.deps.add(34853),this.tiff.enabled=k.some(n=>this[n].enabled===!0)||this.makerNote||this.userComment;for(let n of k)this[n].finalizeFilters()}get onlyTiff(){return!fi.map(e=>this[e].enabled).some(e=>e===!0)&&this.tiff.enabled}checkLoadedPlugins(){for(let e of rt)this[e].enabled&&!M.has(e)&&it("segment parser",e)}}function Dt(i,e){let t,s,a,n,r=[];for(a of e){for(n of(t=ht.get(a),s=[],t))(i.includes(n[0])||i.includes(n[1]))&&s.push(n[0]);s.length&&r.push([a,s])}return r}function He(i,e){return i!==void 0?i:e!==void 0?e:void 0}function Oe(i,e){for(let t of e)i.add(t)}b(ct,"default",B);class Ds{constructor(e){b(this,"parsers",{}),b(this,"output",{}),b(this,"errors",[]),b(this,"pushToErrors",t=>this.errors.push(t)),this.options=ct.useCached(e)}async read(e){this.file=await function(t,s){return typeof t=="string"?_t(t,s):pe&&!Cs&&t instanceof HTMLImageElement?_t(t.src,s):t instanceof Uint8Array||t instanceof ArrayBuffer||t instanceof DataView?new fe(t):pe&&t instanceof Blob?st(t,s,"blob",at):void O("Invalid input argument")}(e,this.options)}setup(){if(this.fileParser)return;let{file:e}=this,t=e.getUint16(0);for(let[s,a]of pi)if(a.canHandle(e,t))return this.fileParser=new a(this.options,this.file,this.parsers),e[s]=!0;this.file.close&&this.file.close(),O("Unknown file format")}async parse(){let{output:e,errors:t}=this;return this.setup(),this.options.silentErrors?(await this.executeParsers().catch(this.pushToErrors),t.push(...this.fileParser.errors)):await this.executeParsers(),this.file.close&&this.file.close(),this.options.silentErrors&&t.length>0&&(e.errors=t),ci(s=e)?void 0:s;var s}async executeParsers(){let{output:e}=this;await this.fileParser.parse();let t=Object.values(this.parsers).map(async s=>{let a=await s.parse();s.assignToOutput(e,a)});this.options.silentErrors&&(t=t.map(s=>s.catch(this.pushToErrors))),await Promise.all(t)}async extractThumbnail(){this.setup();let{options:e,file:t}=this,s=M.get("tiff",e);var a;if(t.tiff?a={start:0,type:"tiff"}:t.jpeg&&(a=await this.fileParser.getOrFindSegment("tiff")),a===void 0)return;let n=await this.fileParser.ensureSegmentChunk(a),r=this.parsers.tiff=new s(n,e,t),l=await r.extractThumbnail();return t.close&&t.close(),l}}class ie{static findPosition(e,t){let s=e.getUint16(t+2)+2,a=typeof this.headerLength=="function"?this.headerLength(e,t,s):this.headerLength,n=t+a,r=s-a;return{offset:t,length:s,headerLength:a,start:n,size:r,end:n+r}}static parse(e,t={}){return new this(e,new ct({[this.type]:t}),e).parse()}normalizeInput(e){return e instanceof fe?e:new fe(e)}constructor(e,t={},s){b(this,"errors",[]),b(this,"raw",new Map),b(this,"handleError",a=>{if(!this.options.silentErrors)throw a;this.errors.push(a.message)}),this.chunk=this.normalizeInput(e),this.file=s,this.type=this.constructor.type,this.globalOptions=this.options=t,this.localOptions=t[this.type],this.canTranslate=this.localOptions&&this.localOptions.translate}translate(){this.canTranslate&&(this.translated=this.translateBlock(this.raw,this.type))}get output(){return this.translated?this.translated:this.raw?Object.fromEntries(this.raw):void 0}translateBlock(e,t){let s=Es.get(t),a=As.get(t),n=ht.get(t),r=this.options[t],l=r.reviveValues&&!!s,u=r.translateValues&&!!a,d=r.translateKeys&&!!n,h={};for(let[c,p]of e)l&&s.has(c)?p=s.get(c)(p):u&&a.has(c)&&(p=this.translateValue(p,a.get(c))),d&&n.has(c)&&(c=n.get(c)||c),h[c]=p;return h}translateValue(e,t){return t[e]||t.DEFAULT||e}assignToOutput(e,t){this.assignObjectToOutput(e,this.constructor.type,t)}assignObjectToOutput(e,t,s){if(this.globalOptions.mergeOutput)return Object.assign(e,s);e[t]?Object.assign(e[t],s):e[t]=s}}b(ie,"headerLength",4),b(ie,"type",void 0),b(ie,"multiSegment",!1),b(ie,"canHandle",()=>!1);function ks(i){return i===192||i===194||i===196||i===219||i===221||i===218||i===254}function Os(i){return i>=224&&i<=239}function Bs(i,e,t){for(let[s,a]of M)if(a.canHandle(i,e,t))return s}class kt extends class{constructor(e,t,s){b(this,"errors",[]),b(this,"ensureSegmentChunk",async a=>{let n=a.start,r=a.size||65536;if(this.file.chunked)if(this.file.available(n,r))a.chunk=this.file.subarray(n,r);else try{a.chunk=await this.file.readChunk(n,r)}catch(l){O(`Couldn't read segment: ${JSON.stringify(a)}. ${l.message}`)}else this.file.byteLength>n+r?a.chunk=this.file.subarray(n,r):a.size===void 0?a.chunk=this.file.subarray(n):O("Segment unreachable: "+JSON.stringify(a));return a.chunk}),this.extendOptions&&this.extendOptions(e),this.options=e,this.file=t,this.parsers=s}injectSegment(e,t){this.options[e].enabled&&this.createParser(e,t)}createParser(e,t){let s=new(M.get(e))(t,this.options,this.file);return this.parsers[e]=s}createParsers(e){for(let t of e){let{type:s,chunk:a}=t,n=this.options[s];if(n&&n.enabled){let r=this.parsers[s];r&&r.append||r||this.createParser(s,a)}}}async readSegments(e){let t=e.map(this.ensureSegmentChunk);await Promise.all(t)}}{constructor(...e){super(...e),b(this,"appSegments",[]),b(this,"jpegSegments",[]),b(this,"unknownSegments",[])}static canHandle(e,t){return t===65496}async parse(){await this.findAppSegments(),await this.readSegments(this.appSegments),this.mergeMultiSegments(),this.createParsers(this.mergedAppSegments||this.appSegments)}setupSegmentFinderArgs(e){e===!0?(this.findAll=!0,this.wanted=new Set(M.keyList())):(e=e===void 0?M.keyList().filter(t=>this.options[t].enabled):e.filter(t=>this.options[t].enabled&&M.has(t)),this.findAll=!1,this.remaining=new Set(e),this.wanted=new Set(e)),this.unfinishedMultiSegment=!1}async findAppSegments(e=0,t){this.setupSegmentFinderArgs(t);let{file:s,findAll:a,wanted:n,remaining:r}=this;if(!a&&this.file.chunked&&(a=Array.from(n).some(l=>{let u=M.get(l),d=this.options[l];return u.multiSegment&&d.multiSegment}),a&&await this.file.readWhole()),e=this.findAppSegmentsInRange(e,s.byteLength),!this.options.onlyTiff&&s.chunked){let l=!1;for(;r.size>0&&!l&&(s.canReadNextChunk||this.unfinishedMultiSegment);){let{nextChunkOffset:u}=s,d=this.appSegments.some(h=>!this.file.available(h.offset||h.start,h.length||h.size));if(l=e>u&&!d?!await s.readNextChunk(e):!await s.readNextChunk(u),(e=this.findAppSegmentsInRange(e,s.byteLength))===void 0)return}}}findAppSegmentsInRange(e,t){t-=2;let s,a,n,r,l,u,{file:d,findAll:h,wanted:c,remaining:p,options:m}=this;for(;e<t;e++)if(d.getUint8(e)===255){if(s=d.getUint8(e+1),Os(s)){if(a=d.getUint16(e+2),n=Bs(d,e,a),n&&c.has(n)&&(r=M.get(n),l=r.findPosition(d,e),u=m[n],l.type=n,this.appSegments.push(l),!h&&(r.multiSegment&&u.multiSegment?(this.unfinishedMultiSegment=l.chunkNumber<l.chunkCount,this.unfinishedMultiSegment||p.delete(n)):p.delete(n),p.size===0)))break;m.recordUnknownSegments&&(l=ie.findPosition(d,e),l.marker=s,this.unknownSegments.push(l)),e+=a+1}else if(ks(s)){if(a=d.getUint16(e+2),s===218&&m.stopAfterSos!==!1)return;m.recordJpegSegments&&this.jpegSegments.push({offset:e,length:a,marker:s}),e+=a+1}}return e}mergeMultiSegments(){if(!this.appSegments.some(t=>t.multiSegment))return;let e=function(t,s){let a,n,r,l=new Map;for(let u=0;u<t.length;u++)a=t[u],n=a[s],l.has(n)?r=l.get(n):l.set(n,r=[]),r.push(a);return Array.from(l)}(this.appSegments,"type");this.mergedAppSegments=e.map(([t,s])=>{let a=M.get(t,this.options);return a.handleMultiSegments?{type:t,chunk:a.handleMultiSegments(s)}:s[0]})}getSegment(e){return this.appSegments.find(t=>t.type===e)}async getOrFindSegment(e){let t=this.getSegment(e);return t===void 0&&(await this.findAppSegments(0,[e]),t=this.getSegment(e)),t}}b(kt,"type","jpeg"),pi.set("jpeg",kt);const Ns=[void 0,1,1,2,4,8,1,1,2,4,8,4,8,4];class Is extends ie{parseHeader(){var e=this.chunk.getUint16();e===18761?this.le=!0:e===19789&&(this.le=!1),this.chunk.le=this.le,this.headerParsed=!0}parseTags(e,t,s=new Map){let{pick:a,skip:n}=this.options[t];a=new Set(a);let r=a.size>0,l=n.size===0,u=this.chunk.getUint16(e);e+=2;for(let d=0;d<u;d++){let h=this.chunk.getUint16(e);if(r){if(a.has(h)&&(s.set(h,this.parseTag(e,h,t)),a.delete(h),a.size===0))break}else!l&&n.has(h)||s.set(h,this.parseTag(e,h,t));e+=12}return s}parseTag(e,t,s){let{chunk:a}=this,n=a.getUint16(e+2),r=a.getUint32(e+4),l=Ns[n];if(l*r<=4?e+=8:e=a.getUint32(e+8),(n<1||n>13)&&O(`Invalid TIFF value type. block: ${s.toUpperCase()}, tag: ${t.toString(16)}, type: ${n}, offset ${e}`),e>a.byteLength&&O(`Invalid TIFF value offset. block: ${s.toUpperCase()}, tag: ${t.toString(16)}, type: ${n}, offset ${e} is outside of chunk size ${a.byteLength}`),n===1)return a.getUint8Array(e,r);if(n===2)return(u=function(d){for(;d.endsWith("\0");)d=d.slice(0,-1);return d}(u=a.getString(e,r)).trim())===""?void 0:u;var u;if(n===7)return a.getUint8Array(e,r);if(r===1)return this.parseTagValue(n,e);{let d=new(function(c){switch(c){case 1:return Uint8Array;case 3:return Uint16Array;case 4:return Uint32Array;case 5:return Array;case 6:return Int8Array;case 8:return Int16Array;case 9:return Int32Array;case 10:return Array;case 11:return Float32Array;case 12:return Float64Array;default:return Array}}(n))(r),h=l;for(let c=0;c<r;c++)d[c]=this.parseTagValue(n,e),e+=h;return d}}parseTagValue(e,t){let{chunk:s}=this;switch(e){case 1:return s.getUint8(t);case 3:return s.getUint16(t);case 4:return s.getUint32(t);case 5:return s.getUint32(t)/s.getUint32(t+4);case 6:return s.getInt8(t);case 8:return s.getInt16(t);case 9:return s.getInt32(t);case 10:return s.getInt32(t)/s.getInt32(t+4);case 11:return s.getFloat(t);case 12:return s.getDouble(t);case 13:return s.getUint32(t);default:O(`Invalid tiff type ${e}`)}}}class Ve extends Is{static canHandle(e,t){return e.getUint8(t+1)===225&&e.getUint32(t+4)===1165519206&&e.getUint16(t+8)===0}async parse(){this.parseHeader();let{options:e}=this;return e.ifd0.enabled&&await this.parseIfd0Block(),e.exif.enabled&&await this.safeParse("parseExifBlock"),e.gps.enabled&&await this.safeParse("parseGpsBlock"),e.interop.enabled&&await this.safeParse("parseInteropBlock"),e.ifd1.enabled&&await this.safeParse("parseThumbnailBlock"),this.createOutput()}safeParse(e){let t=this[e]();return t.catch!==void 0&&(t=t.catch(this.handleError)),t}findIfd0Offset(){this.ifd0Offset===void 0&&(this.ifd0Offset=this.chunk.getUint32(4))}findIfd1Offset(){if(this.ifd1Offset===void 0){this.findIfd0Offset();let e=this.chunk.getUint16(this.ifd0Offset),t=this.ifd0Offset+2+12*e;this.ifd1Offset=this.chunk.getUint32(t)}}parseBlock(e,t){let s=new Map;return this[t]=s,this.parseTags(e,t,s),s}async parseIfd0Block(){if(this.ifd0)return;let{file:e}=this;this.findIfd0Offset(),this.ifd0Offset<8&&O("Malformed EXIF data"),!e.chunked&&this.ifd0Offset>e.byteLength&&O(`IFD0 offset points to outside of file.
this.ifd0Offset: ${this.ifd0Offset}, file.byteLength: ${e.byteLength}`),e.tiff&&await e.ensureChunk(this.ifd0Offset,Ct(this.options));let t=this.parseBlock(this.ifd0Offset,"ifd0");return t.size!==0?(this.exifOffset=t.get(34665),this.interopOffset=t.get(40965),this.gpsOffset=t.get(34853),this.xmp=t.get(700),this.iptc=t.get(33723),this.icc=t.get(34675),this.options.sanitize&&(t.delete(34665),t.delete(40965),t.delete(34853),t.delete(700),t.delete(33723),t.delete(34675)),t):void 0}async parseExifBlock(){if(this.exif||(this.ifd0||await this.parseIfd0Block(),this.exifOffset===void 0))return;this.file.tiff&&await this.file.ensureChunk(this.exifOffset,Ct(this.options));let e=this.parseBlock(this.exifOffset,"exif");return this.interopOffset||(this.interopOffset=e.get(40965)),this.makerNote=e.get(37500),this.userComment=e.get(37510),this.options.sanitize&&(e.delete(40965),e.delete(37500),e.delete(37510)),this.unpack(e,41728),this.unpack(e,41729),e}unpack(e,t){let s=e.get(t);s&&s.length===1&&e.set(t,s[0])}async parseGpsBlock(){if(this.gps||(this.ifd0||await this.parseIfd0Block(),this.gpsOffset===void 0))return;let e=this.parseBlock(this.gpsOffset,"gps");return e&&e.has(2)&&e.has(4)&&(e.set("latitude",Ot(...e.get(2),e.get(1))),e.set("longitude",Ot(...e.get(4),e.get(3)))),e}async parseInteropBlock(){if(!this.interop&&(this.ifd0||await this.parseIfd0Block(),this.interopOffset!==void 0||this.exif||await this.parseExifBlock(),this.interopOffset!==void 0))return this.parseBlock(this.interopOffset,"interop")}async parseThumbnailBlock(e=!1){if(!this.ifd1&&!this.ifd1Parsed&&(!this.options.mergeOutput||e))return this.findIfd1Offset(),this.ifd1Offset>0&&(this.parseBlock(this.ifd1Offset,"ifd1"),this.ifd1Parsed=!0),this.ifd1}async extractThumbnail(){if(this.headerParsed||this.parseHeader(),this.ifd1Parsed||await this.parseThumbnailBlock(!0),this.ifd1===void 0)return;let e=this.ifd1.get(513),t=this.ifd1.get(514);return this.chunk.getUint8Array(e,t)}get image(){return this.ifd0}get thumbnail(){return this.ifd1}createOutput(){let e,t,s,a={};for(t of k)if(e=this[t],!ci(e))if(s=this.canTranslate?this.translateBlock(e,t):Object.fromEntries(e),this.options.mergeOutput){if(t==="ifd1")continue;Object.assign(a,s)}else a[t]=s;return this.makerNote&&(a.makerNote=this.makerNote),this.userComment&&(a.userComment=this.userComment),a}assignToOutput(e,t){if(this.globalOptions.mergeOutput)Object.assign(e,t);else for(let[s,a]of Object.entries(t))this.assignObjectToOutput(e,s,a)}}function Ot(i,e,t,s){var a=i+e/60+t/3600;return s!=="S"&&s!=="W"||(a*=-1),a}b(Ve,"type","tiff"),b(Ve,"headerLength",10),M.set("tiff",Ve);const pt={ifd0:!1,ifd1:!1,exif:!1,gps:!1,interop:!1,sanitize:!1,reviveValues:!0,translateKeys:!1,translateValues:!1,mergeOutput:!1};Object.assign({},pt,{firstChunkSize:4e4,gps:[1,2,3,4]});Object.assign({},pt,{tiff:!1,ifd1:!0,mergeOutput:!1});const Us=Object.assign({},pt,{firstChunkSize:4e4,ifd0:[274]});async function Rs(i){let e=new Ds(Us);await e.read(i);let t=await e.parse();if(t&&t.ifd0)return t.ifd0[274]}const Ms=Object.freeze({1:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:0,rad:0},2:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:0,rad:0},3:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:180,rad:180*Math.PI/180},4:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:180,rad:180*Math.PI/180},5:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:90,rad:90*Math.PI/180},6:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:90,rad:90*Math.PI/180},7:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:270,rad:270*Math.PI/180},8:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:270,rad:270*Math.PI/180}});let ue=!0,he=!0;if(typeof navigator=="object"){let i=navigator.userAgent;if(i.includes("iPad")||i.includes("iPhone")){let e=i.match(/OS (\d+)_(\d+)/);if(e){let[,t,s]=e;ue=Number(t)+.1*Number(s)<13.4,he=!1}}else if(i.includes("OS X 10")){let[,e]=i.match(/OS X 10[_.](\d+)/);ue=he=Number(e)<15}if(i.includes("Chrome/")){let[,e]=i.match(/Chrome\/(\d+)/);ue=he=Number(e)<81}else if(i.includes("Firefox/")){let[,e]=i.match(/Firefox\/(\d+)/);ue=he=Number(e)<77}}async function Ls(i){let e=await Rs(i);return Object.assign({canvas:ue,css:he},Ms[e])}class xs extends fe{constructor(...e){super(...e),b(this,"ranges",new zs),this.byteLength!==0&&this.ranges.add(0,this.byteLength)}_tryExtend(e,t,s){if(e===0&&this.byteLength===0&&s){let a=new DataView(s.buffer||s,s.byteOffset,s.byteLength);this._swapDataView(a)}else{let a=e+t;if(a>this.byteLength){let{dataView:n}=this._extend(a);this._swapDataView(n)}}}_extend(e){let t;t=hi?ui.allocUnsafe(e):new Uint8Array(e);let s=new DataView(t.buffer,t.byteOffset,t.byteLength);return t.set(new Uint8Array(this.buffer,this.byteOffset,this.byteLength),0),{uintView:t,dataView:s}}subarray(e,t,s=!1){return t=t||this._lengthToEnd(e),s&&this._tryExtend(e,t),this.ranges.add(e,t),super.subarray(e,t)}set(e,t,s=!1){s&&this._tryExtend(t,e.byteLength,e);let a=super.set(e,t);return this.ranges.add(t,a.byteLength),a}async ensureChunk(e,t){this.chunked&&(this.ranges.available(e,t)||await this.readChunk(e,t))}available(e,t){return this.ranges.available(e,t)}}class zs{constructor(){b(this,"list",[])}get length(){return this.list.length}add(e,t,s=0){let a=e+t,n=this.list.filter(r=>Bt(e,r.offset,a)||Bt(e,r.end,a));if(n.length>0){e=Math.min(e,...n.map(l=>l.offset)),a=Math.max(a,...n.map(l=>l.end)),t=a-e;let r=n.shift();r.offset=e,r.length=t,r.end=a,this.list=this.list.filter(l=>!n.includes(l))}else this.list.push({offset:e,length:t,end:a})}available(e,t){let s=e+t;return this.list.some(a=>a.offset<=e&&s<=a.end)}}function Bt(i,e,t){return i<=e&&e<=t}class $s extends xs{constructor(e,t){super(0),b(this,"chunksRead",0),this.input=e,this.options=t}async readWhole(){this.chunked=!1,await this.readChunk(this.nextChunkOffset)}async readChunked(){this.chunked=!0,await this.readChunk(0,this.options.firstChunkSize)}async readNextChunk(e=this.nextChunkOffset){if(this.fullyRead)return this.chunksRead++,!1;let t=this.options.chunkSize,s=await this.readChunk(e,t);return!!s&&s.byteLength===t}async readChunk(e,t){if(this.chunksRead++,(t=this.safeWrapAddress(e,t))!==0)return this._readChunk(e,t)}safeWrapAddress(e,t){return this.size!==void 0&&e+t>this.size?Math.max(0,this.size-e):t}get nextChunkOffset(){if(this.ranges.list.length!==0)return this.ranges.list[0].length}get canReadNextChunk(){return this.chunksRead<this.options.chunkLimit}get fullyRead(){return this.size!==void 0&&this.nextChunkOffset===this.size}read(){return this.options.chunked?this.readChunked():this.readWhole()}close(){}}ut.set("blob",class extends $s{async readWhole(){this.chunked=!1;let i=await at(this.input);this._swapArrayBuffer(i)}readChunked(){return this.chunked=!0,this.size=this.input.size,super.readChunked()}async _readChunk(i,e){let t=e?i+e:void 0,s=this.input.slice(i,t),a=await at(s);return this.set(a,i,!0)}});const Hs={strings:{generatingThumbnails:"Generating thumbnails..."}},Vs={version:"3.0.8"};function qs(i,e,t){try{i.getContext("2d").getImageData(0,0,1,1)}catch(s){if(s.code===18)return Promise.reject(new Error("cannot read image, probably an svg with external resources"))}return i.toBlob?new Promise(s=>{i.toBlob(s,e,t)}).then(s=>{if(s===null)throw new Error("cannot read image, probably an svg with external resources");return s}):Promise.resolve().then(()=>Ss(i.toDataURL(e,t),{})).then(s=>{if(s===null)throw new Error("could not extract blob, probably an old browser");return s})}function js(i,e){let t=i.width,s=i.height;(e.deg===90||e.deg===270)&&(t=i.height,s=i.width);const a=document.createElement("canvas");a.width=t,a.height=s;const n=a.getContext("2d");return n.translate(t/2,s/2),e.canvas&&(n.rotate(e.rad),n.scale(e.scaleX,e.scaleY)),n.drawImage(i,-i.width/2,-i.height/2,i.width,i.height),a}function Ws(i){const e=i.width/i.height,t=5e6,s=4096;let a=Math.floor(Math.sqrt(t*e)),n=Math.floor(t/Math.sqrt(t*e));if(a>s&&(a=s,n=Math.round(a/e)),n>s&&(n=s,a=Math.round(e*n)),i.width>a){const r=document.createElement("canvas");return r.width=a,r.height=n,r.getContext("2d").drawImage(i,0,0,a,n),r}return i}class yi extends Ne{constructor(e,t){super(e,t),this.onFileAdded=a=>{!a.preview&&a.data&&Pt(a.type)&&!a.isRemote&&this.addToQueue(a.id)},this.onCancelRequest=a=>{const n=this.queue.indexOf(a.id);n!==-1&&this.queue.splice(n,1)},this.onFileRemoved=a=>{const n=this.queue.indexOf(a.id);n!==-1&&this.queue.splice(n,1),a.preview&&Ft(a.preview)&&URL.revokeObjectURL(a.preview)},this.onRestored=()=>{this.uppy.getFiles().filter(n=>n.isRestored).forEach(n=>{(!n.preview||Ft(n.preview))&&this.addToQueue(n.id)})},this.onAllFilesRemoved=()=>{this.queue=[]},this.waitUntilAllProcessed=a=>{a.forEach(r=>{const l=this.uppy.getFile(r);this.uppy.emit("preprocess-progress",l,{mode:"indeterminate",message:this.i18n("generatingThumbnails")})});const n=()=>{a.forEach(r=>{const l=this.uppy.getFile(r);this.uppy.emit("preprocess-complete",l)})};return new Promise(r=>{this.queueProcessing?this.uppy.once("thumbnail:all-generated",()=>{n(),r()}):(n(),r())})},this.type="modifier",this.id=this.opts.id||"ThumbnailGenerator",this.title="Thumbnail Generator",this.queue=[],this.queueProcessing=!1,this.defaultThumbnailDimension=200,this.thumbnailType=this.opts.thumbnailType||"image/jpeg",this.defaultLocale=Hs;const s={thumbnailWidth:null,thumbnailHeight:null,waitForThumbnailsBeforeUpload:!1,lazy:!1};if(this.opts={...s,...t},this.i18nInit(),this.opts.lazy&&this.opts.waitForThumbnailsBeforeUpload)throw new Error("ThumbnailGenerator: The `lazy` and `waitForThumbnailsBeforeUpload` options are mutually exclusive. Please ensure at most one of them is set to `true`.")}createThumbnail(e,t,s){const a=URL.createObjectURL(e.data),n=new Promise((l,u)=>{const d=new Image;d.src=a,d.addEventListener("load",()=>{URL.revokeObjectURL(a),l(d)}),d.addEventListener("error",h=>{URL.revokeObjectURL(a),u(h.error||new Error("Could not create thumbnail"))})}),r=Ls(e.data).catch(()=>1);return Promise.all([n,r]).then(l=>{let[u,d]=l;const h=this.getProportionalDimensions(u,t,s,d.deg),c=js(u,d),p=this.resizeImage(c,h.width,h.height);return qs(p,this.thumbnailType,80)}).then(l=>URL.createObjectURL(l))}getProportionalDimensions(e,t,s,a){let n=e.width/e.height;return(a===90||a===270)&&(n=e.height/e.width),t!=null?{width:t,height:Math.round(t/n)}:s!=null?{width:Math.round(s*n),height:s}:{width:this.defaultThumbnailDimension,height:Math.round(this.defaultThumbnailDimension/n)}}resizeImage(e,t,s){let a=Ws(e),n=Math.ceil(Math.log2(a.width/t));n<1&&(n=1);let r=t*2**(n-1),l=s*2**(n-1);const u=2;for(;n--;){const d=document.createElement("canvas");d.width=r,d.height=l,d.getContext("2d").drawImage(a,0,0,r,l),a=d,r=Math.round(r/u),l=Math.round(l/u)}return a}setPreviewURL(e,t){this.uppy.setFileState(e,{preview:t})}addToQueue(e){this.queue.push(e),this.queueProcessing===!1&&this.processQueue()}processQueue(){if(this.queueProcessing=!0,this.queue.length>0){const e=this.uppy.getFile(this.queue.shift());return e?this.requestThumbnail(e).catch(()=>{}).then(()=>this.processQueue()):(this.uppy.log("[ThumbnailGenerator] file was removed before a thumbnail could be generated, but not removed from the queue. This is probably a bug","error"),Promise.resolve())}return this.queueProcessing=!1,this.uppy.log("[ThumbnailGenerator] Emptied thumbnail queue"),this.uppy.emit("thumbnail:all-generated"),Promise.resolve()}requestThumbnail(e){return Pt(e.type)&&!e.isRemote?this.createThumbnail(e,this.opts.thumbnailWidth,this.opts.thumbnailHeight).then(t=>{this.setPreviewURL(e.id,t),this.uppy.log(`[ThumbnailGenerator] Generated thumbnail for ${e.id}`),this.uppy.emit("thumbnail:generated",this.uppy.getFile(e.id),t)}).catch(t=>{this.uppy.log(`[ThumbnailGenerator] Failed thumbnail for ${e.id}:`,"warning"),this.uppy.log(t,"warning"),this.uppy.emit("thumbnail:error",this.uppy.getFile(e.id),t)}):Promise.resolve()}install(){this.uppy.on("file-removed",this.onFileRemoved),this.uppy.on("cancel-all",this.onAllFilesRemoved),this.opts.lazy?(this.uppy.on("thumbnail:request",this.onFileAdded),this.uppy.on("thumbnail:cancel",this.onCancelRequest)):(this.uppy.on("thumbnail:request",this.onFileAdded),this.uppy.on("file-added",this.onFileAdded),this.uppy.on("restored",this.onRestored)),this.opts.waitForThumbnailsBeforeUpload&&this.uppy.addPreProcessor(this.waitUntilAllProcessed)}uninstall(){this.uppy.off("file-removed",this.onFileRemoved),this.uppy.off("cancel-all",this.onAllFilesRemoved),this.opts.lazy?(this.uppy.off("thumbnail:request",this.onFileAdded),this.uppy.off("thumbnail:cancel",this.onCancelRequest)):(this.uppy.off("thumbnail:request",this.onFileAdded),this.uppy.off("file-added",this.onFileAdded),this.uppy.off("restored",this.onRestored)),this.opts.waitForThumbnailsBeforeUpload&&this.uppy.removePreProcessor(this.waitUntilAllProcessed)}}yi.VERSION=Vs.version;function Nt(i){if(typeof i=="string"){const e=document.querySelectorAll(i);return e.length===0?null:Array.from(e)}return typeof i=="object"&&Vi(i)?[i]:null}const ce=Array.from;function bi(i,e,t,s){let{onSuccess:a}=s;i.readEntries(n=>{const r=[...e,...n];n.length?queueMicrotask(()=>{bi(i,r,t,{onSuccess:a})}):a(r)},n=>{t(n),a(e)})}function vi(i,e){return i==null?i:{kind:i.isFile?"file":i.isDirectory?"directory":void 0,name:i.name,getFile(){return new Promise((t,s)=>i.file(t,s))},async*values(){const t=i.createReader();yield*await new Promise(a=>{bi(t,[],e,{onSuccess:n=>a(n.map(r=>vi(r,e)))})})},isSameEntry:void 0}}function wi(i,e,t){try{return t===void 0&&(t=void 0),async function*(){const s=()=>`${e}/${i.name}`;if(i.kind==="file"){const a=await i.getFile();a!=null?(a.relativePath=e?s():null,yield a):t!=null&&(yield t)}else if(i.kind==="directory")for await(const a of i.values())yield*wi(a,e?s():i.name);else t!=null&&(yield t)}()}catch(s){return Promise.reject(s)}}async function*Gs(i,e){const t=await Promise.all(Array.from(i.items,async s=>{var a;let n;const r=()=>typeof s.getAsEntry=="function"?s.getAsEntry():s.webkitGetAsEntry();return(a=n)!=null||(n=vi(r(),e)),{fileSystemHandle:n,lastResortFile:s.getAsFile()}}));for(const{lastResortFile:s,fileSystemHandle:a}of t)if(a!=null)try{yield*wi(a,"",s)}catch(n){s!=null?yield s:e(n)}else s!=null&&(yield s)}function Ks(i){const e=ce(i.files);return Promise.resolve(e)}async function Xs(i,e){var t;const s=(t=e==null?void 0:e.logDropError)!=null?t:Function.prototype;try{const a=[];for await(const n of Gs(i,s))a.push(n);return a}catch{return Ks(i)}}var Ys={exports:{}};(function(i){var e=Object.prototype.hasOwnProperty,t="~";function s(){}Object.create&&(s.prototype=Object.create(null),new s().__proto__||(t=!1));function a(u,d,h){this.fn=u,this.context=d,this.once=h||!1}function n(u,d,h,c,p){if(typeof h!="function")throw new TypeError("The listener must be a function");var m=new a(h,c||u,p),g=t?t+d:d;return u._events[g]?u._events[g].fn?u._events[g]=[u._events[g],m]:u._events[g].push(m):(u._events[g]=m,u._eventsCount++),u}function r(u,d){--u._eventsCount===0?u._events=new s:delete u._events[d]}function l(){this._events=new s,this._eventsCount=0}l.prototype.eventNames=function(){var d=[],h,c;if(this._eventsCount===0)return d;for(c in h=this._events)e.call(h,c)&&d.push(t?c.slice(1):c);return Object.getOwnPropertySymbols?d.concat(Object.getOwnPropertySymbols(h)):d},l.prototype.listeners=function(d){var h=t?t+d:d,c=this._events[h];if(!c)return[];if(c.fn)return[c.fn];for(var p=0,m=c.length,g=new Array(m);p<m;p++)g[p]=c[p].fn;return g},l.prototype.listenerCount=function(d){var h=t?t+d:d,c=this._events[h];return c?c.fn?1:c.length:0},l.prototype.emit=function(d,h,c,p,m,g){var v=t?t+d:d;if(!this._events[v])return!1;var f=this._events[v],w=arguments.length,S,y;if(f.fn){switch(f.once&&this.removeListener(d,f.fn,void 0,!0),w){case 1:return f.fn.call(f.context),!0;case 2:return f.fn.call(f.context,h),!0;case 3:return f.fn.call(f.context,h,c),!0;case 4:return f.fn.call(f.context,h,c,p),!0;case 5:return f.fn.call(f.context,h,c,p,m),!0;case 6:return f.fn.call(f.context,h,c,p,m,g),!0}for(y=1,S=new Array(w-1);y<w;y++)S[y-1]=arguments[y];f.fn.apply(f.context,S)}else{var F=f.length,T;for(y=0;y<F;y++)switch(f[y].once&&this.removeListener(d,f[y].fn,void 0,!0),w){case 1:f[y].fn.call(f[y].context);break;case 2:f[y].fn.call(f[y].context,h);break;case 3:f[y].fn.call(f[y].context,h,c);break;case 4:f[y].fn.call(f[y].context,h,c,p);break;default:if(!S)for(T=1,S=new Array(w-1);T<w;T++)S[T-1]=arguments[T];f[y].fn.apply(f[y].context,S)}}return!0},l.prototype.on=function(d,h,c){return n(this,d,h,c,!1)},l.prototype.once=function(d,h,c){return n(this,d,h,c,!0)},l.prototype.removeListener=function(d,h,c,p){var m=t?t+d:d;if(!this._events[m])return this;if(!h)return r(this,m),this;var g=this._events[m];if(g.fn)g.fn===h&&(!p||g.once)&&(!c||g.context===c)&&r(this,m);else{for(var v=0,f=[],w=g.length;v<w;v++)(g[v].fn!==h||p&&!g[v].once||c&&g[v].context!==c)&&f.push(g[v]);f.length?this._events[m]=f.length===1?f[0]:f:r(this,m)}return this},l.prototype.removeAllListeners=function(d){var h;return d?(h=t?t+d:d,this._events[h]&&r(this,h)):(this._events=new s,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=t,l.EventEmitter=l,i.exports=l})(Ys);globalThis&&globalThis.__classPrivateFieldGet;globalThis&&globalThis.__classPrivateFieldSet;globalThis&&globalThis.__classPrivateFieldGet;var me,_,qe,It,Be=0,Fi=[],De=[],D=qi,Ut=D.__b,Rt=D.__r,Mt=D.diffed,Lt=D.__c,xt=D.unmount,zt=D.__;function ft(i,e){D.__h&&D.__h(_,i,Be||e),Be=0;var t=_.__H||(_.__H={__:[],__h:[]});return i>=t.__.length&&t.__.push({__V:De}),t.__[i]}function $t(i){return Be=1,Qs(Ci,i)}function Qs(i,e,t){var s=ft(me++,2);if(s.t=i,!s.__c&&(s.__=[t?t(e):Ci(void 0,e),function(l){var u=s.__N?s.__N[0]:s.__[0],d=s.t(u,l);u!==d&&(s.__N=[d,s.__[1]],s.__c.setState({}))}],s.__c=_,!_.u)){var a=function(l,u,d){if(!s.__c.__H)return!0;var h=s.__c.__H.__.filter(function(p){return!!p.__c});if(h.every(function(p){return!p.__N}))return!n||n.call(this,l,u,d);var c=!1;return h.forEach(function(p){if(p.__N){var m=p.__[0];p.__=p.__N,p.__N=void 0,m!==p.__[0]&&(c=!0)}}),!(!c&&s.__c.props===l)&&(!n||n.call(this,l,u,d))};_.u=!0;var n=_.shouldComponentUpdate,r=_.componentWillUpdate;_.componentWillUpdate=function(l,u,d){if(this.__e){var h=n;n=void 0,a(l,u,d),n=h}r&&r.call(this,l,u,d)},_.shouldComponentUpdate=a}return s.__N||s.__}function Js(i,e){var t=ft(me++,3);!D.__s&&Si(t.__H,e)&&(t.__=i,t.i=e,_.__H.__h.push(t))}function Pi(i,e){var t=ft(me++,7);return Si(t.__H,e)?(t.__V=i(),t.i=e,t.__h=i,t.__V):t.__}function Zs(i,e){return Be=8,Pi(function(){return i},e)}function en(){for(var i;i=Fi.shift();)if(i.__P&&i.__H)try{i.__H.__h.forEach(ke),i.__H.__h.forEach(ot),i.__H.__h=[]}catch(e){i.__H.__h=[],D.__e(e,i.__v)}}D.__b=function(i){_=null,Ut&&Ut(i)},D.__=function(i,e){i&&e.__k&&e.__k.__m&&(i.__m=e.__k.__m),zt&&zt(i,e)},D.__r=function(i){Rt&&Rt(i),me=0;var e=(_=i.__c).__H;e&&(qe===_?(e.__h=[],_.__h=[],e.__.forEach(function(t){t.__N&&(t.__=t.__N),t.__V=De,t.__N=t.i=void 0})):(e.__h.forEach(ke),e.__h.forEach(ot),e.__h=[],me=0)),qe=_},D.diffed=function(i){Mt&&Mt(i);var e=i.__c;e&&e.__H&&(e.__H.__h.length&&(Fi.push(e)!==1&&It===D.requestAnimationFrame||((It=D.requestAnimationFrame)||tn)(en)),e.__H.__.forEach(function(t){t.i&&(t.__H=t.i),t.__V!==De&&(t.__=t.__V),t.i=void 0,t.__V=De})),qe=_=null},D.__c=function(i,e){e.some(function(t){try{t.__h.forEach(ke),t.__h=t.__h.filter(function(s){return!s.__||ot(s)})}catch(s){e.some(function(a){a.__h&&(a.__h=[])}),e=[],D.__e(s,t.__v)}}),Lt&&Lt(i,e)},D.unmount=function(i){xt&&xt(i);var e,t=i.__c;t&&t.__H&&(t.__H.__.forEach(function(s){try{ke(s)}catch(a){e=a}}),t.__H=void 0,e&&D.__e(e,t.__v))};var Ht=typeof requestAnimationFrame=="function";function tn(i){var e,t=function(){clearTimeout(s),Ht&&cancelAnimationFrame(e),setTimeout(i)},s=setTimeout(t,100);Ht&&(e=requestAnimationFrame(t))}function ke(i){var e=_,t=i.__c;typeof t=="function"&&(i.__c=void 0,t()),_=e}function ot(i){var e=_;i.__c=i.__(),_=e}function Si(i,e){return!i||i.length!==e.length||e.some(function(t,s){return t!==i[s]})}function Ci(i,e){return typeof e=="function"?e(i):e}function lt(){return lt=Object.assign?Object.assign.bind():function(i){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(i[s]=t[s])}return i},lt.apply(this,arguments)}const sn={position:"relative",width:"100%",minHeight:"100%"},nn={position:"absolute",top:0,left:0,width:"100%",overflow:"visible"};class an extends se{constructor(e){super(e),this.handleScroll=()=>{this.setState({offset:this.base.scrollTop})},this.handleResize=()=>{this.resize()},this.focusElement=null,this.state={offset:0,height:0}}componentDidMount(){this.resize(),window.addEventListener("resize",this.handleResize)}componentWillUpdate(){this.base.contains(document.activeElement)&&(this.focusElement=document.activeElement)}componentDidUpdate(){this.focusElement&&this.focusElement.parentNode&&document.activeElement!==this.focusElement&&this.focusElement.focus(),this.focusElement=null,this.resize()}componentWillUnmount(){window.removeEventListener("resize",this.handleResize)}resize(){const{height:e}=this.state;e!==this.base.offsetHeight&&this.setState({height:this.base.offsetHeight})}render(e){let{data:t,rowHeight:s,renderRow:a,overscanCount:n=10,...r}=e;const{offset:l,height:u}=this.state;let d=Math.floor(l/s),h=Math.floor(u/s);n&&(d=Math.max(0,d-d%n),h+=n);const c=d+h+4,p=t.slice(d,c),m={...sn,height:t.length*s},g={...nn,top:d*s};return o("div",lt({onScroll:this.handleScroll},r),o("div",{role:"presentation",style:m},o("div",{role:"presentation",style:g},p.map(a))))}}function rn(){return o("svg",{"aria-hidden":"true",focusable:"false",width:"30",height:"30",viewBox:"0 0 30 30"},o("path",{d:"M15 30c8.284 0 15-6.716 15-15 0-8.284-6.716-15-15-15C6.716 0 0 6.716 0 15c0 8.284 6.716 15 15 15zm4.258-12.676v6.846h-8.426v-6.846H5.204l9.82-12.364 9.82 12.364H19.26z"}))}var Vt=Number.isNaN||function(e){return typeof e=="number"&&e!==e};function on(i,e){return!!(i===e||Vt(i)&&Vt(e))}function ln(i,e){if(i.length!==e.length)return!1;for(var t=0;t<i.length;t++)if(!on(i[t],e[t]))return!1;return!0}function qt(i,e){e===void 0&&(e=ln);var t=null;function s(){for(var a=[],n=0;n<arguments.length;n++)a[n]=arguments[n];if(t&&t.lastThis===this&&e(a,t.lastArgs))return t.lastResult;var r=i.apply(this,a);return t={lastResult:r,lastArgs:a,lastThis:this},r}return s.clear=function(){t=null},s}const Ti=['a[href]:not([tabindex^="-"]):not([inert]):not([aria-hidden])','area[href]:not([tabindex^="-"]):not([inert]):not([aria-hidden])',"input:not([disabled]):not([inert]):not([aria-hidden])","select:not([disabled]):not([inert]):not([aria-hidden])","textarea:not([disabled]):not([inert]):not([aria-hidden])","button:not([disabled]):not([inert]):not([aria-hidden])",'iframe:not([tabindex^="-"]):not([inert]):not([aria-hidden])','object:not([tabindex^="-"]):not([inert]):not([aria-hidden])','embed:not([tabindex^="-"]):not([inert]):not([aria-hidden])','[contenteditable]:not([tabindex^="-"]):not([inert]):not([aria-hidden])','[tabindex]:not([tabindex^="-"]):not([inert]):not([aria-hidden])'];function _i(i,e){if(e){const t=i.querySelector(`[data-uppy-paneltype="${e}"]`);if(t)return t}return i}function jt(i,e){const t=e[0];t&&(t.focus(),i.preventDefault())}function dn(i,e){const t=e[e.length-1];t&&(t.focus(),i.preventDefault())}function un(i){return i.contains(document.activeElement)}function Ai(i,e,t){const s=_i(t,e),a=ce(s.querySelectorAll(Ti)),n=a.indexOf(document.activeElement);un(s)?i.shiftKey&&n===0?dn(i,a):!i.shiftKey&&n===a.length-1&&jt(i,a):jt(i,a)}function hn(i,e,t){e===null||Ai(i,e,t)}function cn(){let i=!1;return ji((t,s)=>{const a=_i(t,s),n=a.contains(document.activeElement);if(n&&i)return;const r=a.querySelector("[data-uppy-super-focusable]");if(!(n&&!r))if(r)r.focus({preventScroll:!0}),i=!0;else{const l=a.querySelector(Ti);l==null||l.focus({preventScroll:!0}),i=!1}},260)}function pn(){const i=document.body;return!(!("draggable"in i)||!("ondragstart"in i&&"ondrop"in i)||!("FormData"in window)||!("FileReader"in window))}var fn=function(e,t){if(e===t)return!0;for(var s in e)if(!(s in t))return!1;for(var s in t)if(e[s]!==t[s])return!1;return!0};const mn=ei(fn);function gn(){return o("svg",{"aria-hidden":"true",focusable:"false",width:"25",height:"25",viewBox:"0 0 25 25"},o("g",{fill:"#686DE0",fillRule:"evenodd"},o("path",{d:"M5 7v10h15V7H5zm0-1h15a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V7a1 1 0 0 1 1-1z",fillRule:"nonzero"}),o("path",{d:"M6.35 17.172l4.994-5.026a.5.5 0 0 1 .707 0l2.16 2.16 3.505-3.505a.5.5 0 0 1 .707 0l2.336 2.31-.707.72-1.983-1.97-3.505 3.505a.5.5 0 0 1-.707 0l-2.16-2.159-3.938 3.939-1.409.026z",fillRule:"nonzero"}),o("circle",{cx:"7.5",cy:"9.5",r:"1.5"})))}function yn(){return o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"25",height:"25",viewBox:"0 0 25 25"},o("path",{d:"M9.5 18.64c0 1.14-1.145 2-2.5 2s-2.5-.86-2.5-2c0-1.14 1.145-2 2.5-2 .557 0 1.079.145 1.5.396V7.25a.5.5 0 0 1 .379-.485l9-2.25A.5.5 0 0 1 18.5 5v11.64c0 1.14-1.145 2-2.5 2s-2.5-.86-2.5-2c0-1.14 1.145-2 2.5-2 .557 0 1.079.145 1.5.396V8.67l-8 2v7.97zm8-11v-2l-8 2v2l8-2zM7 19.64c.855 0 1.5-.484 1.5-1s-.645-1-1.5-1-1.5.484-1.5 1 .645 1 1.5 1zm9-2c.855 0 1.5-.484 1.5-1s-.645-1-1.5-1-1.5.484-1.5 1 .645 1 1.5 1z",fill:"#049BCF",fillRule:"nonzero"}))}function bn(){return o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"25",height:"25",viewBox:"0 0 25 25"},o("path",{d:"M16 11.834l4.486-2.691A1 1 0 0 1 22 10v6a1 1 0 0 1-1.514.857L16 14.167V17a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V9a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v2.834zM15 9H5v8h10V9zm1 4l5 3v-6l-5 3z",fill:"#19AF67",fillRule:"nonzero"}))}function vn(){return o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"25",height:"25",viewBox:"0 0 25 25"},o("path",{d:"M9.766 8.295c-.691-1.843-.539-3.401.747-3.726 1.643-.414 2.505.938 2.39 3.299-.039.79-.194 1.662-.537 3.148.324.49.66.967 1.055 1.51.17.231.382.488.629.757 1.866-.128 3.653.114 4.918.655 1.487.635 2.192 1.685 1.614 2.84-.566 1.133-1.839 1.084-3.416.249-1.141-.604-2.457-1.634-3.51-2.707a13.467 13.467 0 0 0-2.238.426c-1.392 4.051-4.534 6.453-5.707 4.572-.986-1.58 1.38-4.206 4.914-5.375.097-.322.185-.656.264-1.001.08-.353.306-1.31.407-1.737-.678-1.059-1.2-2.031-1.53-2.91zm2.098 4.87c-.033.144-.068.287-.104.427l.033-.01-.012.038a14.065 14.065 0 0 1 1.02-.197l-.032-.033.052-.004a7.902 7.902 0 0 1-.208-.271c-.197-.27-.38-.526-.555-.775l-.006.028-.002-.003c-.076.323-.148.632-.186.8zm5.77 2.978c1.143.605 1.832.632 2.054.187.26-.519-.087-1.034-1.113-1.473-.911-.39-2.175-.608-3.55-.608.845.766 1.787 1.459 2.609 1.894zM6.559 18.789c.14.223.693.16 1.425-.413.827-.648 1.61-1.747 2.208-3.206-2.563 1.064-4.102 2.867-3.633 3.62zm5.345-10.97c.088-1.793-.351-2.48-1.146-2.28-.473.119-.564 1.05-.056 2.405.213.566.52 1.188.908 1.859.18-.858.268-1.453.294-1.984z",fill:"#E2514A",fillRule:"nonzero"}))}function wn(){return o("svg",{"aria-hidden":"true",focusable:"false",width:"25",height:"25",viewBox:"0 0 25 25"},o("path",{d:"M10.45 2.05h1.05a.5.5 0 0 1 .5.5v.024a.5.5 0 0 1-.5.5h-1.05a.5.5 0 0 1-.5-.5V2.55a.5.5 0 0 1 .5-.5zm2.05 1.024h1.05a.5.5 0 0 1 .5.5V3.6a.5.5 0 0 1-.5.5H12.5a.5.5 0 0 1-.5-.5v-.025a.5.5 0 0 1 .5-.5v-.001zM10.45 0h1.05a.5.5 0 0 1 .5.5v.025a.5.5 0 0 1-.5.5h-1.05a.5.5 0 0 1-.5-.5V.5a.5.5 0 0 1 .5-.5zm2.05 1.025h1.05a.5.5 0 0 1 .5.5v.024a.5.5 0 0 1-.5.5H12.5a.5.5 0 0 1-.5-.5v-.024a.5.5 0 0 1 .5-.5zm-2.05 3.074h1.05a.5.5 0 0 1 .5.5v.025a.5.5 0 0 1-.5.5h-1.05a.5.5 0 0 1-.5-.5v-.025a.5.5 0 0 1 .5-.5zm2.05 1.025h1.05a.5.5 0 0 1 .5.5v.024a.5.5 0 0 1-.5.5H12.5a.5.5 0 0 1-.5-.5v-.024a.5.5 0 0 1 .5-.5zm-2.05 1.024h1.05a.5.5 0 0 1 .5.5v.025a.5.5 0 0 1-.5.5h-1.05a.5.5 0 0 1-.5-.5v-.025a.5.5 0 0 1 .5-.5zm2.05 1.025h1.05a.5.5 0 0 1 .5.5v.025a.5.5 0 0 1-.5.5H12.5a.5.5 0 0 1-.5-.5v-.025a.5.5 0 0 1 .5-.5zm-2.05 1.025h1.05a.5.5 0 0 1 .5.5v.025a.5.5 0 0 1-.5.5h-1.05a.5.5 0 0 1-.5-.5v-.025a.5.5 0 0 1 .5-.5zm2.05 1.025h1.05a.5.5 0 0 1 .5.5v.024a.5.5 0 0 1-.5.5H12.5a.5.5 0 0 1-.5-.5v-.024a.5.5 0 0 1 .5-.5zm-1.656 3.074l-.82 5.946c.52.302 1.174.458 1.976.458.803 0 1.455-.156 1.975-.458l-.82-5.946h-2.311zm0-1.025h2.312c.512 0 .946.378 1.015.885l.82 5.946c.056.412-.142.817-.501 1.026-.686.398-1.515.597-2.49.597-.974 0-1.804-.199-2.49-.597a1.025 1.025 0 0 1-.5-1.026l.819-5.946c.07-.507.503-.885 1.015-.885zm.545 6.6a.5.5 0 0 1-.397-.561l.143-.999a.5.5 0 0 1 .495-.429h.74a.5.5 0 0 1 .495.43l.143.998a.5.5 0 0 1-.397.561c-.404.08-.819.08-1.222 0z",fill:"#00C469",fillRule:"nonzero"}))}function Fn(){return o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"25",height:"25",viewBox:"0 0 25 25"},o("g",{fill:"#A7AFB7",fillRule:"nonzero"},o("path",{d:"M5.5 22a.5.5 0 0 1-.5-.5v-18a.5.5 0 0 1 .5-.5h10.719a.5.5 0 0 1 .367.16l3.281 3.556a.5.5 0 0 1 .133.339V21.5a.5.5 0 0 1-.5.5h-14zm.5-1h13V7.25L16 4H6v17z"}),o("path",{d:"M15 4v3a1 1 0 0 0 1 1h3V7h-3V4h-1z"})))}function Pn(){return o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"25",height:"25",viewBox:"0 0 25 25"},o("path",{d:"M4.5 7h13a.5.5 0 1 1 0 1h-13a.5.5 0 0 1 0-1zm0 3h15a.5.5 0 1 1 0 1h-15a.5.5 0 1 1 0-1zm0 3h15a.5.5 0 1 1 0 1h-15a.5.5 0 1 1 0-1zm0 3h10a.5.5 0 1 1 0 1h-10a.5.5 0 1 1 0-1z",fill:"#5A5E69",fillRule:"nonzero"}))}function mt(i){const e={color:"#838999",icon:Fn()};if(!i)return e;const t=i.split("/")[0],s=i.split("/")[1];return t==="text"?{color:"#5a5e69",icon:Pn()}:t==="image"?{color:"#686de0",icon:gn()}:t==="audio"?{color:"#068dbb",icon:yn()}:t==="video"?{color:"#19af67",icon:bn()}:t==="application"&&s==="pdf"?{color:"#e25149",icon:vn()}:t==="application"&&["zip","x-7z-compressed","x-rar-compressed","x-tar","x-gzip","x-apple-diskimage"].indexOf(s)!==-1?{color:"#00C469",icon:wn()}:e}function Ei(i){const{file:e}=i;if(e.preview)return o("img",{className:"uppy-Dashboard-Item-previewImg",alt:e.name,src:e.preview});const{color:t,icon:s}=mt(e.type);return o("div",{className:"uppy-Dashboard-Item-previewIconWrap"},o("span",{className:"uppy-Dashboard-Item-previewIcon",style:{color:t}},s),o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-Dashboard-Item-previewIconBg",width:"58",height:"76",viewBox:"0 0 58 76"},o("rect",{fill:"#FFF",width:"58",height:"76",rx:"3",fillRule:"evenodd"})))}const Sn=(i,e)=>(typeof e=="function"?e():e).filter(a=>a.id===i)[0].name;function Di(i){const{file:e,toggleFileCard:t,i18n:s,metaFields:a}=i,{missingRequiredMetaFields:n}=e;if(!(n!=null&&n.length))return null;const r=n.map(l=>Sn(l,a)).join(", ");return o("div",{className:"uppy-Dashboard-Item-errorMessage"},s("missingRequiredMetaFields",{smart_count:n.length,fields:r})," ",o("button",{type:"button",class:"uppy-u-reset uppy-Dashboard-Item-errorMessageBtn",onClick:()=>t(!0,e.id)},s("editFile")))}function Cn(i){const{file:e,i18n:t,toggleFileCard:s,metaFields:a,showLinkToFileUploadResult:n}=i,r="rgba(255, 255, 255, 0.5)",l=e.preview?r:mt(i.file.type).color;return o("div",{className:"uppy-Dashboard-Item-previewInnerWrap",style:{backgroundColor:l}},n&&e.uploadURL&&o("a",{className:"uppy-Dashboard-Item-previewLink",href:e.uploadURL,rel:"noreferrer noopener",target:"_blank","aria-label":e.meta.name},o("span",{hidden:!0},e.meta.name)),o(Ei,{file:e}),o(Di,{file:e,i18n:t,toggleFileCard:s,metaFields:a}))}function Tn(i){if(!i.isUploaded){if(i.error&&!i.hideRetryButton){i.uppy.retryUpload(i.file.id);return}i.resumableUploads&&!i.hidePauseResumeButton?i.uppy.pauseResume(i.file.id):i.individualCancellation&&!i.hideCancelButton&&i.uppy.removeFile(i.file.id)}}function Wt(i){return i.isUploaded?i.i18n("uploadComplete"):i.error?i.i18n("retryUpload"):i.resumableUploads?i.file.isPaused?i.i18n("resumeUpload"):i.i18n("pauseUpload"):i.individualCancellation?i.i18n("cancelUpload"):""}function je(i){return o("div",{className:"uppy-Dashboard-Item-progress"},o("button",{className:"uppy-u-reset uppy-c-btn uppy-Dashboard-Item-progressIndicator",type:"button","aria-label":Wt(i),title:Wt(i),onClick:()=>Tn(i)},i.children))}function _e(i){let{children:e}=i;return o("svg",{"aria-hidden":"true",focusable:"false",width:"70",height:"70",viewBox:"0 0 36 36",className:"uppy-c-icon uppy-Dashboard-Item-progressIcon--circle"},e)}function We(i){let{progress:e}=i;const t=2*Math.PI*15;return o("g",null,o("circle",{className:"uppy-Dashboard-Item-progressIcon--bg",r:"15",cx:"18",cy:"18","stroke-width":"2",fill:"none"}),o("circle",{className:"uppy-Dashboard-Item-progressIcon--progress",r:"15",cx:"18",cy:"18",transform:"rotate(-90, 18, 18)",fill:"none","stroke-width":"2","stroke-dasharray":t,"stroke-dashoffset":t-t/100*e}))}function _n(i){if(!i.file.progress.uploadStarted)return null;if(i.isUploaded)return o("div",{className:"uppy-Dashboard-Item-progress"},o("div",{className:"uppy-Dashboard-Item-progressIndicator"},o(_e,null,o("circle",{r:"15",cx:"18",cy:"18",fill:"#1bb240"}),o("polygon",{className:"uppy-Dashboard-Item-progressIcon--check",transform:"translate(2, 3)",points:"14 22.5 7 15.2457065 8.99985857 13.1732815 14 18.3547104 22.9729883 9 25 11.1005634"}))));if(!i.recoveredState)return i.error&&!i.hideRetryButton?o(je,i,o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon uppy-Dashboard-Item-progressIcon--retry",width:"28",height:"31",viewBox:"0 0 16 19"},o("path",{d:"M16 11a8 8 0 1 1-8-8v2a6 6 0 1 0 6 6h2z"}),o("path",{d:"M7.9 3H10v2H7.9z"}),o("path",{d:"M8.536.5l3.535 3.536-1.414 1.414L7.12 1.914z"}),o("path",{d:"M10.657 2.621l1.414 1.415L8.536 7.57 7.12 6.157z"}))):i.resumableUploads&&!i.hidePauseResumeButton?o(je,i,o(_e,null,o(We,{progress:i.file.progress.percentage}),i.file.isPaused?o("polygon",{className:"uppy-Dashboard-Item-progressIcon--play",transform:"translate(3, 3)",points:"12 20 12 10 20 15"}):o("g",{className:"uppy-Dashboard-Item-progressIcon--pause",transform:"translate(14.5, 13)"},o("rect",{x:"0",y:"0",width:"2",height:"10",rx:"0"}),o("rect",{x:"5",y:"0",width:"2",height:"10",rx:"0"})))):!i.resumableUploads&&i.individualCancellation&&!i.hideCancelButton?o(je,i,o(_e,null,o(We,{progress:i.file.progress.percentage}),o("polygon",{className:"cancel",transform:"translate(2, 2)",points:"19.8856516 11.0625 16 14.9481516 12.1019737 11.0625 11.0625 12.1143484 14.9481516 16 11.0625 19.8980263 12.1019737 20.9375 16 17.0518484 19.8856516 20.9375 20.9375 19.8980263 17.0518484 16 20.9375 12"}))):o("div",{className:"uppy-Dashboard-Item-progress"},o("div",{className:"uppy-Dashboard-Item-progressIndicator"},o(_e,null,o(We,{progress:i.file.progress.percentage}))))}const Ge="...";function ki(i,e){if(e===0)return"";if(i.length<=e)return i;if(e<=Ge.length+1)return`${i.slice(0,e-1)}…`;const t=e-Ge.length,s=Math.ceil(t/2),a=Math.floor(t/2);return i.slice(0,s)+Ge+i.slice(-a)}const An=i=>{const{author:e,name:t}=i.file.meta;function s(){return i.isSingleFile&&i.containerHeight>=350?90:i.containerWidth<=352?35:i.containerWidth<=576?60:e?20:30}return o("div",{className:"uppy-Dashboard-Item-name",title:t},ki(t,s()))},En=i=>{var e;const{author:t}=i.file.meta,s=(e=i.file.remote)==null?void 0:e.providerName,a="·";return t?o("div",{className:"uppy-Dashboard-Item-author"},o("a",{href:`${t.url}?utm_source=Companion&utm_medium=referral`,target:"_blank",rel:"noopener noreferrer"},ki(t.name,13)),s?o(Ee,null,` ${a} `,s,` ${a} `):null):null},Dn=i=>i.file.size&&o("div",{className:"uppy-Dashboard-Item-statusSize"},tt(i.file.size)),kn=i=>i.file.isGhost&&o("span",null," • ",o("button",{className:"uppy-u-reset uppy-c-btn uppy-Dashboard-Item-reSelect",type:"button",onClick:i.toggleAddFilesPanel},i.i18n("reSelect"))),On=i=>{let{file:e,onClick:t}=i;return e.error?o("button",{className:"uppy-u-reset uppy-c-btn uppy-Dashboard-Item-errorDetails","aria-label":e.error,"data-microtip-position":"bottom","data-microtip-size":"medium",onClick:t,type:"button"},"?"):null};function Bn(i){const{file:e}=i;return o("div",{className:"uppy-Dashboard-Item-fileInfo","data-uppy-file-source":e.source},o("div",{className:"uppy-Dashboard-Item-fileName"},An(i),o(On,{file:i.file,onClick:()=>alert(i.file.error)})),o("div",{className:"uppy-Dashboard-Item-status"},En(i),Dn(i),kn(i)),o(Di,{file:i.file,i18n:i.i18n,toggleFileCard:i.toggleFileCard,metaFields:i.metaFields}))}function Nn(i,e){return e===void 0&&(e="Copy the URL below"),new Promise(t=>{const s=document.createElement("textarea");s.setAttribute("style",{position:"fixed",top:0,left:0,width:"2em",height:"2em",padding:0,border:"none",outline:"none",boxShadow:"none",background:"transparent"}),s.value=i,document.body.appendChild(s),s.select();const a=()=>{document.body.removeChild(s),window.prompt(e,i),t()};try{return document.execCommand("copy")?(document.body.removeChild(s),t()):a("copy command unavailable")}catch{return document.body.removeChild(s),a()}})}function In(i){let{file:e,uploadInProgressOrComplete:t,metaFields:s,canEditFile:a,i18n:n,onClick:r}=i;return!t&&s&&s.length>0||!t&&a(e)?o("button",{className:"uppy-u-reset uppy-c-btn uppy-Dashboard-Item-action uppy-Dashboard-Item-action--edit",type:"button","aria-label":n("editFileWithFilename",{file:e.meta.name}),title:n("editFileWithFilename",{file:e.meta.name}),onClick:()=>r()},o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"14",height:"14",viewBox:"0 0 14 14"},o("g",{fillRule:"evenodd"},o("path",{d:"M1.5 10.793h2.793A1 1 0 0 0 5 10.5L11.5 4a1 1 0 0 0 0-1.414L9.707.793a1 1 0 0 0-1.414 0l-6.5 6.5A1 1 0 0 0 1.5 8v2.793zm1-1V8L9 1.5l1.793 1.793-6.5 6.5H2.5z",fillRule:"nonzero"}),o("rect",{x:"1",y:"12.293",width:"11",height:"1",rx:".5"}),o("path",{fillRule:"nonzero",d:"M6.793 2.5L9.5 5.207l.707-.707L7.5 1.793z"})))):null}function Un(i){let{i18n:e,onClick:t,file:s}=i;return o("button",{className:"uppy-u-reset uppy-Dashboard-Item-action uppy-Dashboard-Item-action--remove",type:"button","aria-label":e("removeFile",{file:s.meta.name}),title:e("removeFile",{file:s.meta.name}),onClick:()=>t()},o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"18",height:"18",viewBox:"0 0 18 18"},o("path",{d:"M9 0C4.034 0 0 4.034 0 9s4.034 9 9 9 9-4.034 9-9-4.034-9-9-9z"}),o("path",{fill:"#FFF",d:"M13 12.222l-.778.778L9 9.778 5.778 13 5 12.222 8.222 9 5 5.778 5.778 5 9 8.222 12.222 5l.778.778L9.778 9z"})))}const Rn=(i,e)=>{Nn(e.file.uploadURL,e.i18n("copyLinkToClipboardFallback")).then(()=>{e.uppy.log("Link copied to clipboard."),e.uppy.info(e.i18n("copyLinkToClipboardSuccess"),"info",3e3)}).catch(e.uppy.log).then(()=>i.target.focus({preventScroll:!0}))};function Mn(i){const{i18n:e}=i;return o("button",{className:"uppy-u-reset uppy-Dashboard-Item-action uppy-Dashboard-Item-action--copyLink",type:"button","aria-label":e("copyLink"),title:e("copyLink"),onClick:t=>Rn(t,i)},o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"14",height:"14",viewBox:"0 0 14 12"},o("path",{d:"M7.94 7.703a2.613 2.613 0 0 1-.626 2.681l-.852.851a2.597 2.597 0 0 1-1.849.766A2.616 2.616 0 0 1 2.764 7.54l.852-.852a2.596 2.596 0 0 1 2.69-.625L5.267 7.099a1.44 1.44 0 0 0-.833.407l-.852.851a1.458 1.458 0 0 0 1.03 2.486c.39 0 .755-.152 1.03-.426l.852-.852c.231-.231.363-.522.406-.824l1.04-1.038zm4.295-5.937A2.596 2.596 0 0 0 10.387 1c-.698 0-1.355.272-1.849.766l-.852.851a2.614 2.614 0 0 0-.624 2.688l1.036-1.036c.041-.304.173-.6.407-.833l.852-.852c.275-.275.64-.426 1.03-.426a1.458 1.458 0 0 1 1.03 2.486l-.852.851a1.442 1.442 0 0 1-.824.406l-1.04 1.04a2.596 2.596 0 0 0 2.683-.628l.851-.85a2.616 2.616 0 0 0 0-3.697zm-6.88 6.883a.577.577 0 0 0 .82 0l3.474-3.474a.579.579 0 1 0-.819-.82L5.355 7.83a.579.579 0 0 0 0 .819z"})))}function Ln(i){const{uppy:e,file:t,uploadInProgressOrComplete:s,canEditFile:a,metaFields:n,showLinkToFileUploadResult:r,showRemoveButton:l,i18n:u,toggleFileCard:d,openFileEditor:h}=i;return o("div",{className:"uppy-Dashboard-Item-actionWrapper"},o(In,{i18n:u,file:t,uploadInProgressOrComplete:s,canEditFile:a,metaFields:n,onClick:()=>{n&&n.length>0?d(!0,t.id):h(t)}}),r&&t.uploadURL?o(Mn,{file:t,uppy:e,i18n:u}):null,l?o(Un,{i18n:u,file:t,uppy:e,onClick:()=>i.uppy.removeFile(t.id,"removed-by-user")}):null)}class xn extends se{componentDidMount(){const{file:e}=this.props;e.preview||this.props.handleRequestThumbnail(e)}shouldComponentUpdate(e){return!mn(this.props,e)}componentDidUpdate(){const{file:e}=this.props;e.preview||this.props.handleRequestThumbnail(e)}componentWillUnmount(){const{file:e}=this.props;e.preview||this.props.handleCancelThumbnail(e)}render(){const{file:e}=this.props,t=e.progress.preprocess||e.progress.postprocess,s=e.progress.uploadComplete&&!t&&!e.error,a=e.progress.uploadStarted||t,n=e.progress.uploadStarted&&!e.progress.uploadComplete||t,r=e.error||!1,{isGhost:l}=e;let u=(this.props.individualCancellation||!n)&&!s;s&&this.props.showRemoveButtonAfterComplete&&(u=!0);const d=L({"uppy-Dashboard-Item":!0,"is-inprogress":n&&!this.props.recoveredState,"is-processing":t,"is-complete":s,"is-error":!!r,"is-resumable":this.props.resumableUploads,"is-noIndividualCancellation":!this.props.individualCancellation,"is-ghost":l});return o("div",{className:d,id:`uppy_${e.id}`,role:this.props.role},o("div",{className:"uppy-Dashboard-Item-preview"},o(Cn,{file:e,showLinkToFileUploadResult:this.props.showLinkToFileUploadResult,i18n:this.props.i18n,toggleFileCard:this.props.toggleFileCard,metaFields:this.props.metaFields}),o(_n,{uppy:this.props.uppy,file:e,error:r,isUploaded:s,hideRetryButton:this.props.hideRetryButton,hideCancelButton:this.props.hideCancelButton,hidePauseResumeButton:this.props.hidePauseResumeButton,recoveredState:this.props.recoveredState,showRemoveButtonAfterComplete:this.props.showRemoveButtonAfterComplete,resumableUploads:this.props.resumableUploads,individualCancellation:this.props.individualCancellation,i18n:this.props.i18n})),o("div",{className:"uppy-Dashboard-Item-fileInfoAndButtons"},o(Bn,{file:e,id:this.props.id,acquirers:this.props.acquirers,containerWidth:this.props.containerWidth,containerHeight:this.props.containerHeight,i18n:this.props.i18n,toggleAddFilesPanel:this.props.toggleAddFilesPanel,toggleFileCard:this.props.toggleFileCard,metaFields:this.props.metaFields,isSingleFile:this.props.isSingleFile}),o(Ln,{file:e,metaFields:this.props.metaFields,showLinkToFileUploadResult:this.props.showLinkToFileUploadResult,showRemoveButton:u,canEditFile:this.props.canEditFile,uploadInProgressOrComplete:a,toggleFileCard:this.props.toggleFileCard,openFileEditor:this.props.openFileEditor,uppy:this.props.uppy,i18n:this.props.i18n})))}}function zn(i,e){const t=[];let s=[];return i.forEach(a=>{s.length<e?s.push(a):(t.push(s),s=[a])}),s.length&&t.push(s),t}const $n=i=>{let{id:e,error:t,i18n:s,uppy:a,files:n,acquirers:r,resumableUploads:l,hideRetryButton:u,hidePauseResumeButton:d,hideCancelButton:h,showLinkToFileUploadResult:c,showRemoveButtonAfterComplete:p,isWide:m,metaFields:g,isSingleFile:v,toggleFileCard:f,handleRequestThumbnail:w,handleCancelThumbnail:S,recoveredState:y,individualCancellation:F,itemsPerRow:T,openFileEditor:I,canEditFile:A,toggleAddFilesPanel:E,containerWidth:H,containerHeight:ge}=i;const Ie=T===1?71:200,ye=Pi(()=>{const Z=(Ue,ae)=>n[ae].isGhost-n[Ue].isGhost,G=Object.keys(n);return y&&G.sort(Z),zn(G,T)},[n,T,y]),be=Z=>o("div",{class:"uppy-Dashboard-filesInner",role:"presentation",key:Z[0]},Z.map(G=>o(xn,{key:G,uppy:a,id:e,error:t,i18n:s,acquirers:r,resumableUploads:l,individualCancellation:F,hideRetryButton:u,hidePauseResumeButton:d,hideCancelButton:h,showLinkToFileUploadResult:c,showRemoveButtonAfterComplete:p,isWide:m,metaFields:g,recoveredState:y,isSingleFile:v,containerWidth:H,containerHeight:ge,toggleFileCard:f,handleRequestThumbnail:w,handleCancelThumbnail:S,role:"listitem",openFileEditor:I,canEditFile:A,toggleAddFilesPanel:E,file:n[G]})));return v?o("div",{class:"uppy-Dashboard-files"},be(ye[0])):o(an,{class:"uppy-Dashboard-files",role:"list",data:ye,renderRow:be,rowHeight:Ie})};let Oi;Oi=Symbol.for("uppy test: disable unused locale key warning");class Bi extends se{constructor(){super(...arguments),this.triggerFileInputClick=()=>{this.fileInput.click()},this.triggerFolderInputClick=()=>{this.folderInput.click()},this.triggerVideoCameraInputClick=()=>{this.mobileVideoFileInput.click()},this.triggerPhotoCameraInputClick=()=>{this.mobilePhotoFileInput.click()},this.onFileInputChange=e=>{this.props.handleInputChange(e),e.target.value=null},this.renderHiddenInput=(e,t)=>o("input",{className:"uppy-Dashboard-input",hidden:!0,"aria-hidden":"true",tabIndex:-1,webkitdirectory:e,type:"file",name:"files[]",multiple:this.props.maxNumberOfFiles!==1,onChange:this.onFileInputChange,accept:this.props.allowedFileTypes,ref:t}),this.renderHiddenCameraInput=(e,t,s)=>{const n={photo:"image/*",video:"video/*"}[e];return o("input",{className:"uppy-Dashboard-input",hidden:!0,"aria-hidden":"true",tabIndex:-1,type:"file",name:`camera-${e}`,onChange:this.onFileInputChange,capture:t,accept:n,ref:s})},this.renderMyDeviceAcquirer=()=>o("div",{className:"uppy-DashboardTab",role:"presentation","data-uppy-acquirer-id":"MyDevice"},o("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-DashboardTab-btn",role:"tab",tabIndex:0,"data-uppy-super-focusable":!0,onClick:this.triggerFileInputClick},o("div",{className:"uppy-DashboardTab-inner"},o("svg",{className:"uppy-DashboardTab-iconMyDevice","aria-hidden":"true",focusable:"false",width:"32",height:"32",viewBox:"0 0 32 32"},o("path",{d:"M8.45 22.087l-1.305-6.674h17.678l-1.572 6.674H8.45zm4.975-12.412l1.083 1.765a.823.823 0 00.715.386h7.951V13.5H8.587V9.675h4.838zM26.043 13.5h-1.195v-2.598c0-.463-.336-.75-.798-.75h-8.356l-1.082-1.766A.823.823 0 0013.897 8H7.728c-.462 0-.815.256-.815.718V13.5h-.956a.97.97 0 00-.746.37.972.972 0 00-.19.81l1.724 8.565c.095.44.484.755.933.755H24c.44 0 .824-.3.929-.727l2.043-8.568a.972.972 0 00-.176-.825.967.967 0 00-.753-.38z",fill:"currentcolor","fill-rule":"evenodd"}))),o("div",{className:"uppy-DashboardTab-name"},this.props.i18n("myDevice")))),this.renderPhotoCamera=()=>o("div",{className:"uppy-DashboardTab",role:"presentation","data-uppy-acquirer-id":"MobilePhotoCamera"},o("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-DashboardTab-btn",role:"tab",tabIndex:0,"data-uppy-super-focusable":!0,onClick:this.triggerPhotoCameraInputClick},o("div",{className:"uppy-DashboardTab-inner"},o("svg",{"aria-hidden":"true",focusable:"false",width:"32",height:"32",viewBox:"0 0 32 32"},o("path",{d:"M23.5 9.5c1.417 0 2.5 1.083 2.5 2.5v9.167c0 1.416-1.083 2.5-2.5 2.5h-15c-1.417 0-2.5-1.084-2.5-2.5V12c0-1.417 1.083-2.5 2.5-2.5h2.917l1.416-2.167C13 7.167 13.25 7 13.5 7h5c.25 0 .5.167.667.333L20.583 9.5H23.5zM16 11.417a4.706 4.706 0 00-4.75 4.75 4.704 4.704 0 004.75 4.75 4.703 4.703 0 004.75-4.75c0-2.663-2.09-4.75-4.75-4.75zm0 7.825c-1.744 0-3.076-1.332-3.076-3.074 0-1.745 1.333-3.077 3.076-3.077 1.744 0 3.074 1.333 3.074 3.076s-1.33 3.075-3.074 3.075z",fill:"#02B383","fill-rule":"nonzero"}))),o("div",{className:"uppy-DashboardTab-name"},this.props.i18n("takePictureBtn")))),this.renderVideoCamera=()=>o("div",{className:"uppy-DashboardTab",role:"presentation","data-uppy-acquirer-id":"MobileVideoCamera"},o("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-DashboardTab-btn",role:"tab",tabIndex:0,"data-uppy-super-focusable":!0,onClick:this.triggerVideoCameraInputClick},o("div",{className:"uppy-DashboardTab-inner"},o("svg",{"aria-hidden":"true",width:"32",height:"32",viewBox:"0 0 32 32"},o("path",{fill:"#FF675E",fillRule:"nonzero",d:"m21.254 14.277 2.941-2.588c.797-.313 1.243.818 1.09 1.554-.01 2.094.02 4.189-.017 6.282-.126.915-1.145 1.08-1.58.34l-2.434-2.142c-.192.287-.504 1.305-.738.468-.104-1.293-.028-2.596-.05-3.894.047-.312.381.823.426 1.069.063-.384.206-.744.362-1.09zm-12.939-3.73c3.858.013 7.717-.025 11.574.02.912.129 1.492 1.237 1.351 2.217-.019 2.412.04 4.83-.03 7.239-.17 1.025-1.166 1.59-2.029 1.429-3.705-.012-7.41.025-11.114-.019-.913-.129-1.492-1.237-1.352-2.217.018-2.404-.036-4.813.029-7.214.136-.82.83-1.473 1.571-1.454z "}))),o("div",{className:"uppy-DashboardTab-name"},this.props.i18n("recordVideoBtn")))),this.renderBrowseButton=(e,t)=>{const s=this.props.acquirers.length;return o("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-Dashboard-browse",onClick:t,"data-uppy-super-focusable":s===0},e)},this.renderDropPasteBrowseTagline=e=>{const t=this.renderBrowseButton(this.props.i18n("browseFiles"),this.triggerFileInputClick),s=this.renderBrowseButton(this.props.i18n("browseFolders"),this.triggerFolderInputClick),a=this.props.fileManagerSelectionType,n=a.charAt(0).toUpperCase()+a.slice(1);return o("div",{class:"uppy-Dashboard-AddFiles-title"},this.props.disableLocalFiles?this.props.i18n("importFiles"):e>0?this.props.i18nArray(`dropPasteImport${n}`,{browseFiles:t,browseFolders:s,browse:t}):this.props.i18nArray(`dropPaste${n}`,{browseFiles:t,browseFolders:s,browse:t}))},this.renderAcquirer=e=>o("div",{className:"uppy-DashboardTab",role:"presentation","data-uppy-acquirer-id":e.id},o("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-DashboardTab-btn",role:"tab",tabIndex:0,"data-cy":e.id,"aria-controls":`uppy-DashboardContent-panel--${e.id}`,"aria-selected":this.props.activePickerPanel.id===e.id,"data-uppy-super-focusable":!0,onClick:()=>this.props.showPanel(e.id)},o("div",{className:"uppy-DashboardTab-inner"},e.icon()),o("div",{className:"uppy-DashboardTab-name"},e.name))),this.renderAcquirers=e=>{const t=[...e],s=t.splice(e.length-2,e.length);return o(Ee,null,t.map(a=>this.renderAcquirer(a)),o("span",{role:"presentation",style:{"white-space":"nowrap"}},s.map(a=>this.renderAcquirer(a))))},this.renderSourcesList=(e,t)=>{const{showNativePhotoCameraButton:s,showNativeVideoCameraButton:a}=this.props;let n=[];const r="myDevice";t||n.push({key:r,elements:this.renderMyDeviceAcquirer()}),s&&n.push({key:"nativePhotoCameraButton",elements:this.renderPhotoCamera()}),a&&n.push({key:"nativePhotoCameraButton",elements:this.renderVideoCamera()}),n.push(...e.map(c=>({key:c.id,elements:this.renderAcquirer(c)}))),n.length===1&&n[0].key===r&&(n=[]);const u=[...n],d=u.splice(n.length-2,n.length),h=c=>c.map(p=>{let{key:m,elements:g}=p;return o(Ee,{key:m},g)});return o(Ee,null,this.renderDropPasteBrowseTagline(n.length),o("div",{className:"uppy-Dashboard-AddFiles-list",role:"tablist"},h(u),o("span",{role:"presentation",style:{"white-space":"nowrap"}},h(d))))}}[Oi](){this.props.i18nArray("dropPasteBoth"),this.props.i18nArray("dropPasteFiles"),this.props.i18nArray("dropPasteFolders"),this.props.i18nArray("dropPasteImportBoth"),this.props.i18nArray("dropPasteImportFiles"),this.props.i18nArray("dropPasteImportFolders")}renderPoweredByUppy(){const{i18nArray:e}=this.props,t=o("span",null,o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon uppy-Dashboard-poweredByIcon",width:"11",height:"11",viewBox:"0 0 11 11"},o("path",{d:"M7.365 10.5l-.01-4.045h2.612L5.5.806l-4.467 5.65h2.604l.01 4.044h3.718z",fillRule:"evenodd"})),o("span",{className:"uppy-Dashboard-poweredByUppy"},"Uppy")),s=e("poweredBy",{uppy:t});return o("a",{tabIndex:"-1",href:"https://uppy.io",rel:"noreferrer noopener",target:"_blank",className:"uppy-Dashboard-poweredBy"},s)}render(){const{showNativePhotoCameraButton:e,showNativeVideoCameraButton:t,nativeCameraFacingMode:s}=this.props;return o("div",{className:"uppy-Dashboard-AddFiles"},this.renderHiddenInput(!1,a=>{this.fileInput=a}),this.renderHiddenInput(!0,a=>{this.folderInput=a}),e&&this.renderHiddenCameraInput("photo",s,a=>{this.mobilePhotoFileInput=a}),t&&this.renderHiddenCameraInput("video",s,a=>{this.mobileVideoFileInput=a}),this.renderSourcesList(this.props.acquirers,this.props.disableLocalFiles),o("div",{className:"uppy-Dashboard-AddFiles-info"},this.props.note&&o("div",{className:"uppy-Dashboard-note"},this.props.note),this.props.proudlyDisplayPoweredByUppy&&this.renderPoweredByUppy(this.props)))}}const Hn=i=>o("div",{className:L("uppy-Dashboard-AddFilesPanel",i.className),"data-uppy-panelType":"AddFiles","aria-hidden":!i.showAddFilesPanel},o("div",{className:"uppy-DashboardContent-bar"},o("div",{className:"uppy-DashboardContent-title",role:"heading","aria-level":"1"},i.i18n("addingMoreFiles")),o("button",{className:"uppy-DashboardContent-back",type:"button",onClick:()=>i.toggleAddFilesPanel(!1)},i.i18n("back"))),o(Bi,i));function W(i){const{tagName:e}=i.target;if(e==="INPUT"||e==="TEXTAREA"){i.stopPropagation();return}i.preventDefault(),i.stopPropagation()}function Vn(i){let{activePickerPanel:e,className:t,hideAllPanels:s,i18n:a,state:n,uppy:r}=i;return o("div",{className:L("uppy-DashboardContent-panel",t),role:"tabpanel","data-uppy-panelType":"PickerPanel",id:`uppy-DashboardContent-panel--${e.id}`,onDragOver:W,onDragLeave:W,onDrop:W,onPaste:W},o("div",{className:"uppy-DashboardContent-bar"},o("div",{className:"uppy-DashboardContent-title",role:"heading","aria-level":"1"},a("importFrom",{name:e.name})),o("button",{className:"uppy-DashboardContent-back",type:"button",onClick:s},a("cancel"))),o("div",{className:"uppy-DashboardContent-panelBody"},r.getPlugin(e.id).render(n)))}function qn(i){const e=i.files[i.fileCardFor],t=()=>{i.uppy.emit("file-editor:cancel",e),i.closeFileEditor()};return o("div",{className:L("uppy-DashboardContent-panel",i.className),role:"tabpanel","data-uppy-panelType":"FileEditor",id:"uppy-DashboardContent-panel--editor"},o("div",{className:"uppy-DashboardContent-bar"},o("div",{className:"uppy-DashboardContent-title",role:"heading","aria-level":"1"},i.i18nArray("editing",{file:o("span",{className:"uppy-DashboardContent-titleFile"},e.meta?e.meta.name:e.name)})),o("button",{className:"uppy-DashboardContent-back",type:"button",onClick:t},i.i18n("cancel")),o("button",{className:"uppy-DashboardContent-save",type:"button",onClick:i.saveFileEditor},i.i18n("save"))),o("div",{className:"uppy-DashboardContent-panelBody"},i.editors.map(s=>i.uppy.getPlugin(s.id).render(i.state))))}const z={STATE_ERROR:"error",STATE_WAITING:"waiting",STATE_PREPROCESSING:"preprocessing",STATE_UPLOADING:"uploading",STATE_POSTPROCESSING:"postprocessing",STATE_COMPLETE:"complete",STATE_PAUSED:"paused"};function jn(i,e,t,s){if(s===void 0&&(s={}),i)return z.STATE_ERROR;if(e)return z.STATE_COMPLETE;if(t)return z.STATE_PAUSED;let a=z.STATE_WAITING;const n=Object.keys(s);for(let r=0;r<n.length;r++){const{progress:l}=s[n[r]];if(l.uploadStarted&&!l.uploadComplete)return z.STATE_UPLOADING;l.preprocess&&a!==z.STATE_UPLOADING&&(a=z.STATE_PREPROCESSING),l.postprocess&&a!==z.STATE_UPLOADING&&a!==z.STATE_PREPROCESSING&&(a=z.STATE_POSTPROCESSING)}return a}function Wn(i){let{files:e,i18n:t,isAllComplete:s,isAllErrored:a,isAllPaused:n,inProgressNotPausedFiles:r,newFiles:l,processingFiles:u}=i;switch(jn(a,s,n,e)){case"uploading":return t("uploadingXFiles",{smart_count:r.length});case"preprocessing":case"postprocessing":return t("processingXFiles",{smart_count:u.length});case"paused":return t("uploadPaused");case"waiting":return t("xFilesSelected",{smart_count:l.length});case"complete":return t("uploadComplete");case"error":return t("error")}}function Gn(i){const{i18n:e,isAllComplete:t,hideCancelButton:s,maxNumberOfFiles:a,toggleAddFilesPanel:n,uppy:r}=i;let{allowNewUpload:l}=i;return l&&a&&(l=i.totalFileCount<i.maxNumberOfFiles),o("div",{className:"uppy-DashboardContent-bar"},!t&&!s?o("button",{className:"uppy-DashboardContent-back",type:"button",onClick:()=>r.cancelAll()},e("cancel")):o("div",null),o("div",{className:"uppy-DashboardContent-title",role:"heading","aria-level":"1"},o(Wn,i)),l?o("button",{className:"uppy-DashboardContent-addMore",type:"button","aria-label":e("addMoreFiles"),title:e("addMoreFiles"),onClick:()=>n(!0)},o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"15",height:"15",viewBox:"0 0 15 15"},o("path",{d:"M8 6.5h6a.5.5 0 0 1 .5.5v.5a.5.5 0 0 1-.5.5H8v6a.5.5 0 0 1-.5.5H7a.5.5 0 0 1-.5-.5V8h-6a.5.5 0 0 1-.5-.5V7a.5.5 0 0 1 .5-.5h6v-6A.5.5 0 0 1 7 0h.5a.5.5 0 0 1 .5.5v6z"})),o("span",{className:"uppy-DashboardContent-addMoreCaption"},e("addMore"))):o("div",null))}function Kn(i){const{computedMetaFields:e,requiredMetaFields:t,updateMeta:s,form:a,formState:n}=i,r={text:"uppy-u-reset uppy-c-textInput uppy-Dashboard-FileCard-input"};return e.map(l=>{const u=`uppy-Dashboard-FileCard-input-${l.id}`,d=t.includes(l.id);return o("fieldset",{key:l.id,className:"uppy-Dashboard-FileCard-fieldset"},o("label",{className:"uppy-Dashboard-FileCard-label",htmlFor:u},l.name),l.render!==void 0?l.render({value:n[l.id],onChange:h=>s(h,l.id),fieldCSSClasses:r,required:d,form:a.id},o):o("input",{className:r.text,id:u,form:a.id,type:l.type||"text",required:d,value:n[l.id],placeholder:l.placeholder,onInput:h=>s(h.target.value,l.id),"data-uppy-super-focusable":!0}))})}function Xn(i){var e;const{files:t,fileCardFor:s,toggleFileCard:a,saveFileCard:n,metaFields:r,requiredMetaFields:l,openFileEditor:u,i18n:d,i18nArray:h,className:c,canEditFile:p}=i,m=()=>typeof r=="function"?r(t[s]):r,g=t[s],v=(e=m())!=null?e:[],f=p(g),w={};v.forEach(E=>{var H;w[E.id]=(H=g.meta[E.id])!=null?H:""});const[S,y]=$t(w),F=Zs(E=>{E.preventDefault(),n(S,s)},[n,S,s]),T=(E,H)=>{y({...S,[H]:E})},I=()=>{a(!1)},[A]=$t(()=>{const E=document.createElement("form");return E.setAttribute("tabindex","-1"),E.id=ti(),E});return Js(()=>(document.body.appendChild(A),A.addEventListener("submit",F),()=>{A.removeEventListener("submit",F),document.body.removeChild(A)}),[A,F]),o("div",{className:L("uppy-Dashboard-FileCard",c),"data-uppy-panelType":"FileCard",onDragOver:W,onDragLeave:W,onDrop:W,onPaste:W},o("div",{className:"uppy-DashboardContent-bar"},o("div",{className:"uppy-DashboardContent-title",role:"heading","aria-level":"1"},h("editing",{file:o("span",{className:"uppy-DashboardContent-titleFile"},g.meta?g.meta.name:g.name)})),o("button",{className:"uppy-DashboardContent-back",type:"button",form:A.id,title:d("finishEditingFile"),onClick:I},d("cancel"))),o("div",{className:"uppy-Dashboard-FileCard-inner"},o("div",{className:"uppy-Dashboard-FileCard-preview",style:{backgroundColor:mt(g.type).color}},o(Ei,{file:g}),f&&o("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-Dashboard-FileCard-edit",onClick:E=>{F(E),u(g)}},d("editImage"))),o("div",{className:"uppy-Dashboard-FileCard-info"},o(Kn,{computedMetaFields:v,requiredMetaFields:l,updateMeta:T,form:A,formState:S})),o("div",{className:"uppy-Dashboard-FileCard-actions"},o("button",{className:"uppy-u-reset uppy-c-btn uppy-c-btn-primary uppy-Dashboard-FileCard-actionsBtn",type:"submit",form:A.id},d("saveChanges")),o("button",{className:"uppy-u-reset uppy-c-btn uppy-c-btn-link uppy-Dashboard-FileCard-actionsBtn",type:"button",onClick:I,form:A.id},d("cancel")))))}const ee="uppy-transition-slideDownUp",Gt=250;class Ae extends se{constructor(e){super(e),this.state={cachedChildren:null,className:""}}componentWillUpdate(e){const{cachedChildren:t}=this.state,s=Q(e.children)[0];if(t===s)return null;const a={cachedChildren:s};s&&!t&&(a.className=`${ee}-enter`,cancelAnimationFrame(this.animationFrame),clearTimeout(this.leaveTimeout),this.leaveTimeout=void 0,this.animationFrame=requestAnimationFrame(()=>{this.setState({className:`${ee}-enter ${ee}-enter-active`}),this.enterTimeout=setTimeout(()=>{this.setState({className:""})},Gt)})),t&&!s&&this.leaveTimeout===void 0&&(a.cachedChildren=t,a.className=`${ee}-leave`,cancelAnimationFrame(this.animationFrame),clearTimeout(this.enterTimeout),this.enterTimeout=void 0,this.animationFrame=requestAnimationFrame(()=>{this.setState({className:`${ee}-leave ${ee}-leave-active`}),this.leaveTimeout=setTimeout(()=>{this.setState({cachedChildren:null,className:""})},Gt)})),this.setState(a)}render(){const{cachedChildren:e,className:t}=this.state;return e?Zt(e,{className:L(t,e.props.className)}):null}}function J(){return J=Object.assign?Object.assign.bind():function(i){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(i[s]=t[s])}return i},J.apply(this,arguments)}const Kt=900,Xt=700,Ke=576,Yt=330;function Yn(i){const e=i.totalFileCount===0,t=i.totalFileCount===1,s=i.containerWidth>Ke,a=i.containerHeight>Yt,n=L({"uppy-Dashboard":!0,"uppy-Dashboard--isDisabled":i.disabled,"uppy-Dashboard--animateOpenClose":i.animateOpenClose,"uppy-Dashboard--isClosing":i.isClosing,"uppy-Dashboard--isDraggingOver":i.isDraggingOver,"uppy-Dashboard--modal":!i.inline,"uppy-size--md":i.containerWidth>Ke,"uppy-size--lg":i.containerWidth>Xt,"uppy-size--xl":i.containerWidth>Kt,"uppy-size--height-md":i.containerHeight>Yt,"uppy-Dashboard--isAddFilesPanelVisible":i.showAddFilesPanel,"uppy-Dashboard--isInnerWrapVisible":i.areInsidesReadyToBeVisible,"uppy-Dashboard--singleFile":i.singleFileFullScreen&&t&&a});let r=1;i.containerWidth>Kt?r=5:i.containerWidth>Xt?r=4:i.containerWidth>Ke&&(r=3);const l=i.showSelectedFiles&&!e,u=i.recoveredState?Object.keys(i.recoveredState.files).length:null,d=i.files?Object.keys(i.files).filter(p=>i.files[p].isGhost).length:null,h=()=>d>0?i.i18n("recoveredXFiles",{smart_count:d}):i.i18n("recoveredAllFiles");return o("div",{className:n,"data-uppy-theme":i.theme,"data-uppy-num-acquirers":i.acquirers.length,"data-uppy-drag-drop-supported":!i.disableLocalFiles&&pn(),"aria-hidden":i.inline?"false":i.isHidden,"aria-disabled":i.disabled,"aria-label":i.inline?i.i18n("dashboardTitle"):i.i18n("dashboardWindowTitle"),onPaste:i.handlePaste,onDragOver:i.handleDragOver,onDragLeave:i.handleDragLeave,onDrop:i.handleDrop},o("div",{"aria-hidden":"true",className:"uppy-Dashboard-overlay",tabIndex:-1,onClick:i.handleClickOutside}),o("div",{className:"uppy-Dashboard-inner","aria-modal":!i.inline&&"true",role:!i.inline&&"dialog",style:{width:i.inline&&i.width?i.width:"",height:i.inline&&i.height?i.height:""}},i.inline?null:o("button",{className:"uppy-u-reset uppy-Dashboard-close",type:"button","aria-label":i.i18n("closeModal"),title:i.i18n("closeModal"),onClick:i.closeModal},o("span",{"aria-hidden":"true"},"×")),o("div",{className:"uppy-Dashboard-innerWrap"},o("div",{className:"uppy-Dashboard-dropFilesHereHint"},i.i18n("dropHint")),l&&o(Gn,i),u&&o("div",{className:"uppy-Dashboard-serviceMsg"},o("svg",{className:"uppy-Dashboard-serviceMsg-icon","aria-hidden":"true",focusable:"false",width:"21",height:"16",viewBox:"0 0 24 19"},o("g",{transform:"translate(0 -1)",fill:"none",fillRule:"evenodd"},o("path",{d:"M12.857 1.43l10.234 17.056A1 1 0 0122.234 20H1.766a1 1 0 01-.857-1.514L11.143 1.429a1 1 0 011.714 0z",fill:"#FFD300"}),o("path",{fill:"#000",d:"M11 6h2l-.3 8h-1.4z"}),o("circle",{fill:"#000",cx:"12",cy:"17",r:"1"}))),o("strong",{className:"uppy-Dashboard-serviceMsg-title"},i.i18n("sessionRestored")),o("div",{className:"uppy-Dashboard-serviceMsg-text"},h())),l?o($n,{id:i.id,error:i.error,i18n:i.i18n,uppy:i.uppy,files:i.files,acquirers:i.acquirers,resumableUploads:i.resumableUploads,hideRetryButton:i.hideRetryButton,hidePauseResumeButton:i.hidePauseResumeButton,hideCancelButton:i.hideCancelButton,showLinkToFileUploadResult:i.showLinkToFileUploadResult,showRemoveButtonAfterComplete:i.showRemoveButtonAfterComplete,isWide:i.isWide,metaFields:i.metaFields,toggleFileCard:i.toggleFileCard,handleRequestThumbnail:i.handleRequestThumbnail,handleCancelThumbnail:i.handleCancelThumbnail,recoveredState:i.recoveredState,individualCancellation:i.individualCancellation,openFileEditor:i.openFileEditor,canEditFile:i.canEditFile,toggleAddFilesPanel:i.toggleAddFilesPanel,isSingleFile:t,itemsPerRow:r}):o(Bi,J({},i,{isSizeMD:s})),o(Ae,null,i.showAddFilesPanel?o(Hn,J({key:"AddFiles"},i,{isSizeMD:s})):null),o(Ae,null,i.fileCardFor?o(Xn,J({key:"FileCard"},i)):null),o(Ae,null,i.activePickerPanel?o(Vn,J({key:"Picker"},i)):null),o(Ae,null,i.showFileEditor?o(qn,J({key:"Editor"},i)):null),o("div",{className:"uppy-Dashboard-progressindicators"},i.progressindicators.map(p=>i.uppy.getPlugin(p.id).render(i.state))))))}const Qn={strings:{closeModal:"Close Modal",addMoreFiles:"Add more files",addingMoreFiles:"Adding more files",importFrom:"Import from %{name}",dashboardWindowTitle:"Uppy Dashboard Window (Press escape to close)",dashboardTitle:"Uppy Dashboard",copyLinkToClipboardSuccess:"Link copied to clipboard.",copyLinkToClipboardFallback:"Copy the URL below",copyLink:"Copy link",back:"Back",removeFile:"Remove file",editFile:"Edit file",editImage:"Edit image",editing:"Editing %{file}",error:"Error",finishEditingFile:"Finish editing file",saveChanges:"Save changes",myDevice:"My Device",dropHint:"Drop your files here",uploadComplete:"Upload complete",uploadPaused:"Upload paused",resumeUpload:"Resume upload",pauseUpload:"Pause upload",retryUpload:"Retry upload",cancelUpload:"Cancel upload",xFilesSelected:{0:"%{smart_count} file selected",1:"%{smart_count} files selected"},uploadingXFiles:{0:"Uploading %{smart_count} file",1:"Uploading %{smart_count} files"},processingXFiles:{0:"Processing %{smart_count} file",1:"Processing %{smart_count} files"},poweredBy:"Powered by %{uppy}",addMore:"Add more",editFileWithFilename:"Edit file %{file}",save:"Save",cancel:"Cancel",dropPasteFiles:"Drop files here or %{browseFiles}",dropPasteFolders:"Drop files here or %{browseFolders}",dropPasteBoth:"Drop files here, %{browseFiles} or %{browseFolders}",dropPasteImportFiles:"Drop files here, %{browseFiles} or import from:",dropPasteImportFolders:"Drop files here, %{browseFolders} or import from:",dropPasteImportBoth:"Drop files here, %{browseFiles}, %{browseFolders} or import from:",importFiles:"Import files from:",browseFiles:"browse files",browseFolders:"browse folders",recoveredXFiles:{0:"We could not fully recover 1 file. Please re-select it and resume the upload.",1:"We could not fully recover %{smart_count} files. Please re-select them and resume the upload."},recoveredAllFiles:"We restored all files. You can now resume the upload.",sessionRestored:"Session restored",reSelect:"Re-select",missingRequiredMetaFields:{0:"Missing required meta field: %{fields}.",1:"Missing required meta fields: %{fields}."},takePictureBtn:"Take Picture",recordVideoBtn:"Record Video"}};function P(i,e){if(!Object.prototype.hasOwnProperty.call(i,e))throw new TypeError("attempted to use private field on non-instance");return i}var Jn=0;function x(i){return"__private_"+Jn+++"_"+i}const Zn={version:"3.7.5"},Xe=qt.default||qt,Qt=9,ea=27;function Jt(){const i={};return i.promise=new Promise((e,t)=>{i.resolve=e,i.reject=t}),i}var K=x("disabledNodes"),V=x("generateLargeThumbnailIfSingleFile"),de=x("openFileEditorWhenFilesAdded"),X=x("attachRenderFunctionToTarget"),Ye=x("isTargetSupported"),Qe=x("getAcquirers"),Je=x("getProgressIndicators"),q=x("getEditors"),Ze=x("addSpecifiedPluginsFromOptions"),et=x("autoDiscoverPlugins"),Y=x("addSupportedPluginIfNoTarget");let ta=class extends Ne{constructor(e,t){var s;super(e,t),s=this,Object.defineProperty(this,K,{writable:!0,value:null}),this.removeTarget=n=>{const l=this.getPluginState().targets.filter(u=>u.id!==n.id);this.setPluginState({targets:l})},this.addTarget=n=>{const r=n.id||n.constructor.name,l=n.title||r,u=n.type;if(u!=="acquirer"&&u!=="progressindicator"&&u!=="editor"){const p="Dashboard: can only be targeted by plugins of types: acquirer, progressindicator, editor";this.uppy.log(p,"error");return}const d={id:r,name:l,type:u},c=this.getPluginState().targets.slice();return c.push(d),this.setPluginState({targets:c}),this.el},this.hideAllPanels=()=>{const n=this.getPluginState(),r={activePickerPanel:!1,showAddFilesPanel:!1,activeOverlayType:null,fileCardFor:null,showFileEditor:!1};n.activePickerPanel===r.activePickerPanel&&n.showAddFilesPanel===r.showAddFilesPanel&&n.showFileEditor===r.showFileEditor&&n.activeOverlayType===r.activeOverlayType||(this.setPluginState(r),this.uppy.emit("dashboard:close-panel",n.activePickerPanel.id))},this.showPanel=n=>{const{targets:r}=this.getPluginState(),l=r.filter(u=>u.type==="acquirer"&&u.id===n)[0];this.setPluginState({activePickerPanel:l,activeOverlayType:"PickerPanel"}),this.uppy.emit("dashboard:show-panel",n)},this.canEditFile=n=>{const{targets:r}=this.getPluginState();return P(this,q)[q](r).some(u=>this.uppy.getPlugin(u.id).canEditFile(n))},this.openFileEditor=n=>{const{targets:r}=this.getPluginState(),l=P(this,q)[q](r);this.setPluginState({showFileEditor:!0,fileCardFor:n.id||null,activeOverlayType:"FileEditor"}),l.forEach(u=>{this.uppy.getPlugin(u.id).selectFile(n)})},this.closeFileEditor=()=>{const{metaFields:n}=this.getPluginState();n&&n.length>0?this.setPluginState({showFileEditor:!1,activeOverlayType:"FileCard"}):this.setPluginState({showFileEditor:!1,fileCardFor:null,activeOverlayType:"AddFiles"})},this.saveFileEditor=()=>{const{targets:n}=this.getPluginState();P(this,q)[q](n).forEach(l=>{this.uppy.getPlugin(l.id).save()}),this.closeFileEditor()},this.openModal=()=>{const{promise:n,resolve:r}=Jt();if(this.savedScrollPosition=window.pageYOffset,this.savedActiveElement=document.activeElement,this.opts.disablePageScrollWhenModalOpen&&document.body.classList.add("uppy-Dashboard-isFixed"),this.opts.animateOpenClose&&this.getPluginState().isClosing){const l=()=>{this.setPluginState({isHidden:!1}),this.el.removeEventListener("animationend",l,!1),r()};this.el.addEventListener("animationend",l,!1)}else this.setPluginState({isHidden:!1}),r();return this.opts.browserBackButtonClose&&this.updateBrowserHistory(),document.addEventListener("keydown",this.handleKeyDownInModal),this.uppy.emit("dashboard:modal-open"),n},this.closeModal=function(n){n===void 0&&(n={});const{manualClose:r=!0}=n,{isHidden:l,isClosing:u}=s.getPluginState();if(l||u)return;const{promise:d,resolve:h}=Jt();if(s.opts.disablePageScrollWhenModalOpen&&document.body.classList.remove("uppy-Dashboard-isFixed"),s.opts.animateOpenClose){s.setPluginState({isClosing:!0});const p=()=>{s.setPluginState({isHidden:!0,isClosing:!1}),s.superFocus.cancel(),s.savedActiveElement.focus(),s.el.removeEventListener("animationend",p,!1),h()};s.el.addEventListener("animationend",p,!1)}else s.setPluginState({isHidden:!0}),s.superFocus.cancel(),s.savedActiveElement.focus(),h();if(document.removeEventListener("keydown",s.handleKeyDownInModal),r&&s.opts.browserBackButtonClose){var c;(c=history.state)!=null&&c[s.modalName]&&history.back()}return s.uppy.emit("dashboard:modal-closed"),d},this.isModalOpen=()=>!this.getPluginState().isHidden||!1,this.requestCloseModal=()=>this.opts.onRequestCloseModal?this.opts.onRequestCloseModal():this.closeModal(),this.setDarkModeCapability=n=>{const{capabilities:r}=this.uppy.getState();this.uppy.setState({capabilities:{...r,darkMode:n}})},this.handleSystemDarkModeChange=n=>{const r=n.matches;this.uppy.log(`[Dashboard] Dark mode is ${r?"on":"off"}`),this.setDarkModeCapability(r)},this.toggleFileCard=(n,r)=>{const l=this.uppy.getFile(r);n?this.uppy.emit("dashboard:file-edit-start",l):this.uppy.emit("dashboard:file-edit-complete",l),this.setPluginState({fileCardFor:n?r:null,activeOverlayType:n?"FileCard":null})},this.toggleAddFilesPanel=n=>{this.setPluginState({showAddFilesPanel:n,activeOverlayType:n?"AddFiles":null})},this.addFiles=n=>{const r=n.map(l=>({source:this.id,name:l.name,type:l.type,data:l,meta:{relativePath:l.relativePath||l.webkitRelativePath||null}}));try{this.uppy.addFiles(r)}catch(l){this.uppy.log(l)}},this.startListeningToResize=()=>{this.resizeObserver=new ResizeObserver(n=>{const r=n[0],{width:l,height:u}=r.contentRect;this.setPluginState({containerWidth:l,containerHeight:u,areInsidesReadyToBeVisible:!0})}),this.resizeObserver.observe(this.el.querySelector(".uppy-Dashboard-inner")),this.makeDashboardInsidesVisibleAnywayTimeout=setTimeout(()=>{const n=this.getPluginState(),r=!this.opts.inline&&n.isHidden;!n.areInsidesReadyToBeVisible&&!r&&(this.uppy.log("[Dashboard] resize event didn’t fire on time: defaulted to mobile layout","warning"),this.setPluginState({areInsidesReadyToBeVisible:!0}))},1e3)},this.stopListeningToResize=()=>{this.resizeObserver.disconnect(),clearTimeout(this.makeDashboardInsidesVisibleAnywayTimeout)},this.recordIfFocusedOnUppyRecently=n=>{this.el.contains(n.target)?this.ifFocusedOnUppyRecently=!0:(this.ifFocusedOnUppyRecently=!1,this.superFocus.cancel())},this.disableInteractiveElements=n=>{var r;const l=["a[href]","input:not([disabled])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])",'[role="button"]:not([disabled])'],u=(r=P(this,K)[K])!=null?r:ce(this.el.querySelectorAll(l)).filter(d=>!d.classList.contains("uppy-Dashboard-close"));for(const d of u)d.tagName==="A"?d.setAttribute("aria-disabled",n):d.disabled=n;n?P(this,K)[K]=u:P(this,K)[K]=null,this.dashboardIsDisabled=n},this.updateBrowserHistory=()=>{var n;(n=history.state)!=null&&n[this.modalName]||history.pushState({...history.state,[this.modalName]:!0},""),window.addEventListener("popstate",this.handlePopState,!1)},this.handlePopState=n=>{var r;this.isModalOpen()&&(!n.state||!n.state[this.modalName])&&this.closeModal({manualClose:!1}),!this.isModalOpen()&&(r=n.state)!=null&&r[this.modalName]&&history.back()},this.handleKeyDownInModal=n=>{n.keyCode===ea&&this.requestCloseModal(n),n.keyCode===Qt&&Ai(n,this.getPluginState().activeOverlayType,this.el)},this.handleClickOutside=()=>{this.opts.closeModalOnClickOutside&&this.requestCloseModal()},this.handlePaste=n=>{this.uppy.iteratePlugins(l=>{l.type==="acquirer"&&(l.handleRootPaste==null||l.handleRootPaste(n))});const r=ce(n.clipboardData.files);r.length>0&&(this.uppy.log("[Dashboard] Files pasted"),this.addFiles(r))},this.handleInputChange=n=>{n.preventDefault();const r=ce(n.target.files);r.length>0&&(this.uppy.log("[Dashboard] Files selected through input"),this.addFiles(r))},this.handleDragOver=n=>{var r,l;n.preventDefault(),n.stopPropagation();const u=()=>{let p=!0;return this.uppy.iteratePlugins(m=>{m.canHandleRootDrop!=null&&m.canHandleRootDrop(n)&&(p=!0)}),p},d=()=>{const{types:p}=n.dataTransfer;return p.some(m=>m==="Files")},h=u(),c=d();if(!h&&!c||this.opts.disabled||this.opts.disableLocalFiles&&(c||!h)||!this.uppy.getState().allowNewUpload){n.dataTransfer.dropEffect="none",clearTimeout(this.removeDragOverClassTimeout);return}n.dataTransfer.dropEffect="copy",clearTimeout(this.removeDragOverClassTimeout),this.setPluginState({isDraggingOver:!0}),(r=(l=this.opts).onDragOver)==null||r.call(l,n)},this.handleDragLeave=n=>{var r,l;n.preventDefault(),n.stopPropagation(),clearTimeout(this.removeDragOverClassTimeout),this.removeDragOverClassTimeout=setTimeout(()=>{this.setPluginState({isDraggingOver:!1})},50),(r=(l=this.opts).onDragLeave)==null||r.call(l,n)},this.handleDrop=async n=>{var r,l;n.preventDefault(),n.stopPropagation(),clearTimeout(this.removeDragOverClassTimeout),this.setPluginState({isDraggingOver:!1}),this.uppy.iteratePlugins(c=>{c.type==="acquirer"&&(c.handleRootDrop==null||c.handleRootDrop(n))});let u=!1;const d=c=>{this.uppy.log(c,"error"),u||(this.uppy.info(c.message,"error"),u=!0)};this.uppy.log("[Dashboard] Processing dropped files");const h=await Xs(n.dataTransfer,{logDropError:d});h.length>0&&(this.uppy.log("[Dashboard] Files dropped"),this.addFiles(h)),(r=(l=this.opts).onDrop)==null||r.call(l,n)},this.handleRequestThumbnail=n=>{this.opts.waitForThumbnailsBeforeUpload||this.uppy.emit("thumbnail:request",n)},this.handleCancelThumbnail=n=>{this.opts.waitForThumbnailsBeforeUpload||this.uppy.emit("thumbnail:cancel",n)},this.handleKeyDownInInline=n=>{n.keyCode===Qt&&hn(n,this.getPluginState().activeOverlayType,this.el)},this.handlePasteOnBody=n=>{this.el.contains(document.activeElement)&&this.handlePaste(n)},this.handleComplete=n=>{let{failed:r}=n;this.opts.closeAfterFinish&&r.length===0&&this.requestCloseModal()},this.handleCancelRestore=()=>{this.uppy.emit("restore-canceled")},Object.defineProperty(this,V,{writable:!0,value:()=>{if(this.opts.disableThumbnailGenerator)return;const n=600,r=this.uppy.getFiles();if(r.length===1){const l=this.uppy.getPlugin(`${this.id}:ThumbnailGenerator`);l==null||l.setOptions({thumbnailWidth:n});const u={...r[0],preview:void 0};l.requestThumbnail(u).then(()=>{l==null||l.setOptions({thumbnailWidth:this.opts.thumbnailWidth})})}}}),Object.defineProperty(this,de,{writable:!0,value:n=>{const r=n[0],{metaFields:l}=this.getPluginState(),u=l&&l.length>0,d=this.canEditFile(r);u?this.toggleFileCard(!0,r.id):d&&this.openFileEditor(r)}}),this.initEvents=()=>{if(this.opts.trigger&&!this.opts.inline){const n=Nt(this.opts.trigger);n?n.forEach(r=>r.addEventListener("click",this.openModal)):this.uppy.log("Dashboard modal trigger not found. Make sure `trigger` is set in Dashboard options, unless you are planning to call `dashboard.openModal()` method yourself","warning")}this.startListeningToResize(),document.addEventListener("paste",this.handlePasteOnBody),this.uppy.on("plugin-added",P(this,Y)[Y]),this.uppy.on("plugin-remove",this.removeTarget),this.uppy.on("file-added",this.hideAllPanels),this.uppy.on("dashboard:modal-closed",this.hideAllPanels),this.uppy.on("complete",this.handleComplete),this.uppy.on("files-added",P(this,V)[V]),this.uppy.on("file-removed",P(this,V)[V]),document.addEventListener("focus",this.recordIfFocusedOnUppyRecently,!0),document.addEventListener("click",this.recordIfFocusedOnUppyRecently,!0),this.opts.inline&&this.el.addEventListener("keydown",this.handleKeyDownInInline),this.opts.autoOpenFileEditor&&this.uppy.on("files-added",P(this,de)[de])},this.removeEvents=()=>{const n=Nt(this.opts.trigger);!this.opts.inline&&n&&n.forEach(r=>r.removeEventListener("click",this.openModal)),this.stopListeningToResize(),document.removeEventListener("paste",this.handlePasteOnBody),window.removeEventListener("popstate",this.handlePopState,!1),this.uppy.off("plugin-added",P(this,Y)[Y]),this.uppy.off("plugin-remove",this.removeTarget),this.uppy.off("file-added",this.hideAllPanels),this.uppy.off("dashboard:modal-closed",this.hideAllPanels),this.uppy.off("complete",this.handleComplete),this.uppy.off("files-added",P(this,V)[V]),this.uppy.off("file-removed",P(this,V)[V]),document.removeEventListener("focus",this.recordIfFocusedOnUppyRecently),document.removeEventListener("click",this.recordIfFocusedOnUppyRecently),this.opts.inline&&this.el.removeEventListener("keydown",this.handleKeyDownInInline),this.opts.autoOpenFileEditor&&this.uppy.off("files-added",P(this,de)[de])},this.superFocusOnEachUpdate=()=>{const n=this.el.contains(document.activeElement),r=document.activeElement===document.body||document.activeElement===null,l=this.uppy.getState().info.length===0,u=!this.opts.inline;l&&(u||n||r&&this.ifFocusedOnUppyRecently)?this.superFocus(this.el,this.getPluginState().activeOverlayType):this.superFocus.cancel()},this.afterUpdate=()=>{if(this.opts.disabled&&!this.dashboardIsDisabled){this.disableInteractiveElements(!0);return}!this.opts.disabled&&this.dashboardIsDisabled&&this.disableInteractiveElements(!1),this.superFocusOnEachUpdate()},this.saveFileCard=(n,r)=>{this.uppy.setFileMeta(r,n),this.toggleFileCard(!1,r)},Object.defineProperty(this,X,{writable:!0,value:n=>{const r=this.uppy.getPlugin(n.id);return{...n,icon:r.icon||this.opts.defaultPickerIcon,render:r.render}}}),Object.defineProperty(this,Ye,{writable:!0,value:n=>{const r=this.uppy.getPlugin(n.id);return typeof r.isSupported!="function"?!0:r.isSupported()}}),Object.defineProperty(this,Qe,{writable:!0,value:Xe(n=>n.filter(r=>r.type==="acquirer"&&P(this,Ye)[Ye](r)).map(P(this,X)[X]))}),Object.defineProperty(this,Je,{writable:!0,value:Xe(n=>n.filter(r=>r.type==="progressindicator").map(P(this,X)[X]))}),Object.defineProperty(this,q,{writable:!0,value:Xe(n=>n.filter(r=>r.type==="editor").map(P(this,X)[X]))}),this.render=n=>{const r=this.getPluginState(),{files:l,capabilities:u,allowNewUpload:d}=n,{newFiles:h,uploadStartedFiles:c,completeFiles:p,erroredFiles:m,inProgressFiles:g,inProgressNotPausedFiles:v,processingFiles:f,isUploadStarted:w,isAllComplete:S,isAllErrored:y,isAllPaused:F}=this.uppy.getObjectOfFilesPerState(),T=P(this,Qe)[Qe](r.targets),I=P(this,Je)[Je](r.targets),A=P(this,q)[q](r.targets);let E;return this.opts.theme==="auto"?E=u.darkMode?"dark":"light":E=this.opts.theme,["files","folders","both"].indexOf(this.opts.fileManagerSelectionType)<0&&(this.opts.fileManagerSelectionType="files",console.warn(`Unsupported option for "fileManagerSelectionType". Using default of "${this.opts.fileManagerSelectionType}".`)),Yn({state:n,isHidden:r.isHidden,files:l,newFiles:h,uploadStartedFiles:c,completeFiles:p,erroredFiles:m,inProgressFiles:g,inProgressNotPausedFiles:v,processingFiles:f,isUploadStarted:w,isAllComplete:S,isAllErrored:y,isAllPaused:F,totalFileCount:Object.keys(l).length,totalProgress:n.totalProgress,allowNewUpload:d,acquirers:T,theme:E,disabled:this.opts.disabled,disableLocalFiles:this.opts.disableLocalFiles,direction:this.opts.direction,activePickerPanel:r.activePickerPanel,showFileEditor:r.showFileEditor,saveFileEditor:this.saveFileEditor,closeFileEditor:this.closeFileEditor,disableInteractiveElements:this.disableInteractiveElements,animateOpenClose:this.opts.animateOpenClose,isClosing:r.isClosing,progressindicators:I,editors:A,autoProceed:this.uppy.opts.autoProceed,id:this.id,closeModal:this.requestCloseModal,handleClickOutside:this.handleClickOutside,handleInputChange:this.handleInputChange,handlePaste:this.handlePaste,inline:this.opts.inline,showPanel:this.showPanel,hideAllPanels:this.hideAllPanels,i18n:this.i18n,i18nArray:this.i18nArray,uppy:this.uppy,note:this.opts.note,recoveredState:n.recoveredState,metaFields:r.metaFields,resumableUploads:u.resumableUploads||!1,individualCancellation:u.individualCancellation,isMobileDevice:u.isMobileDevice,fileCardFor:r.fileCardFor,toggleFileCard:this.toggleFileCard,toggleAddFilesPanel:this.toggleAddFilesPanel,showAddFilesPanel:r.showAddFilesPanel,saveFileCard:this.saveFileCard,openFileEditor:this.openFileEditor,canEditFile:this.canEditFile,width:this.opts.width,height:this.opts.height,showLinkToFileUploadResult:this.opts.showLinkToFileUploadResult,fileManagerSelectionType:this.opts.fileManagerSelectionType,proudlyDisplayPoweredByUppy:this.opts.proudlyDisplayPoweredByUppy,hideCancelButton:this.opts.hideCancelButton,hideRetryButton:this.opts.hideRetryButton,hidePauseResumeButton:this.opts.hidePauseResumeButton,showRemoveButtonAfterComplete:this.opts.showRemoveButtonAfterComplete,containerWidth:r.containerWidth,containerHeight:r.containerHeight,areInsidesReadyToBeVisible:r.areInsidesReadyToBeVisible,isTargetDOMEl:this.isTargetDOMEl,parentElement:this.el,allowedFileTypes:this.uppy.opts.restrictions.allowedFileTypes,maxNumberOfFiles:this.uppy.opts.restrictions.maxNumberOfFiles,requiredMetaFields:this.uppy.opts.restrictions.requiredMetaFields,showSelectedFiles:this.opts.showSelectedFiles,showNativePhotoCameraButton:this.opts.showNativePhotoCameraButton,showNativeVideoCameraButton:this.opts.showNativeVideoCameraButton,nativeCameraFacingMode:this.opts.nativeCameraFacingMode,singleFileFullScreen:this.opts.singleFileFullScreen,handleCancelRestore:this.handleCancelRestore,handleRequestThumbnail:this.handleRequestThumbnail,handleCancelThumbnail:this.handleCancelThumbnail,isDraggingOver:r.isDraggingOver,handleDragOver:this.handleDragOver,handleDragLeave:this.handleDragLeave,handleDrop:this.handleDrop})},Object.defineProperty(this,Ze,{writable:!0,value:()=>{(this.opts.plugins||[]).forEach(r=>{const l=this.uppy.getPlugin(r);l?l.mount(this,l):this.uppy.log(`[Uppy] Dashboard could not find plugin '${r}', make sure to uppy.use() the plugins you are specifying`,"warning")})}}),Object.defineProperty(this,et,{writable:!0,value:()=>{this.uppy.iteratePlugins(P(this,Y)[Y])}}),Object.defineProperty(this,Y,{writable:!0,value:n=>{var r;const l=["acquirer","editor"];n&&!((r=n.opts)!=null&&r.target)&&l.includes(n.type)&&(this.getPluginState().targets.some(d=>n.id===d.id)||n.mount(this,n))}}),this.install=()=>{this.setPluginState({isHidden:!0,fileCardFor:null,activeOverlayType:null,showAddFilesPanel:!1,activePickerPanel:!1,showFileEditor:!1,metaFields:this.opts.metaFields,targets:[],areInsidesReadyToBeVisible:!1,isDraggingOver:!1});const{inline:n,closeAfterFinish:r}=this.opts;if(n&&r)throw new Error("[Dashboard] `closeAfterFinish: true` cannot be used on an inline Dashboard, because an inline Dashboard cannot be closed at all. Either set `inline: false`, or disable the `closeAfterFinish` option.");const{allowMultipleUploads:l,allowMultipleUploadBatches:u}=this.uppy.opts;(l||u)&&r&&this.uppy.log("[Dashboard] When using `closeAfterFinish`, we recommended setting the `allowMultipleUploadBatches` option to `false` in the Uppy constructor. See https://uppy.io/docs/uppy/#allowMultipleUploads-true","warning");const{target:d}=this.opts;d&&this.mount(d,this),this.opts.disableStatusBar||this.uppy.use(ri,{id:`${this.id}:StatusBar`,target:this,hideUploadButton:this.opts.hideUploadButton,hideRetryButton:this.opts.hideRetryButton,hidePauseResumeButton:this.opts.hidePauseResumeButton,hideCancelButton:this.opts.hideCancelButton,showProgressDetails:this.opts.showProgressDetails,hideAfterFinish:this.opts.hideProgressAfterFinish,locale:this.opts.locale,doneButtonHandler:this.opts.doneButtonHandler}),this.opts.disableInformer||this.uppy.use(li,{id:`${this.id}:Informer`,target:this}),this.opts.disableThumbnailGenerator||this.uppy.use(yi,{id:`${this.id}:ThumbnailGenerator`,thumbnailWidth:this.opts.thumbnailWidth,thumbnailHeight:this.opts.thumbnailHeight,thumbnailType:this.opts.thumbnailType,waitForThumbnailsBeforeUpload:this.opts.waitForThumbnailsBeforeUpload,lazy:!this.opts.waitForThumbnailsBeforeUpload}),this.darkModeMediaQuery=typeof window<"u"&&window.matchMedia?window.matchMedia("(prefers-color-scheme: dark)"):null;const h=this.darkModeMediaQuery?this.darkModeMediaQuery.matches:!1;this.uppy.log(`[Dashboard] Dark mode is ${h?"on":"off"}`),this.setDarkModeCapability(h),this.opts.theme==="auto"&&this.darkModeMediaQuery.addListener(this.handleSystemDarkModeChange),P(this,Ze)[Ze](),P(this,et)[et](),this.initEvents()},this.uninstall=()=>{if(!this.opts.disableInformer){const r=this.uppy.getPlugin(`${this.id}:Informer`);r&&this.uppy.removePlugin(r)}if(!this.opts.disableStatusBar){const r=this.uppy.getPlugin(`${this.id}:StatusBar`);r&&this.uppy.removePlugin(r)}if(!this.opts.disableThumbnailGenerator){const r=this.uppy.getPlugin(`${this.id}:ThumbnailGenerator`);r&&this.uppy.removePlugin(r)}(this.opts.plugins||[]).forEach(r=>{const l=this.uppy.getPlugin(r);l&&l.unmount()}),this.opts.theme==="auto"&&this.darkModeMediaQuery.removeListener(this.handleSystemDarkModeChange),this.opts.disablePageScrollWhenModalOpen&&document.body.classList.remove("uppy-Dashboard-isFixed"),this.unmount(),this.removeEvents()},this.id=this.opts.id||"Dashboard",this.title="Dashboard",this.type="orchestrator",this.modalName=`uppy-Dashboard-${ti()}`,this.defaultLocale=Qn;const a={target:"body",metaFields:[],trigger:null,inline:!1,width:750,height:550,thumbnailWidth:280,thumbnailType:"image/jpeg",waitForThumbnailsBeforeUpload:!1,defaultPickerIcon:rn,showLinkToFileUploadResult:!1,showProgressDetails:!1,hideUploadButton:!1,hideCancelButton:!1,hideRetryButton:!1,hidePauseResumeButton:!1,hideProgressAfterFinish:!1,doneButtonHandler:()=>{this.uppy.clearUploadedFiles(),this.requestCloseModal()},note:null,closeModalOnClickOutside:!1,closeAfterFinish:!1,singleFileFullScreen:!0,disableStatusBar:!1,disableInformer:!1,disableThumbnailGenerator:!1,disablePageScrollWhenModalOpen:!0,animateOpenClose:!0,fileManagerSelectionType:"files",proudlyDisplayPoweredByUppy:!0,onRequestCloseModal:()=>this.closeModal(),showSelectedFiles:!0,showRemoveButtonAfterComplete:!1,browserBackButtonClose:!1,showNativePhotoCameraButton:!1,showNativeVideoCameraButton:!1,theme:"light",autoOpenFileEditor:!1,disabled:!1,disableLocalFiles:!1};this.opts={...a,...t},this.i18nInit(),this.superFocus=cn(),this.ifFocusedOnUppyRecently=!1,this.makeDashboardInsidesVisibleAnywayTimeout=null,this.removeDragOverClassTimeout=null}};ta.VERSION=Zn.version;export{ta as D,ri as S,Xs as g,pn as i,ce as t};
