import { LogoutLogo } from 'Assets/svgs/LogoutLogo';
import { MyPlansLogo } from 'Assets/svgs/MyPlansLogo';
import { ProfileLogo } from 'Assets/svgs/ProfileLogo';
import { AuthContext } from 'Src/authContext';
import React from 'react'
import { useNavigate } from 'react-router';

const ChatMenuDropDown = () => {
    const navigate = useNavigate()
    const { state: states, dispatch } = React.useContext(AuthContext);
  return (
    <div className="absolute right-0 left-0 mx-auto bottom-0 mb-[4.5rem] w-[100%] bg-white text-[#1F1F1F] text-[14px] border rounded-lg shadow-lg ">
    <ul>
      <li
        className="hover:bg-companyRed hover:bg-opacity-10 hover:text-companyRed py-4 px-4 hover:border rounded-t-lg border-b flex items-center font-medium"
        onClick={() => {
          navigate("/user/profile");
        }}
      >
        <div className="mr-[12px]">
          <ProfileLogo />
        </div>
        My Profile
      </li>
      <li
        className="hover:bg-companyRed hover:bg-opacity-10 hover:text-companyRed py-4 px-4 border-b flex items-center font-medium"
        onClick={() => {
          navigate("/user/plans");
        }}
      >
        <div className="mr-[12px]">
          <MyPlansLogo />
        </div>
        My Plan
      </li>
      <li
        className="hover:bg-companyRed hover:bg-opacity-10 hover:text-companyRed py-4 px-4 border-b flex items-center text-red-600 font-medium"
        onClick={() => {
          navigate("/user/login");
          dispatch({
            type: "LOGOUT",
          });
        }}
      >
        <div className="mr-[12px]">
          <LogoutLogo />
        </div>
        Logout
      </li>
    </ul>
  </div>
  )
}

export default ChatMenuDropDown
