import React, { useState } from "react";
import { AuthContext, tokenExpireError } from "../authContext";
import { NavLink } from "react-router-dom";
import { GlobalContext } from "../globalContext";
import aboutimg from "../assets/images/aboutimg.png";
import { useNavigate } from "react-router-dom";
import Navbar from "./NavBar";
import MkdSDK from "Utils/MkdSDK";
import SkeletonLoading from "./SkeletonLoading";
import FooterComp from "./FooterComp";
import AdSense from "react-adsense";
import { usePageContext } from "Src/pageContext";
let sdk = new MkdSDK();
const CollaborationPage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { pageLayout } = usePageContext();
  const [cms, setCms] = useState();

  const navigate = useNavigate();
  // console.log(pageLayout);

  React.useEffect(() => {
    async function getallcms() {
      try {
        const result = await sdk.getallcms();
        // console.log("wre", result);
        if (result) {
          setCms(result?.list);
        }
      } catch (err) {
        console.log("Error:", err);
        tokenExpireError(dispatch, err.message);
      }
    }
    getallcms();
  }, []);

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "user",
      },
    });
  }, []);
  return (
    <div className="relative ">
      <Navbar />
      <div className="h-full flex flex-col justify-between items-center">
        <div className="bg-white pb-8 rounded  px-4 flex flex-col justify-center items-start">
          {!cms && <SkeletonLoading padding="py-[5rem] pt-[10rem]" />}
          {!cms && <SkeletonLoading padding="" counter={20} />}

          <h3 className=" font-semibold">
            {" "}
            {`
              ${
                cms?.find((item) => item.content_key === "Collab_header_text")
                  ?.content_value
              }`}
          </h3>
          <div className=" md:flex md:flex-row flex flex-col items-center  justify-start max-w-[2140px] w-full px-2 py-8 gap-2">
            {/* <div className=" border w-full flex justify-between gap-4"> */}
            <div className="  border-red-500 w-full flex flex-col items-start">
              <div className="flex items-center justify-start pb-4">
                <p className=" text-left max-w-4xl">
                  {`
              ${
                cms?.find((item) => item.content_key === "Collab_text")
                  ?.content_value
              }`}
                </p>
              </div>

              <div className=" w-full flex">
                <div className="  border-red-500 w-full h-full max-h-[500px] max-w-[600px]">
                  {!cms && <SkeletonLoading padding="py-[10rem]" />}
                  <div className=" w-full my-4 flex justify-center">
                    <video
                      src={`
              ${
                cms?.find((item) => item.content_key === "Collab_video")
                  ?.content_value
              }`}
                      autoPlay
                      loop
                      muted
                    />
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-start pb-4">
                <p className=" text-left max-w-4xl">
                  {`
              ${
                cms?.find((item) => item.content_key === "Collab_text")
                  ?.content_value
              }`}
                </p>
              </div>

              <div className="flex justify-start  border-red-500 w-full h-full max-h-[500px] max-w-[600px]">
                {!cms && <SkeletonLoading padding="py-[10rem]" />}
                <div className=" w-full my-4 flex justify-center">
                  <img
                    src={`
              ${
                cms?.find((item) => item.content_key === "Collab_picture")
                  ?.content_value
              }`}
                    alt=""
                    className=" w-full h-full"
                  />
                </div>
              </div>
              <div className="flex items-center justify-start pb-4">
                <p className=" text-left max-w-4xl">
                  {`
              ${
                cms?.find((item) => item.content_key === "Collab_text2")
                  ?.content_value
              }`}
                </p>
              </div>

              <div className="  border-red-500 w-full h-full ">
                {!cms && <SkeletonLoading padding="py-[10rem]" />}
                <div className=" border-blue-500 p-4 w-full h-full max-h-[500px] max-w-[500px] my-4 flex justify-center">
                  <img
                    src={`
                        ${
                          cms?.find(
                            (item) => item.content_key === "Collab_picture2"
                          )?.content_value
                        }`}
                    alt=""
                    className=" w-full h-full"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <FooterComp />
      </div>
    </div>
  );
};

export default CollaborationPage;
