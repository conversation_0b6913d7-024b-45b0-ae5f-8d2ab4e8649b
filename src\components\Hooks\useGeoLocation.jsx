import axios from "axios";
import { useEffect } from "react";
import { useState } from "react";

export default function useGeoLocation() {
  const [locationData, setLocationData] = useState(null);
  useEffect(() => {
    getLocation();
  }, []);

  async function getLocation() {
    try {
      // Use ipapi.co for HTTPS support
      const res = await axios.get("https://ipapi.co/json/");
      if (res.status === 200) {
       
        
        setLocationData(res.data);
      } else {
        setLocationData(null);
        console.warn("Geolocation API returned non-200 status:", res.status);
      }
    } catch (error) {
      setLocationData(null);
      console.error("Error fetching geolocation:", error);
    }
  }

  return {
    city: locationData?.city,
    country: locationData?.country,
    countryCode: locationData?.country_code,
    lat: locationData?.latitude,
    lon: locationData?.longitude,
    region: locationData?.region,
    regionCode: locationData?.region_code,
    timezone: locationData?.timezone,
    zip: locationData?.postal,
  };
}
