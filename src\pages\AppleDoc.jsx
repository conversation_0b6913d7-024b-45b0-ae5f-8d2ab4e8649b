import React, { useEffect } from "react";

const AppleDoc = () => {
  // useEffect(() => {
  //   // Trigger the document download
  //   const downloadLink = document.createElement("a");
  //   // downloadLink.href = "../../public/.well-known/apple-developer-merchantid-domain-association"; // adjust the path to your document
  //   downloadLink.href =
  //     " /.well-known/apple-developer-merchantid-domain-association"; // adjust the path to your document
  //   downloadLink.download = "apple-document";
  //   document.body.appendChild(downloadLink);
  //   downloadLink.click();
  //   document.body.removeChild(downloadLink);
  // }, []);
  // useEffect(() => {
  //   // Trigger the document download
  //   const downloadLink = document.createElement("a");
  //   // downloadLink.href = "../../public/.well-known/apple-developer-merchantid-domain-association"; // adjust the path to your document
  //   downloadLink.href =
  //     " /.well-known/apple-developer-merchantid-domain-association"; // adjust the path to your document
  //   downloadLink.download = "apple-document";
  //   document.body.appendChild(downloadLink);
  //   downloadLink.click();
  //   document.body.removeChild(downloadLink);
  // }, []);
  // useEffect(() => {
  //   // Trigger the document download
  //   const downloadLink = document.createElement("a");
  //   // downloadLink.href = "../../public/.well-known/apple-developer-merchantid-domain-association"; // adjust the path to your document
  //   downloadLink.href =
  //     " /.well-known/apple-developer-merchantid-domain-association"; // adjust the path to your document
  //   downloadLink.download = "apple-document";
  //   document.body.appendChild(downloadLink);
  //   downloadLink.click();
  //   document.body.removeChild(downloadLink);
  // }, []);

  // useEffect(() => {
  //   // Function to trigger automatic download
  //   const downloadFile = () => {
  //     const fileUrl = "/apple-developer-merchantid-domain-association";
  //     const anchor = document.createElement("a");
  //     anchor.href = fileUrl;
  //     anchor.download = "apple-developer-merchantid-domain-association";
  //     anchor.click();
  //   };

  //   // Trigger the download when the component mounts
  //   downloadFile();
  // }, []);

  // Empty dependency array ensures that this effect runs only once

  return (
    <div>
      {/* <a
        href="../assets/doc/.well-known/apple-developer-merchantid-domain-association"
        target="_blank"
       
      >
        Document
      </a> */}
    </div>
  );
};

export default AppleDoc;
