import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "../utils/MkdSDK";
import { GlobalContext, showToast } from "../globalContext";
import { useNavigate, useParams } from "react-router-dom";
import { AuthContext, tokenExpireError } from "../authContext";
import { sportList } from "Utils/utils";
import { BackButton } from "Components/BackButton";

let sdk = new MkdSDK();

const EditAdminPromptPage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const schema = yup
    .object({
      prompt: yup.string(),
      slug: yup.string(),
    })
    .required();
  const [fileObj, setFileObj] = React.useState({});
  const navigate = useNavigate();

  const [prompt, setPrompt] = useState("");
  const [slug, setSlug] = useState("");

  const [id, setId] = useState(0);

  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const params = useParams();

  useEffect(function () {
    (async function () {
      try {
        sdk.setTable("custom_prompt");
        const result = await sdk.callRestAPI({ id: Number(params?.id) }, "GET");
        if (!result.error) {
          setValue("prompt", result.model.prompt);
          setValue("slug", result.model.slug);

          setPrompt(result.model.prompt);
          setSlug(result.model.slug);

          setId(result.model.id);
        }
      } catch (error) {
        console.log("error", error);
        tokenExpireError(dispatch, error.message);
      }
    })();
  }, []);

  const onSubmit = async (_data) => {
    try {
      sdk.setTable("custom_prompt");
      for (let item in fileObj) {
        let formData = new FormData();
        formData.append("file", fileObj[item].file);
        let uploadResult = await sdk.uploadImage(formData);
        _data[item] = uploadResult.url;
      }
      const result = await sdk.callRestAPI(
        {
          id: id,
          prompt: _data.prompt,
          slug: _data.slug ?? slug,
        },
        "PUT"
      );

      if (!result.error) {
        showToast(globalDispatch, "Updated");
        navigate("/admin/prompt");
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error) {
      console.log("Error", error);
      setError("name", {
        type: "manual",
        message: error.message,
      });
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "prompt",
      },
    });
  }, []);

  return (
    <div className=" shadow-md rounded   mx-auto p-5">
      <div className=" mb-4"> 
        <BackButton/>
      </div>
      <h4 className="text-2xl font-medium mb-2">Edit prompt</h4>
      <form className=" w-full max-w-lg" onSubmit={handleSubmit(onSubmit)}>
        <div className="mb-4  ">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="prompt"
          >
            Prompt
          </label>
          <textarea
            placeholder="Prompt"
            {...register("prompt")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline min-h-40 ${
              errors.prompt?.message ? "border-red-500" : ""
            }`}
            row={30}
          ></textarea>
          <p className="text-red-500 text-xs italic">{errors.name?.message}</p>
        </div>

        <div className="mb-4  ">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="slug"
          >
            Slug
          </label>
          <select
            name="slug"
            id="slug"
            className="shadow border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline capitalize "
            value={slug}
            {...register("slug", {
              onChange: (e) => setSlug(e.target.value),
            })}
          >
            <option value="">Select sport</option>
            {sportList.map((option) => (
              <option className="capitalize" name={option} value={option} key={option}>
                {option}
              </option>
            ))}
          </select>
          <p className="text-red-500 text-xs italic">{errors.slug?.message}</p>
        </div>

        <button
          type="submit"
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Submit
        </button>
      </form>
    </div>
  );
};

export default EditAdminPromptPage;
