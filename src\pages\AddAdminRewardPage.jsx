import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "../utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { AuthContext, tokenExpireError } from "../authContext";
import { GlobalContext, showToast } from "../globalContext";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { isImage, empty, isVideo, isPdf } from "../utils/utils";

let sdk = new MkdSDK();
const AddAdminRewardPage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [plans, setPlans] = useState();
  const [message, setMessage] = useState();
  const schema = yup
    .object({
      create_at: yup.string(),
      update_at: yup.string(),
      email: yup.string(),
    })
    .required();

  const selectType = [
    { key: "", value: "Basic" },
    { key: "text", value: "Premium" },
    { key: "image", value: "Deluxe" },
    { key: "number", value: "Number" },
  ];
  const status = [
    { key: "0", value: "Active" },
    { key: "1", value: "Expired" },
  ];

  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    setError,
    setValue,

    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const onSubmit = async (_data) => {
    // console.log(_data);
    try {
      const result = await sdk.sendreward({
        email: _data.email,
        messages: _data.messages,
      });

      if (!result.error) {
        showToast(globalDispatch, "Reward Sent");
        navigate("/admin/payments");
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error) {
      console.log("Error", error);
      setError("create_at", {
        type: "manual",
        message: error.message,
      });
      tokenExpireError(dispatch, error.message);
    }
  };

  const handlegenerate = async (_data) => {
    let sdk = new MkdSDK();

    try {
      // let test = plans.find((plan) => plan.id === _data.plan);
      // console.log("messages", test);
      // console.log(
      //   "messages",
      //   plans.find((plan) => plan.id === _data.plan)?.messages
      // );
      // console.log("messages", _data.plan);
      // console.log("messages", plans);

      sdk.setTable("coupon");

      // plan_id: _data.plan1,
      const result = await sdk.callRestAPI(
        {
          code: _data.code,
          plan_id: 10,
          expired: _data.status,
          messages: parseInt(_data.messages),
        },
        "POST"
      );
      // console.log("result", result);
      if (!result.error) {
        showToast(globalDispatch, "Added");
        navigate("/admin/rewards");
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error) {
      console.log("Error", error);
      setError("name", {
        type: "manual",
        message: error.message,
      });
      tokenExpireError(dispatch, error.message);
    }
  };

  const handlegenerate1 = async (_data) => {
    try {
      const result = await sdk.sendreward({
        email: _data.email,
        plan_id: _data.plan,
      });

      if (!result.error) {
        showToast(globalDispatch, "Reward Sent");
        // navigate("/admin/lenders");
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error) {
      console.log("Error", error);
      setError("create_at", {
        type: "manual",
        message: error.message,
      });
      tokenExpireError(dispatch, error.message);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "rewards",
      },
    });
  }, []);
  React.useEffect(() => {
    const getplans = async () => {
      const plans = await sdk.getplans();
      setPlans(plans?.list?.reverse());
      // console.log("plans", plans);
    };
    getplans();
  }, []);
  // console.log("log", plans);
  // console.log("mess", message);

  // React.useEffect(() => {
  //   console.log("mess", message);
  //   let value= plans?.filter((plan) => plan.id == message)
  //   console.log("messages",value );
  // }, [message]);

  return (
    <div>
      {/* Send Reward*/}
      {/* <div className=" shadow-md rounded  mx-auto p-5">
        <h4 className="text-2xl font-medium">Send Rewards</h4>
        <form className=" w-full max-w-lg" onSubmit={handleSubmit(onSubmit)}>

          <div className="mb-4  ">
            <label
              className="block text-gray-700 text-sm font-bold mb-2"
              htmlFor="update_at"
            >
              Winner Email
            </label>
            <input
              type="email"
              placeholder="winner email"
              {...register("email")}
              className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
                errors.email?.message ? "border-red-500" : ""
              }`}
            />
            <p className="text-red-500 text-xs italic">
              {errors.email?.message}
            </p>
          </div>

          <div className="mb-4  ">
            <label
              className="block text-gray-700 text-sm font-bold mb-2"
              htmlFor="update_at"
            >
              Number of messages 
            </label>
            <input
              type="number"
              placeholder=" Number of messages"
              {...register("messages")}
              className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
                errors.messages?.message ? "border-red-500" : ""
              }`}
            />
            <p className="text-red-500 text-xs italic">
            </p>
          </div>

         

          <button
            type="submit"
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
          >
            Submit
          </button>
        </form>
      </div> */}

      {/* Generate Coupon */}
      <div className=" shadow-md rounded  mx-auto p-5 mt-6">
        <h4 className="text-2xl font-medium">Generate Coupon</h4>
        <form
          className=" w-full max-w-lg"
          onSubmit={handleSubmit(handlegenerate)}
        >
          <div className="mb-4  ">
            <label
              className="block text-gray-700 text-sm font-bold mb-2"
              htmlFor="update_at"
            >
              Code
            </label>
            <input
              type="text"
              placeholder="code"
              {...register("code")}
              className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
                errors.code?.message ? "border-red-500" : ""
              }`}
            />
            <p className="text-red-500 text-xs italic">
              {errors.code?.message}
            </p>
          </div>
          <div className="mb-4  ">
            <label
              className="block text-gray-700 text-sm font-bold mb-2"
              htmlFor="update_at"
            >
              Number of messages
            </label>
            <input
              type="number"
              placeholder="messages"
              {...register("messages")}
              className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
                errors.code?.message ? "border-red-500" : ""
              }`}
            />
            <p className="text-red-500 text-xs italic">
              {errors.code?.message}
            </p>
          </div>

          {/* <div className="mb-4 w-full   ">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Plan
            </label>
            <select
              className="shadow border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"
              {...register("plan1")}
            >
              <option name="plan1" value={""} defaultValue=""></option>
              {plans?.map((option) => (
                <option
                  name="plan1"
                  value={option.id}
                  key={option.id}
                  defaultValue=""
                >
                  {option.name}
                </option>
              ))}
            </select>
            <p className="text-red-500 text-xs italic"></p>
          </div> */}

          <div className="mb-4 w-full   ">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Status
            </label>
            <select
              className="shadow border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"
              {...register("status")}
            >
              <option name="status" defaultValue=""></option>

              {status?.map((option) => (
                <option
                  name="status"
                  value={option.key}
                  key={option.key}
                  defaultValue=""
                >
                  {option.value}
                </option>
              ))}
            </select>
            <p className="text-red-500 text-xs italic"></p>
          </div>

          <button
            type="submit"
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
          >
            Generate
          </button>
        </form>
      </div>
    </div>
  );
};

export default AddAdminRewardPage;
