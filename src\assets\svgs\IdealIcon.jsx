import React from "react";

export const IdealIcon = ({ className = "" }) => {
  return (
    <svg
      className={`${className}`}
      width="48"
      height="48"
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_313_3607)">
        <rect width="48" height="48" rx="11.52" fill="#FA6FB3" />
        <g clip-path="url(#clip1_313_3607)">
          <path
            d="M13.2246 14.2256V33.787H24.6291C32.1522 33.787 35.4146 29.5739 35.4146 23.9871C35.4146 18.4221 32.1522 14.2256 24.6291 14.2256H13.2246Z"
            fill="white"
          />
          <path
            d="M14.457 15.459H24.6305C31.5354 15.459 34.1822 19.2297 34.1822 23.9864C34.1822 29.6902 30.4862 32.5522 24.6305 32.5522H14.457V15.459ZM15.4433 16.4452V31.5665H24.6305C30.1771 31.5665 33.195 28.9819 33.195 23.9864C33.195 18.8525 29.9236 16.4449 24.6305 16.4449H15.4433V16.4452Z"
            fill="black"
          />
          <path
            d="M16.6758 25.0986H19.6168V30.3329H16.6758V25.0986Z"
            fill="black"
          />
          <path
            d="M18.1461 24.2884C19.1556 24.2884 19.9739 23.4701 19.9739 22.4606C19.9739 21.4511 19.1556 20.6328 18.1461 20.6328C17.1367 20.6328 16.3184 21.4511 16.3184 22.4606C16.3184 23.4701 17.1367 24.2884 18.1461 24.2884Z"
            fill="black"
          />
          <path
            d="M25.7847 23.3165V24.1641H23.6892V20.763H25.7178V21.6102H24.5364V22.004H25.6536V22.8516H24.5364V23.3168L25.7847 23.3165ZM26.1514 24.1647L27.1768 20.7618H28.3823L29.4074 24.1647H28.5255L28.3331 23.5065H27.2247L27.0321 24.1647H26.1514ZM27.4732 22.6598H28.0865L27.8052 21.6986H27.7539L27.4732 22.6598ZM29.8335 20.7621H30.6813V23.3168H31.9362C31.5919 18.6813 27.946 17.6797 24.6311 17.6797H21.0983V20.7639H21.6211C22.5745 20.7639 23.1664 21.4103 23.1664 22.4506C23.1664 23.524 22.5886 24.1647 21.6211 24.1647H21.0983V30.3355H24.6314C30.0183 30.3355 31.8943 27.8335 31.9606 24.1641H29.8335V20.7621ZM21.0977 21.6111V23.3168H21.6211C21.9838 23.3168 22.3191 23.2119 22.3191 22.4503C22.3191 21.7064 21.9461 21.6111 21.6211 21.6111H21.0977Z"
            fill="#D50072"
          />
        </g>
      </g>
      <defs>
        <clipPath id="clip0_313_3607">
          <rect width="48" height="48" rx="12" fill="white" />
        </clipPath>
        <clipPath id="clip1_313_3607">
          <rect
            width="26.738"
            height="24"
            fill="white"
            transform="translate(11 12)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
