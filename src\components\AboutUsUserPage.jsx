import React, { useState } from "react";
import { AuthContext, tokenExpireError } from "../authContext";
import { NavLink } from "react-router-dom";
import { GlobalContext } from "../globalContext";
import aboutimg from "../assets/images/aboutimg.png";
import { useNavigate } from "react-router-dom";
import Navbar from "./NavBar";
import MkdSDK from "Utils/MkdSDK";
import SkeletonLoading from "./SkeletonLoading";
import FooterComp from "./FooterComp";
import { getCmsValue } from "Utils/utils";
import DisplayAdsByAge from "./DisplayAdsByAge";
import { useData } from "Src/dataContext";
let sdk = new MkdSDK();
const AboutUsUserPage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  // const [cms, setCms] = useState();
  const { cms } = useData();

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "user",
      },
    });
  }, []);
  return (
    <div className="relative ">
      <Navbar />
      <div className="h-[100vh] flex flex-col justify-between">
        <div className="bg-white pb-8 rounded  px-4 flex flex-col justify-center items-center">
          {!cms && <SkeletonLoading padding="py-[5rem] pt-[10rem]" />}
          {!cms && <SkeletonLoading padding="" counter={20} />}
          <div
            className="bg-gradient-to-r from-red-600 to-black text-white py-20 px-4 sm:px-6 lg:px-8 text-center flex flex-col justify-between items-center w-full "
            style={{
              // backgroundImage: `url('/path/to/your/background-image.jpg')`,
              backgroundImage: `url(${getCmsValue(cms, "Aboutus_image")})`,
              backgroundSize: "cover",
              backgroundPosition: "center",
            }}
          >
            <div className="max-w-7xl mx-auto">
              <h1 className="text-[24px] sm:text-5xl lg:text-[64px] font-semibold leading-tight mb-4">
                {getCmsValue(cms, "Aboutus_headings")}
              </h1>
            </div>

            <div className="max-w-[1280px] px-2 md:text-center flex md:justify-center justify-center text-center w-full">
              <h1 className="md:text-[30px] text-[20px] md:text-center text-center font-normal leading-tight ">
                {getCmsValue(cms, "Aboutus_sub_heading")}
              </h1>
            </div>
          </div>

          {/* <DisplayAdsByAge /> */}

          {/* <h1 className="text-3xl font-bold mb-4">About Us</h1> */}
          
          <div className=" max-w-[1280px] px-2 md:text-justify text-center mt-4 w-full">
            {cms
              ? JSON.parse(getCmsValue(cms, "Aboutus_texts")).map(
                  (content) => (
                    <div className="md:text-[16px] text-[15px] mb-4 mt-4 w-full ">
                      <p className="text-[#3a3a3a] md:text-[16px] text-[15px] font-semibold ">
                        {content.key} 
                      </p>
                      <p className="text-[#999999] md:text-[16px] text-[15px] ">
                        {content.value}
                      </p>
                      <div className=" w-full max-w-md md:mx-0 mx-auto mt-2">
                        <img src={content.image} alt="" />
                      </div>
                    </div>
                  )
                )
              : null}
          </div>
          
          <DisplayAdsByAge />
        </div>
        <FooterComp />
      </div>
    </div>
  );
};

export default AboutUsUserPage;
