import{e as Xe,t as De,T as Ke,n as Je,B as Ze}from"./aws-s3-9d3fc29b.js";import{g as ke}from"../vendor-b0222800.js";function se(s,e){if(!Object.prototype.hasOwnProperty.call(s,e))throw new TypeError("attempted to use private field on non-instance");return s}var Qe=0;function Me(s){return"__private_"+Qe+++"_"+s}const et={version:"3.2.2"};var C=Me("callbacks"),ce=Me("publish");class Ce{constructor(){Object.defineProperty(this,ce,{value:tt}),this.state={},Object.defineProperty(this,C,{writable:!0,value:new Set})}getState(){return this.state}setState(e){const t={...this.state},i={...this.state,...e};this.state=i,se(this,ce)[ce](t,i,e)}subscribe(e){return se(this,C)[C].add(e),()=>{se(this,C)[C].delete(e)}}}function tt(){for(var s=arguments.length,e=new Array(s),t=0;t<s;t++)e[t]=arguments[t];se(this,C)[C].forEach(i=>{i(...e)})}Ce.VERSION=et.version;function je(s){const e=s.lastIndexOf(".");return e===-1||e===s.length-1?{name:s,extension:void 0}:{name:s.slice(0,e),extension:s.slice(e+1)}}const Fe={__proto__:null,md:"text/markdown",markdown:"text/markdown",mp4:"video/mp4",mp3:"audio/mp3",svg:"image/svg+xml",jpg:"image/jpeg",png:"image/png",webp:"image/webp",gif:"image/gif",heic:"image/heic",heif:"image/heif",yaml:"text/yaml",yml:"text/yaml",csv:"text/csv",tsv:"text/tab-separated-values",tab:"text/tab-separated-values",avi:"video/x-msvideo",mks:"video/x-matroska",mkv:"video/x-matroska",mov:"video/quicktime",dicom:"application/dicom",doc:"application/msword",docm:"application/vnd.ms-word.document.macroenabled.12",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",dot:"application/msword",dotm:"application/vnd.ms-word.template.macroenabled.12",dotx:"application/vnd.openxmlformats-officedocument.wordprocessingml.template",xla:"application/vnd.ms-excel",xlam:"application/vnd.ms-excel.addin.macroenabled.12",xlc:"application/vnd.ms-excel",xlf:"application/x-xliff+xml",xlm:"application/vnd.ms-excel",xls:"application/vnd.ms-excel",xlsb:"application/vnd.ms-excel.sheet.binary.macroenabled.12",xlsm:"application/vnd.ms-excel.sheet.macroenabled.12",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",xlt:"application/vnd.ms-excel",xltm:"application/vnd.ms-excel.template.macroenabled.12",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template",xlw:"application/vnd.ms-excel",txt:"text/plain",text:"text/plain",conf:"text/plain",log:"text/plain",pdf:"application/pdf",zip:"application/zip","7z":"application/x-7z-compressed",rar:"application/x-rar-compressed",tar:"application/x-tar",gz:"application/gzip",dmg:"application/x-apple-diskimage"};function Ne(s){var e;if(s.type)return s.type;const t=s.name?(e=je(s.name).extension)==null?void 0:e.toLowerCase():null;return t&&t in Fe?Fe[t]:"application/octet-stream"}function st(s){return s.charCodeAt(0).toString(32)}function Pe(s){let e="";return s.replace(/[^A-Z0-9]/gi,t=>(e+=`-${st(t)}`,"/"))+e}function it(s){let e="uppy";return typeof s.name=="string"&&(e+=`-${Pe(s.name.toLowerCase())}`),s.type!==void 0&&(e+=`-${s.type}`),s.meta&&typeof s.meta.relativePath=="string"&&(e+=`-${Pe(s.meta.relativePath.toLowerCase())}`),s.data.size!==void 0&&(e+=`-${s.data.size}`),s.data.lastModified!==void 0&&(e+=`-${s.data.lastModified}`),e}function rt(s){return!s.isRemote||!s.remote?!1:new Set(["box","dropbox","drive","facebook","unsplash"]).has(s.remote.provider)}function ot(s){if(rt(s))return s.id;const e=Ne(s);return it({...s,type:e})}function nt(s){if(s==null&&typeof navigator<"u"&&(s=navigator.userAgent),!s)return!0;const e=/Edge\/(\d+\.\d+)/.exec(s);if(!e)return!0;const i=e[1].split(".",2),r=parseInt(i[0],10),o=parseInt(i[1],10);return r<15||r===15&&o<15063||r>18||r===18&&o>=18218}function lt(s,e){return e.name?e.name:s.split("/")[0]==="image"?`${s.split("/")[0]}.${s.split("/")[1]}`:"noname"}function pe(s){return s<10?`0${s}`:s.toString()}function ie(){const s=new Date,e=pe(s.getHours()),t=pe(s.getMinutes()),i=pe(s.getSeconds());return`${e}:${t}:${i}`}const at={debug:()=>{},warn:()=>{},error:function(){for(var s=arguments.length,e=new Array(s),t=0;t<s;t++)e[t]=arguments[t];return console.error(`[Uppy] [${ie()}]`,...e)}},dt={debug:function(){for(var s=arguments.length,e=new Array(s),t=0;t<s;t++)e[t]=arguments[t];return console.debug(`[Uppy] [${ie()}]`,...e)},warn:function(){for(var s=arguments.length,e=new Array(s),t=0;t<s;t++)e[t]=arguments[t];return console.warn(`[Uppy] [${ie()}]`,...e)},error:function(){for(var s=arguments.length,e=new Array(s),t=0;t<s;t++)e[t]=arguments[t];return console.error(`[Uppy] [${ie()}]`,...e)}};var ut=function(e){if(typeof e!="number"||Number.isNaN(e))throw new TypeError(`Expected a number, got ${typeof e}`);const t=e<0,i=["B","KB","MB","GB","TB","PB","EB","ZB","YB"];if(t&&(e=-e),e<1)return`${(t?"-":"")+e} B`;const r=Math.min(Math.floor(Math.log(e)/Math.log(1024)),i.length-1);e=Number(e/1024**r);const o=i[r];return e>=10||e%1===0?`${(t?"-":"")+e.toFixed(0)} ${o}`:`${(t?"-":"")+e.toFixed(1)} ${o}`};const he=ke(ut);function Re(s,e){this.text=s=s||"",this.hasWild=~s.indexOf("*"),this.separator=e,this.parts=s.split(e)}Re.prototype.match=function(s){var e=!0,t=this.parts,i,r=t.length,o;if(typeof s=="string"||s instanceof String)if(!this.hasWild&&this.text!=s)e=!1;else{for(o=(s||"").split(this.separator),i=0;e&&i<r;i++)t[i]!=="*"&&(i<o.length?e=t[i]===o[i]:e=!1);e=e&&o}else if(typeof s.splice=="function")for(e=[],i=s.length;i--;)this.match(s[i])&&(e[e.length]=s[i]);else if(typeof s=="object"){e={};for(var n in s)this.match(n)&&(e[n]=s[n])}return e};var ct=function(s,e,t){var i=new Re(s,t||/[\/\.]/);return typeof e<"u"?i.match(e):i},pt=ct,ht=/[\/\+\.]/,ft=function(s,e){function t(i){var r=pt(i,s,ht);return r&&r.length>=2}return e?t(e.split(";")[0]):t};const gt=ke(ft),_t={maxFileSize:null,minFileSize:null,maxTotalFileSize:null,maxNumberOfFiles:null,minNumberOfFiles:null,allowedFileTypes:null,requiredMetaFields:[]};class U extends Error{constructor(e,t){var i;super(e),this.isRestriction=!0,this.isUserFacing=(i=t==null?void 0:t.isUserFacing)!=null?i:!0,t!=null&&t.file&&(this.file=t.file)}}class mt{constructor(e,t){this.i18n=t,this.getOpts=()=>{var i;const r=e();if(((i=r.restrictions)==null?void 0:i.allowedFileTypes)!=null&&!Array.isArray(r.restrictions.allowedFileTypes))throw new TypeError("`restrictions.allowedFileTypes` must be an array");return r}}validateAggregateRestrictions(e,t){const{maxTotalFileSize:i,maxNumberOfFiles:r}=this.getOpts().restrictions;if(r&&e.filter(n=>!n.isGhost).length+t.length>r)throw new U(`${this.i18n("youCanOnlyUploadX",{smart_count:r})}`);if(i){let o=e.reduce((n,a)=>{var u;return n+((u=a.size)!=null?u:0)},0);for(const n of t)if(n.size!=null&&(o+=n.size,o>i))throw new U(this.i18n("exceedsSize",{size:he(i),file:n.name}))}}validateSingleFile(e){const{maxFileSize:t,minFileSize:i,allowedFileTypes:r}=this.getOpts().restrictions;if(r&&!r.some(n=>n.includes("/")?e.type?gt(e.type.replace(/;.*?$/,""),n):!1:n[0]==="."&&e.extension?e.extension.toLowerCase()===n.slice(1).toLowerCase():!1)){const n=r.join(", ");throw new U(this.i18n("youCanOnlyUploadFileTypes",{types:n}),{file:e})}if(t&&e.size!=null&&e.size>t)throw new U(this.i18n("exceedsSize",{size:he(t),file:e.name}),{file:e});if(i&&e.size!=null&&e.size<i)throw new U(this.i18n("inferiorSize",{size:he(i)}),{file:e})}validate(e,t){t.forEach(i=>{this.validateSingleFile(i)}),this.validateAggregateRestrictions(e,t)}validateMinNumberOfFiles(e){const{minNumberOfFiles:t}=this.getOpts().restrictions;if(t&&Object.keys(e).length<t)throw new U(this.i18n("youHaveToAtLeastSelectX",{smart_count:t}))}getMissingRequiredMetaFields(e){const t=new U(this.i18n("missingRequiredMetaFieldOnFile",{fileName:e.name})),{requiredMetaFields:i}=this.getOpts().restrictions,r=[];for(const o of i)(!Object.hasOwn(e.meta,o)||e.meta[o]==="")&&r.push(o);return{missingFields:r,error:t}}}const vt={strings:{addBulkFilesFailed:{0:"Failed to add %{smart_count} file due to an internal error",1:"Failed to add %{smart_count} files due to internal errors"},youCanOnlyUploadX:{0:"You can only upload %{smart_count} file",1:"You can only upload %{smart_count} files"},youHaveToAtLeastSelectX:{0:"You have to select at least %{smart_count} file",1:"You have to select at least %{smart_count} files"},exceedsSize:"%{file} exceeds maximum allowed size of %{size}",missingRequiredMetaField:"Missing required meta fields",missingRequiredMetaFieldOnFile:"Missing required meta fields in %{fileName}",inferiorSize:"This file is smaller than the allowed size of %{size}",youCanOnlyUploadFileTypes:"You can only upload: %{types}",noMoreFilesAllowed:"Cannot add more files",noDuplicates:"Cannot add the duplicate file '%{fileName}', it already exists",companionError:"Connection with Companion failed",authAborted:"Authentication aborted",companionUnauthorizeHint:"To unauthorize to your %{provider} account, please go to %{url}",failedToUpload:"Failed to upload %{file}",noInternetConnection:"No Internet connection",connectedToInternet:"Connected to the Internet",noFilesFound:"You have no files or folders here",noSearchResults:"Unfortunately, there are no results for this search",selectX:{0:"Select %{smart_count}",1:"Select %{smart_count}"},allFilesFromFolderNamed:"All files from folder %{name}",openFolderNamed:"Open folder %{name}",cancel:"Cancel",logOut:"Log out",filter:"Filter",resetFilter:"Reset filter",loading:"Loading...",loadedXFiles:"Loaded %{numFiles} files",authenticateWithTitle:"Please authenticate with %{pluginName} to select files",authenticateWith:"Connect to %{pluginName}",signInWithGoogle:"Sign in with Google",searchImages:"Search for images",enterTextToSearch:"Enter text to search for images",search:"Search",resetSearch:"Reset search",emptyFolderAdded:"No files were added from empty folder",addedNumFiles:"Added %{numFiles} file(s)",folderAlreadyAdded:'The folder "%{folder}" was already added',folderAdded:{0:"Added %{smart_count} file from %{folder}",1:"Added %{smart_count} files from %{folder}"},additionalRestrictionsFailed:"%{count} additional restrictions were not fulfilled"}};let ze,Le;function c(s,e){if(!Object.prototype.hasOwnProperty.call(s,e))throw new TypeError("attempted to use private field on non-instance");return s}var yt=0;function v(s){return"__private_"+yt+++"_"+s}const wt={version:"3.9.3"},ee={totalProgress:0,allowNewUpload:!0,error:null,recoveredState:null};var b=v("plugins"),F=v("restricter"),I=v("storeUnsubscribe"),T=v("emitter"),j=v("preProcessors"),N=v("uploaders"),A=v("postProcessors"),P=v("informAndEmit"),X=v("checkRequiredMetaFieldsOnFile"),fe=v("checkRequiredMetaFields"),q=v("assertNewUploadAllowed"),me=v("transformFile"),H=v("startIfAutoProceed"),W=v("checkAndUpdateFileState"),ge=v("addListeners"),E=v("updateOnlineStatus"),G=v("requestClientById"),O=v("createUpload"),_e=v("getUpload"),z=v("removeUpload"),$=v("runUpload");ze=Symbol.for("uppy test: getPlugins");Le=Symbol.for("uppy test: createUpload");class le{constructor(e){Object.defineProperty(this,$,{value:kt}),Object.defineProperty(this,z,{value:At}),Object.defineProperty(this,_e,{value:$t}),Object.defineProperty(this,O,{value:Ot}),Object.defineProperty(this,ge,{value:Tt}),Object.defineProperty(this,W,{value:Et}),Object.defineProperty(this,H,{value:Ut}),Object.defineProperty(this,me,{value:xt}),Object.defineProperty(this,q,{value:Pt}),Object.defineProperty(this,fe,{value:Ft}),Object.defineProperty(this,X,{value:St}),Object.defineProperty(this,P,{value:bt}),Object.defineProperty(this,b,{writable:!0,value:Object.create(null)}),Object.defineProperty(this,F,{writable:!0,value:void 0}),Object.defineProperty(this,I,{writable:!0,value:void 0}),Object.defineProperty(this,T,{writable:!0,value:Xe()}),Object.defineProperty(this,j,{writable:!0,value:new Set}),Object.defineProperty(this,N,{writable:!0,value:new Set}),Object.defineProperty(this,A,{writable:!0,value:new Set}),this.scheduledAutoProceed=null,this.wasOffline=!1,this.calculateProgress=De((r,o)=>{const n=this.getFile(r==null?void 0:r.id);if(r==null||!n){this.log(`Not setting progress for a file that has been removed: ${r==null?void 0:r.id}`);return}if(n.progress.percentage===100){this.log(`Not setting progress for a file that has been already uploaded: ${r.id}`);return}const a=Number.isFinite(o.bytesTotal)&&o.bytesTotal>0;this.setFileState(r.id,{progress:{...n.progress,bytesUploaded:o.bytesUploaded,bytesTotal:o.bytesTotal,percentage:a?Math.round(o.bytesUploaded/o.bytesTotal*100):0}}),this.calculateTotalProgress()},500,{leading:!0,trailing:!0}),Object.defineProperty(this,E,{writable:!0,value:this.updateOnlineStatus.bind(this)}),Object.defineProperty(this,G,{writable:!0,value:new Map}),this.defaultLocale=vt;const t={id:"uppy",autoProceed:!1,allowMultipleUploadBatches:!0,debug:!1,restrictions:_t,meta:{},onBeforeFileAdded:(r,o)=>!Object.hasOwn(o,r.id),onBeforeUpload:r=>r,store:new Ce,logger:at,infoTimeout:5e3},i={...t,...e};this.opts={...i,restrictions:{...t.restrictions,...e&&e.restrictions}},e&&e.logger&&e.debug?this.log("You are using a custom `logger`, but also set `debug: true`, which uses built-in logger to output logs to console. Ignoring `debug: true` and using your custom `logger`.","warning"):e&&e.debug&&(this.opts.logger=dt),this.log(`Using Core v${le.VERSION}`),this.i18nInit(),this.store=this.opts.store,this.setState({...ee,plugins:{},files:{},currentUploads:{},capabilities:{uploadProgress:nt(),individualCancellation:!0,resumableUploads:!1},meta:{...this.opts.meta},info:[]}),c(this,F)[F]=new mt(()=>this.opts,this.i18n),c(this,I)[I]=this.store.subscribe((r,o,n)=>{this.emit("state-update",r,o,n),this.updateAll(o)}),this.opts.debug&&typeof window<"u"&&(window[this.opts.id]=this),c(this,ge)[ge]()}emit(e){for(var t=arguments.length,i=new Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];c(this,T)[T].emit(e,...i)}on(e,t){return c(this,T)[T].on(e,t),this}once(e,t){return c(this,T)[T].once(e,t),this}off(e,t){return c(this,T)[T].off(e,t),this}updateAll(e){this.iteratePlugins(t=>{t.update(e)})}setState(e){this.store.setState(e)}getState(){return this.store.getState()}patchFilesState(e){const t=this.getState().files;this.setState({files:{...t,...Object.fromEntries(Object.entries(e).map(i=>{let[r,o]=i;return[r,{...t[r],...o}]}))}})}setFileState(e,t){if(!this.getState().files[e])throw new Error(`Can’t set state for ${e} (the file could have been removed)`);this.patchFilesState({[e]:t})}i18nInit(){const e=i=>this.log(`Missing i18n string: ${i}`,"error"),t=new Ke([this.defaultLocale,this.opts.locale],{onMissingKey:e});this.i18n=t.translate.bind(t),this.i18nArray=t.translateArray.bind(t),this.locale=t.locale}setOptions(e){this.opts={...this.opts,...e,restrictions:{...this.opts.restrictions,...e==null?void 0:e.restrictions}},e.meta&&this.setMeta(e.meta),this.i18nInit(),e.locale&&this.iteratePlugins(t=>{t.setOptions(e)}),this.setState(void 0)}resetProgress(){const e={percentage:0,bytesUploaded:0,uploadComplete:!1,uploadStarted:null},t={...this.getState().files},i={};Object.keys(t).forEach(r=>{i[r]={...t[r],progress:{...t[r].progress,...e}}}),this.setState({files:i,...ee}),this.emit("reset-progress")}clearUploadedFiles(){this.setState({...ee,files:{}})}addPreProcessor(e){c(this,j)[j].add(e)}removePreProcessor(e){return c(this,j)[j].delete(e)}addPostProcessor(e){c(this,A)[A].add(e)}removePostProcessor(e){return c(this,A)[A].delete(e)}addUploader(e){c(this,N)[N].add(e)}removeUploader(e){return c(this,N)[N].delete(e)}setMeta(e){const t={...this.getState().meta,...e},i={...this.getState().files};Object.keys(i).forEach(r=>{i[r]={...i[r],meta:{...i[r].meta,...e}}}),this.log("Adding metadata:"),this.log(e),this.setState({meta:t,files:i})}setFileMeta(e,t){const i={...this.getState().files};if(!i[e]){this.log("Was trying to set metadata for a file that has been removed: ",e);return}const r={...i[e].meta,...t};i[e]={...i[e],meta:r},this.setState({files:i})}getFile(e){return this.getState().files[e]}getFiles(){const{files:e}=this.getState();return Object.values(e)}getFilesByIds(e){return e.map(t=>this.getFile(t))}getObjectOfFilesPerState(){const{files:e,totalProgress:t,error:i}=this.getState(),r=Object.values(e),o=r.filter(f=>{let{progress:S}=f;return!S.uploadComplete&&S.uploadStarted}),n=r.filter(f=>!f.progress.uploadStarted),a=r.filter(f=>f.progress.uploadStarted||f.progress.preprocess||f.progress.postprocess),u=r.filter(f=>f.progress.uploadStarted),d=r.filter(f=>f.isPaused),h=r.filter(f=>f.progress.uploadComplete),l=r.filter(f=>f.error),g=o.filter(f=>!f.isPaused),p=r.filter(f=>f.progress.preprocess||f.progress.postprocess);return{newFiles:n,startedFiles:a,uploadStartedFiles:u,pausedFiles:d,completeFiles:h,erroredFiles:l,inProgressFiles:o,inProgressNotPausedFiles:g,processingFiles:p,isUploadStarted:u.length>0,isAllComplete:t===100&&h.length===r.length&&p.length===0,isAllErrored:!!i&&l.length===r.length,isAllPaused:o.length!==0&&d.length===o.length,isUploadInProgress:o.length>0,isSomeGhost:r.some(f=>f.isGhost)}}validateRestrictions(e,t){t===void 0&&(t=this.getFiles());try{c(this,F)[F].validate(t,[e])}catch(i){return i}return null}checkIfFileAlreadyExists(e){const{files:t}=this.getState();return!!(t[e]&&!t[e].isGhost)}addFile(e){c(this,q)[q](e);const{nextFilesState:t,validFilesToAdd:i,errors:r}=c(this,W)[W]([e]),o=r.filter(a=>a.isRestriction);if(c(this,P)[P](o),r.length>0)throw r[0];this.setState({files:t});const[n]=i;return this.emit("file-added",n),this.emit("files-added",i),this.log(`Added file: ${n.name}, ${n.id}, mime type: ${n.type}`),c(this,H)[H](),n.id}addFiles(e){c(this,q)[q]();const{nextFilesState:t,validFilesToAdd:i,errors:r}=c(this,W)[W](e),o=r.filter(a=>a.isRestriction);c(this,P)[P](o);const n=r.filter(a=>!a.isRestriction);if(n.length>0){let a=`Multiple errors occurred while adding files:
`;if(n.forEach(u=>{a+=`
 * ${u.message}`}),this.info({message:this.i18n("addBulkFilesFailed",{smart_count:n.length}),details:a},"error",this.opts.infoTimeout),typeof AggregateError=="function")throw new AggregateError(n,a);{const u=new Error(a);throw u.errors=n,u}}this.setState({files:t}),i.forEach(a=>{this.emit("file-added",a)}),this.emit("files-added",i),i.length>5?this.log(`Added batch of ${i.length} files`):Object.values(i).forEach(a=>{this.log(`Added file: ${a.name}
 id: ${a.id}
 type: ${a.type}`)}),i.length>0&&c(this,H)[H]()}removeFiles(e,t){const{files:i,currentUploads:r}=this.getState(),o={...i},n={...r},a=Object.create(null);e.forEach(l=>{i[l]&&(a[l]=i[l],delete o[l])});function u(l){return a[l]===void 0}Object.keys(n).forEach(l=>{const g=r[l].fileIDs.filter(u);if(g.length===0){delete n[l];return}const{capabilities:p}=this.getState();if(g.length!==r[l].fileIDs.length&&!p.individualCancellation)throw new Error("individualCancellation is disabled");n[l]={...r[l],fileIDs:g}});const d={currentUploads:n,files:o};Object.keys(o).length===0&&(d.allowNewUpload=!0,d.error=null,d.recoveredState=null),this.setState(d),this.calculateTotalProgress();const h=Object.keys(a);h.forEach(l=>{this.emit("file-removed",a[l],t)}),h.length>5?this.log(`Removed ${h.length} files`):this.log(`Removed files: ${h.join(", ")}`)}removeFile(e,t){this.removeFiles([e],t)}pauseResume(e){if(!this.getState().capabilities.resumableUploads||this.getFile(e).progress.uploadComplete)return;const i=!(this.getFile(e).isPaused||!1);return this.setFileState(e,{isPaused:i}),this.emit("upload-pause",e,i),i}pauseAll(){const e={...this.getState().files};Object.keys(e).filter(i=>!e[i].progress.uploadComplete&&e[i].progress.uploadStarted).forEach(i=>{const r={...e[i],isPaused:!0};e[i]=r}),this.setState({files:e}),this.emit("pause-all")}resumeAll(){const e={...this.getState().files};Object.keys(e).filter(i=>!e[i].progress.uploadComplete&&e[i].progress.uploadStarted).forEach(i=>{const r={...e[i],isPaused:!1,error:null};e[i]=r}),this.setState({files:e}),this.emit("resume-all")}retryAll(){const e={...this.getState().files},t=Object.keys(e).filter(r=>e[r].error);if(t.forEach(r=>{const o={...e[r],isPaused:!1,error:null};e[r]=o}),this.setState({files:e,error:null}),this.emit("retry-all",t),t.length===0)return Promise.resolve({successful:[],failed:[]});const i=c(this,O)[O](t,{forceAllowNewUpload:!0});return c(this,$)[$](i)}cancelAll(e){let{reason:t="user"}=e===void 0?{}:e;if(this.emit("cancel-all",{reason:t}),t==="user"){const{files:i}=this.getState(),r=Object.keys(i);r.length&&this.removeFiles(r,"cancel-all"),this.setState(ee)}}retryUpload(e){this.setFileState(e,{error:null,isPaused:!1}),this.emit("upload-retry",e);const t=c(this,O)[O]([e],{forceAllowNewUpload:!0});return c(this,$)[$](t)}logout(){this.iteratePlugins(e=>{var t;(t=e.provider)==null||t.logout==null||t.logout()})}calculateTotalProgress(){const t=this.getFiles().filter(d=>d.progress.uploadStarted||d.progress.preprocess||d.progress.postprocess);if(t.length===0){this.emit("progress",0),this.setState({totalProgress:0});return}const i=t.filter(d=>d.progress.bytesTotal!=null),r=t.filter(d=>d.progress.bytesTotal==null);if(i.length===0){const d=t.length*100,h=r.reduce((g,p)=>g+p.progress.percentage,0),l=Math.round(h/d*100);this.setState({totalProgress:l});return}let o=i.reduce((d,h)=>{var l;return d+((l=h.progress.bytesTotal)!=null?l:0)},0);const n=o/i.length;o+=n*r.length;let a=0;i.forEach(d=>{a+=d.progress.bytesUploaded}),r.forEach(d=>{a+=n*(d.progress.percentage||0)/100});let u=o===0?0:Math.round(a/o*100);u>100&&(u=100),this.setState({totalProgress:u}),this.emit("progress",u)}updateOnlineStatus(){var e;((e=window.navigator.onLine)!=null?e:!0)?(this.emit("is-online"),this.wasOffline&&(this.emit("back-online"),this.info(this.i18n("connectedToInternet"),"success",3e3),this.wasOffline=!1)):(this.emit("is-offline"),this.info(this.i18n("noInternetConnection"),"error",0),this.wasOffline=!0)}getID(){return this.opts.id}use(e,t){if(typeof e!="function"){const n=`Expected a plugin class, but got ${e===null?"null":typeof e}. Please verify that the plugin was imported and spelled correctly.`;throw new TypeError(n)}const i=new e(this,t),r=i.id;if(!r)throw new Error("Your plugin must have an id");if(!i.type)throw new Error("Your plugin must have a type");const o=this.getPlugin(r);if(o){const n=`Already found a plugin named '${o.id}'. Tried to use: '${r}'.
Uppy plugins must have unique \`id\` options. See https://uppy.io/docs/plugins/#id.`;throw new Error(n)}return e.VERSION&&this.log(`Using ${r} v${e.VERSION}`),i.type in c(this,b)[b]?c(this,b)[b][i.type].push(i):c(this,b)[b][i.type]=[i],i.install(),this.emit("plugin-added",i),this}getPlugin(e){for(const t of Object.values(c(this,b)[b])){const i=t.find(r=>r.id===e);if(i!=null)return i}}[ze](e){return c(this,b)[b][e]}iteratePlugins(e){Object.values(c(this,b)[b]).flat(1).forEach(e)}removePlugin(e){this.log(`Removing plugin ${e.id}`),this.emit("plugin-remove",e),e.uninstall&&e.uninstall();const t=c(this,b)[b][e.type],i=t.findIndex(n=>n.id===e.id);i!==-1&&t.splice(i,1);const o={plugins:{...this.getState().plugins,[e.id]:void 0}};this.setState(o)}close(e){let{reason:t}=e===void 0?{}:e;this.log(`Closing Uppy instance ${this.opts.id}: removing all files and uninstalling plugins`),this.cancelAll({reason:t}),c(this,I)[I](),this.iteratePlugins(i=>{this.removePlugin(i)}),typeof window<"u"&&window.removeEventListener&&(window.removeEventListener("online",c(this,E)[E]),window.removeEventListener("offline",c(this,E)[E]))}hideInfo(){const{info:e}=this.getState();this.setState({info:e.slice(1)}),this.emit("info-hidden")}info(e,t,i){t===void 0&&(t="info"),i===void 0&&(i=3e3);const r=typeof e=="object";this.setState({info:[...this.getState().info,{type:t,message:r?e.message:e,details:r?e.details:null}]}),setTimeout(()=>this.hideInfo(),i),this.emit("info-visible")}log(e,t){const{logger:i}=this.opts;switch(t){case"error":i.error(e);break;case"warning":i.warn(e);break;default:i.debug(e);break}}registerRequestClient(e,t){c(this,G)[G].set(e,t)}getRequestClientForFile(e){if(!e.remote)throw new Error(`Tried to get RequestClient for a non-remote file ${e.id}`);const t=c(this,G)[G].get(e.remote.requestClientId);if(t==null)throw new Error(`requestClientId "${e.remote.requestClientId}" not registered for file "${e.id}"`);return t}restore(e){return this.log(`Core: attempting to restore upload "${e}"`),this.getState().currentUploads[e]?c(this,$)[$](e):(c(this,z)[z](e),Promise.reject(new Error("Nonexistent upload")))}[Le](){return c(this,O)[O](...arguments)}addResultData(e,t){if(!c(this,_e)[_e](e)){this.log(`Not setting result for an upload that has been removed: ${e}`);return}const{currentUploads:i}=this.getState(),r={...i[e],result:{...i[e].result,...t}};this.setState({currentUploads:{...i,[e]:r}})}upload(){var e;(e=c(this,b)[b].uploader)!=null&&e.length||this.log("No uploader type plugins are used","warning");let{files:t}=this.getState();const i=this.opts.onBeforeUpload(t);return i===!1?Promise.reject(new Error("Not starting the upload because onBeforeUpload returned false")):(i&&typeof i=="object"&&(t=i,this.setState({files:t})),Promise.resolve().then(()=>c(this,F)[F].validateMinNumberOfFiles(t)).catch(r=>{throw c(this,P)[P]([r]),r}).then(()=>{if(!c(this,fe)[fe](t))throw new U(this.i18n("missingRequiredMetaField"))}).catch(r=>{throw r}).then(()=>{const{currentUploads:r}=this.getState(),o=Object.values(r).flatMap(u=>u.fileIDs),n=[];Object.keys(t).forEach(u=>{const d=this.getFile(u);!d.progress.uploadStarted&&o.indexOf(u)===-1&&n.push(d.id)});const a=c(this,O)[O](n);return c(this,$)[$](a)}).catch(r=>{throw this.emit("error",r),this.log(r,"error"),r}))}}function bt(s){for(const o of s)o.isRestriction?this.emit("restriction-failed",o.file,o):this.emit("error",o,o.file),this.log(o,"warning");const e=s.filter(o=>o.isUserFacing),t=4,i=e.slice(0,t),r=e.slice(t);i.forEach(o=>{let{message:n,details:a=""}=o;this.info({message:n,details:a},"error",this.opts.infoTimeout)}),r.length>0&&this.info({message:this.i18n("additionalRestrictionsFailed",{count:r.length})})}function St(s){const{missingFields:e,error:t}=c(this,F)[F].getMissingRequiredMetaFields(s);return e.length>0?(this.setFileState(s.id,{missingRequiredMetaFields:e}),this.log(t.message),this.emit("restriction-failed",s,t),!1):!0}function Ft(s){let e=!0;for(const t of Object.values(s))c(this,X)[X](t)||(e=!1);return e}function Pt(s){const{allowNewUpload:e}=this.getState();if(e===!1){const t=new U(this.i18n("noMoreFilesAllowed"),{file:s});throw c(this,P)[P]([t]),t}}function xt(s){const e=s instanceof File?{name:s.name,type:s.type,size:s.size,data:s}:s,t=Ne(e),i=lt(t,e),r=je(i).extension,o=ot(e),n=e.meta||{};n.name=i,n.type=t;const a=Number.isFinite(e.data.size)?e.data.size:null;return{source:e.source||"",id:o,name:i,extension:r||"",meta:{...this.getState().meta,...n},type:t,data:e.data,progress:{percentage:0,bytesUploaded:0,bytesTotal:a,uploadComplete:!1,uploadStarted:null},size:a,isGhost:!1,isRemote:e.isRemote||!1,remote:e.remote||"",preview:e.preview}}function Ut(){this.opts.autoProceed&&!this.scheduledAutoProceed&&(this.scheduledAutoProceed=setTimeout(()=>{this.scheduledAutoProceed=null,this.upload().catch(s=>{s.isRestriction||this.log(s.stack||s.message||s)})},4))}function Et(s){const{files:e}=this.getState(),t={...e},i=[],r=[];for(const n of s)try{var o;let a=c(this,me)[me](n);const u=(o=e[a.id])==null?void 0:o.isGhost;u&&(a={...e[a.id],isGhost:!1,data:n.data},this.log(`Replaced the blob in the restored ghost file: ${a.name}, ${a.id}`));const d=this.opts.onBeforeFileAdded(a,t);if(!d&&this.checkIfFileAlreadyExists(a.id))throw new U(this.i18n("noDuplicates",{fileName:a.name}),{file:n});if(d===!1&&!u)throw new U("Cannot add the file because onBeforeFileAdded returned false.",{isUserFacing:!1,file:n});typeof d=="object"&&d!==null&&(a=d),c(this,F)[F].validateSingleFile(a),t[a.id]=a,i.push(a)}catch(a){r.push(a)}try{c(this,F)[F].validateAggregateRestrictions(Object.values(e),i)}catch(n){return r.push(n),{nextFilesState:e,validFilesToAdd:[],errors:r}}return{nextFilesState:t,validFilesToAdd:i,errors:r}}function Tt(){const s=(i,r,o)=>{let n=i.message||"Unknown error";i.details&&(n+=` ${i.details}`),this.setState({error:n}),r!=null&&r.id in this.getState().files&&this.setFileState(r.id,{error:n,response:o})};this.on("error",s),this.on("upload-error",(i,r,o)=>{if(s(r,i,o),typeof r=="object"&&r.message){var n;this.log(r.message,"error");const a=new Error(this.i18n("failedToUpload",{file:(n=i==null?void 0:i.name)!=null?n:""}));a.isUserFacing=!0,a.details=r.message,r.details&&(a.details+=` ${r.details}`),c(this,P)[P]([a])}else c(this,P)[P]([r])});let e=null;this.on("upload-stalled",(i,r)=>{const{message:o}=i,n=r.map(a=>a.meta.name).join(", ");e||(this.info({message:o,details:n},"warning",this.opts.infoTimeout),e=setTimeout(()=>{e=null},this.opts.infoTimeout)),this.log(`${o} ${n}`.trim(),"warning")}),this.on("upload",()=>{this.setState({error:null})});const t=i=>{const r=i.filter(n=>{const a=n!=null&&this.getFile(n.id);return a||this.log(`Not setting progress for a file that has been removed: ${n==null?void 0:n.id}`),a}),o=Object.fromEntries(r.map(n=>[n.id,{progress:{uploadStarted:Date.now(),uploadComplete:!1,percentage:0,bytesUploaded:0,bytesTotal:n.size}}]));this.patchFilesState(o)};this.on("upload-start",i=>{i.forEach(r=>{this.emit("upload-started",r)}),t(i)}),this.on("upload-progress",this.calculateProgress),this.on("upload-success",(i,r)=>{if(i==null||!this.getFile(i.id)){this.log(`Not setting progress for a file that has been removed: ${i==null?void 0:i.id}`);return}const o=this.getFile(i.id).progress;this.setFileState(i.id,{progress:{...o,postprocess:c(this,A)[A].size>0?{mode:"indeterminate"}:void 0,uploadComplete:!0,percentage:100,bytesUploaded:o.bytesTotal},response:r,uploadURL:r.uploadURL,isPaused:!1}),i.size==null&&this.setFileState(i.id,{size:r.bytesUploaded||o.bytesTotal}),this.calculateTotalProgress()}),this.on("preprocess-progress",(i,r)=>{if(i==null||!this.getFile(i.id)){this.log(`Not setting progress for a file that has been removed: ${i==null?void 0:i.id}`);return}this.setFileState(i.id,{progress:{...this.getFile(i.id).progress,preprocess:r}})}),this.on("preprocess-complete",i=>{if(i==null||!this.getFile(i.id)){this.log(`Not setting progress for a file that has been removed: ${i==null?void 0:i.id}`);return}const r={...this.getState().files};r[i.id]={...r[i.id],progress:{...r[i.id].progress}},delete r[i.id].progress.preprocess,this.setState({files:r})}),this.on("postprocess-progress",(i,r)=>{if(i==null||!this.getFile(i.id)){this.log(`Not setting progress for a file that has been removed: ${i==null?void 0:i.id}`);return}this.setFileState(i.id,{progress:{...this.getState().files[i.id].progress,postprocess:r}})}),this.on("postprocess-complete",i=>{if(i==null||!this.getFile(i.id)){this.log(`Not setting progress for a file that has been removed: ${i==null?void 0:i.id}`);return}const r={...this.getState().files};r[i.id]={...r[i.id],progress:{...r[i.id].progress}},delete r[i.id].progress.postprocess,this.setState({files:r})}),this.on("restored",()=>{this.calculateTotalProgress()}),this.on("dashboard:file-edit-complete",i=>{i&&c(this,X)[X](i)}),typeof window<"u"&&window.addEventListener&&(window.addEventListener("online",c(this,E)[E]),window.addEventListener("offline",c(this,E)[E]),setTimeout(c(this,E)[E],3e3))}function Ot(s,e){e===void 0&&(e={});const{forceAllowNewUpload:t=!1}=e,{allowNewUpload:i,currentUploads:r}=this.getState();if(!i&&!t)throw new Error("Cannot create a new upload: already uploading.");const o=Je();return this.emit("upload",{id:o,fileIDs:s}),this.setState({allowNewUpload:this.opts.allowMultipleUploadBatches!==!1&&this.opts.allowMultipleUploads!==!1,currentUploads:{...r,[o]:{fileIDs:s,step:0,result:{}}}}),o}function $t(s){const{currentUploads:e}=this.getState();return e[s]}function At(s){const e={...this.getState().currentUploads};delete e[s],this.setState({currentUploads:e})}async function kt(s){const e=()=>{const{currentUploads:o}=this.getState();return o[s]};let t=e();const i=[...c(this,j)[j],...c(this,N)[N],...c(this,A)[A]];try{for(let o=t.step||0;o<i.length&&t;o++){const n=i[o];this.setState({currentUploads:{...this.getState().currentUploads,[s]:{...t,step:o}}});const{fileIDs:a}=t;await n(a,s),t=e()}}catch(o){throw c(this,z)[z](s),o}if(t){t.fileIDs.forEach(u=>{const d=this.getFile(u);d&&d.progress.postprocess&&this.emit("postprocess-complete",d)});const o=t.fileIDs.map(u=>this.getFile(u)),n=o.filter(u=>!u.error),a=o.filter(u=>u.error);this.addResultData(s,{successful:n,failed:a,uploadID:s}),t=e()}let r;return t&&(r=t.result,this.emit("complete",r),c(this,z)[z](s)),r==null&&this.log(`Not setting result for an upload that has been removed: ${s}`),r}le.VERSION=wt.version;const Xt=le;var K,_,Be,R,xe,Ie,ve,D={},qe=[],Mt=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,ae=Array.isArray;function k(s,e){for(var t in e)s[t]=e[t];return s}function He(s){var e=s.parentNode;e&&e.removeChild(s)}function Ct(s,e,t){var i,r,o,n={};for(o in e)o=="key"?i=e[o]:o=="ref"?r=e[o]:n[o]=e[o];if(arguments.length>2&&(n.children=arguments.length>3?K.call(arguments,2):t),typeof s=="function"&&s.defaultProps!=null)for(o in s.defaultProps)n[o]===void 0&&(n[o]=s.defaultProps[o]);return Y(s,n,i,r,null)}function Y(s,e,t,i,r){var o={type:s,props:e,key:t,ref:i,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:r??++Be,__i:-1,__u:0};return r==null&&_.vnode!=null&&_.vnode(o),o}function Dt(){return{current:null}}function de(s){return s.children}function re(s,e){this.props=s,this.context=e}function L(s,e){if(e==null)return s.__?L(s.__,s.__i+1):null;for(var t;e<s.__k.length;e++)if((t=s.__k[e])!=null&&t.__e!=null)return t.__e;return typeof s.type=="function"?L(s):null}function We(s){var e,t;if((s=s.__)!=null&&s.__c!=null){for(s.__e=s.__c.base=null,e=0;e<s.__k.length;e++)if((t=s.__k[e])!=null&&t.__e!=null){s.__e=s.__c.base=t.__e;break}return We(s)}}function Ue(s){(!s.__d&&(s.__d=!0)&&R.push(s)&&!oe.__r++||xe!==_.debounceRendering)&&((xe=_.debounceRendering)||Ie)(oe)}function oe(){var s,e,t,i,r,o,n,a,u;for(R.sort(ve);s=R.shift();)s.__d&&(e=R.length,i=void 0,o=(r=(t=s).__v).__e,a=[],u=[],(n=t.__P)&&((i=k({},r)).__v=r.__v+1,_.vnode&&_.vnode(i),we(n,i,r,t.__n,n.ownerSVGElement!==void 0,32&r.__u?[o]:null,a,o??L(r),!!(32&r.__u),u),i.__v=r.__v,i.__.__k[i.__i]=i,Ye(a,i,u),i.__e!=o&&We(i)),R.length>e&&R.sort(ve));oe.__r=0}function Ge(s,e,t,i,r,o,n,a,u,d,h){var l,g,p,f,S,y=i&&i.__k||qe,m=e.length;for(t.__d=u,jt(t,e,y),u=t.__d,l=0;l<m;l++)(p=t.__k[l])!=null&&typeof p!="boolean"&&typeof p!="function"&&(g=p.__i===-1?D:y[p.__i]||D,p.__i=l,we(s,p,g,r,o,n,a,u,d,h),f=p.__e,p.ref&&g.ref!=p.ref&&(g.ref&&be(g.ref,null,p),h.push(p.ref,p.__c||f,p)),S==null&&f!=null&&(S=f),65536&p.__u||g.__k===p.__k?(f||g.__e!=u||(u=L(g)),u=Ve(p,u,s)):typeof p.type=="function"&&p.__d!==void 0?u=p.__d:f&&(u=f.nextSibling),p.__d=void 0,p.__u&=-196609);t.__d=u,t.__e=S}function jt(s,e,t){var i,r,o,n,a,u=e.length,d=t.length,h=d,l=0;for(s.__k=[],i=0;i<u;i++)n=i+l,(r=s.__k[i]=(r=e[i])==null||typeof r=="boolean"||typeof r=="function"?null:typeof r=="string"||typeof r=="number"||typeof r=="bigint"||r.constructor==String?Y(null,r,null,null,null):ae(r)?Y(de,{children:r},null,null,null):r.constructor===void 0&&r.__b>0?Y(r.type,r.props,r.key,r.ref?r.ref:null,r.__v):r)!=null?(r.__=s,r.__b=s.__b+1,a=Rt(r,t,n,h),r.__i=a,o=null,a!==-1&&(h--,(o=t[a])&&(o.__u|=131072)),o==null||o.__v===null?(a==-1&&l--,typeof r.type!="function"&&(r.__u|=65536)):a!==n&&(a===n+1?l++:a>n?h>u-n?l+=a-n:l--:a<n?a==n-1&&(l=a-n):l=0,a!==i+l&&(r.__u|=65536))):(o=t[n])&&o.key==null&&o.__e&&!(131072&o.__u)&&(o.__e==s.__d&&(s.__d=L(o)),ye(o,o,!1),t[n]=null,h--);if(h)for(i=0;i<d;i++)(o=t[i])!=null&&!(131072&o.__u)&&(o.__e==s.__d&&(s.__d=L(o)),ye(o,o))}function Ve(s,e,t){var i,r;if(typeof s.type=="function"){for(i=s.__k,r=0;i&&r<i.length;r++)i[r]&&(i[r].__=s,e=Ve(i[r],e,t));return e}s.__e!=e&&(t.insertBefore(s.__e,e||null),e=s.__e);do e=e&&e.nextSibling;while(e!=null&&e.nodeType===8);return e}function Nt(s,e){return e=e||[],s==null||typeof s=="boolean"||(ae(s)?s.some(function(t){Nt(t,e)}):e.push(s)),e}function Rt(s,e,t,i){var r=s.key,o=s.type,n=t-1,a=t+1,u=e[t];if(u===null||u&&r==u.key&&o===u.type&&!(131072&u.__u))return t;if(i>(u!=null&&!(131072&u.__u)?1:0))for(;n>=0||a<e.length;){if(n>=0){if((u=e[n])&&!(131072&u.__u)&&r==u.key&&o===u.type)return n;n--}if(a<e.length){if((u=e[a])&&!(131072&u.__u)&&r==u.key&&o===u.type)return a;a++}}return-1}function Ee(s,e,t){e[0]==="-"?s.setProperty(e,t??""):s[e]=t==null?"":typeof t!="number"||Mt.test(e)?t:t+"px"}function te(s,e,t,i,r){var o;e:if(e==="style")if(typeof t=="string")s.style.cssText=t;else{if(typeof i=="string"&&(s.style.cssText=i=""),i)for(e in i)t&&e in t||Ee(s.style,e,"");if(t)for(e in t)i&&t[e]===i[e]||Ee(s.style,e,t[e])}else if(e[0]==="o"&&e[1]==="n")o=e!==(e=e.replace(/(PointerCapture)$|Capture$/i,"$1")),e=e.toLowerCase()in s||e==="onFocusOut"||e==="onFocusIn"?e.toLowerCase().slice(2):e.slice(2),s.l||(s.l={}),s.l[e+o]=t,t?i?t.u=i.u:(t.u=Date.now(),s.addEventListener(e,o?Oe:Te,o)):s.removeEventListener(e,o?Oe:Te,o);else{if(r)e=e.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(e!=="width"&&e!=="height"&&e!=="href"&&e!=="list"&&e!=="form"&&e!=="tabIndex"&&e!=="download"&&e!=="rowSpan"&&e!=="colSpan"&&e!=="role"&&e in s)try{s[e]=t??"";break e}catch{}typeof t=="function"||(t==null||t===!1&&e[4]!=="-"?s.removeAttribute(e):s.setAttribute(e,t))}}function Te(s){if(this.l){var e=this.l[s.type+!1];if(s.t){if(s.t<=e.u)return}else s.t=Date.now();return e(_.event?_.event(s):s)}}function Oe(s){if(this.l)return this.l[s.type+!0](_.event?_.event(s):s)}function we(s,e,t,i,r,o,n,a,u,d){var h,l,g,p,f,S,y,m,w,M,J,B,Se,Z,ue,x=e.type;if(e.constructor!==void 0)return null;128&t.__u&&(u=!!(32&t.__u),o=[a=e.__e=t.__e]),(h=_.__b)&&h(e);e:if(typeof x=="function")try{if(m=e.props,w=(h=x.contextType)&&i[h.__c],M=h?w?w.props.value:h.__:i,t.__c?y=(l=e.__c=t.__c).__=l.__E:("prototype"in x&&x.prototype.render?e.__c=l=new x(m,M):(e.__c=l=new re(m,M),l.constructor=x,l.render=Lt),w&&w.sub(l),l.props=m,l.state||(l.state={}),l.context=M,l.__n=i,g=l.__d=!0,l.__h=[],l._sb=[]),l.__s==null&&(l.__s=l.state),x.getDerivedStateFromProps!=null&&(l.__s==l.state&&(l.__s=k({},l.__s)),k(l.__s,x.getDerivedStateFromProps(m,l.__s))),p=l.props,f=l.state,l.__v=e,g)x.getDerivedStateFromProps==null&&l.componentWillMount!=null&&l.componentWillMount(),l.componentDidMount!=null&&l.__h.push(l.componentDidMount);else{if(x.getDerivedStateFromProps==null&&m!==p&&l.componentWillReceiveProps!=null&&l.componentWillReceiveProps(m,M),!l.__e&&(l.shouldComponentUpdate!=null&&l.shouldComponentUpdate(m,l.__s,M)===!1||e.__v===t.__v)){for(e.__v!==t.__v&&(l.props=m,l.state=l.__s,l.__d=!1),e.__e=t.__e,e.__k=t.__k,e.__k.forEach(function(Q){Q&&(Q.__=e)}),J=0;J<l._sb.length;J++)l.__h.push(l._sb[J]);l._sb=[],l.__h.length&&n.push(l);break e}l.componentWillUpdate!=null&&l.componentWillUpdate(m,l.__s,M),l.componentDidUpdate!=null&&l.__h.push(function(){l.componentDidUpdate(p,f,S)})}if(l.context=M,l.props=m,l.__P=s,l.__e=!1,B=_.__r,Se=0,"prototype"in x&&x.prototype.render){for(l.state=l.__s,l.__d=!1,B&&B(e),h=l.render(l.props,l.state,l.context),Z=0;Z<l._sb.length;Z++)l.__h.push(l._sb[Z]);l._sb=[]}else do l.__d=!1,B&&B(e),h=l.render(l.props,l.state,l.context),l.state=l.__s;while(l.__d&&++Se<25);l.state=l.__s,l.getChildContext!=null&&(i=k(k({},i),l.getChildContext())),g||l.getSnapshotBeforeUpdate==null||(S=l.getSnapshotBeforeUpdate(p,f)),Ge(s,ae(ue=h!=null&&h.type===de&&h.key==null?h.props.children:h)?ue:[ue],e,t,i,r,o,n,a,u,d),l.base=e.__e,e.__u&=-161,l.__h.length&&n.push(l),y&&(l.__E=l.__=null)}catch(Q){e.__v=null,u||o!=null?(e.__e=a,e.__u|=u?160:32,o[o.indexOf(a)]=null):(e.__e=t.__e,e.__k=t.__k),_.__e(Q,e,t)}else o==null&&e.__v===t.__v?(e.__k=t.__k,e.__e=t.__e):e.__e=zt(t.__e,e,t,i,r,o,n,u,d);(h=_.diffed)&&h(e)}function Ye(s,e,t){e.__d=void 0;for(var i=0;i<t.length;i++)be(t[i],t[++i],t[++i]);_.__c&&_.__c(e,s),s.some(function(r){try{s=r.__h,r.__h=[],s.some(function(o){o.call(r)})}catch(o){_.__e(o,r.__v)}})}function zt(s,e,t,i,r,o,n,a,u){var d,h,l,g,p,f,S,y=t.props,m=e.props,w=e.type;if(w==="svg"&&(r=!0),o!=null){for(d=0;d<o.length;d++)if((p=o[d])&&"setAttribute"in p==!!w&&(w?p.localName===w:p.nodeType===3)){s=p,o[d]=null;break}}if(s==null){if(w===null)return document.createTextNode(m);s=r?document.createElementNS("http://www.w3.org/2000/svg",w):document.createElement(w,m.is&&m),o=null,a=!1}if(w===null)y===m||a&&s.data===m||(s.data=m);else{if(o=o&&K.call(s.childNodes),y=t.props||D,!a&&o!=null)for(y={},d=0;d<s.attributes.length;d++)y[(p=s.attributes[d]).name]=p.value;for(d in y)p=y[d],d=="children"||(d=="dangerouslySetInnerHTML"?l=p:d==="key"||d in m||te(s,d,null,p,r));for(d in m)p=m[d],d=="children"?g=p:d=="dangerouslySetInnerHTML"?h=p:d=="value"?f=p:d=="checked"?S=p:d==="key"||a&&typeof p!="function"||y[d]===p||te(s,d,p,y[d],r);if(h)a||l&&(h.__html===l.__html||h.__html===s.innerHTML)||(s.innerHTML=h.__html),e.__k=[];else if(l&&(s.innerHTML=""),Ge(s,ae(g)?g:[g],e,t,i,r&&w!=="foreignObject",o,n,o?o[0]:t.__k&&L(t,0),a,u),o!=null)for(d=o.length;d--;)o[d]!=null&&He(o[d]);a||(d="value",f!==void 0&&(f!==s[d]||w==="progress"&&!f||w==="option"&&f!==y[d])&&te(s,d,f,y[d],!1),d="checked",S!==void 0&&S!==s[d]&&te(s,d,S,y[d],!1))}return s}function be(s,e,t){try{typeof s=="function"?s(e):s.current=e}catch(i){_.__e(i,t)}}function ye(s,e,t){var i,r;if(_.unmount&&_.unmount(s),(i=s.ref)&&(i.current&&i.current!==s.__e||be(i,null,e)),(i=s.__c)!=null){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(o){_.__e(o,e)}i.base=i.__P=null,s.__c=void 0}if(i=s.__k)for(r=0;r<i.length;r++)i[r]&&ye(i[r],e,t||typeof s.type!="function");t||s.__e==null||He(s.__e),s.__=s.__e=s.__d=void 0}function Lt(s,e,t){return this.constructor(s,t)}function $e(s,e,t){var i,r,o,n;_.__&&_.__(s,e),r=(i=typeof t=="function")?null:t&&t.__k||e.__k,o=[],n=[],we(e,s=(!i&&t||e).__k=Ct(de,null,[s]),r||D,D,e.ownerSVGElement!==void 0,!i&&t?[t]:r?null:e.firstChild?K.call(e.childNodes):null,o,!i&&t?t:r?r.__e:e.firstChild,i,n),Ye(o,s,n)}function Kt(s,e,t){var i,r,o,n,a=k({},s.props);for(o in s.type&&s.type.defaultProps&&(n=s.type.defaultProps),e)o=="key"?i=e[o]:o=="ref"?r=e[o]:a[o]=e[o]===void 0&&n!==void 0?n[o]:e[o];return arguments.length>2&&(a.children=arguments.length>3?K.call(arguments,2):t),Y(s.type,a,i||s.key,r||s.ref,null)}K=qe.slice,_={__e:function(s,e,t,i){for(var r,o,n;e=e.__;)if((r=e.__c)&&!r.__)try{if((o=r.constructor)&&o.getDerivedStateFromError!=null&&(r.setState(o.getDerivedStateFromError(s)),n=r.__d),r.componentDidCatch!=null&&(r.componentDidCatch(s,i||{}),n=r.__d),n)return r.__E=r}catch(a){s=a}throw s}},Be=0,re.prototype.setState=function(s,e){var t;t=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=k({},this.state),typeof s=="function"&&(s=s(k({},t),this.props)),s&&k(t,s),s!=null&&this.__v&&(e&&this._sb.push(e),Ue(this))},re.prototype.forceUpdate=function(s){this.__v&&(this.__e=!0,s&&this.__h.push(s),Ue(this))},re.prototype.render=de,R=[],Ie=typeof Promise=="function"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,ve=function(s,e){return s.__v.__b-e.__v.__b},oe.__r=0;function Bt(s){return typeof s!="object"||s===null||!("nodeType"in s)?!1:s.nodeType===Node.ELEMENT_NODE}function It(s,e){return e===void 0&&(e=document),typeof s=="string"?e.querySelector(s):Bt(s)?s:null}function qt(s){for(var e;s&&!s.dir;)s=s.parentNode;return(e=s)==null?void 0:e.dir}function Ae(s,e){if(!Object.prototype.hasOwnProperty.call(s,e))throw new TypeError("attempted to use private field on non-instance");return s}var Ht=0;function Wt(s){return"__private_"+Ht+++"_"+s}function Gt(s){let e=null,t;return function(){for(var i=arguments.length,r=new Array(i),o=0;o<i;o++)r[o]=arguments[o];return t=r,e||(e=Promise.resolve().then(()=>(e=null,s(...t)))),e}}var V=Wt("updateUI");class ne extends Ze{constructor(){super(...arguments),Object.defineProperty(this,V,{writable:!0,value:void 0})}getTargetPlugin(e){let t;if(typeof(e==null?void 0:e.addTarget)=="function")t=e,t instanceof ne||console.warn(new Error("The provided plugin is not an instance of UIPlugin. This is an indication of a bug with the way Uppy is bundled.",{cause:{targetPlugin:t,UIPlugin:ne}}));else if(typeof e=="function"){const i=e;this.uppy.iteratePlugins(r=>{r instanceof i&&(t=r)})}return t}mount(e,t){const i=t.id,r=It(e);if(r){this.isTargetDOMEl=!0;const a=document.createElement("div");return a.classList.add("uppy-Root"),Ae(this,V)[V]=Gt(u=>{this.uppy.getPlugin(this.id)&&($e(this.render(u),a),this.afterUpdate())}),this.uppy.log(`Installing ${i} to a DOM element '${e}'`),this.opts.replaceTargetContent&&(r.innerHTML=""),$e(this.render(this.uppy.getState()),a),this.el=a,r.appendChild(a),a.dir=this.opts.direction||qt(a)||"ltr",this.onMount(),this.el}const o=this.getTargetPlugin(e);if(o)return this.uppy.log(`Installing ${i} to ${o.id}`),this.parent=o,this.el=o.addTarget(t),this.onMount(),this.el;this.uppy.log(`Not installing ${i}`);let n=`Invalid target option given to ${i}.`;throw typeof e=="function"?n+=" The given target is not a Plugin class. Please check that you're not specifying a React Component instead of a plugin. If you are using @uppy/* packages directly, make sure you have only 1 version of @uppy/core installed: run `npm ls @uppy/core` on the command line and verify that all the versions match and are deduped correctly.":n+="If you meant to target an HTML element, please make sure that the element exists. Check that the <script> tag initializing Uppy is right before the closing </body> tag at the end of the page. (see https://github.com/transloadit/uppy/issues/1042)\n\nIf you meant to target a plugin, please confirm that your `import` statements or `require` calls are correct.",new Error(n)}render(e){throw new Error("Extend the render method to add your plugin to a DOM element")}update(e){if(this.el!=null){var t,i;(t=(i=Ae(this,V))[V])==null||t.call(i,e)}}unmount(){if(this.isTargetDOMEl){var e;(e=this.el)==null||e.remove()}this.onUnmount()}onMount(){}onUnmount(){}}const Jt=ne;export{Nt as $,Kt as B,Jt as U,Dt as _,de as a,re as b,Xt as c,qt as g,Bt as i,_ as l,he as p,Ct as y};
