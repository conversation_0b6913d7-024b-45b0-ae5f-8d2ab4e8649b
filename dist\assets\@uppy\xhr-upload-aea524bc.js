import{B as P,i as v,f as D,a as N,R as H,P as M,n as q,N as O,b as L}from"./aws-s3-9d3fc29b.js";import{E as X}from"./tus-2ad25a40.js";const _={strings:{uploadStalled:"Upload has not made any progress for %{seconds} seconds. You may want to retry it."}};function y(e,o){if(!Object.prototype.hasOwnProperty.call(e,o))throw new TypeError("attempted to use private field on non-instance");return e}var j=0;function f(e){return"__private_"+j+++"_"+e}const A={version:"3.6.4"};function S(e,o){let s=o;return s||(s=new Error("Upload error")),typeof s=="string"&&(s=new Error(s)),s instanceof Error||(s=Object.assign(new Error("Upload error"),{data:s})),L(e)?(s=new O(s,e),s):(s.request=e,s)}function C(e){return e.data.slice(0,e.data.size,e.meta.type)}const $={formData:!0,fieldName:"file",method:"post",allowedMetaFields:null,responseUrlFieldName:"url",bundle:!1,headers:{},timeout:30*1e3,limit:5,withCredentials:!1,responseType:"",getResponseData(e){let o={};try{o=JSON.parse(e)}catch{}return o},getResponseError(e,o){let s=new Error("Upload error");return L(o)&&(s=new O(s,o)),s},validateStatus(e){return e>=200&&e<300}};var R=f("uploadLocalFile"),F=f("uploadBundle"),T=f("getCompanionClientArgs"),U=f("uploadFiles"),g=f("handleUpload");class B extends P{constructor(o,s){if(super(o,{...$,fieldName:s.bundle?"files[]":"file",...s}),Object.defineProperty(this,U,{value:I}),Object.defineProperty(this,T,{value:W}),Object.defineProperty(this,F,{value:z}),Object.defineProperty(this,R,{value:k}),Object.defineProperty(this,g,{writable:!0,value:async r=>{if(r.length===0){this.uppy.log("[XHRUpload] No files to upload!");return}this.opts.limit===0&&!this.opts[v]&&this.uppy.log("[XHRUpload] When uploading multiple files at once, consider setting the `limit` option (to `10` for example), to limit the number of concurrent uploads, which helps prevent memory and network issues: https://uppy.io/docs/xhr-upload/#limit-0","warning"),this.uppy.log("[XHRUpload] Uploading...");const i=this.uppy.getFilesByIds(r),p=D(i),d=N(p);if(this.uppy.emit("upload-start",d),this.opts.bundle){if(p.some(t=>t.isRemote))throw new Error("Can’t upload remote files when the `bundle: true` option is set");if(typeof this.opts.headers=="function")throw new TypeError("`headers` may not be a function when the `bundle: true` option is set");await y(this,F)[F](p)}else await y(this,U)[U](p)}}),this.type="uploader",this.id=this.opts.id||"XHRUpload",this.defaultLocale=_,this.i18nInit(),v in this.opts?this.requests=this.opts[v]:this.requests=new H(this.opts.limit),this.opts.bundle&&!this.opts.formData)throw new Error("`opts.formData` must be true when `opts.bundle` is enabled.");if(this.opts.bundle&&typeof this.opts.headers=="function")throw new Error("`opts.headers` can not be a function when the `bundle: true` option is set.");if((s==null?void 0:s.allowedMetaFields)===void 0&&"metaFields"in this.opts)throw new Error("The `metaFields` option has been renamed to `allowedMetaFields`.");this.uploaderEvents=Object.create(null)}getOptions(o){const s=this.uppy.getState().xhrUpload,{headers:r}=this.opts,i={...this.opts,...s||{},...o.xhrUpload||{},headers:{}};return typeof r=="function"?i.headers=r(o):Object.assign(i.headers,this.opts.headers),s&&Object.assign(i.headers,s.headers),o.xhrUpload&&Object.assign(i.headers,o.xhrUpload.headers),i}addMetadata(o,s,r){(Array.isArray(r.allowedMetaFields)?r.allowedMetaFields:Object.keys(s)).forEach(p=>{const d=s[p];Array.isArray(d)?d.forEach(l=>o.append(p,l)):o.append(p,d)})}createFormDataUpload(o,s){const r=new FormData;this.addMetadata(r,o.meta,s);const i=C(o);return o.name?r.append(s.fieldName,i,o.meta.name):r.append(s.fieldName,i),r}createBundledUpload(o,s){const r=new FormData,{meta:i}=this.uppy.getState();return this.addMetadata(r,i,s),o.forEach(p=>{const d=this.getOptions(p),l=C(p);p.name?r.append(d.fieldName,l,p.name):r.append(d.fieldName,l)}),r}install(){if(this.opts.bundle){const{capabilities:o}=this.uppy.getState();this.uppy.setState({capabilities:{...o,individualCancellation:!1}})}this.uppy.addUploader(y(this,g)[g])}uninstall(){if(this.opts.bundle){const{capabilities:o}=this.uppy.getState();this.uppy.setState({capabilities:{...o,individualCancellation:!0}})}this.uppy.removeUploader(y(this,g)[g])}}async function k(e,o,s){const r=this.getOptions(e),i=Date.now();return this.uppy.log(`uploading ${o} of ${s}`),new Promise((p,d)=>{const l=r.formData?this.createFormDataUpload(e,r):e.data,t=new XMLHttpRequest,c=new X(this.uppy);this.uploaderEvents[e.id]=c;let u;const m=new M(r.timeout,()=>{const a=new Error(this.i18n("uploadStalled",{seconds:Math.ceil(r.timeout/1e3)}));this.uppy.emit("upload-stalled",a,[e])}),n=q();t.upload.addEventListener("loadstart",()=>{this.uppy.log(`[XHRUpload] ${n} started`)}),t.upload.addEventListener("progress",a=>{this.uppy.log(`[XHRUpload] ${n} progress: ${a.loaded} / ${a.total}`),m.progress(),a.lengthComputable&&this.uppy.emit("upload-progress",this.uppy.getFile(e.id),{uploader:this,uploadStarted:i,bytesUploaded:a.loaded,bytesTotal:a.total})}),t.addEventListener("load",()=>{if(this.uppy.log(`[XHRUpload] ${n} finished`),m.done(),u.done(),this.uploaderEvents[e.id]&&(this.uploaderEvents[e.id].remove(),this.uploaderEvents[e.id]=null),r.validateStatus(t.status,t.responseText,t)){const b=r.getResponseData(t.responseText,t),E=b==null?void 0:b[r.responseUrlFieldName],x={status:t.status,body:b,uploadURL:E};return this.uppy.emit("upload-success",this.uppy.getFile(e.id),x),E&&this.uppy.log(`Download ${e.name} from ${E}`),p(e)}const a=r.getResponseData(t.responseText,t),h=S(t,r.getResponseError(t.responseText,t)),w={status:t.status,body:a};return this.uppy.emit("upload-error",e,h,w),d(h)}),t.addEventListener("error",()=>{this.uppy.log(`[XHRUpload] ${n} errored`),m.done(),u.done(),this.uploaderEvents[e.id]&&(this.uploaderEvents[e.id].remove(),this.uploaderEvents[e.id]=null);const a=S(t,r.getResponseError(t.responseText,t));return this.uppy.emit("upload-error",e,a),d(a)}),t.open(r.method.toUpperCase(),r.endpoint,!0),t.withCredentials=r.withCredentials,r.responseType!==""&&(t.responseType=r.responseType),u=this.requests.run(()=>{const a=this.getOptions(e);return Object.keys(a.headers).forEach(h=>{t.setRequestHeader(h,a.headers[h])}),t.send(l),()=>{m.done(),t.abort()}}),c.onFileRemove(e.id,()=>{u.abort(),d(new Error("File removed"))}),c.onCancelAll(e.id,a=>{let{reason:h}=a;h==="user"&&u.abort(),d(new Error("Upload cancelled"))})})}function z(e){return new Promise((o,s)=>{const{endpoint:r}=this.opts,{method:i}=this.opts,p=Date.now(),d=this.uppy.getState().xhrUpload,l=this.createBundledUpload(e,{...this.opts,...d||{}}),t=new XMLHttpRequest,c=n=>{e.forEach(a=>{this.uppy.emit("upload-error",a,n)})},u=new M(this.opts.timeout,()=>{const n=new Error(this.i18n("uploadStalled",{seconds:Math.ceil(this.opts.timeout/1e3)}));this.uppy.emit("upload-stalled",n,e)});t.upload.addEventListener("loadstart",()=>{this.uppy.log("[XHRUpload] started uploading bundle"),u.progress()}),t.upload.addEventListener("progress",n=>{u.progress(),n.lengthComputable&&e.forEach(a=>{this.uppy.emit("upload-progress",this.uppy.getFile(a.id),{uploader:this,uploadStarted:p,bytesUploaded:n.loaded/n.total*a.size,bytesTotal:a.size})})}),t.addEventListener("load",()=>{if(u.done(),this.opts.validateStatus(t.status,t.responseText,t)){const a=this.opts.getResponseData(t.responseText,t),h={status:t.status,body:a};return e.forEach(w=>{this.uppy.emit("upload-success",this.uppy.getFile(w.id),h)}),o()}const n=this.opts.getResponseError(t.responseText,t)||new O("Upload error",t);return c(n),s(n)}),t.addEventListener("error",()=>{u.done();const n=this.opts.getResponseError(t.responseText,t)||new Error("Upload error");return c(n),s(n)}),this.uppy.on("cancel-all",function(n){let{reason:a}=n===void 0?{}:n;a==="user"&&(u.done(),t.abort())}),t.open(i.toUpperCase(),r,!0),t.withCredentials=this.opts.withCredentials,this.opts.responseType!==""&&(t.responseType=this.opts.responseType);const m=this.opts.headers;Object.keys(m).forEach(n=>{t.setRequestHeader(n,m[n])}),t.send(l)})}function W(e){var o;const s=this.getOptions(e),r=Array.isArray(s.allowedMetaFields)?s.allowedMetaFields:Object.keys(e.meta);return{...(o=e.remote)==null?void 0:o.body,protocol:"multipart",endpoint:s.endpoint,size:e.data.size,fieldname:s.fieldName,metadata:Object.fromEntries(r.map(i=>[i,e.meta[i]])),httpMethod:s.method,useFormData:s.formData,headers:s.headers}}async function I(e){await Promise.allSettled(e.map((o,s)=>{const r=s+1,i=e.length;if(o.isRemote){const p=()=>this.requests,d=new AbortController,l=c=>{c.id===o.id&&d.abort()};this.uppy.on("file-removed",l);const t=this.uppy.getRequestClientForFile(o).uploadRemoteFile(o,y(this,T)[T](o),{signal:d.signal,getQueue:p});return this.requests.wrapSyncFunction(()=>{this.uppy.off("file-removed",l)},{priority:-1})(),t}return y(this,R)[R](o,r,i)}))}B.VERSION=A.version;export{B as X};
