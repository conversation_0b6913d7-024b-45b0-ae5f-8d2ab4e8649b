import React from 'react';

const ProgressBar = ({ percent, className }) => {

  return (
    <div className={`progress-bar ${className} `}
    // style={{ flexDirection: 'column-reverse' , display:"flex"}}
    >
      <div
        className="progress-bar-inner"
        style={{ width: `${percent}%` }}
      ></div>
      {/* <div className="progress-bar-label">{`${percent}%`}</div> */}
      <div className="progress-bar-label"> </div>
    </div>
  );
};

export default ProgressBar;