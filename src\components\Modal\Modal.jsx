import React, { memo, useEffect, useState } from "react";
import { CloseIcon } from "Assets/svgs";
import { CountdownCircleTimer } from "react-countdown-circle-timer";

const Modal = ({
  children,
  title,
  isOpen = false,
  modalCloseClick,
  modalHeader,
  classes,
  showCloseAfter,
}) => {
  const [showCloseIcon, setShowCloseIcon] = useState(false);

  //   useEffect(() => {
  //     const timeoutId = setTimeout(() => {
  //       setShowCloseIcon(prev=>!prev);
  //     }, showCloseAfter);

  //     return () => clearTimeout(timeoutId);
  //   }, []);

  const resetCloseIcon = () => {
    setShowCloseIcon(false);
  };

  if (isOpen) {
    const timeoutId = setTimeout(() => {
      setShowCloseIcon(true);
    }, showCloseAfter);
  } else {
    // setShowCloseIcon(false);
  }


  return (
    <div
      style={{
        zIndex: 100000002,
        transform: "translate(-50%, -50%)",
      }}
      className={`w-full  h-[100vh] fixed top-[50%] left-[50%] modal-holder bg-[#00000099] md:p-20 items-center justify-center   ${
        isOpen ? "flex" : "hidden"
      }`}
    >
      <div
        className={`shadow md:p-10 p-4 bg-white md:rounded-lg ${classes?.modalDialog} w-full  max-w-md `}
      >
        {modalHeader && (
          <>
            <div className={`flex justify-between  pb-2 `}>
              <h5 className={`font-normal text-gray-600 ${classes?.title} text-center text-lg  w-full`}>
                {title}
              </h5>
              {showCloseIcon ? (
                <div
                  className="modal-close cursor-pointer"
                  onClick={() => {
                    modalCloseClick();
                    resetCloseIcon();
                  }} 
                >
                  
                  <CloseIcon />
                </div>
              ) : (
                <CountdownCircleTimer
                  isPlaying
                  duration={!showCloseIcon? showCloseAfter/1000: 0}
                  colors="#f93434"
                  colorsTime={[7, 5, 2, 0]}
                  size={50}
                  strokeWidth={2}
                >
                  {({ remainingTime }) => remainingTime}
                </CountdownCircleTimer>
              )}
            </div>
          </>
        )}

        <div className="mt-4">{children}</div>
      </div>
    </div>
  );
};

const ModalMemo = memo(Modal);
export { ModalMemo as Modal };
