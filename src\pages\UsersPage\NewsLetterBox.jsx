import { InteractiveButton } from "Components/InteractiveButton";
import { AuthContext, tokenExpireError } from "Src/authContext";
import { GlobalContext, showToast } from "Src/globalContext";
import MkdSDK from "Utils/MkdSDK";
import React, { useEffect, useState } from "react";

let sdk = new MkdSDK();

const NewsLetterBox = ({ setIsNewsLetterOpen }) => {
  const [email, setEmail] = useState("");
  const [submitted, setSubmitted] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [discountCode, setDiscountCode] = useState("");
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setIsUploading(true);

      const result = await sdk.submitNewsLetter(email);
      if (!result.error) {
        // navigate("/admin/cms");
        showToast(globalDispatch, "Added");
        setIsUploading(false);
        localStorage.setItem('hasClosedNewsletter', 'true');
        setDiscountCode("WELCOME10");
        setSubmitted(true);
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
        else{
          showToast(globalDispatch, result?.message,3000,"error");
        }
        setIsUploading(false);
      }
    } catch (error) {
      console.log("Error", error);
      setError("page", {
        type: "manual",
        message: error.message,
      });
      tokenExpireError(dispatch, error.message);
      setIsUploading(false);
    }

    // setIsNewsLetterOpen(false);
  };

  return (
    <div className=" w-full md:max-w-lg max-w-md mx-auto mt-10 md:p-6 p-4 border rounded-lg  bg-white">
      {!submitted ? (
        <form onSubmit={handleSubmit} className="space-y-4">
          <h2 className=" text-xl md:text-2xl font-bold text-gray-800">
            Subscribe to our Newsletter
          </h2>
          <p className="text-gray-600">
            Get a 10% discount on your first purchase
          </p>
          <div className="md:flex md:flex-row md:items-end justify-between flex-col gap-4">
            <div className=" w-full">

            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-700"
            >
              Email address
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="md:mt-0 mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-companyRed focus:border-companyRed sm:text-sm"
            />
          </div>
          <InteractiveButton
            loading={isUploading}
            type="submit"
            className="md:w-[200px] md:mt-0 mt-2 w-full py-2 px-4 border border-transparent  shadow-sm text-sm font-medium text-white bg-companyRed hover:bg-companyRed  rounded-full"
          >
            Subscribe
          </InteractiveButton>
            </div>
        </form>
      ) : (
        <div className="text-center space-y-4">
          <h2 className="text-2xl font-bold text-gray-800">
            Thank you for subscribing!
          </h2>
          {/* <p className="text-gray-600">
          Enjoy a 10% discount on your first purchase
          </p> */}
          <>
          
          <div className="my-6 p-4 bg-gray-50 rounded-lg">
            <p className="text-gray-600 mb-2">Your discount code:</p>
            <p className="text-2xl font-bold text-companyRed">{discountCode}</p>
          </div>


          <p className="text-gray-600">
            {/* <span>

            You will receive an email with your discount code.
            </span> */}
            Use this code at checkout to get 10% off your first purchase.
          </p>
          {/* <InteractiveButton
            onClick={() => setIsNewsLetterOpen(false)}
            className="mt-4 w-full py-2 px-4 border border-transparent shadow-sm text-sm font-medium text-white bg-companyRed hover:bg-companyRed focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-companyRed rounded-full"
          >
            Close
          </InteractiveButton> */}
          </>
        </div>
      )}
    </div>
  );
};

export default NewsLetterBox;
