import{r as f}from"./vendor-b0222800.js";import"./@emotion/react-32889a6e.js";/*!
 * cookie
 * Copyright(c) 2012-2014 <PERSON>
 * Copyright(c) 2015 <PERSON>
 * MIT Licensed
 */var p=w,l=m,C=Object.prototype.toString,h=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function w(i,e){if(typeof i!="string")throw new TypeError("argument str must be a string");for(var o={},t=e||{},a=t.decode||O,r=0;r<i.length;){var n=i.indexOf("=",r);if(n===-1)break;var s=i.indexOf(";",r);if(s===-1)s=i.length;else if(s<n){r=i.lastIndexOf(";",n-1)+1;continue}var c=i.slice(r,n).trim();if(o[c]===void 0){var u=i.slice(n+1,s).trim();u.charCodeAt(0)===34&&(u=u.slice(1,-1)),o[c]=S(u,a)}r=s+1}return o}function m(i,e,o){var t=o||{},a=t.encode||k;if(typeof a!="function")throw new TypeError("option encode is invalid");if(!h.test(i))throw new TypeError("argument name is invalid");var r=a(e);if(r&&!h.test(r))throw new TypeError("argument val is invalid");var n=i+"="+r;if(t.maxAge!=null){var s=t.maxAge-0;if(isNaN(s)||!isFinite(s))throw new TypeError("option maxAge is invalid");n+="; Max-Age="+Math.floor(s)}if(t.domain){if(!h.test(t.domain))throw new TypeError("option domain is invalid");n+="; Domain="+t.domain}if(t.path){if(!h.test(t.path))throw new TypeError("option path is invalid");n+="; Path="+t.path}if(t.expires){var c=t.expires;if(!y(c)||isNaN(c.valueOf()))throw new TypeError("option expires is invalid");n+="; Expires="+c.toUTCString()}if(t.httpOnly&&(n+="; HttpOnly"),t.secure&&(n+="; Secure"),t.partitioned&&(n+="; Partitioned"),t.priority){var u=typeof t.priority=="string"?t.priority.toLowerCase():t.priority;switch(u){case"low":n+="; Priority=Low";break;case"medium":n+="; Priority=Medium";break;case"high":n+="; Priority=High";break;default:throw new TypeError("option priority is invalid")}}if(t.sameSite){var g=typeof t.sameSite=="string"?t.sameSite.toLowerCase():t.sameSite;switch(g){case!0:n+="; SameSite=Strict";break;case"lax":n+="; SameSite=Lax";break;case"strict":n+="; SameSite=Strict";break;case"none":n+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}}return n}function O(i){return i.indexOf("%")!==-1?decodeURIComponent(i):i}function k(i){return encodeURIComponent(i)}function y(i){return C.call(i)==="[object Date]"||i instanceof Date}function S(i,e){try{return e(i)}catch{return i}}function v(){const i=typeof global>"u"?void 0:global.TEST_HAS_DOCUMENT_COOKIE;return typeof i=="boolean"?i:typeof document=="object"&&typeof document.cookie=="string"}function E(i){return typeof i=="string"?p(i):typeof i=="object"&&i!==null?i:{}}function d(i,e={}){const o=b(i);if(!e.doNotParse)try{return JSON.parse(o)}catch{}return i}function b(i){return i&&i[0]==="j"&&i[1]===":"?i.substr(2):i}class _{constructor(e,o={}){this.changeListeners=[],this.HAS_DOCUMENT_COOKIE=!1,this.update=()=>{if(!this.HAS_DOCUMENT_COOKIE)return;const a=this.cookies;this.cookies=p(document.cookie),this._checkChanges(a)};const t=typeof document>"u"?"":document.cookie;this.cookies=E(e||t),this.defaultSetOptions=o,this.HAS_DOCUMENT_COOKIE=v()}_emitChange(e){for(let o=0;o<this.changeListeners.length;++o)this.changeListeners[o](e)}_checkChanges(e){new Set(Object.keys(e).concat(Object.keys(this.cookies))).forEach(t=>{e[t]!==this.cookies[t]&&this._emitChange({name:t,value:d(this.cookies[t])})})}_startPolling(){this.pollingInterval=setInterval(this.update,300)}_stopPolling(){this.pollingInterval&&clearInterval(this.pollingInterval)}get(e,o={}){return o.doNotUpdate||this.update(),d(this.cookies[e],o)}getAll(e={}){e.doNotUpdate||this.update();const o={};for(let t in this.cookies)o[t]=d(this.cookies[t],e);return o}set(e,o,t){t?t=Object.assign(Object.assign({},this.defaultSetOptions),t):t=this.defaultSetOptions;const a=typeof o=="string"?o:JSON.stringify(o);this.cookies=Object.assign(Object.assign({},this.cookies),{[e]:a}),this.HAS_DOCUMENT_COOKIE&&(document.cookie=l(e,a,t)),this._emitChange({name:e,value:o,options:t})}remove(e,o){const t=o=Object.assign(Object.assign(Object.assign({},this.defaultSetOptions),o),{expires:new Date(1970,1,1,0,0,1),maxAge:0});this.cookies=Object.assign({},this.cookies),delete this.cookies[e],this.HAS_DOCUMENT_COOKIE&&(document.cookie=l(e,"",t)),this._emitChange({name:e,value:void 0,options:o})}addChangeListener(e){this.changeListeners.push(e),this.HAS_DOCUMENT_COOKIE&&this.changeListeners.length===1&&(typeof window=="object"&&"cookieStore"in window?window.cookieStore.addEventListener("change",this.update):this._startPolling())}removeChangeListener(e){const o=this.changeListeners.indexOf(e);o>=0&&this.changeListeners.splice(o,1),this.HAS_DOCUMENT_COOKIE&&this.changeListeners.length===0&&(typeof window=="object"&&"cookieStore"in window?window.cookieStore.removeEventListener("change",this.update):this._stopPolling())}}const T=f.createContext(new _),L=T;function N(){return typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u"}function D(i){const e=f.useContext(L);if(!e)throw new Error("Missing <CookiesProvider>");const[o,t]=f.useState(()=>e.getAll());N()&&f.useLayoutEffect(()=>{function s(){const c=e.getAll({doNotUpdate:!0});j(i||null,c,o)&&t(c)}return e.addChangeListener(s),()=>{e.removeChangeListener(s)}},[e,o]);const a=f.useMemo(()=>e.set.bind(e),[e]),r=f.useMemo(()=>e.remove.bind(e),[e]),n=f.useMemo(()=>e.update.bind(e),[e]);return[o,a,r,n]}function j(i,e,o){if(!i)return!0;for(let t of i)if(e[t]!==o[t])return!0;return!1}export{D as u};
