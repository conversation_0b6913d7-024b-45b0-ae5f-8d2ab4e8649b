import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { GlobalContext, showToast } from "Src/globalContext";
import { AuthContext, tokenExpireError } from "Src/authContext";
import { InteractiveButton } from "Components/InteractiveButton";
import Navbar from "Components/NavBar";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEye } from "@fortawesome/free-solid-svg-icons";
import { Link, useNavigate } from "react-router-dom";
import ModalPrompt from "Components/Modal/ModalPrompt";

let sdk = new MkdSDK();

const UserProfilePage = () => {
  const schema = yup
    .object({
      email: yup.string().email().required(),
    })
    .required();

  const { state, dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  const [oldEmail, setOldEmail] = useState("");
  const [oldFirstName, setOldFirstName] = useState("");
  const [oldLastName, setOldLastName] = useState("");
  const [oldGender, setOldGender] = useState("");
  const [oldAge, setOldAge] = useState("");
  const [submitLoading, setSubmitLoading] = useState(false);
  const [passwordShown, setPasswordShown] = useState(false);
  const [modal, setModal] = useState(false);
  const [deleteModal, setDeleteModal] = useState(false);
  const togglePasswordVisiblity = () => {
    setPasswordShown((passwordShown) => !passwordShown);
  };

  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    setError,
    setValue,
    reset,
    watch,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const watchedFields = watch();

  const isFormChanged =
    oldFirstName !== watchedFields.firstName ||
    oldLastName !== watchedFields.lastName ||
    oldEmail !== watchedFields.email ||
    oldAge !== watchedFields.age ||
    oldGender !== watchedFields.gender ||
    watchedFields.password;

  function convertDate(dateString) {
    const date = new Date(dateString);

    const year = 2001;
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");

    const formattedDate = `${year}-${month}-${day}`;
    return formattedDate;
  }

  const getProfile = async () => {
    try {
      const result = await sdk.getProfile();
      setValue("email", result.email);
      setValue("firstName", result.first_name);
      setValue("lastName", result.last_name);
      setValue("age", convertDate(result.age));
      setValue("gender", result.gender);
      setOldEmail(result.email);
      setOldFirstName(result.first_name);
      setOldLastName(result.last_name);
      setOldGender(result.gender);
      setOldAge(convertDate(result.age));
      // console.log();
    } catch (error) {
      console.log("Error", error);
      tokenExpireError(
        dispatch,
        error.response?.data.message
          ? error.response?.data.message
          : error.message
      );
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "profile",
      },
    });

    (async function () {
      getProfile();
    })();
  }, []);

  const deleteAccount = async () => {
    try {
      const result = await sdk.deleteUserProfile();

      if (result) {
        showToast(globalDispatch, result?.message, 2000);
        dispatch({
          type: "LOGOUT",
        });
        navigate("/");
      }
    } catch (err) {
      throw new Error(err);
    }
  };

  const handledeleteAccountClick = async () => {
    setModal((prev) => !prev);
  };
  const handleConfirmDeleteAccountClick = async () => {
    setDeleteModal((prev) => !prev);
    setModal((prev) => !prev);
  };

  const onSubmit = async (data) => {
    try {
      setSubmitLoading(true);

      // Name Update
      if ((oldFirstName !== data.firstName) | (oldLastName !== data.lastName)) {
        // const updatename = await sdk.updatename(
        //   data.firstName,
        //   data.lastName,
        //   state.user,
        //   state.token
        // );
        sdk.setTable("user");

        const updatename = await sdk.callRestAPI(
          {
            id: state.user,

            last_name: data.lastName,
            first_name: data.firstName,
          },
          "PUT"
        );
        if (updatename.error === false) {
          showToast(globalDispatch, "Profile Updated", 2000);
          data.firstName && localStorage.setItem("firstName", data.firstName);
          data.lastName && localStorage.setItem("lastName", data.lastName);
          // console.log("toast", typeof updatename.error);
        } else {
          if (updatename.validation) {
            const keys = Object.keys(updatename.validation);
            for (let i = 0; i < keys.length; i++) {
              const field = keys[i];
              setError(field, {
                type: "manual",
                message: updatename.validation[field],
              });
            }
          }
        }
      }

      // Email Update not allowed to change email for now
      if (oldEmail !== data.email) {
        const emailresult = await sdk.updateEmail(data.email);
        if (!emailresult.error) {
          showToast(globalDispatch, "Profile Updated", 1000);
        } else {
          if (emailresult.validation) {
            const keys = Object.keys(emailresult.validation);
            for (let i = 0; i < keys.length; i++) {
              const field = keys[i];
              setError(field, {
                type: "manual",
                message: emailresult.validation[field],
              });
            }
          }
        }
      }

      // Age Update
      if (oldAge !== data.age) {
        sdk.setTable("user");

        const ageresult = await sdk.callRestAPI(
          {
            id: state.user,

            age: data.age,
          },
          "PUT"
        );

        if (!ageresult.error) {
          showToast(globalDispatch, "Profile Updated", 1000);
        } else {
          if (ageresult.validation) {
            const keys = Object.keys(ageresult.validation);
            for (let i = 0; i < keys.length; i++) {
              const field = keys[i];
              setError(field, {
                type: "manual",
                message: ageresult.validation[field],
              });
            }
          }
        }
      }

      // Gender Update
      if (oldGender !== data.gender) {
        sdk.setTable("user");

        const genderresult = await sdk.callRestAPI(
          {
            id: state.user,

            gender: data.gender,
          },
          "PUT"
        );

        if (!genderresult.error) {
          showToast(globalDispatch, "Profile Updated", 1000);
        } else {
          if (genderresult.validation) {
            const keys = Object.keys(genderresult.validation);
            for (let i = 0; i < keys.length; i++) {
              const field = keys[i];
              setError(field, {
                type: "manual",
                message: genderresult.validation[field],
              });
            }
          }
        }
      }

      // Password Update
      if (data.password.length > 0) {
        const passwordresult = await sdk.updatePassword(data.password);
        if (!passwordresult.error) {
          showToast(globalDispatch, "Profile Updated", 2000);
        } else {
          if (passwordresult.validation) {
            const keys = Object.keys(passwordresult.validation);
            for (let i = 0; i < keys.length; i++) {
              const field = keys[i];
              setError(field, {
                type: "manual",
                message: passwordresult.validation[field],
              });
            }
          }
        }
      }
      getProfile();
      setSubmitLoading(false);
    } catch (error) {
      setSubmitLoading(false);
      console.log("Error", error);
      setError("email", {
        type: "manual",
        message: error.response.data.message
          ? error.response.data.message
          : error.message,
      });
      tokenExpireError(
        dispatch,
        error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  };

  const selectGenderStatus = [
    { key: "0", value: "Male" },
    { key: "1", value: "Female" },
    // { key: "2", value: "Trans" },
  ];

  return (
    <>
      <Navbar />
      <main className="">
        <div className="bg-white rounded md:p-0 p-5 flex flex-col items-center w-full h-full justify-between gap-[10px]  max-w-[1280px] mx-auto">
          <div className="text-left flex flex-col items-center md:w-[60vw] w-[100%]">
            <h4 className="md:text-[40px] text-[32px] text-companyBlack md:font-semibold font-medium w-full md:text-left text-center max-w-[486px]">
              My Profile
            </h4>
            <p className="md:text-[16px] text-[14px]  text-[#9E9E9E] mb-8 mt-4 md:text-left text-center md:text-base max-w-lg">
              Gaze and attention modeling powered by AI is optimizing virtual
              reality experiences
            </p>
            <form
              onSubmit={handleSubmit(onSubmit)}
              className="max-w-lg text-center flex flex-col w-[100%]"
            >
              <div className={`md:mb-4 mb-6 `}>
                <div
                  className={` flex border-2  rounded-full items-center justify-center bg-[#E0E0E0] p-2 px-4 ${
                    errors && errors.email?.message ? "border-red-500" : ""
                  }`}
                >
                  <span className="inline mr-2 ">
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M14.166 17.0837H5.83268C3.33268 17.0837 1.66602 15.8337 1.66602 12.917V7.08366C1.66602 4.16699 3.33268 2.91699 5.83268 2.91699H14.166C16.666 2.91699 18.3327 4.16699 18.3327 7.08366V12.917C18.3327 15.8337 16.666 17.0837 14.166 17.0837Z"
                        stroke="#9CA3AF"
                        stroke-width="1.5"
                        stroke-miterlimit="10"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M14.1673 7.5L11.559 9.58333C10.7006 10.2667 9.29231 10.2667 8.43398 9.58333L5.83398 7.5"
                        stroke="#9CA3AF"
                        stroke-width="1.5"
                        stroke-miterlimit="10"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </span>

                  <input
                    type="email"
                    autoComplete="off"
                    placeholder="Enter your email"
                    disabled
                    {...register("email")}
                    className={`resize-none  p-2 px-4 bg-transparent active:outline-0 shadow-none appearance-none rounded-full w-full py-2  text-[#959AA3] leading-tight focus:outline-0 focus:shadow-none inline border-none focus:ring-0 focus:border-none focus:bg-black`}
                  />
                </div>
                <p className="text-red-500 text-xs italic">
                  {errors.email?.message}
                </p>
              </div>

              <div className="md:mb-4 mb-6">
                <div
                  className={` flex border-2  rounded-full items-center justify-center bg-[#F9FAFB] p-2 px-4 ${
                    errors && errors.email?.message ? "border-red-500" : ""
                  }`}
                >
                  <span className="inline mr-2 ">
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M10.0002 10.0003C12.3013 10.0003 14.1668 8.13485 14.1668 5.83366C14.1668 3.53247 12.3013 1.66699 10.0002 1.66699C7.69898 1.66699 5.8335 3.53247 5.8335 5.83366C5.8335 8.13485 7.69898 10.0003 10.0002 10.0003Z"
                        stroke="#9CA3AF"
                        stroke-width="1.25"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M17.1585 18.3333C17.1585 15.1083 13.9501 12.5 10.0001 12.5C6.05013 12.5 2.8418 15.1083 2.8418 18.3333"
                        stroke="#9CA3AF"
                        stroke-width="1.25"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </span>
                  <input
                    {...register("firstName")}
                    name="firstName"
                    className={
                      "resize-none  p-2 px-4 bg-transparent active:outline-0 shadow-none appearance-none rounded-full w-full py-2  text-black leading-tight focus:outline-0 focus:shadow-none inline border-none focus:ring-0 focus:border-none focus:bg-transparent"
                    }
                    id="firstName"
                    type="text"
                    placeholder="First Name"
                  />
                </div>
                <p className="text-red-500 text-xs italic"></p>
              </div>

              <div className="md:mb-4 mb-6">
                <div
                  className={` flex border-2  rounded-full items-center justify-center bg-[#F9FAFB] p-2 px-4 ${
                    errors && errors.email?.message ? "border-red-500" : ""
                  }`}
                >
                  <span className="inline mr-2 ">
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M10.0002 10.0003C12.3013 10.0003 14.1668 8.13485 14.1668 5.83366C14.1668 3.53247 12.3013 1.66699 10.0002 1.66699C7.69898 1.66699 5.8335 3.53247 5.8335 5.83366C5.8335 8.13485 7.69898 10.0003 10.0002 10.0003Z"
                        stroke="#9CA3AF"
                        stroke-width="1.25"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M17.1585 18.3333C17.1585 15.1083 13.9501 12.5 10.0001 12.5C6.05013 12.5 2.8418 15.1083 2.8418 18.3333"
                        stroke="#9CA3AF"
                        stroke-width="1.25"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </span>
                  <input
                    {...register("lastName")}
                    name="lastName"
                    className={
                      "resize-none  p-2 px-4 bg-transparent active:outline-0 shadow-none appearance-none rounded-full w-full py-2  text-black leading-tight focus:outline-0 focus:shadow-none inline border-none focus:ring-0 focus:border-none focus:bg-transparent"
                    }
                    id="lastName"
                    type="text"
                    placeholder="Last Name"
                  />
                </div>
                <p className="text-red-500 text-xs italic"></p>
              </div>

              <div className="md:mb-4 mb-6">
                <div
                  className={` flex border-2  rounded-full items-center justify-start bg-[#F9FAFB]  px-4 ${
                    errors && errors.email?.message ? "border-red-500" : ""
                  }`}
                >
                  <FontAwesomeIcon
                    icon="fa-solid fa-calendar"
                    style={{ color: "#c5c8d3" }}
                  />

                  <div className=" w-full flex flex-col ">
                    <input
                      {...register("age")}
                      name="age"
                      className={
                        "resize-none border-none px-4 bg-transparent active:outline-0 shadow-none appearance-none rounded-full w-full  text-black leading-tight focus:outline-0 focus:shadow-none inline  focus:ring-0 focus:border-none focus:bg-transparent"
                      }
                      id="age"
                      type="date"
                      placeholder="Age"
                    />
                    <p className=" text-[#828282ab] w-full max-w-[100px] text-xs ">
                      Date of Birth
                    </p>
                  </div>
                </div>
                {/* <p className="text-red-500 text-xs italic"></p> */}
              </div>
              <div className="md:mb-4 mb-6">
                <div
                  className={` flex border-2  rounded-full items-center justify-center bg-[#F9FAFB] p-2 px-4 ${
                    errors && errors.email?.message ? "border-red-500" : ""
                  }`}
                >
                  <span className="inline mr-2 ">
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M10.0002 10.0003C12.3013 10.0003 14.1668 8.13485 14.1668 5.83366C14.1668 3.53247 12.3013 1.66699 10.0002 1.66699C7.69898 1.66699 5.8335 3.53247 5.8335 5.83366C5.8335 8.13485 7.69898 10.0003 10.0002 10.0003Z"
                        stroke="#9CA3AF"
                        stroke-width="1.25"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M17.1585 18.3333C17.1585 15.1083 13.9501 12.5 10.0001 12.5C6.05013 12.5 2.8418 15.1083 2.8418 18.3333"
                        stroke="#9CA3AF"
                        stroke-width="1.25"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </span>

                  <select
                    className={
                      "resize-none  p-2 px-4 bg-transparent active:outline-0 shadow-none appearance-none rounded-full w-full py-2  text-black leading-tight focus:outline-0 focus:shadow-none inline border-none focus:ring-0 focus:border-none focus:bg-transparent"
                    }
                    {...register("gender")}
                  >
                    <option value=""> Select Gender</option>
                    {selectGenderStatus.map((option) => (
                      <option name="gender" value={option.key} key={option.key}>
                        {option.value}
                      </option>
                    ))}
                  </select>
                </div>
                <p className="text-red-500 text-xs italic"></p>
              </div>

              <div className="mb-6">
                <div
                  className={` flex border-2  rounded-full items-center justify-center bg-[#F9FAFB] p-2 px-4  ${
                    errors && errors.password?.message ? "border-red-500" : ""
                  }`}
                >
                  <span className="inline mr-2 ">
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M5 8.33366V6.66699C5 3.90866 5.83333 1.66699 10 1.66699C14.1667 1.66699 15 3.90866 15 6.66699V8.33366"
                        stroke="#9CA3AF"
                        stroke-width="1.25"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M9.99935 15.4167C11.1499 15.4167 12.0827 14.4839 12.0827 13.3333C12.0827 12.1827 11.1499 11.25 9.99935 11.25C8.84876 11.25 7.91602 12.1827 7.91602 13.3333C7.91602 14.4839 8.84876 15.4167 9.99935 15.4167Z"
                        stroke="#9CA3AF"
                        stroke-width="1.25"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M14.166 18.333H5.83268C2.49935 18.333 1.66602 17.4997 1.66602 14.1663V12.4997C1.66602 9.16634 2.49935 8.33301 5.83268 8.33301H14.166C17.4993 8.33301 18.3327 9.16634 18.3327 12.4997V14.1663C18.3327 17.4997 17.4993 18.333 14.166 18.333Z"
                        stroke="#9CA3AF"
                        stroke-width="1.25"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </span>
                  <input
                    autoComplete="off"
                    type={passwordShown ? "text" : "password"}
                    name="password"
                    placeholder="Change password"
                    {...register("password")}
                    className={` focus:outline-none active:outline-none flex-grow p-2 px-4 appearance-none rounded-full w-full py-2 text-black bg-transparent leading-tight focus: shadow-outline border-none focus:ring-0 focus:border-none focus:bg-[#F9FAFB]`}
                  />
                  <i
                    onClick={togglePasswordVisiblity}
                    className=" ml-2 cursor-pointer"
                  >
                    <FontAwesomeIcon
                      icon={faEye}
                      style={{ color: `${passwordShown ? "darkred" : "gray"}` }}
                    />
                  </i>
                </div>
                {/* <p className="text-red-500 text-xs italic">
                  {errors.password?.message}
                </p> */}
              </div>
              <div className="flex items-center w-[100%] justify-between">
                <InteractiveButton
                  className="bg-companyRed disabled:bg-red-300 w-[100%] hover:bg-red-700 disabled:cursor-not-allowed text-white text[16px] font-medium py-2 px-4 rounded-full focus:outline-none focus:shadow-outline"
                  type="submit"
                  loading={submitLoading}
                  disabled={submitLoading || !isFormChanged}
                >
                  Submit
                </InteractiveButton>
              </div>
            </form>
          </div>
          <div className="md:w-[60vw] max-w-lg w-[100%] mt-[100px]">
            <p className="md:text-left text-center text-[#BCBCBC] w-full text-base">
              2022 Kaizen, All rights reserved
            </p>
          </div>

          <div className=" w-full flex justify-end   border-companyRed sm:container p-5">
            <button
              className=" text-white text[16px] bg-companyRed rounded-full p-2 px-4 text-sm"
              onClick={handledeleteAccountClick}
            >
              Delete Account
            </button>
          </div>
        </div>
        {modal && (
          <ModalPrompt
            message={
              "This action will delete your account permanently. Are you sure you want to continue?"
            }
            actionHandler={handleConfirmDeleteAccountClick}
            closeModalFunction={() => setModal((prev) => !prev)}
            rejectText="Cancel"
            acceptText="Continue"
          />
        )}
        {deleteModal && (
          <ModalPrompt
            message={"Please provide a reason for this action"}
            actionHandler={deleteAccount}
            // actionHandler={() => console.log("hey")}
            closeModalFunction={() => setDeleteModal((prev) => !prev)}
            rejectText="Cancel"
            acceptText="Delete"
          >
            <textarea
              type="text"
              placeholder="Reason for deleting account"
           
              {...register("reason")}
              className={`resize-none  p-2 px-4 bg-transparent active:outline-0 shadow-none appearance-none rounded-lg w-full py-2  text-[#959AA3] leading-tight focus:outline-0 focus:shadow-none inline border focus:ring-0 focus:border `}
            />
          </ModalPrompt>
        )}
      </main>
    </>
  );
};

export default UserProfilePage;
