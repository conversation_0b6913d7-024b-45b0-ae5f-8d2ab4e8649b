import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "../utils/MkdSDK";
import { useNavigate, useParams } from "react-router-dom";
import { AuthContext, tokenExpireError } from "../authContext";
import { GlobalContext, showToast } from "../globalContext";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { isImage, empty, isVideo, isPdf } from "../utils/utils";
import { useEffect } from "react";

let sdk = new MkdSDK();
const EditAdminRewardPage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [plans, setPlans] = useState();
  const [expired, setExpired] = useState("");
  const [planId, setPlanId] = useState("");
  const [id, setId] = useState(0);
  const schema = yup
    .object({
      create_at: yup.string(),
      update_at: yup.string(),
      email: yup.string(),
    })
    .required();
    const params = useParams();

  const selectType = [
    { key: "", value: "Basic" },
    { key: "text", value: "Premium" },
    { key: "image", value: "Deluxe" },
    { key: "number", value: "Number" },
  ];
  const status = [
    { key: "0", value: "Active" },
    { key: "1", value: "Expired" },
  ];

  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });


  useEffect(function () {
    (async function () {
      try {
        sdk.setTable("coupon");
        const result = await sdk.callRestAPI({ id: Number(params?.id) }, "GET");
        if (!result.error) {
          setValue("code", result.model.code);
          setValue("plan", result.model.plan_id);
          setValue("status", result.model.expired);
          // console.log(result.model);
          setExpired(result.model.expired)
          setPlanId(result.model.plan_id)

          // setName(result.model.name);
          // setDescription(result.model.description);
          // setAmount(result.model.amount/100);
          // setStripeId(result.model.stripe_id);
          // setMessages(result.model.messages);
          setId(result.model.id);
        }
      } catch (error) {
        console.log("error", error);
        tokenExpireError(dispatch, error.message);
      }
    })();
  }, []);
  const handlegenerate = async (_data) => {
    let sdk = new MkdSDK();

    try {
     

      sdk.setTable("coupon");

      const result = await sdk.callRestAPI(
        {
          id:id,
          code: _data.code,
          plan_id: _data.plan,
          expired: _data.status,
        },
        "PUT"
      );
      // console.log("result", result);
      if (!result.error) {
        showToast(globalDispatch, "Added");
        navigate("/admin/rewards");
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error) {
      console.log("Error", error);
      setError("name", {
        type: "manual",
        message: error.message,
      });
      tokenExpireError(dispatch, error.message);
    }
  };



  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "rewards",
      },
    });
  }, []);
  React.useEffect(() => {
    const getplans = async () => {
      const plans = await sdk.getplans();
      setPlans(plans?.list?.reverse());
      // console.log("plans", plans);
    };
    getplans();
  }, []);
  // console.log("log", plans);

  return (
    <div>

      {/* Generate Coupon */}
      <div className=" shadow-md rounded  mx-auto p-5 mt-6">
        <h4 className="text-2xl font-medium">Edit Coupon</h4>
        <form
          className=" w-full max-w-lg"
          onSubmit={handleSubmit(handlegenerate)}
        >
          <div className="mb-4  ">
            <label
              className="block text-gray-700 text-sm font-bold mb-2"
              htmlFor="update_at"
            >
              Code
            </label>
            <input
              type="code"
              placeholder="code"
              {...register("code")}
              className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
                errors.code?.message ? "border-red-500" : ""
              }`}
            />
            <p className="text-red-500 text-xs italic">
              {errors.code?.message}
            </p>
          </div>

          <div className="mb-4 w-full   ">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Plan
            </label>
            <select
              className="shadow border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"
              {...register("plan")}
            >
              {plans?.map((option) => (
                <option
                  name="plan"
                  value={option.id}
                  key={option.id}
                  defaultValue=""
                >
                  {option.name}
                </option>
              ))}
            </select>
            <p className="text-red-500 text-xs italic"></p>
          </div>

          <div className="mb-4 w-full   ">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Status
            </label>
            <select
              className="shadow border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"
              {...register("status")}
            >
              <option name="status" defaultValue=""></option>

              {status?.map((option) => (
                <option
                  name="status"
                  value={option.key}
                  key={option.key}
                  defaultValue=""
                >
                  {option.value}
                </option>
              ))}
            </select>
            <p className="text-red-500 text-xs italic"></p>
          </div>

          {/* <input
            type="email"
            placeholder="winner email"
            {...register("email")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
              errors.email?.message ? "border-red-500" : ""
            }`}
          /> */}
          {/* <p
            className={`shadow appearance-none border rounded w-full py-4 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline mb-4`}
            placeholder="coupon code"
          >
            {" "}
          </p> */}

          <button
            type="submit"
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
          >
            Update
          </button>
        </form>
      </div>
    </div>
  );
};

export default EditAdminRewardPage;
