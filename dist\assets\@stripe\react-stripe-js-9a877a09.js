import{a}from"../vendor-b0222800.js";import{P as s}from"../@ckeditor/ckeditor5-react-48fc30c1.js";function G(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(r);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(r,i).enumerable})),t.push.apply(t,n)}return t}function Q(r){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?G(Object(t),!0).forEach(function(n){ne(r,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):G(Object(t)).forEach(function(n){Object.defineProperty(r,n,Object.getOwnPropertyDescriptor(t,n))})}return r}function W(r){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?W=function(e){return typeof e}:W=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},W(r)}function ne(r,e,t){return e in r?Object.defineProperty(r,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[e]=t,r}function $(r,e){return Ce(r)||ve(r,e)||he(r,e)||ye()}function Ce(r){if(Array.isArray(r))return r}function ve(r,e){var t=r&&(typeof Symbol<"u"&&r[Symbol.iterator]||r["@@iterator"]);if(t!=null){var n=[],i=!0,o=!1,p,v;try{for(t=t.call(r);!(i=(p=t.next()).done)&&(n.push(p.value),!(e&&n.length===e));i=!0);}catch(u){o=!0,v=u}finally{try{!i&&t.return!=null&&t.return()}finally{if(o)throw v}}return n}}function he(r,e){if(r){if(typeof r=="string")return Z(r,e);var t=Object.prototype.toString.call(r).slice(8,-1);if(t==="Object"&&r.constructor&&(t=r.constructor.name),t==="Map"||t==="Set")return Array.from(r);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Z(r,e)}}function Z(r,e){(e==null||e>r.length)&&(e=r.length);for(var t=0,n=new Array(e);t<e;t++)n[t]=r[t];return n}function ye(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var K=function(e){var t=a.useRef(e);return a.useEffect(function(){t.current=e},[e]),t.current},P=function(e){return e!==null&&W(e)==="object"},ge=function(e){return P(e)&&typeof e.then=="function"},Se=function(e){return P(e)&&typeof e.elements=="function"&&typeof e.createToken=="function"&&typeof e.createPaymentMethod=="function"&&typeof e.confirmCardPayment=="function"},ee="[object Object]",Ee=function r(e,t){if(!P(e)||!P(t))return e===t;var n=Array.isArray(e),i=Array.isArray(t);if(n!==i)return!1;var o=Object.prototype.toString.call(e)===ee,p=Object.prototype.toString.call(t)===ee;if(o!==p)return!1;if(!o&&!n)return e===t;var v=Object.keys(e),u=Object.keys(t);if(v.length!==u.length)return!1;for(var S={},E=0;E<v.length;E+=1)S[v[E]]=!0;for(var h=0;h<u.length;h+=1)S[u[h]]=!0;var y=Object.keys(S);if(y.length!==v.length)return!1;var R=e,A=t,g=function(O){return r(R[O],A[O])};return y.every(g)},ae=function(e,t,n){return P(e)?Object.keys(e).reduce(function(i,o){var p=!P(t)||!Ee(e[o],t[o]);return n.includes(o)?(p&&console.warn("Unsupported prop change: options.".concat(o," is not a mutable property.")),i):p?Q(Q({},i||{}),{},ne({},o,e[o])):i},null):null},oe="Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.",te=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:oe;if(e===null||Se(e))return e;throw new Error(t)},ke=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:oe;if(ge(e))return{tag:"async",stripePromise:Promise.resolve(e).then(function(i){return te(i,t)})};var n=te(e,t);return n===null?{tag:"empty"}:{tag:"sync",stripe:n}},be=function(e){!e||!e._registerWrapper||!e.registerAppInfo||(e._registerWrapper({name:"react-stripe-js",version:"2.6.2"}),e.registerAppInfo({name:"react-stripe-js",version:"2.6.2",url:"https://stripe.com/docs/stripe-js/react"}))},B=a.createContext(null);B.displayName="ElementsContext";var ue=function(e,t){if(!e)throw new Error("Could not find Elements context; You need to wrap the part of your app that ".concat(t," in an <Elements> provider."));return e},J=a.createContext(null);J.displayName="CartElementContext";var xe=function(e,t){if(!e)throw new Error("Could not find Elements context; You need to wrap the part of your app that ".concat(t," in an <Elements> provider."));return e},Oe=function(e){var t=e.stripe,n=e.options,i=e.children,o=a.useMemo(function(){return ke(t)},[t]),p=a.useState(null),v=$(p,2),u=v[0],S=v[1],E=a.useState(null),h=$(E,2),y=h[0],R=h[1],A=a.useState(function(){return{stripe:o.tag==="sync"?o.stripe:null,elements:o.tag==="sync"?o.stripe.elements(n):null}}),g=$(A,2),C=g[0],O=g[1];a.useEffect(function(){var b=!0,T=function(_){O(function(I){return I.stripe?I:{stripe:_,elements:_.elements(n)}})};return o.tag==="async"&&!C.stripe?o.stripePromise.then(function(x){x&&b&&T(x)}):o.tag==="sync"&&!C.stripe&&T(o.stripe),function(){b=!1}},[o,C,n]);var j=K(t);a.useEffect(function(){j!==null&&j!==t&&console.warn("Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.")},[j,t]);var N=K(n);return a.useEffect(function(){if(C.elements){var b=ae(n,N,["clientSecret","fonts"]);b&&C.elements.update(b)}},[n,N,C.elements]),a.useEffect(function(){be(C.stripe)},[C.stripe]),a.createElement(B.Provider,{value:C},a.createElement(J.Provider,{value:{cart:u,setCart:S,cartState:y,setCartState:R}},i))};Oe.propTypes={stripe:s.any,options:s.object};var Pe=function(e){var t=a.useContext(B);return ue(t,e)},Re={cart:null,cartState:null,setCart:function(){},setCartState:function(){}},re=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=a.useContext(J);return t?Re:xe(n,e)},Ie=function(){var e=Pe("calls useElements()"),t=e.elements;return t};s.func.isRequired;var d=function(e,t,n){var i=!!n,o=a.useRef(n);a.useEffect(function(){o.current=n},[n]),a.useEffect(function(){if(!i||!e)return function(){};var p=function(){o.current&&o.current.apply(o,arguments)};return e.on(t,p),function(){e.off(t,p)}},[i,t,e,o])},se=a.createContext(null);se.displayName="CustomCheckoutSdkContext";var Ae=function(e,t){if(!e)throw new Error("Could not find CustomCheckoutProvider context; You need to wrap the part of your app that ".concat(t," in an <CustomCheckoutProvider> provider."));return e},je=a.createContext(null);je.displayName="CustomCheckoutContext";s.any,s.shape({clientSecret:s.string.isRequired,elementsOptions:s.object}).isRequired;var F=function(e){var t=a.useContext(se),n=a.useContext(B);if(t&&n)throw new Error("You cannot wrap the part of your app that ".concat(e," in both <CustomCheckoutProvider> and <Elements> providers."));return t?Ae(t,e):ue(n,e)},we=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},c=function(e,t){var n="".concat(we(e),"Element"),i=function(u){var S=u.id,E=u.className,h=u.options,y=h===void 0?{}:h,R=u.onBlur,A=u.onFocus,g=u.onReady,C=u.onChange,O=u.onEscape,j=u.onClick,N=u.onLoadError,b=u.onLoaderStart,T=u.onNetworksChange,x=u.onCheckout,_=u.onLineItemClick,I=u.onConfirm,ie=u.onCancel,ce=u.onShippingAddressChange,le=u.onShippingRateChange,w=F("mounts <".concat(n,">")),U="elements"in w?w.elements:null,L="customCheckoutSdk"in w?w.customCheckoutSdk:null,fe=a.useState(null),z=$(fe,2),m=z[0],pe=z[1],k=a.useRef(null),q=a.useRef(null),H=re("mounts <".concat(n,">"),"customCheckoutSdk"in w),D=H.setCart,Y=H.setCartState;d(m,"blur",R),d(m,"focus",A),d(m,"escape",O),d(m,"click",j),d(m,"loaderror",N),d(m,"loaderstart",b),d(m,"networkschange",T),d(m,"lineitemclick",_),d(m,"confirm",I),d(m,"cancel",ie),d(m,"shippingaddresschange",ce),d(m,"shippingratechange",le);var M;e==="cart"?M=function(X){Y(X),g&&g(X)}:g&&(e==="expressCheckout"?M=g:M=function(){g(m)}),d(m,"ready",M);var me=e==="cart"?function(f){Y(f),C&&C(f)}:C;d(m,"change",me);var de=e==="cart"?function(f){Y(f),x&&x(f)}:x;d(m,"checkout",de),a.useLayoutEffect(function(){if(k.current===null&&q.current!==null&&(U||L)){var f=null;L?f=L.createElement(e,y):U&&(f=U.create(e,y)),e==="cart"&&D&&D(f),k.current=f,pe(f),f&&f.mount(q.current)}},[U,L,y,D]);var V=K(y);return a.useEffect(function(){if(k.current){var f=ae(y,V,["paymentRequest"]);f&&k.current.update(f)}},[y,V]),a.useLayoutEffect(function(){return function(){if(k.current&&typeof k.current.destroy=="function")try{k.current.destroy(),k.current=null}catch{}}},[]),a.createElement("div",{id:S,className:E,ref:q})},o=function(u){var S=F("mounts <".concat(n,">"));re("mounts <".concat(n,">"),"customCheckoutSdk"in S);var E=u.id,h=u.className;return a.createElement("div",{id:E,className:h})},p=t?o:i;return p.propTypes={id:s.string,className:s.string,onChange:s.func,onBlur:s.func,onFocus:s.func,onReady:s.func,onEscape:s.func,onClick:s.func,onLoadError:s.func,onLoaderStart:s.func,onNetworksChange:s.func,onCheckout:s.func,onLineItemClick:s.func,onConfirm:s.func,onCancel:s.func,onShippingAddressChange:s.func,onShippingRateChange:s.func,options:s.object},p.displayName=n,p.__elementType=e,p},l=typeof window>"u",Ne=a.createContext(null);Ne.displayName="EmbeddedCheckoutProviderContext";var Ue=function(){var e=F("calls useStripe()"),t=e.stripe;return t};c("auBankAccount",l);c("card",l);c("cardNumber",l);c("cardExpiry",l);c("cardCvc",l);c("fpxBank",l);c("iban",l);c("idealBank",l);c("p24Bank",l);c("epsBank",l);var Le=c("payment",l);c("expressCheckout",l);c("paymentRequestButton",l);c("linkAuthentication",l);c("address",l);c("shippingAddress",l);c("cart",l);c("paymentMethodMessaging",l);c("affirmMessage",l);c("afterpayClearpayMessage",l);export{Oe as E,Le as P,Ie as a,Ue as u};
