import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
// import MkdSDK from "../utils/MkdSDK";
import { Link, useLocation } from "react-router-dom";
import { useNavigate } from "react-router-dom";
// import { InteractiveButton } from "../components/InteractiveButton";
// import { AuthContext } from "authContext";
// import { GlobalContext, showToast } from "globalContext";
import MkdSDK from "Utils/MkdSDK";
import { InteractiveButton } from "Components/InteractiveButton";
import { AuthContext } from "Src/authContext";
import { GlobalContext, showToast } from "Src/globalContext";
import Spinner from "Components/Spinner";

let sdk = new MkdSDK();

const UserVerifyEmailPage = () => {
  const schema = yup
    .object({
      otp: yup.string().required(),
    })
    .required();

  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: GlobalDispatch } = React.useContext(GlobalContext);

  const [submitLoading, setSubmitLoading] = useState(false);
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const redirect_uri = searchParams.get("redirect_uri");
  const navigate = useNavigate();
  const data = searchParams.get("token");
  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const [message, setMessage] = useState("");
  // const [searchParams, setSearchParams] = useState('');

  // useEffect(() => {
  //   // Function to extract query parameters from the URL
  //   const getQueryParams = () => {
  //     const params = new URLSearchParams(window.location.search);
  //     return params.get('data'); // 'data' is the name of the query parameter
  //   };

  //   // Set the extracted query parameter to state
  //   setSearchParams(getQueryParams());
  // }, []);


  useEffect(() => {
    const handleSignup = async () => {
      try {
        setSubmitLoading(true);
        const result = await sdk.confirmSignupWitOtp(parseInt(data), "user");
        if (!result.error) {
          dispatch({
            type: "LOGIN",
            payload: result,
          });
          if (!result.error) {
            // const update = await sdk.updatename(
            //   JSON.parse(localStorage.getItem("firstName")),
            //   JSON.parse(localStorage.getItem("lastName")),
            //   result.user_id,
            //   result.token
            // );
            const getuser = await sdk.getProfile(result.token);
            dispatch({
              type: "SAVENAME",
              payload: getuser,
            });
            // console.log("user", getuser);
            dispatch({
              type: "NEWUSER",
            });
            localStorage.setItem("newUser", true);
            setSubmitLoading(false);
            showToast(
              GlobalDispatch,
              "Succesfully Registered",
              4000,
              "success"
            );
            navigate("/user/terms", {
              state: { from: location },
              replace: true,
            });
          } else {
            setSubmitLoading(false);
            if (result.validation) {
              const keys = Object.keys(result.validation);
              for (let i = 0; i < keys.length; i++) {
                const field = keys[i];
                setError(field, {
                  type: "manual",
                  message: result.validation[field],
                });
              }
            }
          }
        }
      } catch (error) {
        setSubmitLoading(false);
        console.log("Error", error);
        setError("otp", {
          type: "manual",
          message: error?.response?.data.message
            ? error?.response?.data.message
            : error.message,
        });
      }
    };

    handleSignup();
  }, []);

  return (
    <div className="h-screen m-auto max-h-screen min-h-screen">
      <div className="min-h-full flex justify-center items-center w-full max-h-full h-full">
        <div className=" w-fit  flex flex-col justify-center items-center gap-8 border py-[60px] px-[200px] shadow-lg">
          <h1 className=" font-bold text-3xl">Verify Your Email Address</h1>

          <p>Please verify your email address to continue:</p>

          <button className=" rounded-full px-4 py-1 bg-red-600 text-white">
            SEND VERIFICATION EMAIL
          </button>
        </div>
        {/* <section className="md:w-1/2 w-full flex flex-col items-center justify-center bg-white">
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="flex flex-col max-w-md w-full px-6 mt-[9.375rem]"
          >
            <h1 className="md:font-bold font-semibold md:text-5xl text-3xl text-center mb-8">
              Confirm SignUp
            </h1>
            <div className="mb-4">
              <label
                className="block text-gray-700 text-sm font-bold mb-2"
                htmlFor="otp"
              >
                Enter otp
              </label>

              <input
                type="otp"
                autoComplete="off"
                placeholder="otp"
                maxlength="6"
                {...register("otp")}
                className={`resize-none border-2 p-2 px-4 bg-transparent mb-3 active: outline-none shadow appearance-none rounded w-full py-2  text-gray-700 leading-tight focus:outline-none focus: shadow-outline ${
                  errors && errors.otp?.message ? "border-red-500" : ""
                }`}
              />
              <p className="text-red-500 text-xs italic">
                {errors.otp?.message}
              </p>
            </div>

           

            <InteractiveButton
              type="submit"
              className={`flex justify-center items-center bg-red-600 rounded-full text-white tracking-wide outline-none focus:outline-none rounded  py-2`}
              loading={submitLoading}
              disabled={submitLoading}
            >
              <span>Continue</span>
            </InteractiveButton>
          </form>

          <p className="text-center text-gray-500 text-xs h-10 my-5">
            &copy; {new Date().getFullYear()} manaknightdigital inc. All rights
            reserved.
          </p>
        </section>
        <section
          className="md:block hidden w-1/2"
          style={{
            backgroundImage:
              "url(https://ergo.manaknightdigital.com/login-bg.jpg)",
            backgroundSize: "cover",
            backgroundPosition: "center center",
          }}
        ></section> */}
      </div>
    </div>
  );
};

export default UserVerifyEmailPage;
