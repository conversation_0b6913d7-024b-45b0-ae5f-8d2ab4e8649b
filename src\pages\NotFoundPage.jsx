
  import React from "react";
  import Loader from "../components/Loader";
import { AuthContext } from "Src/authContext";
import { useLocation, useNavigate } from "react-router";
  
  const NotFoundPage = () => {
    const { state, dispatch } = React.useContext(AuthContext);

    const [ loading, setLoading ] = React.useState( true );
    const location = useLocation();
    const searchParams = new URLSearchParams(location.search);
    const redirect_uri = searchParams.get("redirect_uri");
    const navigate = useNavigate();
  
    React.useEffect( () => {
      // console.log("state",state);
      // console.log("state",Number(localStorage.getItem("user")));

      {!Number(localStorage.getItem("user"))?navigate("/user/login",{replace:true}):navigate(redirect_uri ) }
      const interval = setTimeout( () => {
        setLoading( false );
      }, 5000 );
      // return () => clearInterval(interval);
    }, [] );
  
    return (
      <>
        { loading ? (
          <Loader />
        ) : (
          <div className="w-full flex justify-center items-center text-7xl h-screen text-gray-700 ">
            Not Found
          </div>
        ) }
      </>
    );
  };
  
  export default NotFoundPage;
  
  
  