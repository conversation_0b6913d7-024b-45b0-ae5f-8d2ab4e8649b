import{_ as F}from"../@emotion/react-32889a6e.js";import{r as O,R as mr,d as Sn}from"../vendor-b0222800.js";import{_ as Je}from"../@material-ui/core-b35124d7.js";var $n={exports:{}},Qe={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vr=O,hr=Symbol.for("react.element"),gr=Symbol.for("react.fragment"),yr=Object.prototype.hasOwnProperty,wr=vr.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,br={key:!0,ref:!0,__self:!0,__source:!0};function Cn(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)yr.call(t,r)&&!br.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:hr,type:e,key:i,ref:s,props:o,_owner:wr.current}}Qe.Fragment=gr;Qe.jsx=Cn;Qe.jsxs=Cn;$n.exports=Qe;var J=$n.exports;function le(e){if(typeof e!="object"||e===null)return!1;const t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function An(e){if(!le(e))return e;const t={};return Object.keys(e).forEach(n=>{t[n]=An(e[n])}),t}function Tn(e,t,n={clone:!0}){const r=n.clone?F({},e):e;return le(e)&&le(t)&&Object.keys(t).forEach(o=>{le(t[o])&&Object.prototype.hasOwnProperty.call(e,o)&&le(e[o])?r[o]=Tn(e[o],t[o],n):n.clone?r[o]=le(t[o])?An(t[o]):t[o]:r[o]=t[o]}),r}const ls=Object.freeze(Object.defineProperty({__proto__:null,default:Tn,isPlainObject:le},Symbol.toStringTag,{value:"Module"}));function Ln(e){let t="https://mui.com/production-error/?code="+e;for(let n=1;n<arguments.length;n+=1)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified MUI error #"+e+"; visit "+t+" for the full message."}const fs=Object.freeze(Object.defineProperty({__proto__:null,default:Ln},Symbol.toStringTag,{value:"Module"}));var Mn={exports:{}},D={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var $t=Symbol.for("react.element"),Ct=Symbol.for("react.portal"),et=Symbol.for("react.fragment"),tt=Symbol.for("react.strict_mode"),nt=Symbol.for("react.profiler"),rt=Symbol.for("react.provider"),ot=Symbol.for("react.context"),xr=Symbol.for("react.server_context"),it=Symbol.for("react.forward_ref"),st=Symbol.for("react.suspense"),at=Symbol.for("react.suspense_list"),ct=Symbol.for("react.memo"),lt=Symbol.for("react.lazy"),Or=Symbol.for("react.offscreen"),Dn;Dn=Symbol.for("react.module.reference");function X(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case $t:switch(e=e.type,e){case et:case nt:case tt:case st:case at:return e;default:switch(e=e&&e.$$typeof,e){case xr:case ot:case it:case lt:case ct:case rt:return e;default:return t}}case Ct:return t}}}D.ContextConsumer=ot;D.ContextProvider=rt;D.Element=$t;D.ForwardRef=it;D.Fragment=et;D.Lazy=lt;D.Memo=ct;D.Portal=Ct;D.Profiler=nt;D.StrictMode=tt;D.Suspense=st;D.SuspenseList=at;D.isAsyncMode=function(){return!1};D.isConcurrentMode=function(){return!1};D.isContextConsumer=function(e){return X(e)===ot};D.isContextProvider=function(e){return X(e)===rt};D.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===$t};D.isForwardRef=function(e){return X(e)===it};D.isFragment=function(e){return X(e)===et};D.isLazy=function(e){return X(e)===lt};D.isMemo=function(e){return X(e)===ct};D.isPortal=function(e){return X(e)===Ct};D.isProfiler=function(e){return X(e)===nt};D.isStrictMode=function(e){return X(e)===tt};D.isSuspense=function(e){return X(e)===st};D.isSuspenseList=function(e){return X(e)===at};D.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===et||e===nt||e===tt||e===st||e===at||e===Or||typeof e=="object"&&e!==null&&(e.$$typeof===lt||e.$$typeof===ct||e.$$typeof===rt||e.$$typeof===ot||e.$$typeof===it||e.$$typeof===Dn||e.getModuleId!==void 0)};D.typeOf=X;Mn.exports=D;var Zt=Mn.exports;const Er=/^\s*function(?:\s|\s*\/\*.*\*\/\s*)+([^(\s/]*)\s*/;function jn(e){const t=`${e}`.match(Er);return t&&t[1]||""}function kn(e,t=""){return e.displayName||e.name||jn(e)||t}function Jt(e,t,n){const r=kn(t);return e.displayName||(r!==""?`${n}(${r})`:n)}function Rr(e){if(e!=null){if(typeof e=="string")return e;if(typeof e=="function")return kn(e,"Component");if(typeof e=="object")switch(e.$$typeof){case Zt.ForwardRef:return Jt(e,e.render,"ForwardRef");case Zt.Memo:return Jt(e,e.type,"memo");default:return}}}const us=Object.freeze(Object.defineProperty({__proto__:null,default:Rr,getFunctionName:jn},Symbol.toStringTag,{value:"Module"}));function Pr(e){if(typeof e!="string")throw new Error(Ln(7));return e.charAt(0).toUpperCase()+e.slice(1)}const ds=Object.freeze(Object.defineProperty({__proto__:null,default:Pr},Symbol.toStringTag,{value:"Module"}));function ps(...e){return e.reduce((t,n)=>n==null?t:function(...o){t.apply(this,o),n.apply(this,o)},()=>{})}function Sr(e,t=166){let n;function r(...o){const i=()=>{e.apply(this,o)};clearTimeout(n),n=setTimeout(i,t)}return r.clear=()=>{clearTimeout(n)},r}function ms(e,t){return()=>null}function vs(e,t){var n,r;return O.isValidElement(e)&&t.indexOf((n=e.type.muiName)!=null?n:(r=e.type)==null||(r=r._payload)==null||(r=r.value)==null?void 0:r.muiName)!==-1}function he(e){return e&&e.ownerDocument||document}function Qt(e){return he(e).defaultView||window}function hs(e,t){return()=>null}function xt(e,t){typeof e=="function"?e(t):e&&(e.current=t)}const $r=typeof window<"u"?O.useLayoutEffect:O.useEffect,de=$r;let en=0;function Cr(e){const[t,n]=O.useState(e),r=e||t;return O.useEffect(()=>{t==null&&(en+=1,n(`mui-${en}`))},[t]),r}const tn=mr["useId".toString()];function gs(e){if(tn!==void 0){const t=tn();return e??t}return Cr(e)}function ys(e,t,n,r,o){return null}function ws({controlled:e,default:t,name:n,state:r="value"}){const{current:o}=O.useRef(e!==void 0),[i,s]=O.useState(t),a=o?e:i,c=O.useCallback(l=>{o||s(l)},[]);return[a,c]}function Ar(e){const t=O.useRef(e);return de(()=>{t.current=e}),O.useRef((...n)=>(0,t.current)(...n)).current}function ye(...e){return O.useMemo(()=>e.every(t=>t==null)?null:t=>{e.forEach(n=>{xt(n,t)})},e)}const nn={};function Tr(e,t){const n=O.useRef(nn);return n.current===nn&&(n.current=e(t)),n}const Lr=[];function Mr(e){O.useEffect(e,Lr)}class ft{constructor(){this.currentId=null,this.clear=()=>{this.currentId!==null&&(clearTimeout(this.currentId),this.currentId=null)},this.disposeEffect=()=>this.clear}static create(){return new ft}start(t,n){this.clear(),this.currentId=setTimeout(()=>{this.currentId=null,n()},t)}}function bs(){const e=Tr(ft.create).current;return Mr(e.disposeEffect),e}let ut=!0,Ot=!1;const Dr=new ft,jr={text:!0,search:!0,url:!0,tel:!0,email:!0,password:!0,number:!0,date:!0,month:!0,week:!0,time:!0,datetime:!0,"datetime-local":!0};function kr(e){const{type:t,tagName:n}=e;return!!(n==="INPUT"&&jr[t]&&!e.readOnly||n==="TEXTAREA"&&!e.readOnly||e.isContentEditable)}function Fr(e){e.metaKey||e.altKey||e.ctrlKey||(ut=!0)}function bt(){ut=!1}function Nr(){this.visibilityState==="hidden"&&Ot&&(ut=!0)}function Br(e){e.addEventListener("keydown",Fr,!0),e.addEventListener("mousedown",bt,!0),e.addEventListener("pointerdown",bt,!0),e.addEventListener("touchstart",bt,!0),e.addEventListener("visibilitychange",Nr,!0)}function _r(e){const{target:t}=e;try{return t.matches(":focus-visible")}catch{}return ut||kr(t)}function xs(){const e=O.useCallback(o=>{o!=null&&Br(o.ownerDocument)},[]),t=O.useRef(!1);function n(){return t.current?(Ot=!0,Dr.start(100,()=>{Ot=!1}),t.current=!1,!0):!1}function r(o){return _r(o)?(t.current=!0,!0):!1}return{isFocusVisibleRef:t,onFocus:r,onBlur:n,ref:e}}function Os(e){const t=e.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}function Wr(e,t){const n=F({},t);return Object.keys(e).forEach(r=>{if(r.toString().match(/^(components|slots)$/))n[r]=F({},e[r],n[r]);else if(r.toString().match(/^(componentsProps|slotProps)$/)){const o=e[r]||{},i=t[r];n[r]={},!i||!Object.keys(i)?n[r]=o:!o||!Object.keys(o)?n[r]=i:(n[r]=F({},i),Object.keys(o).forEach(s=>{n[r][s]=Wr(o[s],i[s])}))}else n[r]===void 0&&(n[r]=e[r])}),n}function Hr(e,t,n=void 0){const r={};return Object.keys(e).forEach(o=>{r[o]=e[o].reduce((i,s)=>{if(s){const a=t(s);a!==""&&i.push(a),n&&n[s]&&i.push(n[s])}return i},[]).join(" ")}),r}const rn=e=>e,Ir=()=>{let e=rn;return{configure(t){e=t},generate(t){return e(t)},reset(){e=rn}}},Vr=Ir(),zr=Vr,Fn={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function Ur(e,t,n="Mui"){const r=Fn[t];return r?`${n}-${r}`:`${zr.generate(e)}-${t}`}function Es(e,t,n="Mui"){const r={};return t.forEach(o=>{r[o]=Ur(e,o,n)}),r}function qr(e,t=Number.MIN_SAFE_INTEGER,n=Number.MAX_SAFE_INTEGER){return Math.max(t,Math.min(e,n))}const Rs=Object.freeze(Object.defineProperty({__proto__:null,default:qr},Symbol.toStringTag,{value:"Module"}));function on(e){return e.substring(2).toLowerCase()}function Xr(e,t){return t.documentElement.clientWidth<e.clientX||t.documentElement.clientHeight<e.clientY}function Ps(e){const{children:t,disableReactTree:n=!1,mouseEvent:r="onClick",onClickAway:o,touchEvent:i="onTouchEnd"}=e,s=O.useRef(!1),a=O.useRef(null),c=O.useRef(!1),l=O.useRef(!1);O.useEffect(()=>(setTimeout(()=>{c.current=!0},0),()=>{c.current=!1}),[]);const f=ye(t.ref,a),d=Ar(m=>{const p=l.current;l.current=!1;const v=he(a.current);if(!c.current||!a.current||"clientX"in m&&Xr(m,v))return;if(s.current){s.current=!1;return}let g;m.composedPath?g=m.composedPath().indexOf(a.current)>-1:g=!v.documentElement.contains(m.target)||a.current.contains(m.target),!g&&(n||!p)&&o(m)}),h=m=>p=>{l.current=!0;const v=t.props[m];v&&v(p)},u={ref:f};return i!==!1&&(u[i]=h(i)),O.useEffect(()=>{if(i!==!1){const m=on(i),p=he(a.current),v=()=>{s.current=!0};return p.addEventListener(m,d),p.addEventListener("touchmove",v),()=>{p.removeEventListener(m,d),p.removeEventListener("touchmove",v)}}},[d,i]),r!==!1&&(u[r]=h(r)),O.useEffect(()=>{if(r!==!1){const m=on(r),p=he(a.current);return p.addEventListener(m,d),()=>{p.removeEventListener(m,d)}}},[d,r]),J.jsx(O.Fragment,{children:O.cloneElement(t,u)})}function Yr(e){return typeof e=="string"}function Gr(e,t,n){return e===void 0||Yr(e)?t:F({},t,{ownerState:F({},t.ownerState,n)})}const Kr={disableDefaultClasses:!1},Zr=O.createContext(Kr);function Jr(e){const{disableDefaultClasses:t}=O.useContext(Zr);return n=>t?"":e(n)}function Qr(e,t=[]){if(e===void 0)return{};const n={};return Object.keys(e).filter(r=>r.match(/^on[A-Z]/)&&typeof e[r]=="function"&&!t.includes(r)).forEach(r=>{n[r]=e[r]}),n}function eo(e,t,n){return typeof e=="function"?e(t,n):e}function Nn(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=Nn(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function sn(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=Nn(e))&&(r&&(r+=" "),r+=t);return r}function an(e){if(e===void 0)return{};const t={};return Object.keys(e).filter(n=>!(n.match(/^on[A-Z]/)&&typeof e[n]=="function")).forEach(n=>{t[n]=e[n]}),t}function to(e){const{getSlotProps:t,additionalProps:n,externalSlotProps:r,externalForwardedProps:o,className:i}=e;if(!t){const u=sn(n==null?void 0:n.className,i,o==null?void 0:o.className,r==null?void 0:r.className),m=F({},n==null?void 0:n.style,o==null?void 0:o.style,r==null?void 0:r.style),p=F({},n,o,r);return u.length>0&&(p.className=u),Object.keys(m).length>0&&(p.style=m),{props:p,internalRef:void 0}}const s=Qr(F({},o,r)),a=an(r),c=an(o),l=t(s),f=sn(l==null?void 0:l.className,n==null?void 0:n.className,i,o==null?void 0:o.className,r==null?void 0:r.className),d=F({},l==null?void 0:l.style,n==null?void 0:n.style,o==null?void 0:o.style,r==null?void 0:r.style),h=F({},l,n,c,a);return f.length>0&&(h.className=f),Object.keys(d).length>0&&(h.style=d),{props:h,internalRef:l.ref}}const no=["elementType","externalSlotProps","ownerState","skipResolvingSlotProps"];function ro(e){var t;const{elementType:n,externalSlotProps:r,ownerState:o,skipResolvingSlotProps:i=!1}=e,s=Je(e,no),a=i?{}:eo(r,o),{props:c,internalRef:l}=to(F({},s,{externalSlotProps:a})),f=ye(l,a==null?void 0:a.ref,(t=e.additionalProps)==null?void 0:t.ref);return Gr(n,F({},c,{ref:f}),o)}const Bn="base";function oo(e){return`${Bn}--${e}`}function io(e,t){return`${Bn}-${e}-${t}`}function _n(e,t){const n=Fn[t];return n?oo(n):io(e,t)}function so(e,t){const n={};return t.forEach(r=>{n[r]=_n(e,r)}),n}const ao=["top","right","bottom","left"],cn=["start","end"],ln=ao.reduce((e,t)=>e.concat(t,t+"-"+cn[0],t+"-"+cn[1]),[]),we=Math.min,fe=Math.max,qe=Math.round,He=Math.floor,oe=e=>({x:e,y:e}),co={left:"right",right:"left",bottom:"top",top:"bottom"},lo={start:"end",end:"start"};function Et(e,t,n){return fe(e,we(t,n))}function Pe(e,t){return typeof e=="function"?e(t):e}function Q(e){return e.split("-")[0]}function Y(e){return e.split("-")[1]}function Wn(e){return e==="x"?"y":"x"}function At(e){return e==="y"?"height":"width"}function dt(e){return["top","bottom"].includes(Q(e))?"y":"x"}function Tt(e){return Wn(dt(e))}function Hn(e,t,n){n===void 0&&(n=!1);const r=Y(e),o=Tt(e),i=At(o);let s=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(s=Ye(s)),[s,Ye(s)]}function fo(e){const t=Ye(e);return[Xe(e),t,Xe(t)]}function Xe(e){return e.replace(/start|end/g,t=>lo[t])}function uo(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:s;default:return[]}}function po(e,t,n,r){const o=Y(e);let i=uo(Q(e),n==="start",r);return o&&(i=i.map(s=>s+"-"+o),t&&(i=i.concat(i.map(Xe)))),i}function Ye(e){return e.replace(/left|right|bottom|top/g,t=>co[t])}function mo(e){return{top:0,right:0,bottom:0,left:0,...e}}function In(e){return typeof e!="number"?mo(e):{top:e,right:e,bottom:e,left:e}}function Ge(e){return{...e,top:e.y,left:e.x,right:e.x+e.width,bottom:e.y+e.height}}function fn(e,t,n){let{reference:r,floating:o}=e;const i=dt(t),s=Tt(t),a=At(s),c=Q(t),l=i==="y",f=r.x+r.width/2-o.width/2,d=r.y+r.height/2-o.height/2,h=r[a]/2-o[a]/2;let u;switch(c){case"top":u={x:f,y:r.y-o.height};break;case"bottom":u={x:f,y:r.y+r.height};break;case"right":u={x:r.x+r.width,y:d};break;case"left":u={x:r.x-o.width,y:d};break;default:u={x:r.x,y:r.y}}switch(Y(t)){case"start":u[s]-=h*(n&&l?-1:1);break;case"end":u[s]+=h*(n&&l?-1:1);break}return u}const vo=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:s}=n,a=i.filter(Boolean),c=await(s.isRTL==null?void 0:s.isRTL(t));let l=await s.getElementRects({reference:e,floating:t,strategy:o}),{x:f,y:d}=fn(l,r,c),h=r,u={},m=0;for(let p=0;p<a.length;p++){const{name:v,fn:g}=a[p],{x:w,y:x,data:y,reset:b}=await g({x:f,y:d,initialPlacement:r,placement:h,strategy:o,middlewareData:u,rects:l,platform:s,elements:{reference:e,floating:t}});f=w??f,d=x??d,u={...u,[v]:{...u[v],...y}},b&&m<=50&&(m++,typeof b=="object"&&(b.placement&&(h=b.placement),b.rects&&(l=b.rects===!0?await s.getElementRects({reference:e,floating:t,strategy:o}):b.rects),{x:f,y:d}=fn(l,h,c)),p=-1)}return{x:f,y:d,placement:h,strategy:o,middlewareData:u}};async function Lt(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:i,rects:s,elements:a,strategy:c}=e,{boundary:l="clippingAncestors",rootBoundary:f="viewport",elementContext:d="floating",altBoundary:h=!1,padding:u=0}=Pe(t,e),m=In(u),v=a[h?d==="floating"?"reference":"floating":d],g=Ge(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(v)))==null||n?v:v.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(a.floating)),boundary:l,rootBoundary:f,strategy:c})),w=d==="floating"?{...s.floating,x:r,y:o}:s.reference,x=await(i.getOffsetParent==null?void 0:i.getOffsetParent(a.floating)),y=await(i.isElement==null?void 0:i.isElement(x))?await(i.getScale==null?void 0:i.getScale(x))||{x:1,y:1}:{x:1,y:1},b=Ge(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:x,strategy:c}):w);return{top:(g.top-b.top+m.top)/y.y,bottom:(b.bottom-g.bottom+m.bottom)/y.y,left:(g.left-b.left+m.left)/y.x,right:(b.right-g.right+m.right)/y.x}}const ho=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:s,elements:a,middlewareData:c}=t,{element:l,padding:f=0}=Pe(e,t)||{};if(l==null)return{};const d=In(f),h={x:n,y:r},u=Tt(o),m=At(u),p=await s.getDimensions(l),v=u==="y",g=v?"top":"left",w=v?"bottom":"right",x=v?"clientHeight":"clientWidth",y=i.reference[m]+i.reference[u]-h[u]-i.floating[m],b=h[u]-i.reference[u],E=await(s.getOffsetParent==null?void 0:s.getOffsetParent(l));let R=E?E[x]:0;(!R||!await(s.isElement==null?void 0:s.isElement(E)))&&(R=a.floating[x]||i.floating[m]);const C=y/2-b/2,S=R/2-p[m]/2-1,P=we(d[g],S),A=we(d[w],S),T=P,M=R-p[m]-A,k=R/2-p[m]/2+C,j=Et(T,k,M),L=!c.arrow&&Y(o)!=null&&k!==j&&i.reference[m]/2-(k<T?P:A)-p[m]/2<0,$=L?k<T?k-T:k-M:0;return{[u]:h[u]+$,data:{[u]:j,centerOffset:k-j-$,...L&&{alignmentOffset:$}},reset:L}}});function go(e,t,n){return(e?[...n.filter(o=>Y(o)===e),...n.filter(o=>Y(o)!==e)]:n.filter(o=>Q(o)===o)).filter(o=>e?Y(o)===e||(t?Xe(o)!==o:!1):!0)}const yo=function(e){return e===void 0&&(e={}),{name:"autoPlacement",options:e,async fn(t){var n,r,o;const{rects:i,middlewareData:s,placement:a,platform:c,elements:l}=t,{crossAxis:f=!1,alignment:d,allowedPlacements:h=ln,autoAlignment:u=!0,...m}=Pe(e,t),p=d!==void 0||h===ln?go(d||null,u,h):h,v=await Lt(t,m),g=((n=s.autoPlacement)==null?void 0:n.index)||0,w=p[g];if(w==null)return{};const x=Hn(w,i,await(c.isRTL==null?void 0:c.isRTL(l.floating)));if(a!==w)return{reset:{placement:p[0]}};const y=[v[Q(w)],v[x[0]],v[x[1]]],b=[...((r=s.autoPlacement)==null?void 0:r.overflows)||[],{placement:w,overflows:y}],E=p[g+1];if(E)return{data:{index:g+1,overflows:b},reset:{placement:E}};const R=b.map(P=>{const A=Y(P.placement);return[P.placement,A&&f?P.overflows.slice(0,2).reduce((T,M)=>T+M,0):P.overflows[0],P.overflows]}).sort((P,A)=>P[1]-A[1]),S=((o=R.filter(P=>P[2].slice(0,Y(P[0])?2:3).every(A=>A<=0))[0])==null?void 0:o[0])||R[0][0];return S!==a?{data:{index:g+1,overflows:b},reset:{placement:S}}:{}}}},wo=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:s,initialPlacement:a,platform:c,elements:l}=t,{mainAxis:f=!0,crossAxis:d=!0,fallbackPlacements:h,fallbackStrategy:u="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:p=!0,...v}=Pe(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const g=Q(o),w=Q(a)===a,x=await(c.isRTL==null?void 0:c.isRTL(l.floating)),y=h||(w||!p?[Ye(a)]:fo(a));!h&&m!=="none"&&y.push(...po(a,p,m,x));const b=[a,...y],E=await Lt(t,v),R=[];let C=((r=i.flip)==null?void 0:r.overflows)||[];if(f&&R.push(E[g]),d){const T=Hn(o,s,x);R.push(E[T[0]],E[T[1]])}if(C=[...C,{placement:o,overflows:R}],!R.every(T=>T<=0)){var S,P;const T=(((S=i.flip)==null?void 0:S.index)||0)+1,M=b[T];if(M)return{data:{index:T,overflows:C},reset:{placement:M}};let k=(P=C.filter(j=>j.overflows[0]<=0).sort((j,L)=>j.overflows[1]-L.overflows[1])[0])==null?void 0:P.placement;if(!k)switch(u){case"bestFit":{var A;const j=(A=C.map(L=>[L.placement,L.overflows.filter($=>$>0).reduce(($,N)=>$+N,0)]).sort((L,$)=>L[1]-$[1])[0])==null?void 0:A[0];j&&(k=j);break}case"initialPlacement":k=a;break}if(o!==k)return{reset:{placement:k}}}return{}}}};async function bo(e,t){const{placement:n,platform:r,elements:o}=e,i=await(r.isRTL==null?void 0:r.isRTL(o.floating)),s=Q(n),a=Y(n),c=dt(n)==="y",l=["left","top"].includes(s)?-1:1,f=i&&c?-1:1,d=Pe(t,e);let{mainAxis:h,crossAxis:u,alignmentAxis:m}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...d};return a&&typeof m=="number"&&(u=a==="end"?m*-1:m),c?{x:u*f,y:h*l}:{x:h*l,y:u*f}}const Ss=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:s,middlewareData:a}=t,c=await bo(t,e);return s===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:o+c.x,y:i+c.y,data:{...c,placement:s}}}}},xo=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:s=!1,limiter:a={fn:v=>{let{x:g,y:w}=v;return{x:g,y:w}}},...c}=Pe(e,t),l={x:n,y:r},f=await Lt(t,c),d=dt(Q(o)),h=Wn(d);let u=l[h],m=l[d];if(i){const v=h==="y"?"top":"left",g=h==="y"?"bottom":"right",w=u+f[v],x=u-f[g];u=Et(w,u,x)}if(s){const v=d==="y"?"top":"left",g=d==="y"?"bottom":"right",w=m+f[v],x=m-f[g];m=Et(w,m,x)}const p=a.fn({...t,[h]:u,[d]:m});return{...p,data:{x:p.x-n,y:p.y-r}}}}};function ie(e){return Vn(e)?(e.nodeName||"").toLowerCase():"#document"}function H(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function ne(e){var t;return(t=(Vn(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Vn(e){return e instanceof Node||e instanceof H(e).Node}function ee(e){return e instanceof Element||e instanceof H(e).Element}function K(e){return e instanceof HTMLElement||e instanceof H(e).HTMLElement}function un(e){return typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof H(e).ShadowRoot}function je(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=z(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function Oo(e){return["table","td","th"].includes(ie(e))}function Mt(e){const t=Dt(),n=z(e);return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function Eo(e){let t=be(e);for(;K(t)&&!pt(t);){if(Mt(t))return t;t=be(t)}return null}function Dt(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function pt(e){return["html","body","#document"].includes(ie(e))}function z(e){return H(e).getComputedStyle(e)}function mt(e){return ee(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function be(e){if(ie(e)==="html")return e;const t=e.assignedSlot||e.parentNode||un(e)&&e.host||ne(e);return un(t)?t.host:t}function zn(e){const t=be(e);return pt(t)?e.ownerDocument?e.ownerDocument.body:e.body:K(t)&&je(t)?t:zn(t)}function Le(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=zn(e),i=o===((r=e.ownerDocument)==null?void 0:r.body),s=H(o);return i?t.concat(s,s.visualViewport||[],je(o)?o:[],s.frameElement&&n?Le(s.frameElement):[]):t.concat(o,Le(o,[],n))}function Un(e){const t=z(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=K(e),i=o?e.offsetWidth:n,s=o?e.offsetHeight:r,a=qe(n)!==i||qe(r)!==s;return a&&(n=i,r=s),{width:n,height:r,$:a}}function jt(e){return ee(e)?e:e.contextElement}function ge(e){const t=jt(e);if(!K(t))return oe(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=Un(t);let s=(i?qe(n.width):n.width)/r,a=(i?qe(n.height):n.height)/o;return(!s||!Number.isFinite(s))&&(s=1),(!a||!Number.isFinite(a))&&(a=1),{x:s,y:a}}const Ro=oe(0);function qn(e){const t=H(e);return!Dt()||!t.visualViewport?Ro:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Po(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==H(e)?!1:t}function pe(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),i=jt(e);let s=oe(1);t&&(r?ee(r)&&(s=ge(r)):s=ge(e));const a=Po(i,n,r)?qn(i):oe(0);let c=(o.left+a.x)/s.x,l=(o.top+a.y)/s.y,f=o.width/s.x,d=o.height/s.y;if(i){const h=H(i),u=r&&ee(r)?H(r):r;let m=h,p=m.frameElement;for(;p&&r&&u!==m;){const v=ge(p),g=p.getBoundingClientRect(),w=z(p),x=g.left+(p.clientLeft+parseFloat(w.paddingLeft))*v.x,y=g.top+(p.clientTop+parseFloat(w.paddingTop))*v.y;c*=v.x,l*=v.y,f*=v.x,d*=v.y,c+=x,l+=y,m=H(p),p=m.frameElement}}return Ge({width:f,height:d,x:c,y:l})}const So=[":popover-open",":modal"];function Xn(e){return So.some(t=>{try{return e.matches(t)}catch{return!1}})}function $o(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i=o==="fixed",s=ne(r),a=t?Xn(t.floating):!1;if(r===s||a&&i)return n;let c={scrollLeft:0,scrollTop:0},l=oe(1);const f=oe(0),d=K(r);if((d||!d&&!i)&&((ie(r)!=="body"||je(s))&&(c=mt(r)),K(r))){const h=pe(r);l=ge(r),f.x=h.x+r.clientLeft,f.y=h.y+r.clientTop}return{width:n.width*l.x,height:n.height*l.y,x:n.x*l.x-c.scrollLeft*l.x+f.x,y:n.y*l.y-c.scrollTop*l.y+f.y}}function Co(e){return Array.from(e.getClientRects())}function Yn(e){return pe(ne(e)).left+mt(e).scrollLeft}function Ao(e){const t=ne(e),n=mt(e),r=e.ownerDocument.body,o=fe(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=fe(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let s=-n.scrollLeft+Yn(e);const a=-n.scrollTop;return z(r).direction==="rtl"&&(s+=fe(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:s,y:a}}function To(e,t){const n=H(e),r=ne(e),o=n.visualViewport;let i=r.clientWidth,s=r.clientHeight,a=0,c=0;if(o){i=o.width,s=o.height;const l=Dt();(!l||l&&t==="fixed")&&(a=o.offsetLeft,c=o.offsetTop)}return{width:i,height:s,x:a,y:c}}function Lo(e,t){const n=pe(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=K(e)?ge(e):oe(1),s=e.clientWidth*i.x,a=e.clientHeight*i.y,c=o*i.x,l=r*i.y;return{width:s,height:a,x:c,y:l}}function dn(e,t,n){let r;if(t==="viewport")r=To(e,n);else if(t==="document")r=Ao(ne(e));else if(ee(t))r=Lo(t,n);else{const o=qn(e);r={...t,x:t.x-o.x,y:t.y-o.y}}return Ge(r)}function Gn(e,t){const n=be(e);return n===t||!ee(n)||pt(n)?!1:z(n).position==="fixed"||Gn(n,t)}function Mo(e,t){const n=t.get(e);if(n)return n;let r=Le(e,[],!1).filter(a=>ee(a)&&ie(a)!=="body"),o=null;const i=z(e).position==="fixed";let s=i?be(e):e;for(;ee(s)&&!pt(s);){const a=z(s),c=Mt(s);!c&&a.position==="fixed"&&(o=null),(i?!c&&!o:!c&&a.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||je(s)&&!c&&Gn(e,s))?r=r.filter(f=>f!==s):o=a,s=be(s)}return t.set(e,r),r}function Do(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const s=[...n==="clippingAncestors"?Mo(t,this._c):[].concat(n),r],a=s[0],c=s.reduce((l,f)=>{const d=dn(t,f,o);return l.top=fe(d.top,l.top),l.right=we(d.right,l.right),l.bottom=we(d.bottom,l.bottom),l.left=fe(d.left,l.left),l},dn(t,a,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}}function jo(e){const{width:t,height:n}=Un(e);return{width:t,height:n}}function ko(e,t,n){const r=K(t),o=ne(t),i=n==="fixed",s=pe(e,!0,i,t);let a={scrollLeft:0,scrollTop:0};const c=oe(0);if(r||!r&&!i)if((ie(t)!=="body"||je(o))&&(a=mt(t)),r){const d=pe(t,!0,i,t);c.x=d.x+t.clientLeft,c.y=d.y+t.clientTop}else o&&(c.x=Yn(o));const l=s.left+a.scrollLeft-c.x,f=s.top+a.scrollTop-c.y;return{x:l,y:f,width:s.width,height:s.height}}function pn(e,t){return!K(e)||z(e).position==="fixed"?null:t?t(e):e.offsetParent}function Kn(e,t){const n=H(e);if(!K(e)||Xn(e))return n;let r=pn(e,t);for(;r&&Oo(r)&&z(r).position==="static";)r=pn(r,t);return r&&(ie(r)==="html"||ie(r)==="body"&&z(r).position==="static"&&!Mt(r))?n:r||Eo(e)||n}const Fo=async function(e){const t=this.getOffsetParent||Kn,n=this.getDimensions;return{reference:ko(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,...await n(e.floating)}}};function No(e){return z(e).direction==="rtl"}const Bo={convertOffsetParentRelativeRectToViewportRelativeRect:$o,getDocumentElement:ne,getClippingRect:Do,getOffsetParent:Kn,getElementRects:Fo,getClientRects:Co,getDimensions:jo,getScale:ge,isElement:ee,isRTL:No};function _o(e,t){let n=null,r;const o=ne(e);function i(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function s(a,c){a===void 0&&(a=!1),c===void 0&&(c=1),i();const{left:l,top:f,width:d,height:h}=e.getBoundingClientRect();if(a||t(),!d||!h)return;const u=He(f),m=He(o.clientWidth-(l+d)),p=He(o.clientHeight-(f+h)),v=He(l),w={rootMargin:-u+"px "+-m+"px "+-p+"px "+-v+"px",threshold:fe(0,we(1,c))||1};let x=!0;function y(b){const E=b[0].intersectionRatio;if(E!==c){if(!x)return s();E?s(!1,E):r=setTimeout(()=>{s(!1,1e-7)},100)}x=!1}try{n=new IntersectionObserver(y,{...w,root:o.ownerDocument})}catch{n=new IntersectionObserver(y,w)}n.observe(e)}return s(!0),i}function $s(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:c=!1}=r,l=jt(e),f=o||i?[...l?Le(l):[],...Le(t)]:[];f.forEach(g=>{o&&g.addEventListener("scroll",n,{passive:!0}),i&&g.addEventListener("resize",n)});const d=l&&a?_o(l,n):null;let h=-1,u=null;s&&(u=new ResizeObserver(g=>{let[w]=g;w&&w.target===l&&u&&(u.unobserve(t),cancelAnimationFrame(h),h=requestAnimationFrame(()=>{var x;(x=u)==null||x.observe(t)})),n()}),l&&!c&&u.observe(l),u.observe(t));let m,p=c?pe(e):null;c&&v();function v(){const g=pe(e);p&&(g.x!==p.x||g.y!==p.y||g.width!==p.width||g.height!==p.height)&&n(),p=g,m=requestAnimationFrame(v)}return n(),()=>{var g;f.forEach(w=>{o&&w.removeEventListener("scroll",n),i&&w.removeEventListener("resize",n)}),d==null||d(),(g=u)==null||g.disconnect(),u=null,c&&cancelAnimationFrame(m)}}const Cs=yo,As=xo,Ts=wo,mn=ho,Wo=(e,t,n)=>{const r=new Map,o={platform:Bo,...n},i={...o.platform,_c:r};return vo(e,t,{...o,platform:i})},Ls=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?mn({element:r.current,padding:o}).fn(n):{}:r?mn({element:r,padding:o}).fn(n):{}}}};var ze=typeof document<"u"?O.useLayoutEffect:O.useEffect;function Ke(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!Ke(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const i=o[r];if(!(i==="_owner"&&e.$$typeof)&&!Ke(e[i],t[i]))return!1}return!0}return e!==e&&t!==t}function Zn(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function vn(e,t){const n=Zn(e);return Math.round(t*n)/n}function hn(e){const t=O.useRef(e);return ze(()=>{t.current=e}),t}function Ms(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:s}={},transform:a=!0,whileElementsMounted:c,open:l}=e,[f,d]=O.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,u]=O.useState(r);Ke(h,r)||u(r);const[m,p]=O.useState(null),[v,g]=O.useState(null),w=O.useCallback($=>{$!==E.current&&(E.current=$,p($))},[]),x=O.useCallback($=>{$!==R.current&&(R.current=$,g($))},[]),y=i||m,b=s||v,E=O.useRef(null),R=O.useRef(null),C=O.useRef(f),S=c!=null,P=hn(c),A=hn(o),T=O.useCallback(()=>{if(!E.current||!R.current)return;const $={placement:t,strategy:n,middleware:h};A.current&&($.platform=A.current),Wo(E.current,R.current,$).then(N=>{const B={...N,isPositioned:!0};M.current&&!Ke(C.current,B)&&(C.current=B,Sn.flushSync(()=>{d(B)}))})},[h,t,n,A]);ze(()=>{l===!1&&C.current.isPositioned&&(C.current.isPositioned=!1,d($=>({...$,isPositioned:!1})))},[l]);const M=O.useRef(!1);ze(()=>(M.current=!0,()=>{M.current=!1}),[]),ze(()=>{if(y&&(E.current=y),b&&(R.current=b),y&&b){if(P.current)return P.current(y,b,T);T()}},[y,b,T,P,S]);const k=O.useMemo(()=>({reference:E,floating:R,setReference:w,setFloating:x}),[w,x]),j=O.useMemo(()=>({reference:y,floating:b}),[y,b]),L=O.useMemo(()=>{const $={position:n,left:0,top:0};if(!j.floating)return $;const N=vn(j.floating,f.x),B=vn(j.floating,f.y);return a?{...$,transform:"translate("+N+"px, "+B+"px)",...Zn(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:N,top:B}},[n,a,j.floating,f.x,f.y]);return O.useMemo(()=>({...f,update:T,refs:k,elements:j,floatingStyles:L}),[f,T,k,j,L])}function Ho(e){return typeof e=="function"?e():e}const Io=O.forwardRef(function(t,n){const{children:r,container:o,disablePortal:i=!1}=t,[s,a]=O.useState(null),c=ye(O.isValidElement(r)?r.ref:null,n);if(de(()=>{i||a(Ho(o)||document.body)},[o,i]),de(()=>{if(s&&!i)return xt(n,s),()=>{xt(n,null)}},[n,s,i]),i){if(O.isValidElement(r)){const l={ref:c};return O.cloneElement(r,l)}return J.jsx(O.Fragment,{children:r})}return J.jsx(O.Fragment,{children:s&&Sn.createPortal(r,s)})});var _="top",U="bottom",q="right",W="left",kt="auto",ke=[_,U,q,W],xe="start",Me="end",Vo="clippingParents",Jn="viewport",Ce="popper",zo="reference",gn=ke.reduce(function(e,t){return e.concat([t+"-"+xe,t+"-"+Me])},[]),Qn=[].concat(ke,[kt]).reduce(function(e,t){return e.concat([t,t+"-"+xe,t+"-"+Me])},[]),Uo="beforeRead",qo="read",Xo="afterRead",Yo="beforeMain",Go="main",Ko="afterMain",Zo="beforeWrite",Jo="write",Qo="afterWrite",ei=[Uo,qo,Xo,Yo,Go,Ko,Zo,Jo,Qo];function Z(e){return e?(e.nodeName||"").toLowerCase():null}function I(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function me(e){var t=I(e).Element;return e instanceof t||e instanceof Element}function V(e){var t=I(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function Ft(e){if(typeof ShadowRoot>"u")return!1;var t=I(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function ti(e){var t=e.state;Object.keys(t.elements).forEach(function(n){var r=t.styles[n]||{},o=t.attributes[n]||{},i=t.elements[n];!V(i)||!Z(i)||(Object.assign(i.style,r),Object.keys(o).forEach(function(s){var a=o[s];a===!1?i.removeAttribute(s):i.setAttribute(s,a===!0?"":a)}))})}function ni(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(r){var o=t.elements[r],i=t.attributes[r]||{},s=Object.keys(t.styles.hasOwnProperty(r)?t.styles[r]:n[r]),a=s.reduce(function(c,l){return c[l]="",c},{});!V(o)||!Z(o)||(Object.assign(o.style,a),Object.keys(i).forEach(function(c){o.removeAttribute(c)}))})}}const ri={name:"applyStyles",enabled:!0,phase:"write",fn:ti,effect:ni,requires:["computeStyles"]};function G(e){return e.split("-")[0]}var ue=Math.max,Ze=Math.min,Oe=Math.round;function Rt(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function er(){return!/^((?!chrome|android).)*safari/i.test(Rt())}function Ee(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!1);var r=e.getBoundingClientRect(),o=1,i=1;t&&V(e)&&(o=e.offsetWidth>0&&Oe(r.width)/e.offsetWidth||1,i=e.offsetHeight>0&&Oe(r.height)/e.offsetHeight||1);var s=me(e)?I(e):window,a=s.visualViewport,c=!er()&&n,l=(r.left+(c&&a?a.offsetLeft:0))/o,f=(r.top+(c&&a?a.offsetTop:0))/i,d=r.width/o,h=r.height/i;return{width:d,height:h,top:f,right:l+d,bottom:f+h,left:l,x:l,y:f}}function Nt(e){var t=Ee(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function tr(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&Ft(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function te(e){return I(e).getComputedStyle(e)}function oi(e){return["table","td","th"].indexOf(Z(e))>=0}function se(e){return((me(e)?e.ownerDocument:e.document)||window.document).documentElement}function vt(e){return Z(e)==="html"?e:e.assignedSlot||e.parentNode||(Ft(e)?e.host:null)||se(e)}function yn(e){return!V(e)||te(e).position==="fixed"?null:e.offsetParent}function ii(e){var t=/firefox/i.test(Rt()),n=/Trident/i.test(Rt());if(n&&V(e)){var r=te(e);if(r.position==="fixed")return null}var o=vt(e);for(Ft(o)&&(o=o.host);V(o)&&["html","body"].indexOf(Z(o))<0;){var i=te(o);if(i.transform!=="none"||i.perspective!=="none"||i.contain==="paint"||["transform","perspective"].indexOf(i.willChange)!==-1||t&&i.willChange==="filter"||t&&i.filter&&i.filter!=="none")return o;o=o.parentNode}return null}function Fe(e){for(var t=I(e),n=yn(e);n&&oi(n)&&te(n).position==="static";)n=yn(n);return n&&(Z(n)==="html"||Z(n)==="body"&&te(n).position==="static")?t:n||ii(e)||t}function Bt(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Ae(e,t,n){return ue(e,Ze(t,n))}function si(e,t,n){var r=Ae(e,t,n);return r>n?n:r}function nr(){return{top:0,right:0,bottom:0,left:0}}function rr(e){return Object.assign({},nr(),e)}function or(e,t){return t.reduce(function(n,r){return n[r]=e,n},{})}var ai=function(t,n){return t=typeof t=="function"?t(Object.assign({},n.rects,{placement:n.placement})):t,rr(typeof t!="number"?t:or(t,ke))};function ci(e){var t,n=e.state,r=e.name,o=e.options,i=n.elements.arrow,s=n.modifiersData.popperOffsets,a=G(n.placement),c=Bt(a),l=[W,q].indexOf(a)>=0,f=l?"height":"width";if(!(!i||!s)){var d=ai(o.padding,n),h=Nt(i),u=c==="y"?_:W,m=c==="y"?U:q,p=n.rects.reference[f]+n.rects.reference[c]-s[c]-n.rects.popper[f],v=s[c]-n.rects.reference[c],g=Fe(i),w=g?c==="y"?g.clientHeight||0:g.clientWidth||0:0,x=p/2-v/2,y=d[u],b=w-h[f]-d[m],E=w/2-h[f]/2+x,R=Ae(y,E,b),C=c;n.modifiersData[r]=(t={},t[C]=R,t.centerOffset=R-E,t)}}function li(e){var t=e.state,n=e.options,r=n.element,o=r===void 0?"[data-popper-arrow]":r;o!=null&&(typeof o=="string"&&(o=t.elements.popper.querySelector(o),!o)||tr(t.elements.popper,o)&&(t.elements.arrow=o))}const fi={name:"arrow",enabled:!0,phase:"main",fn:ci,effect:li,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Re(e){return e.split("-")[1]}var ui={top:"auto",right:"auto",bottom:"auto",left:"auto"};function di(e,t){var n=e.x,r=e.y,o=t.devicePixelRatio||1;return{x:Oe(n*o)/o||0,y:Oe(r*o)/o||0}}function wn(e){var t,n=e.popper,r=e.popperRect,o=e.placement,i=e.variation,s=e.offsets,a=e.position,c=e.gpuAcceleration,l=e.adaptive,f=e.roundOffsets,d=e.isFixed,h=s.x,u=h===void 0?0:h,m=s.y,p=m===void 0?0:m,v=typeof f=="function"?f({x:u,y:p}):{x:u,y:p};u=v.x,p=v.y;var g=s.hasOwnProperty("x"),w=s.hasOwnProperty("y"),x=W,y=_,b=window;if(l){var E=Fe(n),R="clientHeight",C="clientWidth";if(E===I(n)&&(E=se(n),te(E).position!=="static"&&a==="absolute"&&(R="scrollHeight",C="scrollWidth")),E=E,o===_||(o===W||o===q)&&i===Me){y=U;var S=d&&E===b&&b.visualViewport?b.visualViewport.height:E[R];p-=S-r.height,p*=c?1:-1}if(o===W||(o===_||o===U)&&i===Me){x=q;var P=d&&E===b&&b.visualViewport?b.visualViewport.width:E[C];u-=P-r.width,u*=c?1:-1}}var A=Object.assign({position:a},l&&ui),T=f===!0?di({x:u,y:p},I(n)):{x:u,y:p};if(u=T.x,p=T.y,c){var M;return Object.assign({},A,(M={},M[y]=w?"0":"",M[x]=g?"0":"",M.transform=(b.devicePixelRatio||1)<=1?"translate("+u+"px, "+p+"px)":"translate3d("+u+"px, "+p+"px, 0)",M))}return Object.assign({},A,(t={},t[y]=w?p+"px":"",t[x]=g?u+"px":"",t.transform="",t))}function pi(e){var t=e.state,n=e.options,r=n.gpuAcceleration,o=r===void 0?!0:r,i=n.adaptive,s=i===void 0?!0:i,a=n.roundOffsets,c=a===void 0?!0:a,l={placement:G(t.placement),variation:Re(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:o,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,wn(Object.assign({},l,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:s,roundOffsets:c})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,wn(Object.assign({},l,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:c})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}const mi={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:pi,data:{}};var Ie={passive:!0};function vi(e){var t=e.state,n=e.instance,r=e.options,o=r.scroll,i=o===void 0?!0:o,s=r.resize,a=s===void 0?!0:s,c=I(t.elements.popper),l=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&l.forEach(function(f){f.addEventListener("scroll",n.update,Ie)}),a&&c.addEventListener("resize",n.update,Ie),function(){i&&l.forEach(function(f){f.removeEventListener("scroll",n.update,Ie)}),a&&c.removeEventListener("resize",n.update,Ie)}}const hi={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:vi,data:{}};var gi={left:"right",right:"left",bottom:"top",top:"bottom"};function Ue(e){return e.replace(/left|right|bottom|top/g,function(t){return gi[t]})}var yi={start:"end",end:"start"};function bn(e){return e.replace(/start|end/g,function(t){return yi[t]})}function _t(e){var t=I(e),n=t.pageXOffset,r=t.pageYOffset;return{scrollLeft:n,scrollTop:r}}function Wt(e){return Ee(se(e)).left+_t(e).scrollLeft}function wi(e,t){var n=I(e),r=se(e),o=n.visualViewport,i=r.clientWidth,s=r.clientHeight,a=0,c=0;if(o){i=o.width,s=o.height;var l=er();(l||!l&&t==="fixed")&&(a=o.offsetLeft,c=o.offsetTop)}return{width:i,height:s,x:a+Wt(e),y:c}}function bi(e){var t,n=se(e),r=_t(e),o=(t=e.ownerDocument)==null?void 0:t.body,i=ue(n.scrollWidth,n.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),s=ue(n.scrollHeight,n.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),a=-r.scrollLeft+Wt(e),c=-r.scrollTop;return te(o||n).direction==="rtl"&&(a+=ue(n.clientWidth,o?o.clientWidth:0)-i),{width:i,height:s,x:a,y:c}}function Ht(e){var t=te(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+r)}function ir(e){return["html","body","#document"].indexOf(Z(e))>=0?e.ownerDocument.body:V(e)&&Ht(e)?e:ir(vt(e))}function Te(e,t){var n;t===void 0&&(t=[]);var r=ir(e),o=r===((n=e.ownerDocument)==null?void 0:n.body),i=I(r),s=o?[i].concat(i.visualViewport||[],Ht(r)?r:[]):r,a=t.concat(s);return o?a:a.concat(Te(vt(s)))}function Pt(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function xi(e,t){var n=Ee(e,!1,t==="fixed");return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}function xn(e,t,n){return t===Jn?Pt(wi(e,n)):me(t)?xi(t,n):Pt(bi(se(e)))}function Oi(e){var t=Te(vt(e)),n=["absolute","fixed"].indexOf(te(e).position)>=0,r=n&&V(e)?Fe(e):e;return me(r)?t.filter(function(o){return me(o)&&tr(o,r)&&Z(o)!=="body"}):[]}function Ei(e,t,n,r){var o=t==="clippingParents"?Oi(e):[].concat(t),i=[].concat(o,[n]),s=i[0],a=i.reduce(function(c,l){var f=xn(e,l,r);return c.top=ue(f.top,c.top),c.right=Ze(f.right,c.right),c.bottom=Ze(f.bottom,c.bottom),c.left=ue(f.left,c.left),c},xn(e,s,r));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function sr(e){var t=e.reference,n=e.element,r=e.placement,o=r?G(r):null,i=r?Re(r):null,s=t.x+t.width/2-n.width/2,a=t.y+t.height/2-n.height/2,c;switch(o){case _:c={x:s,y:t.y-n.height};break;case U:c={x:s,y:t.y+t.height};break;case q:c={x:t.x+t.width,y:a};break;case W:c={x:t.x-n.width,y:a};break;default:c={x:t.x,y:t.y}}var l=o?Bt(o):null;if(l!=null){var f=l==="y"?"height":"width";switch(i){case xe:c[l]=c[l]-(t[f]/2-n[f]/2);break;case Me:c[l]=c[l]+(t[f]/2-n[f]/2);break}}return c}function De(e,t){t===void 0&&(t={});var n=t,r=n.placement,o=r===void 0?e.placement:r,i=n.strategy,s=i===void 0?e.strategy:i,a=n.boundary,c=a===void 0?Vo:a,l=n.rootBoundary,f=l===void 0?Jn:l,d=n.elementContext,h=d===void 0?Ce:d,u=n.altBoundary,m=u===void 0?!1:u,p=n.padding,v=p===void 0?0:p,g=rr(typeof v!="number"?v:or(v,ke)),w=h===Ce?zo:Ce,x=e.rects.popper,y=e.elements[m?w:h],b=Ei(me(y)?y:y.contextElement||se(e.elements.popper),c,f,s),E=Ee(e.elements.reference),R=sr({reference:E,element:x,strategy:"absolute",placement:o}),C=Pt(Object.assign({},x,R)),S=h===Ce?C:E,P={top:b.top-S.top+g.top,bottom:S.bottom-b.bottom+g.bottom,left:b.left-S.left+g.left,right:S.right-b.right+g.right},A=e.modifiersData.offset;if(h===Ce&&A){var T=A[o];Object.keys(P).forEach(function(M){var k=[q,U].indexOf(M)>=0?1:-1,j=[_,U].indexOf(M)>=0?"y":"x";P[M]+=T[j]*k})}return P}function Ri(e,t){t===void 0&&(t={});var n=t,r=n.placement,o=n.boundary,i=n.rootBoundary,s=n.padding,a=n.flipVariations,c=n.allowedAutoPlacements,l=c===void 0?Qn:c,f=Re(r),d=f?a?gn:gn.filter(function(m){return Re(m)===f}):ke,h=d.filter(function(m){return l.indexOf(m)>=0});h.length===0&&(h=d);var u=h.reduce(function(m,p){return m[p]=De(e,{placement:p,boundary:o,rootBoundary:i,padding:s})[G(p)],m},{});return Object.keys(u).sort(function(m,p){return u[m]-u[p]})}function Pi(e){if(G(e)===kt)return[];var t=Ue(e);return[bn(e),t,bn(t)]}function Si(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var o=n.mainAxis,i=o===void 0?!0:o,s=n.altAxis,a=s===void 0?!0:s,c=n.fallbackPlacements,l=n.padding,f=n.boundary,d=n.rootBoundary,h=n.altBoundary,u=n.flipVariations,m=u===void 0?!0:u,p=n.allowedAutoPlacements,v=t.options.placement,g=G(v),w=g===v,x=c||(w||!m?[Ue(v)]:Pi(v)),y=[v].concat(x).reduce(function(ve,re){return ve.concat(G(re)===kt?Ri(t,{placement:re,boundary:f,rootBoundary:d,padding:l,flipVariations:m,allowedAutoPlacements:p}):re)},[]),b=t.rects.reference,E=t.rects.popper,R=new Map,C=!0,S=y[0],P=0;P<y.length;P++){var A=y[P],T=G(A),M=Re(A)===xe,k=[_,U].indexOf(T)>=0,j=k?"width":"height",L=De(t,{placement:A,boundary:f,rootBoundary:d,altBoundary:h,padding:l}),$=k?M?q:W:M?U:_;b[j]>E[j]&&($=Ue($));var N=Ue($),B=[];if(i&&B.push(L[T]<=0),a&&B.push(L[$]<=0,L[N]<=0),B.every(function(ve){return ve})){S=A,C=!1;break}R.set(A,B)}if(C)for(var Ne=m?3:1,ht=function(re){var $e=y.find(function(_e){var ae=R.get(_e);if(ae)return ae.slice(0,re).every(function(gt){return gt})});if($e)return S=$e,"break"},Se=Ne;Se>0;Se--){var Be=ht(Se);if(Be==="break")break}t.placement!==S&&(t.modifiersData[r]._skip=!0,t.placement=S,t.reset=!0)}}const $i={name:"flip",enabled:!0,phase:"main",fn:Si,requiresIfExists:["offset"],data:{_skip:!1}};function On(e,t,n){return n===void 0&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function En(e){return[_,q,U,W].some(function(t){return e[t]>=0})}function Ci(e){var t=e.state,n=e.name,r=t.rects.reference,o=t.rects.popper,i=t.modifiersData.preventOverflow,s=De(t,{elementContext:"reference"}),a=De(t,{altBoundary:!0}),c=On(s,r),l=On(a,o,i),f=En(c),d=En(l);t.modifiersData[n]={referenceClippingOffsets:c,popperEscapeOffsets:l,isReferenceHidden:f,hasPopperEscaped:d},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":f,"data-popper-escaped":d})}const Ai={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Ci};function Ti(e,t,n){var r=G(e),o=[W,_].indexOf(r)>=0?-1:1,i=typeof n=="function"?n(Object.assign({},t,{placement:e})):n,s=i[0],a=i[1];return s=s||0,a=(a||0)*o,[W,q].indexOf(r)>=0?{x:a,y:s}:{x:s,y:a}}function Li(e){var t=e.state,n=e.options,r=e.name,o=n.offset,i=o===void 0?[0,0]:o,s=Qn.reduce(function(f,d){return f[d]=Ti(d,t.rects,i),f},{}),a=s[t.placement],c=a.x,l=a.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=c,t.modifiersData.popperOffsets.y+=l),t.modifiersData[r]=s}const Mi={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Li};function Di(e){var t=e.state,n=e.name;t.modifiersData[n]=sr({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})}const ji={name:"popperOffsets",enabled:!0,phase:"read",fn:Di,data:{}};function ki(e){return e==="x"?"y":"x"}function Fi(e){var t=e.state,n=e.options,r=e.name,o=n.mainAxis,i=o===void 0?!0:o,s=n.altAxis,a=s===void 0?!1:s,c=n.boundary,l=n.rootBoundary,f=n.altBoundary,d=n.padding,h=n.tether,u=h===void 0?!0:h,m=n.tetherOffset,p=m===void 0?0:m,v=De(t,{boundary:c,rootBoundary:l,padding:d,altBoundary:f}),g=G(t.placement),w=Re(t.placement),x=!w,y=Bt(g),b=ki(y),E=t.modifiersData.popperOffsets,R=t.rects.reference,C=t.rects.popper,S=typeof p=="function"?p(Object.assign({},t.rects,{placement:t.placement})):p,P=typeof S=="number"?{mainAxis:S,altAxis:S}:Object.assign({mainAxis:0,altAxis:0},S),A=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,T={x:0,y:0};if(E){if(i){var M,k=y==="y"?_:W,j=y==="y"?U:q,L=y==="y"?"height":"width",$=E[y],N=$+v[k],B=$-v[j],Ne=u?-C[L]/2:0,ht=w===xe?R[L]:C[L],Se=w===xe?-C[L]:-R[L],Be=t.elements.arrow,ve=u&&Be?Nt(Be):{width:0,height:0},re=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:nr(),$e=re[k],_e=re[j],ae=Ae(0,R[L],ve[L]),gt=x?R[L]/2-Ne-ae-$e-P.mainAxis:ht-ae-$e-P.mainAxis,cr=x?-R[L]/2+Ne+ae+_e+P.mainAxis:Se+ae+_e+P.mainAxis,yt=t.elements.arrow&&Fe(t.elements.arrow),lr=yt?y==="y"?yt.clientTop||0:yt.clientLeft||0:0,It=(M=A==null?void 0:A[y])!=null?M:0,fr=$+gt-It-lr,ur=$+cr-It,Vt=Ae(u?Ze(N,fr):N,$,u?ue(B,ur):B);E[y]=Vt,T[y]=Vt-$}if(a){var zt,dr=y==="x"?_:W,pr=y==="x"?U:q,ce=E[b],We=b==="y"?"height":"width",Ut=ce+v[dr],qt=ce-v[pr],wt=[_,W].indexOf(g)!==-1,Xt=(zt=A==null?void 0:A[b])!=null?zt:0,Yt=wt?Ut:ce-R[We]-C[We]-Xt+P.altAxis,Gt=wt?ce+R[We]+C[We]-Xt-P.altAxis:qt,Kt=u&&wt?si(Yt,ce,Gt):Ae(u?Yt:Ut,ce,u?Gt:qt);E[b]=Kt,T[b]=Kt-ce}t.modifiersData[r]=T}}const Ni={name:"preventOverflow",enabled:!0,phase:"main",fn:Fi,requiresIfExists:["offset"]};function Bi(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function _i(e){return e===I(e)||!V(e)?_t(e):Bi(e)}function Wi(e){var t=e.getBoundingClientRect(),n=Oe(t.width)/e.offsetWidth||1,r=Oe(t.height)/e.offsetHeight||1;return n!==1||r!==1}function Hi(e,t,n){n===void 0&&(n=!1);var r=V(t),o=V(t)&&Wi(t),i=se(t),s=Ee(e,o,n),a={scrollLeft:0,scrollTop:0},c={x:0,y:0};return(r||!r&&!n)&&((Z(t)!=="body"||Ht(i))&&(a=_i(t)),V(t)?(c=Ee(t,!0),c.x+=t.clientLeft,c.y+=t.clientTop):i&&(c.x=Wt(i))),{x:s.left+a.scrollLeft-c.x,y:s.top+a.scrollTop-c.y,width:s.width,height:s.height}}function Ii(e){var t=new Map,n=new Set,r=[];e.forEach(function(i){t.set(i.name,i)});function o(i){n.add(i.name);var s=[].concat(i.requires||[],i.requiresIfExists||[]);s.forEach(function(a){if(!n.has(a)){var c=t.get(a);c&&o(c)}}),r.push(i)}return e.forEach(function(i){n.has(i.name)||o(i)}),r}function Vi(e){var t=Ii(e);return ei.reduce(function(n,r){return n.concat(t.filter(function(o){return o.phase===r}))},[])}function zi(e){var t;return function(){return t||(t=new Promise(function(n){Promise.resolve().then(function(){t=void 0,n(e())})})),t}}function Ui(e){var t=e.reduce(function(n,r){var o=n[r.name];return n[r.name]=o?Object.assign({},o,r,{options:Object.assign({},o.options,r.options),data:Object.assign({},o.data,r.data)}):r,n},{});return Object.keys(t).map(function(n){return t[n]})}var Rn={placement:"bottom",modifiers:[],strategy:"absolute"};function Pn(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(r){return!(r&&typeof r.getBoundingClientRect=="function")})}function qi(e){e===void 0&&(e={});var t=e,n=t.defaultModifiers,r=n===void 0?[]:n,o=t.defaultOptions,i=o===void 0?Rn:o;return function(a,c,l){l===void 0&&(l=i);var f={placement:"bottom",orderedModifiers:[],options:Object.assign({},Rn,i),modifiersData:{},elements:{reference:a,popper:c},attributes:{},styles:{}},d=[],h=!1,u={state:f,setOptions:function(g){var w=typeof g=="function"?g(f.options):g;p(),f.options=Object.assign({},i,f.options,w),f.scrollParents={reference:me(a)?Te(a):a.contextElement?Te(a.contextElement):[],popper:Te(c)};var x=Vi(Ui([].concat(r,f.options.modifiers)));return f.orderedModifiers=x.filter(function(y){return y.enabled}),m(),u.update()},forceUpdate:function(){if(!h){var g=f.elements,w=g.reference,x=g.popper;if(Pn(w,x)){f.rects={reference:Hi(w,Fe(x),f.options.strategy==="fixed"),popper:Nt(x)},f.reset=!1,f.placement=f.options.placement,f.orderedModifiers.forEach(function(P){return f.modifiersData[P.name]=Object.assign({},P.data)});for(var y=0;y<f.orderedModifiers.length;y++){if(f.reset===!0){f.reset=!1,y=-1;continue}var b=f.orderedModifiers[y],E=b.fn,R=b.options,C=R===void 0?{}:R,S=b.name;typeof E=="function"&&(f=E({state:f,options:C,name:S,instance:u})||f)}}}},update:zi(function(){return new Promise(function(v){u.forceUpdate(),v(f)})}),destroy:function(){p(),h=!0}};if(!Pn(a,c))return u;u.setOptions(l).then(function(v){!h&&l.onFirstUpdate&&l.onFirstUpdate(v)});function m(){f.orderedModifiers.forEach(function(v){var g=v.name,w=v.options,x=w===void 0?{}:w,y=v.effect;if(typeof y=="function"){var b=y({state:f,name:g,instance:u,options:x}),E=function(){};d.push(b||E)}})}function p(){d.forEach(function(v){return v()}),d=[]}return u}}var Xi=[hi,ji,mi,ri,Mi,$i,Ni,fi,Ai],Yi=qi({defaultModifiers:Xi});const ar="Popper";function Gi(e){return _n(ar,e)}so(ar,["root"]);const Ki=["anchorEl","children","direction","disablePortal","modifiers","open","placement","popperOptions","popperRef","slotProps","slots","TransitionProps","ownerState"],Zi=["anchorEl","children","container","direction","disablePortal","keepMounted","modifiers","open","placement","popperOptions","popperRef","style","transition","slotProps","slots"];function Ji(e,t){if(t==="ltr")return e;switch(e){case"bottom-end":return"bottom-start";case"bottom-start":return"bottom-end";case"top-end":return"top-start";case"top-start":return"top-end";default:return e}}function St(e){return typeof e=="function"?e():e}function Qi(e){return e.nodeType!==void 0}const es=()=>Hr({root:["root"]},Jr(Gi)),ts={},ns=O.forwardRef(function(t,n){var r;const{anchorEl:o,children:i,direction:s,disablePortal:a,modifiers:c,open:l,placement:f,popperOptions:d,popperRef:h,slotProps:u={},slots:m={},TransitionProps:p}=t,v=Je(t,Ki),g=O.useRef(null),w=ye(g,n),x=O.useRef(null),y=ye(x,h),b=O.useRef(y);de(()=>{b.current=y},[y]),O.useImperativeHandle(h,()=>x.current,[]);const E=Ji(f,s),[R,C]=O.useState(E),[S,P]=O.useState(St(o));O.useEffect(()=>{x.current&&x.current.forceUpdate()}),O.useEffect(()=>{o&&P(St(o))},[o]),de(()=>{if(!S||!l)return;const j=N=>{C(N.placement)};let L=[{name:"preventOverflow",options:{altBoundary:a}},{name:"flip",options:{altBoundary:a}},{name:"onUpdate",enabled:!0,phase:"afterWrite",fn:({state:N})=>{j(N)}}];c!=null&&(L=L.concat(c)),d&&d.modifiers!=null&&(L=L.concat(d.modifiers));const $=Yi(S,g.current,F({placement:E},d,{modifiers:L}));return b.current($),()=>{$.destroy(),b.current(null)}},[S,a,c,l,d,E]);const A={placement:R};p!==null&&(A.TransitionProps=p);const T=es(),M=(r=m.root)!=null?r:"div",k=ro({elementType:M,externalSlotProps:u.root,externalForwardedProps:v,additionalProps:{role:"tooltip",ref:w},ownerState:t,className:T.root});return J.jsx(M,F({},k,{children:typeof i=="function"?i(A):i}))}),Ds=O.forwardRef(function(t,n){const{anchorEl:r,children:o,container:i,direction:s="ltr",disablePortal:a=!1,keepMounted:c=!1,modifiers:l,open:f,placement:d="bottom",popperOptions:h=ts,popperRef:u,style:m,transition:p=!1,slotProps:v={},slots:g={}}=t,w=Je(t,Zi),[x,y]=O.useState(!0),b=()=>{y(!1)},E=()=>{y(!0)};if(!c&&!f&&(!p||x))return null;let R;if(i)R=i;else if(r){const P=St(r);R=P&&Qi(P)?he(P).body:he(null).body}const C=!f&&c&&(!p||x)?"none":void 0,S=p?{in:f,onEnter:b,onExited:E}:void 0;return J.jsx(Io,{disablePortal:a,container:R,children:J.jsx(ns,F({anchorEl:r,direction:s,disablePortal:a,modifiers:l,ref:n,open:p?!x:f,placement:d,popperOptions:h,popperRef:u,slotProps:v,slots:g},w,{style:F({position:"fixed",top:0,left:0,display:C},m),TransitionProps:S,children:o}))})}),rs=["onChange","maxRows","minRows","style","value"];function Ve(e){return parseInt(e,10)||0}const os={shadow:{visibility:"hidden",position:"absolute",overflow:"hidden",height:0,top:0,left:0,transform:"translateZ(0)"}};function is(e){return e==null||Object.keys(e).length===0||e.outerHeightStyle===0&&!e.overflowing}const js=O.forwardRef(function(t,n){const{onChange:r,maxRows:o,minRows:i=1,style:s,value:a}=t,c=Je(t,rs),{current:l}=O.useRef(a!=null),f=O.useRef(null),d=ye(n,f),h=O.useRef(null),u=O.useCallback(()=>{const v=f.current,w=Qt(v).getComputedStyle(v);if(w.width==="0px")return{outerHeightStyle:0,overflowing:!1};const x=h.current;x.style.width=w.width,x.value=v.value||t.placeholder||"x",x.value.slice(-1)===`
`&&(x.value+=" ");const y=w.boxSizing,b=Ve(w.paddingBottom)+Ve(w.paddingTop),E=Ve(w.borderBottomWidth)+Ve(w.borderTopWidth),R=x.scrollHeight;x.value="x";const C=x.scrollHeight;let S=R;i&&(S=Math.max(Number(i)*C,S)),o&&(S=Math.min(Number(o)*C,S)),S=Math.max(S,C);const P=S+(y==="border-box"?b+E:0),A=Math.abs(S-R)<=1;return{outerHeightStyle:P,overflowing:A}},[o,i,t.placeholder]),m=O.useCallback(()=>{const v=u();if(is(v))return;const g=f.current;g.style.height=`${v.outerHeightStyle}px`,g.style.overflow=v.overflowing?"hidden":""},[u]);de(()=>{const v=()=>{m()};let g;const w=Sr(v),x=f.current,y=Qt(x);y.addEventListener("resize",w);let b;return typeof ResizeObserver<"u"&&(b=new ResizeObserver(v),b.observe(x)),()=>{w.clear(),cancelAnimationFrame(g),y.removeEventListener("resize",w),b&&b.disconnect()}},[u,m]),de(()=>{m()});const p=v=>{l||m(),r&&r(v)};return J.jsxs(O.Fragment,{children:[J.jsx("textarea",F({value:a,onChange:p,ref:d,rows:i,style:s},c)),J.jsx("textarea",{"aria-hidden":!0,className:t.className,readOnly:!0,ref:h,tabIndex:-1,style:F({},os.shadow,s,{paddingTop:0,paddingBottom:0})})]})});export{ws as A,Ar as B,xs as C,zr as D,Os as E,bs as F,Le as G,Ms as H,Ss as I,Cs as J,Ts as K,As as L,Ls as M,$s as N,Ps as O,Ds as P,js as T,Tn as a,Pr as b,ds as c,ls as d,Rs as e,fs as f,us as g,Ln as h,le as i,J as j,de as k,ps as l,Ur as m,Es as n,he as o,Hr as p,Sr as q,Wr as r,ms as s,vs as t,ye as u,Qt as v,hs as w,xt as x,gs as y,ys as z};
