import{j as l,G as Ue,H as Bn,I as Hn,J as _n,K as Kn,L as Gn,M as Wn,N as Lt}from"./@mui/base-0e613ae5.js";import{a as pe,r as c,d as $n,R as At}from"./vendor-b0222800.js";import{t as y}from"./@nextui-org/react-8c36517e.js";var Ft={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},xt=pe.createContext&&pe.createContext(Ft),he=globalThis&&globalThis.__assign||function(){return he=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++){t=arguments[n];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o])}return e},he.apply(this,arguments)},Un=globalThis&&globalThis.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function Dt(e){return e&&e.map(function(t,n){return pe.createElement(t.tag,he({key:n},t.attr),Dt(t.child))})}function de(e){return function(t){return pe.createElement(Yn,he({attr:he({},e.attr)},t),Dt(e.child))}}function Yn(e){var t=function(n){var r=e.attr,o=e.size,a=e.title,s=Un(e,["attr","size","title"]),i=o||n.size||"1em",d;return n.className&&(d=n.className),e.className&&(d=(d?d+" ":"")+e.className),pe.createElement("svg",he({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},n.attr,r,s,{className:d,style:he(he({color:e.color||n.color},n.style),e.style),height:i,width:i,xmlns:"http://www.w3.org/2000/svg"}),a&&pe.createElement("title",null,a),e.children)};return xt!==void 0?pe.createElement(xt.Consumer,null,function(n){return t(n)}):t(Ft)}function zt(e){return de({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"}}]})(e)}function Xn(e){return de({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"}}]})(e)}function qn(e){return de({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"}}]})(e)}function Vn(e){return de({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"}}]})(e)}function Zn(e){return de({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"}}]})(e)}function Bt(e){return de({tag:"svg",attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M19 9l-7 7-7-7"}}]})(e)}function Jn(e){return de({tag:"svg",attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19l-7-7 7-7"}}]})(e)}function Ht(e){return de({tag:"svg",attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 5l7 7-7 7"}}]})(e)}function Qn(e){return de({tag:"svg",attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 15l7-7 7 7"}}]})(e)}function eo(e){return de({tag:"svg",attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18L18 6M6 6l12 12"}}]})(e)}function xe(e){return e!==null&&typeof e=="object"&&e.constructor===Object}function et(e){if(!xe(e))return e;const t={...e};return Object.keys(e).forEach(n=>{t[n]=et(e[n])}),t}function E(e,t){if(xe(t)&&Object.keys(t).length===0)return et({...e,...t});const n={...e,...t};return xe(t)&&xe(e)&&Object.keys(t).forEach(r=>{xe(t[r])&&r in e&&xe(e[r])?n[r]=E(e[r],t[r]):n[r]=xe(t[r])?et(t[r]):t[r]}),n}const _t=c.createContext(void 0);function Kt(){const e=c.useContext(_t);if(!e)throw new Error("useAccordionContext should be used within the AccordionPanelContext provider!");return e}const Gt=({children:e,className:t,theme:n={},...r})=>{const{isOpen:o}=Kt(),a=E(T().theme.accordion.content,n);return l.jsx("div",{className:y(a.base,t),"data-testid":"flowbite-accordion-content",hidden:!o,...r,children:e})},Wt=({children:e,...t})=>{const{alwaysOpen:n}=t,[r,o]=c.useState(t.isOpen),a=n?{...t,isOpen:r,setOpen:()=>o(!r)}:t;return l.jsx(_t.Provider,{value:a,children:e})},$t=({as:e="h2",children:t,className:n,theme:r={},...o})=>{const{arrowIcon:a,flush:s,isOpen:i,setOpen:d}=Kt(),f=()=>typeof d<"u"&&d(),b=E(T().theme.accordion.title,r);return l.jsxs("button",{className:y(b.base,b.flush[s?"on":"off"],b.open[i?"on":"off"],n),onClick:f,type:"button",...o,children:[l.jsx(e,{className:b.heading,"data-testid":"flowbite-accordion-heading",children:t}),a&&l.jsx(a,{"aria-hidden":!0,className:y(b.arrow.base,b.arrow.open[i?"on":"off"]),"data-testid":"flowbite-accordion-arrow"})]})},Ut=({alwaysOpen:e=!1,arrowIcon:t=zt,children:n,flush:r=!1,collapseAll:o=!1,className:a,theme:s={},...i})=>{const[d,f]=c.useState(o?-1:0),b=c.useMemo(()=>c.Children.map(n,(m,g)=>c.cloneElement(m,{alwaysOpen:e,arrowIcon:t,flush:r,isOpen:d===g,setOpen:()=>f(d===g?-1:g)})),[e,t,n,r,d]),u=E(T().theme.accordion.root,s);return l.jsx("div",{className:y(u.base,u.flush[r?"on":"off"],a),"data-testid":"flowbite-accordion",...i,children:b})};Ut.displayName="Accordion";Wt.displayName="Accordion.Panel";$t.displayName="Accordion.Title";Gt.displayName="Accordion.Content";Object.assign(Ut,{Panel:Wt,Title:$t,Content:Gt});const Yt=({children:e,className:t,theme:n={},...r})=>{const o=E(T().theme.avatar.group,n);return l.jsx("div",{"data-testid":"avatar-group-element",className:y(o.base,t),...r,children:e})};Yt.displayName="Avatar.Group";const Xt=({className:e,href:t,theme:n={},total:r,...o})=>{const a=E(T().theme.avatar.groupCounter,n);return l.jsxs("a",{href:t,className:y(a.base,e),...o,children:["+",r]})};Xt.displayName="Avatar.GroupCounter";const qt=({alt:e="",bordered:t=!1,children:n,className:r,color:o="light",img:a,placeholderInitials:s="",rounded:i=!1,size:d="md",stacked:f=!1,status:b,statusPosition:u="top-left",theme:m={},...g})=>{const h=E(T().theme.avatar,m),p=y(h.root.img.base,t&&h.root.bordered,t&&h.root.color[o],i&&h.root.rounded,f&&h.root.stacked,h.root.img.on,h.root.size[d]),N={className:y(p,h.root.img.on),"data-testid":"flowbite-avatar-img"};return l.jsxs("div",{className:y(h.root.base,r),"data-testid":"flowbite-avatar",...g,children:[l.jsxs("div",{className:"relative",children:[a?typeof a=="string"?l.jsx("img",{alt:e,src:a,...N}):a({alt:e,...N}):s?l.jsx("div",{className:y(h.root.img.off,h.root.initials.base,f&&h.root.stacked,t&&h.root.bordered,t&&h.root.color[o],h.root.size[d],i&&h.root.rounded),"data-testid":"flowbite-avatar-initials-placeholder",children:l.jsx("span",{className:y(h.root.initials.text),"data-testid":"flowbite-avatar-initials-placeholder-text",children:s})}):l.jsx("div",{className:y(p,h.root.img.off),"data-testid":"flowbite-avatar-img",children:l.jsx("svg",{className:h.root.img.placeholder,fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:l.jsx("path",{fillRule:"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z",clipRule:"evenodd"})})}),b&&l.jsx("span",{"data-testid":"flowbite-avatar-status",className:y(h.root.status.base,h.root.status[b],h.root.statusPosition[u])})]}),n&&l.jsx("div",{children:n})]})};qt.displayName="Avatar";Object.assign(qt,{Group:Yt,Counter:Xt});const Vt=({children:e,color:t="info",href:n,icon:r,size:o="xs",className:a,theme:s={},...i})=>{const d=E(T().theme.badge,s),f=()=>l.jsxs("span",{className:y(d.root.base,d.root.color[t],d.root.size[o],d.icon[r?"on":"off"],a),"data-testid":"flowbite-badge",...i,children:[r&&l.jsx(r,{"aria-hidden":!0,className:d.icon.size[o],"data-testid":"flowbite-badge-icon"}),e&&l.jsx("span",{children:e})]});return n?l.jsx("a",{className:d.root.href,href:n,children:l.jsx(f,{})}):l.jsx(f,{})};Vt.displayName="Badge";const Zt=c.forwardRef,to=({children:e,as:t,href:n,type:r="button",...o},a)=>{const s=t||(n?"a":"button");return c.createElement(s,{ref:a,href:n,type:r,...o},e)},Jt=Zt(to),Qt=({children:e,className:t,outline:n,pill:r,theme:o={},...a})=>{const s=c.useMemo(()=>c.Children.map(e,(d,f)=>c.cloneElement(d,{outline:n,pill:r,positionInGroup:f===0?"start":f===e.length-1?"end":"middle"})),[e,n,r]),i=E(T().theme.buttonGroup,o);return l.jsx("div",{className:y(i.base,t),role:"group",...a,children:s})};Qt.displayName="Button.Group";const er=({children:e,className:t,color:n="info",disabled:r,fullSized:o,isProcessing:a=!1,processingLabel:s="Loading...",processingSpinner:i,gradientDuoTone:d,gradientMonochrome:f,label:b,outline:u=!1,pill:m=!1,positionInGroup:g="none",size:h="md",theme:p={},...N},R)=>{const{buttonGroup:w,button:W}=T().theme,x=E(W,p),j=N;return l.jsx(Jt,{ref:R,disabled:r,className:y(x.base,r&&x.disabled,!d&&!f&&x.color[n],d&&!f&&x.gradientDuoTone[d],!d&&f&&x.gradient[f],u&&(x.outline.color[n]??x.outline.color.default),x.pill[m?"on":"off"],o&&x.fullSized,w.position[g],t),...j,children:l.jsx("span",{className:y(x.inner.base,x.outline[u?"on":"off"],x.outline.pill[u&&m?"on":"off"],x.size[h],u&&!x.outline.color[n]&&x.inner.outline,a&&x.isProcessing,a&&x.inner.isProcessingPadding[h],x.inner.position[g]),children:l.jsxs(l.Fragment,{children:[a&&l.jsx("span",{className:y(x.spinnerSlot,x.spinnerLeftPosition[h]),children:i||l.jsx(vn,{size:h})}),typeof e<"u"?e:l.jsx("span",{"data-testid":"flowbite-button-label",className:y(x.label),children:a?s:b})]})})})};er.displayName="Button";const ro=Zt(er),tr=Object.assign(ro,{Group:Qt}),rr=({children:e,...t})=>{const n=r=>{const a=r.target.closest('[role="banner"]');a==null||a.remove()};return l.jsx(tr,{onClick:n,...t,children:e})};rr.displayName="Banner.CollapseButton";const nr=({children:e,...t})=>l.jsx("div",{"data-testid":"flowbite-banner",role:"banner",tabIndex:-1,...t,children:e});nr.displayName="Banner";Object.assign(nr,{CollapseButton:rr});const or=c.forwardRef(({children:e,className:t,href:n,icon:r,theme:o={},...a},s)=>{const i=typeof n<"u",d=i?"a":"span",f=E(T().theme.breadcrumb.item,o);return l.jsxs("li",{className:y(f.base,t),...a,children:[l.jsx(Ht,{"aria-hidden":!0,className:f.chevron,"data-testid":"flowbite-breadcrumb-separator"}),l.jsxs(d,{ref:s,className:f.href[i?"on":"off"],"data-testid":"flowbite-breadcrumb-item",href:n,children:[r&&l.jsx(r,{"aria-hidden":!0,className:f.icon}),e]})]})});or.displayName="Breadcrumb.Item";const ar=({children:e,className:t,theme:n={},...r})=>{const o=E(T().theme.breadcrumb.root,n);return l.jsx("nav",{"aria-label":"Breadcrumb",className:y(o.base,t),...r,children:l.jsx("ol",{className:o.list,children:e})})};ar.displayName="Breadcrumb";Object.assign(ar,{Item:or});/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var sr=function(e,t){return(sr=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,r){n.__proto__=r}||function(n,r){for(var o in r)r.hasOwnProperty(o)&&(n[o]=r[o])})(e,t)},no,Oe,oo=(function(e){/*!
  Copyright (c) 2017 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/(function(){var t={}.hasOwnProperty;function n(){for(var r=[],o=0;o<arguments.length;o++){var a=arguments[o];if(a){var s=typeof a;if(s==="string"||s==="number")r.push(a);else if(Array.isArray(a)&&a.length){var i=n.apply(null,a);i&&r.push(i)}else if(s==="object")for(var d in a)t.call(a,d)&&a[d]&&r.push(d)}}return r.join(" ")}e.exports?(n.default=n,e.exports=n):window.classNames=n})()}(Oe={path:no,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(t==null&&Oe.path)}},Oe.exports),Oe.exports);function tt(e,t,n){var r,o,a,s,i;function d(){var b=Date.now()-s;b<t&&b>=0?r=setTimeout(d,t-b):(r=null,n||(i=e.apply(a,o),a=o=null))}t==null&&(t=100);var f=function(){a=this,o=arguments,s=Date.now();var b=n&&!r;return r||(r=setTimeout(d,t)),b&&(i=e.apply(a,o),a=o=null),i};return f.clear=function(){r&&(clearTimeout(r),r=null)},f.flush=function(){r&&(i=e.apply(a,o),a=o=null,clearTimeout(r),r=null)},f}tt.debounce=tt;var ao=tt;(function(e,t){t===void 0&&(t={});var n=t.insertAt;if(e&&typeof document<"u"){var r=document.head||document.getElementsByTagName("head")[0],o=document.createElement("style");o.type="text/css",n==="top"&&r.firstChild?r.insertBefore(o,r.firstChild):r.appendChild(o),o.styleSheet?o.styleSheet.cssText=e:o.appendChild(document.createTextNode(e))}})(`.indiana-scroll-container {
  overflow: auto; }
  .indiana-scroll-container--dragging {
    scroll-behavior: auto !important; }
    .indiana-scroll-container--dragging > * {
      pointer-events: none;
      cursor: -webkit-grab;
      cursor: grab; }
  .indiana-scroll-container--hide-scrollbars {
    overflow: hidden;
    overflow: -moz-scrollbars-none;
    -ms-overflow-style: none;
    scrollbar-width: none; }
    .indiana-scroll-container--hide-scrollbars::-webkit-scrollbar {
      display: none !important;
      height: 0 !important;
      width: 0 !important;
      background: transparent !important;
      -webkit-appearance: none !important; }
  .indiana-scroll-container--native-scroll {
    overflow: auto; }

.indiana-dragging {
  cursor: -webkit-grab;
  cursor: grab; }
`);var Ye,so=(Ye="indiana-scroll-container",function(e,t){if(!e)return Ye;var n;typeof e=="string"?n=e:t=e;var r=Ye;return n&&(r+="__"+n),r+(t?Object.keys(t).reduce(function(o,a){var s=t[a];return s&&(o+=" "+(typeof s=="boolean"?r+"--"+a:r+"--"+a+"_"+s)),o},""):"")});(function(e){function t(n){var r=e.call(this,n)||this;return r.onEndScroll=function(){r.scrolling=!1,!r.pressed&&r.started&&r.processEnd()},r.onScroll=function(o){var a=r.container.current;a.scrollLeft===r.scrollLeft&&a.scrollTop===r.scrollTop||(r.scrolling=!0,r.processScroll(o),r.onEndScroll())},r.onTouchStart=function(o){var a=r.props.nativeMobileScroll;if(r.isDraggable(o.target))if(r.internal=!0,a&&r.scrolling)r.pressed=!0;else{var s=o.touches[0];r.processClick(o,s.clientX,s.clientY),!a&&r.props.stopPropagation&&o.stopPropagation()}},r.onTouchEnd=function(o){var a=r.props.nativeMobileScroll;r.pressed&&(!r.started||r.scrolling&&a?r.pressed=!1:r.processEnd(),r.forceUpdate())},r.onTouchMove=function(o){var a=r.props.nativeMobileScroll;if(r.pressed&&(!a||!r.isMobile)){var s=o.touches[0];s&&r.processMove(o,s.clientX,s.clientY),o.preventDefault(),r.props.stopPropagation&&o.stopPropagation()}},r.onMouseDown=function(o){r.isDraggable(o.target)&&r.isScrollable()&&(r.internal=!0,r.props.buttons.indexOf(o.button)!==-1&&(r.processClick(o,o.clientX,o.clientY),o.preventDefault(),r.props.stopPropagation&&o.stopPropagation()))},r.onMouseMove=function(o){r.pressed&&(r.processMove(o,o.clientX,o.clientY),o.preventDefault(),r.props.stopPropagation&&o.stopPropagation())},r.onMouseUp=function(o){r.pressed&&(r.started?r.processEnd():(r.internal=!1,r.pressed=!1,r.forceUpdate(),r.props.onClick&&r.props.onClick(o)),o.preventDefault(),r.props.stopPropagation&&o.stopPropagation())},r.container=pe.createRef(),r.onEndScroll=ao(r.onEndScroll,300),r.scrolling=!1,r.started=!1,r.pressed=!1,r.internal=!1,r.getRef=r.getRef.bind(r),r}return function(n,r){function o(){this.constructor=n}sr(n,r),n.prototype=r===null?Object.create(r):(o.prototype=r.prototype,new o)}(t,e),t.prototype.componentDidMount=function(){var n=this.props.nativeMobileScroll,r=this.container.current;window.addEventListener("mouseup",this.onMouseUp),window.addEventListener("mousemove",this.onMouseMove),window.addEventListener("touchmove",this.onTouchMove,{passive:!1}),window.addEventListener("touchend",this.onTouchEnd),r.addEventListener("touchstart",this.onTouchStart,{passive:!1}),r.addEventListener("mousedown",this.onMouseDown,{passive:!1}),n&&(this.isMobile=this.isMobileDevice(),this.isMobile&&this.forceUpdate())},t.prototype.componentWillUnmount=function(){window.removeEventListener("mouseup",this.onMouseUp),window.removeEventListener("mousemove",this.onMouseMove),window.removeEventListener("touchmove",this.onTouchMove),window.removeEventListener("touchend",this.onTouchEnd)},t.prototype.getElement=function(){return this.container.current},t.prototype.isMobileDevice=function(){return window.orientation!==void 0||navigator.userAgent.indexOf("IEMobile")!==-1},t.prototype.isDraggable=function(n){var r=this.props.ignoreElements;if(r){var o=n.closest(r);return o===null||o.contains(this.getElement())}return!0},t.prototype.isScrollable=function(){var n=this.container.current;return n&&(n.scrollWidth>n.clientWidth||n.scrollHeight>n.clientHeight)},t.prototype.processClick=function(n,r,o){var a=this.container.current;this.scrollLeft=a.scrollLeft,this.scrollTop=a.scrollTop,this.clientX=r,this.clientY=o,this.pressed=!0},t.prototype.processStart=function(n){n===void 0&&(n=!0);var r=this.props.onStartScroll;this.started=!0,n&&document.body.classList.add("indiana-dragging"),r&&r({external:!this.internal}),this.forceUpdate()},t.prototype.processScroll=function(n){if(this.started){var r=this.props.onScroll;r&&r({external:!this.internal})}else this.processStart(!1)},t.prototype.processMove=function(n,r,o){var a=this.props,s=a.horizontal,i=a.vertical,d=a.activationDistance,f=a.onScroll,b=this.container.current;this.started?(s&&(b.scrollLeft-=r-this.clientX),i&&(b.scrollTop-=o-this.clientY),f&&f({external:!this.internal}),this.clientX=r,this.clientY=o,this.scrollLeft=b.scrollLeft,this.scrollTop=b.scrollTop):(s&&Math.abs(r-this.clientX)>d||i&&Math.abs(o-this.clientY)>d)&&(this.clientX=r,this.clientY=o,this.processStart())},t.prototype.processEnd=function(){var n=this.props.onEndScroll;this.container.current&&n&&n({external:!this.internal}),this.pressed=!1,this.started=!1,this.scrolling=!1,this.internal=!1,document.body.classList.remove("indiana-dragging"),this.forceUpdate()},t.prototype.getRef=function(n){[this.container,this.props.innerRef].forEach(function(r){r&&(typeof r=="function"?r(n):r.current=n)})},t.prototype.render=function(){var n=this.props,r=n.children,o=n.draggingClassName,a=n.className,s=n.style,i=n.hideScrollbars,d=n.component;return pe.createElement(d,{className:oo(a,this.pressed&&o,so({dragging:this.pressed,"hide-scrollbars":i,"native-scroll":this.isMobile})),style:s,ref:this.getRef,onScroll:this.onScroll},r)},t.defaultProps={nativeMobileScroll:!0,hideScrollbars:!0,activationDistance:10,vertical:!0,horizontal:!0,stopPropagation:!1,style:{},component:"div",buttons:[0]},t})(c.PureComponent);const io=c.forwardRef(({className:e,color:t="default",theme:n={},...r},o)=>{const a=E(T().theme.checkbox,n);return l.jsx("input",{ref:o,type:"checkbox",className:y(a.root.base,a.root.color[t],e),...r})});io.displayName="Checkbox";var vt;(function(e){e[e.Days=0]="Days",e[e.Months=1]="Months",e[e.Years=2]="Years",e[e.Decades=3]="Decades"})(vt||(vt={}));var kt;(function(e){e[e.Saturday=0]="Saturday",e[e.Sunday=1]="Sunday",e[e.Monday=2]="Monday",e[e.Tuesday=3]="Tuesday",e[e.Wednesday=4]="Wednesday",e[e.Thursday=5]="Thursday",e[e.Friday=6]="Friday"})(kt||(kt={}));c.createContext(void 0);const lo={root:{base:"divide-y divide-gray-200 border-gray-200 dark:divide-gray-700 dark:border-gray-700",flush:{off:"rounded-lg border",on:"border-b"}},content:{base:"py-5 px-5 last:rounded-b-lg dark:bg-gray-900 first:rounded-t-lg"},title:{arrow:{base:"h-6 w-6 shrink-0",open:{off:"",on:"rotate-180"}},base:"flex w-full items-center justify-between first:rounded-t-lg last:rounded-b-lg py-5 px-5 text-left font-medium text-gray-500 dark:text-gray-400",flush:{off:"hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 dark:hover:bg-gray-800 dark:focus:ring-gray-800",on:"bg-transparent dark:bg-transparent"},heading:"",open:{off:"",on:"text-gray-900 bg-gray-100 dark:bg-gray-800 dark:text-white"}}},co={base:"flex flex-col gap-2 p-4 text-sm",borderAccent:"border-t-4",closeButton:{base:"-mx-1.5 -my-1.5 ml-auto inline-flex h-8 w-8 rounded-lg p-1.5 focus:ring-2",icon:"w-5 h-5",color:{info:"bg-cyan-100 text-cyan-500 hover:bg-cyan-200 focus:ring-cyan-400 dark:bg-cyan-200 dark:text-cyan-600 dark:hover:bg-cyan-300",gray:"bg-gray-100 text-gray-500 hover:bg-gray-200 focus:ring-gray-400 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white",failure:"bg-red-100 text-red-500 hover:bg-red-200 focus:ring-red-400 dark:bg-red-200 dark:text-red-600 dark:hover:bg-red-300",success:"bg-green-100 text-green-500 hover:bg-green-200 focus:ring-green-400 dark:bg-green-200 dark:text-green-600 dark:hover:bg-green-300",warning:"bg-yellow-100 text-yellow-500 hover:bg-yellow-200 focus:ring-yellow-400 dark:bg-yellow-200 dark:text-yellow-600 dark:hover:bg-yellow-300",red:"bg-red-100 text-red-500 hover:bg-red-200 focus:ring-red-400 dark:bg-red-200 dark:text-red-600 dark:hover:bg-red-300",green:"bg-green-100 text-green-500 hover:bg-green-200 focus:ring-green-400 dark:bg-green-200 dark:text-green-600 dark:hover:bg-green-300",yellow:"bg-yellow-100 text-yellow-500 hover:bg-yellow-200 focus:ring-yellow-400 dark:bg-yellow-200 dark:text-yellow-600 dark:hover:bg-yellow-300",blue:"bg-cyan-100 text-cyan-500 hover:bg-cyan-200 focus:ring-cyan-400 dark:bg-cyan-200 dark:text-cyan-600 dark:hover:bg-cyan-300",cyan:"bg-cyan-100 text-cyan-500 hover:bg-cyan-200 focus:ring-cyan-400 dark:bg-cyan-200 dark:text-cyan-600 dark:hover:bg-cyan-300",pink:"bg-pink-100 text-pink-500 hover:bg-pink-200 focus:ring-pink-400 dark:bg-pink-200 dark:text-pink-600 dark:hover:bg-pink-300",lime:"bg-lime-100 text-lime-500 hover:bg-lime-200 focus:ring-lime-400 dark:bg-lime-200 dark:text-lime-600 dark:hover:bg-lime-300",dark:"bg-gray-100 text-gray-500 hover:bg-gray-200 focus:ring-gray-400 dark:bg-gray-200 dark:text-gray-600 dark:hover:bg-gray-300",indigo:"bg-indigo-100 text-indigo-500 hover:bg-indigo-200 focus:ring-indigo-400 dark:bg-indigo-200 dark:text-indigo-600 dark:hover:bg-indigo-300",purple:"bg-purple-100 text-purple-500 hover:bg-purple-200 focus:ring-purple-400 dark:bg-purple-200 dark:text-purple-600 dark:hover:bg-purple-300",teal:"bg-teal-100 text-teal-500 hover:bg-teal-200 focus:ring-teal-400 dark:bg-teal-200 dark:text-teal-600 dark:hover:bg-teal-300",light:"bg-gray-50 text-gray-500 hover:bg-gray-100 focus:ring-gray-200 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-700 dark:hover:text-white"}},color:{info:"text-cyan-700 bg-cyan-100 border-cyan-500 dark:bg-cyan-200 dark:text-cyan-800",gray:"text-gray-700 bg-gray-100 border-gray-500 dark:bg-gray-700 dark:text-gray-300",failure:"text-red-700 bg-red-100 border-red-500 dark:bg-red-200 dark:text-red-800",success:"text-green-700 bg-green-100 border-green-500 dark:bg-green-200 dark:text-green-800",warning:"text-yellow-700 bg-yellow-100 border-yellow-500 dark:bg-yellow-200 dark:text-yellow-800",red:"text-red-700 bg-red-100 border-red-500 dark:bg-red-200 dark:text-red-800",green:"text-green-700 bg-green-100 border-green-500 dark:bg-green-200 dark:text-green-800",yellow:"text-yellow-700 bg-yellow-100 border-yellow-500 dark:bg-yellow-200 dark:text-yellow-800",blue:"text-cyan-700 bg-cyan-100 border-cyan-500 dark:bg-cyan-200 dark:text-cyan-800",cyan:"text-cyan-700 bg-cyan-100 border-cyan-500 dark:bg-cyan-200 dark:text-cyan-800",pink:"text-pink-700 bg-pink-100 border-pink-500 dark:bg-pink-200 dark:text-pink-800",lime:"text-lime-700 bg-lime-100 border-lime-500 dark:bg-lime-200 dark:text-lime-800",dark:"text-gray-200 bg-gray-800 border-gray-600 dark:bg-gray-900 dark:text-gray-300",indigo:"text-indigo-700 bg-indigo-100 border-indigo-500 dark:bg-indigo-200 dark:text-indigo-800",purple:"text-purple-700 bg-purple-100 border-purple-500 dark:bg-purple-200 dark:text-purple-800",teal:"text-teal-700 bg-teal-100 border-teal-500 dark:bg-teal-200 dark:text-teal-800",light:"text-gray-600 bg-gray-50 border-gray-400 dark:bg-gray-500 dark:text-gray-200"},icon:"mr-3 inline h-5 w-5 flex-shrink-0",rounded:"rounded-lg",wrapper:"flex items-center"},uo={root:{base:"flex justify-center items-center space-x-4 rounded",bordered:"p-1 ring-2",rounded:"rounded-full",color:{dark:"ring-gray-800 dark:ring-gray-800",failure:"ring-red-500 dark:ring-red-700",gray:"ring-gray-500 dark:ring-gray-400",info:"ring-cyan-400 dark:ring-cyan-800",light:"ring-gray-300 dark:ring-gray-500",purple:"ring-purple-500 dark:ring-purple-600",success:"ring-green-500 dark:ring-green-500",warning:"ring-yellow-300 dark:ring-yellow-500",pink:"ring-pink-500 dark:ring-pink-500"},img:{base:"rounded",off:"relative overflow-hidden bg-gray-100 dark:bg-gray-600",on:"",placeholder:"absolute w-auto h-auto text-gray-400 -bottom-1"},size:{xs:"w-6 h-6",sm:"w-8 h-8",md:"w-10 h-10",lg:"w-20 h-20",xl:"w-36 h-36"},stacked:"ring-2 ring-gray-300 dark:ring-gray-500",statusPosition:{"bottom-left":"-bottom-1 -left-1","bottom-center":"-bottom-1 center","bottom-right":"-bottom-1 -right-1","top-left":"-top-1 -left-1","top-center":"-top-1 center","top-right":"-top-1 -right-1","center-right":"center -right-1",center:"center center","center-left":"center -left-1"},status:{away:"bg-yellow-400",base:"absolute h-3.5 w-3.5 rounded-full border-2 border-white dark:border-gray-800",busy:"bg-red-400",offline:"bg-gray-400",online:"bg-green-400"},initials:{text:"font-medium text-gray-600 dark:text-gray-300",base:"inline-flex overflow-hidden relative justify-center items-center bg-gray-100 dark:bg-gray-600"}},group:{base:"flex -space-x-4"},groupCounter:{base:"relative flex items-center justify-center w-10 h-10 text-xs font-medium text-white bg-gray-700 rounded-full ring-2 ring-gray-300 hover:bg-gray-600 dark:ring-gray-500"}},fo={root:{base:"flex h-fit items-center gap-1 font-semibold",color:{info:"bg-cyan-100 text-cyan-800 dark:bg-cyan-200 dark:text-cyan-800 group-hover:bg-cyan-200 dark:group-hover:bg-cyan-300",gray:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 group-hover:bg-gray-200 dark:group-hover:bg-gray-600",failure:"bg-red-100 text-red-800 dark:bg-red-200 dark:text-red-900 group-hover:bg-red-200 dark:group-hover:bg-red-300",success:"bg-green-100 text-green-800 dark:bg-green-200 dark:text-green-900 group-hover:bg-green-200 dark:group-hover:bg-green-300",warning:"bg-yellow-100 text-yellow-800 dark:bg-yellow-200 dark:text-yellow-900 group-hover:bg-yellow-200 dark:group-hover:bg-yellow-300",indigo:"bg-indigo-100 text-indigo-800 dark:bg-indigo-200 dark:text-indigo-900 group-hover:bg-indigo-200 dark:group-hover:bg-indigo-300",purple:"bg-purple-100 text-purple-800 dark:bg-purple-200 dark:text-purple-900 group-hover:bg-purple-200 dark:group-hover:bg-purple-300",pink:"bg-pink-100 text-pink-800 dark:bg-pink-200 dark:text-pink-900 group-hover:bg-pink-200 dark:group-hover:bg-pink-300",blue:"bg-cyan-100 text-cyan-800 dark:bg-cyan-200 dark:text-cyan-900 group-hover:bg-cyan-200 dark:group-hover:bg-cyan-300",cyan:"bg-cyan-100 text-cyan-800 dark:bg-cyan-200 dark:text-cyan-900 group-hover:bg-cyan-200 dark:group-hover:bg-cyan-300",dark:"bg-gray-600 text-gray-100 dark:bg-gray-900 dark:text-gray-200 group-hover:bg-gray-500 dark:group-hover:bg-gray-700",light:"bg-gray-200 text-gray-800 dark:bg-gray-400 dark:text-gray-900 group-hover:bg-gray-300 dark:group-hover:bg-gray-500",green:"bg-green-100 text-green-800 dark:bg-green-200 dark:text-green-900 group-hover:bg-green-200 dark:group-hover:bg-green-300",lime:"bg-lime-100 text-lime-800 dark:bg-lime-200 dark:text-lime-900 group-hover:bg-lime-200 dark:group-hover:bg-lime-300",red:"bg-red-100 text-red-800 dark:bg-red-200 dark:text-red-900 group-hover:bg-red-200 dark:group-hover:bg-red-300",teal:"bg-teal-100 text-teal-800 dark:bg-teal-200 dark:text-teal-900 group-hover:bg-teal-200 dark:group-hover:bg-teal-300",yellow:"bg-yellow-100 text-yellow-800 dark:bg-yellow-200 dark:text-yellow-900 group-hover:bg-yellow-200 dark:group-hover:bg-yellow-300"},href:"group",size:{xs:"p-1 text-xs",sm:"p-1.5 text-sm"}},icon:{off:"rounded px-2 py-0.5",on:"rounded-full p-1.5",size:{xs:"w-3 h-3",sm:"w-3.5 h-3.5"}}},go={root:{base:"text-xl italic font-semibold text-gray-900 dark:text-white"}},bo={root:{base:"",list:"flex items-center"},item:{base:"group flex items-center",chevron:"mx-1 h-4 w-4 text-gray-400 group-first:hidden md:mx-2",href:{off:"flex items-center text-sm font-medium text-gray-500 dark:text-gray-400",on:"flex items-center text-sm font-medium text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"},icon:"mr-2 h-4 w-4"}},po={base:"group flex items-stretch items-center justify-center p-0.5 text-center font-medium relative focus:z-10 focus:outline-none",fullSized:"w-full",color:{dark:"text-white bg-gray-800 border border-transparent enabled:hover:bg-gray-900 focus:ring-4 focus:ring-gray-300 dark:bg-gray-800 dark:enabled:hover:bg-gray-700 dark:focus:ring-gray-800 dark:border-gray-700",failure:"text-white bg-red-700 border border-transparent enabled:hover:bg-red-800 focus:ring-4 focus:ring-red-300 dark:bg-red-600 dark:enabled:hover:bg-red-700 dark:focus:ring-red-900",gray:"text-gray-900 bg-white border border-gray-200 enabled:hover:bg-gray-100 enabled:hover:text-cyan-700 :ring-cyan-700 focus:text-cyan-700 dark:bg-transparent dark:text-gray-400 dark:border-gray-600 dark:enabled:hover:text-white dark:enabled:hover:bg-gray-700 focus:ring-2",info:"text-white bg-cyan-700 border border-transparent enabled:hover:bg-cyan-800 focus:ring-4 focus:ring-cyan-300 dark:bg-cyan-600 dark:enabled:hover:bg-cyan-700 dark:focus:ring-cyan-800",light:"text-gray-900 bg-white border border-gray-300 enabled:hover:bg-gray-100 focus:ring-4 focus:ring-cyan-300 dark:bg-gray-600 dark:text-white dark:border-gray-600 dark:enabled:hover:bg-gray-700 dark:enabled:hover:border-gray-700 dark:focus:ring-gray-700",purple:"text-white bg-purple-700 border border-transparent enabled:hover:bg-purple-800 focus:ring-4 focus:ring-purple-300 dark:bg-purple-600 dark:enabled:hover:bg-purple-700 dark:focus:ring-purple-900",success:"text-white bg-green-700 border border-transparent enabled:hover:bg-green-800 focus:ring-4 focus:ring-green-300 dark:bg-green-600 dark:enabled:hover:bg-green-700 dark:focus:ring-green-800",warning:"text-white bg-yellow-400 border border-transparent enabled:hover:bg-yellow-500 focus:ring-4 focus:ring-yellow-300 dark:focus:ring-yellow-900",blue:"text-white bg-blue-700 border border-transparent enabled:hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",cyan:"text-cyan-900 bg-white border border-cyan-300 enabled:hover:bg-cyan-100 focus:ring-4 focus:ring-cyan-300 :bg-cyan-600 dark:text-white dark:border-cyan-600 dark:enabled:hover:bg-cyan-700 dark:enabled:hover:border-cyan-700 dark:focus:ring-cyan-700",green:"text-green-900 bg-white border border-green-300 enabled:hover:bg-green-100 focus:ring-4 focus:ring-green-300 :bg-green-600 dark:text-white dark:border-green-600 dark:enabled:hover:bg-green-700 dark:enabled:hover:border-green-700 dark:focus:ring-green-700",indigo:"text-indigo-900 bg-white border border-indigo-300 enabled:hover:bg-indigo-100 focus:ring-4 focus:ring-indigo-300 :bg-indigo-600 dark:text-white dark:border-indigo-600 dark:enabled:hover:bg-indigo-700 dark:enabled:hover:border-indigo-700 dark:focus:ring-indigo-700",lime:"text-lime-900 bg-white border border-lime-300 enabled:hover:bg-lime-100 focus:ring-4 focus:ring-lime-300 :bg-lime-600 dark:text-white dark:border-lime-600 dark:enabled:hover:bg-lime-700 dark:enabled:hover:border-lime-700 dark:focus:ring-lime-700",pink:"text-pink-900 bg-white border border-pink-300 enabled:hover:bg-pink-100 focus:ring-4 focus:ring-pink-300 :bg-pink-600 dark:text-white dark:border-pink-600 dark:enabled:hover:bg-pink-700 dark:enabled:hover:border-pink-700 dark:focus:ring-pink-700",red:"text-red-900 bg-white border border-red-300 enabled:hover:bg-red-100 focus:ring-4 focus:ring-red-300 :bg-red-600 dark:text-white dark:border-red-600 dark:enabled:hover:bg-red-700 dark:enabled:hover:border-red-700 dark:focus:ring-red-700",teal:"text-teal-900 bg-white border border-teal-300 enabled:hover:bg-teal-100 focus:ring-4 focus:ring-teal-300 :bg-teal-600 dark:text-white dark:border-teal-600 dark:enabled:hover:bg-teal-700 dark:enabled:hover:border-teal-700 dark:focus:ring-teal-700",yellow:"text-yellow-900 bg-white border border-yellow-300 enabled:hover:bg-yellow-100 focus:ring-4 focus:ring-yellow-300 :bg-yellow-600 dark:text-white dark:border-yellow-600 dark:enabled:hover:bg-yellow-700 dark:enabled:hover:border-yellow-700 dark:focus:ring-yellow-700"},disabled:"cursor-not-allowed opacity-50",isProcessing:"cursor-wait",spinnerSlot:"absolute h-full top-0 flex items-center animate-fade-in",spinnerLeftPosition:{xs:"left-2",sm:"left-3",md:"left-4",lg:"left-5",xl:"left-6"},gradient:{cyan:"text-white bg-gradient-to-r from-cyan-400 via-cyan-500 to-cyan-600 enabled:hover:bg-gradient-to-br focus:ring-4 focus:ring-cyan-300 dark:focus:ring-cyan-800",failure:"text-white bg-gradient-to-r from-red-400 via-red-500 to-red-600 enabled:hover:bg-gradient-to-br focus:ring-4 focus:ring-red-300 dark:focus:ring-red-800",info:"text-white bg-gradient-to-r from-cyan-500 via-cyan-600 to-cyan-700 enabled:hover:bg-gradient-to-br focus:ring-4 focus:ring-cyan-300 dark:focus:ring-cyan-800 ",lime:"text-gray-900 bg-gradient-to-r from-lime-200 via-lime-400 to-lime-500 enabled:hover:bg-gradient-to-br focus:ring-4 focus:ring-lime-300 dark:focus:ring-lime-800",pink:"text-white bg-gradient-to-r from-pink-400 via-pink-500 to-pink-600 enabled:hover:bg-gradient-to-br focus:ring-4 focus:ring-pink-300 dark:focus:ring-pink-800",purple:"text-white bg-gradient-to-r from-purple-500 via-purple-600 to-purple-700 enabled:hover:bg-gradient-to-br focus:ring-4 focus:ring-purple-300 dark:focus:ring-purple-800",success:"text-white bg-gradient-to-r from-green-400 via-green-500 to-green-600 enabled:hover:bg-gradient-to-br focus:ring-4 focus:ring-green-300 dark:focus:ring-green-800",teal:"text-white bg-gradient-to-r from-teal-400 via-teal-500 to-teal-600 enabled:hover:bg-gradient-to-br focus:ring-4 focus:ring-teal-300 dark:focus:ring-teal-800"},gradientDuoTone:{cyanToBlue:"text-white bg-gradient-to-r from-cyan-500 to-cyan-500 enabled:hover:bg-gradient-to-bl focus:ring-4 focus:ring-cyan-300 dark:focus:ring-cyan-800",greenToBlue:"text-white bg-gradient-to-br from-green-400 to-cyan-600 enabled:hover:bg-gradient-to-bl focus:ring-4 focus:ring-green-200 dark:focus:ring-green-800",pinkToOrange:"text-white bg-gradient-to-br from-pink-500 to-orange-400 enabled:hover:bg-gradient-to-bl focus:ring-4 focus:ring-pink-200 dark:focus:ring-pink-800",purpleToBlue:"text-white bg-gradient-to-br from-purple-600 to-cyan-500 enabled:hover:bg-gradient-to-bl focus:ring-4 focus:ring-cyan-300 dark:focus:ring-cyan-800",purpleToPink:"text-white bg-gradient-to-r from-purple-500 to-pink-500 enabled:hover:bg-gradient-to-l focus:ring-4 focus:ring-purple-200 dark:focus:ring-purple-800",redToYellow:"text-gray-900 bg-gradient-to-r from-red-200 via-red-300 to-yellow-200 enabled:hover:bg-gradient-to-bl focus:ring-4 focus:ring-red-100 dark:focus:ring-red-400",tealToLime:"text-gray-900 bg-gradient-to-r from-teal-200 to-lime-200 enabled:hover:bg-gradient-to-l enabled:hover:from-teal-200 enabled:hover:to-lime-200 enabled:hover:text-gray-900 focus:ring-4 focus:ring-lime-200 dark:focus:ring-teal-700"},inner:{base:"flex items-stretch items-center transition-all duration-200",position:{none:"",start:"rounded-r-none",middle:"rounded-none",end:"rounded-l-none"},outline:"border border-transparent",isProcessingPadding:{xs:"pl-8",sm:"pl-10",md:"pl-12",lg:"pl-16",xl:"pl-20"}},label:"ml-2 inline-flex h-4 w-4 items-center justify-center rounded-full bg-cyan-200 text-xs font-semibold text-cyan-800",outline:{color:{gray:"border border-gray-900 dark:border-white",default:"border-0",light:""},off:"",on:"flex justify-center bg-white text-gray-900 transition-all duration-75 ease-in group-enabled:group-hover:bg-opacity-0 group-enabled:group-hover:text-inherit dark:bg-gray-900 dark:text-white w-full",pill:{off:"rounded-md",on:"rounded-full"}},pill:{off:"rounded-lg",on:"rounded-full"},size:{xs:"text-xs px-2 py-1",sm:"text-sm px-3 py-1.5",md:"text-sm px-4 py-2",lg:"text-base px-5 py-2.5",xl:"text-base px-6 py-3"}},ho={base:"inline-flex",position:{none:"focus:ring-2",start:"rounded-r-none",middle:"rounded-none border-l-0 pl-0",end:"rounded-l-none border-l-0 pl-0"}},mo={root:{base:"flex rounded-lg border border-gray-200 bg-white shadow-md dark:border-gray-700 dark:bg-gray-800",children:"flex h-full flex-col justify-center gap-4 p-6",horizontal:{off:"flex-col",on:"flex-col md:max-w-xl md:flex-row"},href:"hover:bg-gray-100 dark:hover:bg-gray-700"},img:{base:"",horizontal:{off:"rounded-t-lg",on:"h-96 w-full rounded-t-lg object-cover md:h-auto md:w-48 md:rounded-none md:rounded-l-lg"}}},yo={root:{base:"relative h-full w-full",leftControl:"absolute top-0 left-0 flex h-full items-center justify-center px-4 focus:outline-none",rightControl:"absolute top-0 right-0 flex h-full items-center justify-center px-4 focus:outline-none"},indicators:{active:{off:"bg-white/50 hover:bg-white dark:bg-gray-800/50 dark:hover:bg-gray-800",on:"bg-white dark:bg-gray-800"},base:"h-3 w-3 rounded-full",wrapper:"absolute bottom-5 left-1/2 flex -translate-x-1/2 space-x-3"},item:{base:"absolute top-1/2 left-1/2 block w-full -translate-x-1/2 -translate-y-1/2",wrapper:"w-full flex-shrink-0 transform cursor-grab snap-center"},control:{base:"inline-flex h-8 w-8 items-center justify-center rounded-full bg-white/30 group-hover:bg-white/50 group-focus:outline-none group-focus:ring-4 group-focus:ring-white dark:bg-gray-800/30 dark:group-hover:bg-gray-800/60 dark:group-focus:ring-gray-800/70 sm:h-10 sm:w-10",icon:"h-5 w-5 text-white dark:text-gray-800 sm:h-6 sm:w-6"},scrollContainer:{base:"flex h-full snap-mandatory overflow-y-hidden overflow-x-scroll scroll-smooth rounded-lg",snap:"snap-x"}},xo={root:{base:"h-4 w-4 rounded focus:ring-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 bg-gray-100",color:{default:"focus:ring-cyan-600 dark:ring-offset-cyan-600 dark:focus:ring-cyan-600 text-cyan-600",dark:"focus:ring-gray-800 dark:ring-offset-gray-800 dark:focus:ring-gray-800 text-gray-800",failure:"focus:ring-red-900 dark:ring-offset-red-900 dark:focus:ring-red-900 text-red-900",gray:"focus:ring-gray-900 dark:ring-offset-gray-900 dark:focus:ring-gray-900 text-gray-900",info:"focus:ring-cyan-800 dark:ring-offset-gray-800 dark:focus:ring-cyan-800 text-cyan-800",light:"focus:ring-gray-900 dark:ring-offset-gray-900 dark:focus:ring-gray-900 text-gray-900",purple:"focus:ring-purple-600 dark:ring-offset-purple-600 dark:focus:ring-purple-600 text-purple-600",success:"focus:ring-green-800 dark:ring-offset-green-800 dark:focus:ring-green-800 text-green-800",warning:"focus:ring-yellow-400 dark:ring-offset-yellow-400 dark:focus:ring-yellow-400 text-yellow-400",blue:"focus:ring-blue-600 dark:ring-offset-blue-700 dark:focus:ring-blue-700 text-blue-700",cyan:"focus:ring-cyan-600 dark:ring-offset-cyan-600 dark:focus:ring-cyan-600 text-cyan-600",green:"focus:ring-green-600 dark:ring-offset-green-600 dark:focus:ring-green-600 text-green-600",indigo:"focus:ring-indigo-700 dark:ring-offset-indigo-700 dark:focus:ring-indigo-700 text-indigo-700",lime:"focus:ring-lime-700 dark:ring-offset-lime-700 dark:focus:ring-lime-700 text-lime-700",pink:"focus:ring-pink-600 dark:ring-offset-pink-600 dark:focus:ring-pink-600 text-pink-600",red:"focus:ring-red-600 dark:ring-offset-red-600 dark:focus:ring-red-600 text-red-600",teal:"focus:ring-teal-600 dark:ring-offset-teal-600 dark:focus:ring-teal-600 text-teal-600",yellow:"focus:ring-yellow-400 dark:ring-offset-yellow-400 dark:focus:ring-yellow-400 text-yellow-400"}}},vo={root:{base:"rounded-lg p-2.5 text-sm text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-700",icon:"h-5 w-5"}},ko={root:{base:"relative"},popup:{root:{base:"absolute top-10 z-50 block pt-2",inline:"relative top-0 z-auto",inner:"inline-block rounded-lg bg-white p-4 shadow-lg dark:bg-gray-700"},header:{base:"",title:"px-2 py-3 text-center font-semibold text-gray-900 dark:text-white",selectors:{base:"flex justify-between mb-2",button:{base:"text-sm rounded-lg text-gray-900 dark:text-white bg-white dark:bg-gray-700 font-semibold py-2.5 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-200 view-switch",prev:"",next:"",view:""}}},view:{base:"p-1"},footer:{base:"flex mt-2 space-x-2",button:{base:"w-full rounded-lg px-5 py-2 text-center text-sm font-medium focus:ring-4 focus:ring-cyan-300",today:"bg-cyan-700 text-white hover:bg-cyan-800 dark:bg-cyan-600 dark:hover:bg-cyan-700",clear:"border border-gray-300 bg-white text-gray-900 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600"}}},views:{days:{header:{base:"grid grid-cols-7 mb-1",title:"dow h-6 text-center text-sm font-medium leading-6 text-gray-500 dark:text-gray-400"},items:{base:"grid w-64 grid-cols-7",item:{base:"block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600 ",selected:"bg-cyan-700 text-white hover:bg-cyan-600",disabled:"text-gray-500"}}},months:{items:{base:"grid w-64 grid-cols-4",item:{base:"block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",selected:"bg-cyan-700 text-white hover:bg-cyan-600",disabled:"text-gray-500"}}},years:{items:{base:"grid w-64 grid-cols-4",item:{base:"block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600 text-gray-900",selected:"bg-cyan-700 text-white hover:bg-cyan-600",disabled:"text-gray-500"}}},decades:{items:{base:"grid w-64 grid-cols-4",item:{base:"block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9  hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600 text-gray-900",selected:"bg-cyan-700 text-white hover:bg-cyan-600",disabled:"text-gray-500"}}}}},wo={arrowIcon:"ml-2 h-4 w-4",content:"py-1 focus:outline-none",floating:{animation:"transition-opacity",arrow:{base:"absolute z-10 h-2 w-2 rotate-45",style:{dark:"bg-gray-900 dark:bg-gray-700",light:"bg-white",auto:"bg-white dark:bg-gray-700"},placement:"-4px"},base:"z-10 w-fit rounded divide-y divide-gray-100 shadow focus:outline-none",content:"py-1 text-sm text-gray-700 dark:text-gray-200",divider:"my-1 h-px bg-gray-100 dark:bg-gray-600",header:"block py-2 px-4 text-sm text-gray-700 dark:text-gray-200",hidden:"invisible opacity-0",item:{container:"",base:"flex items-center justify-start py-2 px-4 text-sm text-gray-700 cursor-pointer w-full hover:bg-gray-100 focus:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-600 focus:outline-none dark:hover:text-white dark:focus:bg-gray-600 dark:focus:text-white",icon:"mr-2 h-4 w-4"},style:{dark:"bg-gray-900 text-white dark:bg-gray-700",light:"border border-gray-200 bg-white text-gray-900",auto:"border border-gray-200 bg-white text-gray-900 dark:border-none dark:bg-gray-700 dark:text-white"},target:"w-fit"},inlineWrapper:"flex items-center"},Co={root:{base:"flex"},field:{base:"relative w-full",input:{base:"rounded-lg overflow-hidden block w-full border disabled:cursor-not-allowed disabled:opacity-50",sizes:{sm:"sm:text-xs",md:"text-sm",lg:"sm:text-md"},colors:{gray:"bg-gray-50 border-gray-300 text-gray-900 focus:border-cyan-500 focus:ring-cyan-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-cyan-500 dark:focus:ring-cyan-500",info:"border-cyan-500 bg-cyan-50 text-cyan-900 placeholder-cyan-700 focus:border-cyan-500 focus:ring-cyan-500 dark:border-cyan-400 dark:bg-cyan-100 dark:focus:border-cyan-500 dark:focus:ring-cyan-500",failure:"border-red-500 bg-red-50 text-red-900 placeholder-red-700 focus:border-red-500 focus:ring-red-500 dark:border-red-400 dark:bg-red-100 dark:focus:border-red-500 dark:focus:ring-red-500",warning:"border-yellow-500 bg-yellow-50 text-yellow-900 placeholder-yellow-700 focus:border-yellow-500 focus:ring-yellow-500 dark:border-yellow-400 dark:bg-yellow-100 dark:focus:border-yellow-500 dark:focus:ring-yellow-500",success:"border-green-500 bg-green-50 text-green-900 placeholder-green-700 focus:border-green-500 focus:ring-green-500 dark:border-green-400 dark:bg-green-100 dark:focus:border-green-500 dark:focus:ring-green-500"}}}},No={input:{default:{filled:{sm:"peer block w-full appearance-none rounded-t-lg border-0 border-b-2 border-gray-300 bg-gray-50 px-2.5 pb-2.5 pt-5 text-xs text-gray-900 focus:border-blue-600 focus:outline-none focus:ring-0 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-500",md:"peer block w-full appearance-none rounded-t-lg border-0 border-b-2 border-gray-300 bg-gray-50 px-2.5 pb-2.5 pt-5 text-sm text-gray-900 focus:border-blue-600 focus:outline-none focus:ring-0 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-500"},outlined:{sm:"border-1 peer block w-full appearance-none rounded-lg border-gray-300 bg-transparent px-2.5 pb-2.5 pt-4 text-xs text-gray-900 focus:border-blue-600 focus:outline-none focus:ring-0 dark:border-gray-600 dark:text-white dark:focus:border-blue-500",md:"border-1 peer block w-full appearance-none rounded-lg border-gray-300 bg-transparent px-2.5 pb-2.5 pt-4 text-sm text-gray-900 focus:border-blue-600 focus:outline-none focus:ring-0 dark:border-gray-600 dark:text-white dark:focus:border-blue-500"},standard:{sm:"block py-2.5 px-0 w-full text-xs text-gray-900 bg-transparent border-0 border-b-2 border-gray-300 appearance-none dark:text-white dark:border-gray-600 dark:focus:border-blue-500 focus:outline-none focus:ring-0 focus:border-blue-600 peer",md:"block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b-2 border-gray-300 appearance-none dark:text-white dark:border-gray-600 dark:focus:border-blue-500 focus:outline-none focus:ring-0 focus:border-blue-600 peer"}},success:{filled:{sm:"block rounded-t-lg px-2.5 pb-2.5 pt-5 w-full text-xs text-gray-900 bg-gray-50 dark:bg-gray-700 border-0 border-b-2 border-green-600 dark:border-green-500 appearance-none dark:text-white dark:focus:border-green-500 focus:outline-none focus:ring-0 focus:border-green-600 peer",md:"block rounded-t-lg px-2.5 pb-2.5 pt-5 w-full text-sm text-gray-900 bg-gray-50 dark:bg-gray-700 border-0 border-b-2 border-green-600 dark:border-green-500 appearance-none dark:text-white dark:focus:border-green-500 focus:outline-none focus:ring-0 focus:border-green-600 peer"},outlined:{sm:"block px-2.5 pb-2.5 pt-4 w-full text-xs text-gray-900 bg-transparent rounded-lg border-1 border-green-600 appearance-none dark:text-white dark:border-green-500 dark:focus:border-green-500 focus:outline-none focus:ring-0 focus:border-green-600 peer",md:"block px-2.5 pb-2.5 pt-4 w-full text-sm text-gray-900 bg-transparent rounded-lg border-1 border-green-600 appearance-none dark:text-white dark:border-green-500 dark:focus:border-green-500 focus:outline-none focus:ring-0 focus:border-green-600 peer"},standard:{sm:"block py-2.5 px-0 w-full text-xs text-gray-900 bg-transparent border-0 border-b-2 border-green-600 appearance-none dark:text-white dark:border-green-500 dark:focus:border-green-500 focus:outline-none focus:ring-0 focus:border-green-600 peer",md:"block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b-2 border-green-600 appearance-none dark:text-white dark:border-green-500 dark:focus:border-green-500 focus:outline-none focus:ring-0 focus:border-green-600 peer"}},error:{filled:{sm:"block rounded-t-lg px-2.5 pb-2.5 pt-5 w-full text-xs text-gray-900 bg-gray-50 dark:bg-gray-700 border-0 border-b-2 appearance-none dark:text-white dark:border-red-500 focus:outline-none focus:ring-0 border-red-600 focus:border-red-600 dark:focus-border-red-500 peer",md:"block rounded-t-lg px-2.5 pb-2.5 pt-5 w-full text-sm text-gray-900 bg-gray-50 dark:bg-gray-700 border-0 border-b-2 appearance-none dark:text-white dark:border-red-500 focus:outline-none focus:ring-0 border-red-600 focus:border-red-600 dark:focus-border-red-500 peer"},outlined:{sm:"block px-2.5 pb-2.5 pt-4 w-full text-xs text-gray-900 bg-transparent rounded-lg border-1 appearance-none dark:text-white dark:border-red-500 border-red-600 dark:focus:border-red-500 focus:outline-none focus:ring-0 focus:border-red-600 peer",md:"block px-2.5 pb-2.5 pt-4 w-full text-sm text-gray-900 bg-transparent rounded-lg border-1 appearance-none dark:text-white dark:border-red-500 border-red-600 dark:focus:border-red-500 focus:outline-none focus:ring-0 focus:border-red-600 peer"},standard:{sm:"block py-2.5 px-0 w-full text-xs text-gray-900 bg-transparent border-0 border-b-2 border-red-600 appearance-none dark:text-white dark:border-red-500 dark:focus:border-red-500 focus:outline-none focus:ring-0 focus:border-red-600 peer",md:"block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b-2 border-red-600 appearance-none dark:text-white dark:border-red-500 dark:focus:border-red-500 focus:outline-none focus:ring-0 focus:border-red-600 peer"}}},label:{default:{filled:{sm:"absolute left-2.5 top-4 z-10 origin-[0] -translate-y-4 scale-75 transition-transform text-xs text-gray-500  duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:text-blue-600 dark:text-gray-400 peer-focus:dark:text-blue-500",md:"absolute left-2.5 top-4 z-10 origin-[0] -translate-y-4 scale-75 transition-transform text-sm text-gray-500 duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:text-blue-600 dark:text-gray-400 peer-focus:dark:text-blue-500"},outlined:{sm:"absolute left-1 top-2 z-10 origin-[0] -translate-y-4 scale-75 transition-transform bg-white px-2 text-xs text-gray-500 duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2 peer-focus:text-blue-600 dark:bg-gray-900 dark:text-gray-400 peer-focus:dark:text-blue-500",md:"absolute left-1 top-2 z-10 origin-[0] -translate-y-4 scale-75 transition-transform bg-white px-2 text-sm text-gray-500 duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2 peer-focus:text-blue-600 dark:bg-gray-900 dark:text-gray-400 peer-focus:dark:text-blue-500"},standard:{sm:"absolute text-xs text-gray-500 dark:text-gray-400  transition-transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] duration-300 peer-focus:left-0 peer-focus:text-blue-600 peer-focus:dark:text-blue-500 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6",md:"absolute text-sm text-gray-500 dark:text-gray-400  transition-transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] duration-300 peer-focus:left-0 peer-focus:text-blue-600 peer-focus:dark:text-blue-500 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"}},success:{filled:{sm:"absolute left-2.5 top-4 z-10 origin-[0] -translate-y-4 scale-75 transition-transform text-sm text-green-600 duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:-translate-y-4 peer-focus:scale-75 dark:text-green-500",md:"absolute left-2.5 top-4 z-10 origin-[0] -translate-y-4 scale-75 transition-transform text-sm text-green-600 duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:-translate-y-4 peer-focus:scale-75 dark:text-green-500"},outlined:{sm:"absolute left-1 top-2 z-10 origin-[0] -translate-y-4 scale-75 transition-transform bg-white px-2 text-sm text-green-600 duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2 dark:bg-gray-900 dark:text-green-500",md:"absolute left-1 top-2 z-10 origin-[0] -translate-y-4 scale-75 transition-transform bg-white px-2 text-sm text-green-600 duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2 dark:bg-gray-900 dark:text-green-500"},standard:{sm:"absolute text-xs text-green-600 dark:text-green-500  transition-transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] duration-300 peer-focus:left-0 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6",md:"absolute text-sm text-green-600 dark:text-green-500  transition-transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] duration-300 peer-focus:left-0 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"}},error:{filled:{sm:"absolute left-2.5 top-4 z-10 origin-[0] -translate-y-4 scale-75 transition-transform text-xs text-red-600 duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:-translate-y-4 peer-focus:scale-75 dark:text-red-500",md:"absolute left-2.5 top-4 z-10 origin-[0] -translate-y-4 scale-75 transition-transform text-xs text-red-600 duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:-translate-y-4 peer-focus:scale-75 dark:text-red-500"},outlined:{sm:"absolute left-1 top-2 z-10 origin-[0] -translate-y-4 scale-75 transition-transform bg-white px-2 text-xs text-red-600 duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2 dark:bg-gray-900 dark:text-red-500",md:"absolute left-1 top-2 z-10 origin-[0] -translate-y-4 scale-75 transition-transform bg-white px-2 text-xs text-red-600 duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2 dark:bg-gray-900 dark:text-red-500"},standard:{sm:"absolute text-xs text-red-600 dark:text-red-500  transition-transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] duration-300 peer-focus:left-0 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6",md:"absolute text-sm text-red-600 dark:text-red-500  transition-transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] duration-300 peer-focus:left-0 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"}}},helperText:{default:"mt-2 text-xs text-gray-600 dark:text-gray-400",success:"mt-2 text-xs text-green-600 dark:text-green-400",error:"mt-2 text-xs text-red-600 dark:text-red-400"}},To={root:{base:"w-full rounded-lg bg-white shadow dark:bg-gray-800 md:flex md:items-center md:justify-between",container:"w-full p-6",bgDark:"bg-gray-800"},groupLink:{base:"flex flex-wrap text-sm text-gray-500 dark:text-white",link:{base:"last:mr-0 md:mr-6",href:"hover:underline"},col:"flex-col space-y-4"},icon:{base:"text-gray-500 dark:hover:text-white",size:"h-5 w-5"},title:{base:"mb-6 text-sm font-semibold uppercase text-gray-500 dark:text-white"},divider:{base:"w-full my-6 border-gray-200 sm:mx-auto dark:border-gray-700 lg:my-8"},copyright:{base:"text-sm text-gray-500 dark:text-gray-400 sm:text-center",href:"ml-1 hover:underline",span:"ml-1"},brand:{base:"mb-4 flex items-center sm:mb-0",img:"mr-3 h-8",span:"self-center whitespace-nowrap text-2xl font-semibold text-gray-800 dark:text-white"}},jo={root:{base:"mt-2 text-sm",colors:{gray:"text-gray-500 dark:text-gray-400",info:"text-cyan-700 dark:text-cyan-800",success:"text-green-600 dark:text-green-500",failure:"text-red-600 dark:text-red-500",warning:"text-yellow-500 dark:text-yellow-600"}}},Ro={root:{base:"px-2 py-1.5 mr-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg dark:bg-gray-600 dark:text-gray-100 dark:border-gray-500",icon:"inline-block"}},Eo={root:{base:"text-sm font-medium",disabled:"opacity-50",colors:{default:"text-gray-900 dark:text-white",info:"text-cyan-500 dark:text-cyan-600",failure:"text-red-700 dark:text-red-500",warning:"text-yellow-500 dark:text-yellow-600",success:"text-green-700 dark:text-green-500"}}},Io={root:{base:"list-none rounded-lg border border-gray-200 bg-white text-sm font-medium text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white text-left"},item:{base:"[&>*]:first:rounded-t-lg [&>*]:last:rounded-b-lg [&>*]:last:border-b-0",link:{base:"flex items-center w-full border-b border-gray-200 py-2 px-4 dark:border-gray-600",active:{off:"hover:bg-gray-100 hover:text-cyan-700 focus:text-cyan-700 focus:outline-none focus:ring-2 focus:ring-cyan-700 dark:border-gray-600 dark:hover:bg-gray-600 dark:hover:text-white dark:focus:text-white dark:focus:ring-gray-500",on:"bg-cyan-700 text-white dark:bg-gray-800"},href:{off:"",on:""},icon:"mr-2 h-4 w-4 fill-current"}}},So={root:{base:"fixed top-0 right-0 left-0 z-50 h-modal h-screen overflow-y-auto overflow-x-hidden md:inset-0 md:h-full",show:{on:"flex bg-gray-900 bg-opacity-50 dark:bg-opacity-80",off:"hidden"},sizes:{sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg",xl:"max-w-xl","2xl":"max-w-2xl","3xl":"max-w-3xl","4xl":"max-w-4xl","5xl":"max-w-5xl","6xl":"max-w-6xl","7xl":"max-w-7xl"},positions:{"top-left":"items-start justify-start","top-center":"items-start justify-center","top-right":"items-start justify-end","center-left":"items-center justify-start",center:"items-center justify-center","center-right":"items-center justify-end","bottom-right":"items-end justify-end","bottom-center":"items-end justify-center","bottom-left":"items-end justify-start"}},content:{base:"relative h-full w-full p-4 md:h-auto",inner:"relative rounded-lg bg-white shadow dark:bg-gray-700 flex flex-col max-h-[90vh]"},body:{base:"p-6 flex-1 overflow-auto",popup:"pt-0"},header:{base:"flex items-start justify-between rounded-t dark:border-gray-600 border-b p-5",popup:"p-2 border-b-0",title:"text-xl font-medium text-gray-900 dark:text-white",close:{base:"ml-auto inline-flex items-center rounded-lg bg-transparent p-1.5 text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white",icon:"h-5 w-5"}},footer:{base:"flex items-center space-x-2 rounded-b border-gray-200 p-6 dark:border-gray-600",popup:"border-t"}},Mo={root:{base:"bg-white px-2 py-2.5 dark:border-gray-700 dark:bg-gray-800 sm:px-4",rounded:{on:"rounded",off:""},bordered:{on:"border",off:""},inner:{base:"mx-auto flex flex-wrap items-center justify-between",fluid:{on:"",off:"container"}}},brand:{base:"flex items-center"},collapse:{base:"w-full md:block md:w-auto",list:"mt-4 flex flex-col md:mt-0 md:flex-row md:space-x-8 md:text-sm md:font-medium",hidden:{on:"hidden",off:""}},link:{base:"block py-2 pr-4 pl-3 md:p-0",active:{on:"bg-cyan-700 text-white dark:text-white md:bg-transparent md:text-cyan-700",off:"border-b border-gray-100  text-gray-700 hover:bg-gray-50 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white md:border-0 md:hover:bg-transparent md:hover:text-cyan-700 md:dark:hover:bg-transparent md:dark:hover:text-white"},disabled:{on:"text-gray-400 hover:cursor-not-allowed dark:text-gray-600",off:""}},toggle:{base:"inline-flex items-center rounded-lg p-2 text-sm text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-600 md:hidden",icon:"h-6 w-6 shrink-0"}},Oo={base:"",layout:{table:{base:"text-sm text-gray-700 dark:text-gray-400",span:"font-semibold text-gray-900 dark:text-white"}},pages:{base:"xs:mt-0 mt-2 inline-flex items-center -space-x-px",showIcon:"inline-flex",previous:{base:"ml-0 rounded-l-lg border border-gray-300 bg-white py-2 px-3 leading-tight text-gray-500 enabled:hover:bg-gray-100 enabled:hover:text-gray-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 enabled:dark:hover:bg-gray-700 enabled:dark:hover:text-white",icon:"h-5 w-5"},next:{base:"rounded-r-lg border border-gray-300 bg-white py-2 px-3 leading-tight text-gray-500 enabled:hover:bg-gray-100 enabled:hover:text-gray-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 enabled:dark:hover:bg-gray-700 enabled:dark:hover:text-white",icon:"h-5 w-5"},selector:{base:"w-12 border border-gray-300 bg-white py-2 leading-tight text-gray-500 enabled:hover:bg-gray-100 enabled:hover:text-gray-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 enabled:dark:hover:bg-gray-700 enabled:dark:hover:text-white",active:"bg-cyan-50 text-cyan-600 hover:bg-cyan-100 hover:text-cyan-700 dark:border-gray-700 dark:bg-gray-700 dark:text-white",disabled:"opacity-50 cursor-normal"}}},Po={base:"w-full overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700",label:"mb-1 flex justify-between font-medium dark:text-white",bar:"rounded-full text-center font-medium leading-none text-cyan-300 dark:text-cyan-100 space-x-2",color:{dark:"bg-gray-600 dark:bg-gray-300",blue:"bg-cyan-600",red:"bg-red-600 dark:bg-red-500",green:"bg-green-600 dark:bg-green-500",yellow:"bg-yellow-400",indigo:"bg-indigo-600 dark:bg-indigo-500",purple:"bg-purple-600 dark:bg-purple-500"},size:{sm:"h-1.5",md:"h-2.5",lg:"h-4",xl:"h-6"}},Lo={root:{base:"h-4 w-4 border border-gray-300 focus:ring-2 focus:ring-cyan-500 dark:border-gray-600 dark:bg-gray-700 dark:focus:bg-cyan-600 dark:focus:ring-cyan-600 text-cyan-600"}},Ao={root:{base:"flex"},field:{base:"relative w-full",input:{base:"w-full bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700",sizes:{sm:"h-1 range-sm",md:"h-2",lg:"h-3 range-lg"}}}},Fo={root:{base:"flex items-center"},advanced:{base:"flex items-center",label:"text-sm font-medium text-cyan-600 dark:text-cyan-500",progress:{base:"mx-4 h-5 w-2/4 rounded bg-gray-200 dark:bg-gray-700",fill:"h-5 rounded bg-yellow-400",label:"text-sm font-medium text-cyan-600 dark:text-cyan-500"}},star:{empty:"text-gray-300 dark:text-gray-500",filled:"text-yellow-400",sizes:{sm:"w-5 h-5",md:"w-7 h-7",lg:"w-10 h-10"}}},Do={base:"flex",addon:"inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-200 px-3 text-sm text-gray-900 dark:border-gray-600 dark:bg-gray-600 dark:text-gray-400",field:{base:"relative w-full",icon:{base:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",svg:"h-5 w-5 text-gray-500 dark:text-gray-400"},select:{base:"block w-full border disabled:cursor-not-allowed disabled:opacity-50",withIcon:{on:"pl-10",off:""},withAddon:{on:"rounded-r-lg",off:"rounded-lg"},withShadow:{on:"shadow-sm dark:shadow-sm-light",off:""},sizes:{sm:"p-2 sm:text-xs",md:"p-2.5 text-sm",lg:"sm:text-md p-4"},colors:{gray:"bg-gray-50 border-gray-300 text-gray-900 focus:border-cyan-500 focus:ring-cyan-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-cyan-500 dark:focus:ring-cyan-500",info:"border-cyan-500 bg-cyan-50 text-cyan-900 placeholder-cyan-700 focus:border-cyan-500 focus:ring-cyan-500 dark:border-cyan-400 dark:bg-cyan-100 dark:focus:border-cyan-500 dark:focus:ring-cyan-500",failure:"border-red-500 bg-red-50 text-red-900 placeholder-red-700 focus:border-red-500 focus:ring-red-500 dark:border-red-400 dark:bg-red-100 dark:focus:border-red-500 dark:focus:ring-red-500",warning:"border-yellow-500 bg-yellow-50 text-yellow-900 placeholder-yellow-700 focus:border-yellow-500 focus:ring-yellow-500 dark:border-yellow-400 dark:bg-yellow-100 dark:focus:border-yellow-500 dark:focus:ring-yellow-500",success:"border-green-500 bg-green-50 text-green-900 placeholder-green-700 focus:border-green-500 focus:ring-green-500 dark:border-green-400 dark:bg-green-100 dark:focus:border-green-500 dark:focus:ring-green-500"}}}},zo={root:{base:"h-full",collapsed:{on:"w-16",off:"w-64"},inner:"h-full overflow-y-auto overflow-x-hidden rounded bg-gray-50 py-4 px-3 dark:bg-gray-800"},collapse:{button:"group flex w-full items-center rounded-lg p-2 text-base font-normal text-gray-900 transition duration-75 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700",icon:{base:"h-6 w-6 text-gray-500 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white",open:{off:"",on:"text-gray-900"}},label:{base:"ml-3 flex-1 whitespace-nowrap text-left",icon:{base:"h-6 w-6 transition ease-in-out delay-0",open:{on:"rotate-180",off:""}}},list:"space-y-2 py-2"},cta:{base:"mt-6 rounded-lg p-4 bg-gray-100 dark:bg-gray-700",color:{blue:"bg-cyan-50 dark:bg-cyan-900",dark:"bg-dark-50 dark:bg-dark-900",failure:"bg-red-50 dark:bg-red-900",gray:"bg-alternative-50 dark:bg-alternative-900",green:"bg-green-50 dark:bg-green-900",light:"bg-light-50 dark:bg-light-900",red:"bg-red-50 dark:bg-red-900",purple:"bg-purple-50 dark:bg-purple-900",success:"bg-green-50 dark:bg-green-900",yellow:"bg-yellow-50 dark:bg-yellow-900",warning:"bg-yellow-50 dark:bg-yellow-900"}},item:{base:"flex items-center justify-center rounded-lg p-2 text-base font-normal text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700",active:"bg-gray-100 dark:bg-gray-700",collapsed:{insideCollapse:"group w-full pl-8 transition duration-75",noIcon:"font-bold"},content:{base:"px-3 flex-1 whitespace-nowrap"},icon:{base:"h-6 w-6 flex-shrink-0 text-gray-500 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white",active:"text-gray-700 dark:text-gray-100"},label:"",listItem:""},items:"",itemGroup:"mt-4 space-y-2 border-t border-gray-200 pt-4 first:mt-0 first:border-t-0 first:pt-0 dark:border-gray-700",logo:{base:"mb-5 flex items-center pl-2.5",collapsed:{on:"hidden",off:"self-center whitespace-nowrap text-xl font-semibold dark:text-white"},img:"mr-3 h-6 sm:h-7"}},Bo={base:"inline animate-spin text-gray-200",color:{failure:"fill-red-600",gray:"fill-gray-600",info:"fill-cyan-600",pink:"fill-pink-600",purple:"fill-purple-600",success:"fill-green-500",warning:"fill-yellow-400"},light:{off:{base:"dark:text-gray-600",color:{failure:"",gray:"dark:fill-gray-300",info:"",pink:"",purple:"",success:"",warning:""}},on:{base:"",color:{failure:"",gray:"",info:"",pink:"",purple:"",success:"",warning:""}}},size:{xs:"w-3 h-3",sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8",xl:"w-10 h-10"}},Ho={base:"flex flex-col gap-2",tablist:{base:"flex text-center",styles:{default:"flex-wrap border-b border-gray-200 dark:border-gray-700",underline:"flex-wrap -mb-px border-b border-gray-200 dark:border-gray-700",pills:"flex-wrap font-medium text-sm text-gray-500 dark:text-gray-400 space-x-2",fullWidth:"w-full text-sm font-medium divide-x divide-gray-200 shadow grid grid-flow-col dark:divide-gray-700 dark:text-gray-400 rounded-none"},tabitem:{base:"flex items-center justify-center p-4 rounded-t-lg text-sm font-medium first:ml-0 disabled:cursor-not-allowed disabled:text-gray-400 disabled:dark:text-gray-500 focus:ring-4 focus:ring-cyan-300 focus:outline-none",styles:{default:{base:"rounded-t-lg",active:{on:"bg-gray-100 text-cyan-600 dark:bg-gray-800 dark:text-cyan-500",off:"text-gray-500 hover:bg-gray-50 hover:text-gray-600 dark:text-gray-400 dark:hover:bg-gray-800  dark:hover:text-gray-300"}},underline:{base:"rounded-t-lg",active:{on:"text-cyan-600 rounded-t-lg border-b-2 border-cyan-600 active dark:text-cyan-500 dark:border-cyan-500",off:"border-b-2 border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300"}},pills:{base:"",active:{on:"rounded-lg bg-cyan-600 text-white",off:"rounded-lg hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-gray-800 dark:hover:text-white"}},fullWidth:{base:"ml-0 first:ml-0 w-full rounded-none flex",active:{on:"p-4 text-gray-900 bg-gray-100 active dark:bg-gray-700 dark:text-white rounded-none",off:"bg-white hover:text-gray-700 hover:bg-gray-50 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700 rounded-none"}}},icon:"mr-2 h-5 w-5"}},tabitemcontainer:{base:"",styles:{default:"",underline:"",pills:"",fullWidth:""}},tabpanel:"py-3"},_o={root:{base:"w-full text-left text-sm text-gray-500 dark:text-gray-400",shadow:"absolute bg-white dark:bg-black w-full h-full top-0 left-0 rounded-lg drop-shadow-md -z-10",wrapper:"relative"},body:{base:"group/body",cell:{base:"group-first/body:group-first/row:first:rounded-tl-lg group-first/body:group-first/row:last:rounded-tr-lg group-last/body:group-last/row:first:rounded-bl-lg group-last/body:group-last/row:last:rounded-br-lg px-6 py-4"}},head:{base:"group/head text-xs uppercase text-gray-700 dark:text-gray-400",cell:{base:"group-first/head:first:rounded-tl-lg group-first/head:last:rounded-tr-lg bg-gray-50 dark:bg-gray-700 px-6 py-3"}},row:{base:"group/row",hovered:"hover:bg-gray-50 dark:hover:bg-gray-600",striped:"odd:bg-white even:bg-gray-50 odd:dark:bg-gray-800 even:dark:bg-gray-700"}},Ko={base:"flex",addon:"inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-200 px-3 text-sm text-gray-900 dark:border-gray-600 dark:bg-gray-600 dark:text-gray-400",field:{base:"relative w-full",icon:{base:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",svg:"h-5 w-5 text-gray-500 dark:text-gray-400"},rightIcon:{base:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3",svg:"h-5 w-5 text-gray-500 dark:text-gray-400"},input:{base:"block w-full border disabled:cursor-not-allowed disabled:opacity-50",sizes:{sm:"p-2 sm:text-xs",md:"p-2.5 text-sm",lg:"sm:text-md p-4"},colors:{gray:"bg-gray-50 border-gray-300 text-gray-900 focus:border-cyan-500 focus:ring-cyan-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-cyan-500 dark:focus:ring-cyan-500",info:"border-cyan-500 bg-cyan-50 text-cyan-900 placeholder-cyan-700 focus:border-cyan-500 focus:ring-cyan-500 dark:border-cyan-400 dark:bg-cyan-100 dark:focus:border-cyan-500 dark:focus:ring-cyan-500",failure:"border-red-500 bg-red-50 text-red-900 placeholder-red-700 focus:border-red-500 focus:ring-red-500 dark:border-red-400 dark:bg-red-100 dark:focus:border-red-500 dark:focus:ring-red-500",warning:"border-yellow-500 bg-yellow-50 text-yellow-900 placeholder-yellow-700 focus:border-yellow-500 focus:ring-yellow-500 dark:border-yellow-400 dark:bg-yellow-100 dark:focus:border-yellow-500 dark:focus:ring-yellow-500",success:"border-green-500 bg-green-50 text-green-900 placeholder-green-700 focus:border-green-500 focus:ring-green-500 dark:border-green-400 dark:bg-green-100 dark:focus:border-green-500 dark:focus:ring-green-500"},withRightIcon:{on:"pr-10",off:""},withIcon:{on:"pl-10",off:""},withAddon:{on:"rounded-r-lg",off:"rounded-lg"},withShadow:{on:"shadow-sm dark:shadow-sm-light",off:""}}}},Go={base:"block w-full rounded-lg border disabled:cursor-not-allowed disabled:opacity-50 text-sm",colors:{gray:"bg-gray-50 border-gray-300 text-gray-900 focus:border-cyan-500 focus:ring-cyan-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-cyan-500 dark:focus:ring-cyan-500",info:"border-cyan-500 bg-cyan-50 text-cyan-900 placeholder-cyan-700 focus:border-cyan-500 focus:ring-cyan-500 dark:border-cyan-400 dark:bg-cyan-100 dark:focus:border-cyan-500 dark:focus:ring-cyan-500",failure:"border-red-500 bg-red-50 text-red-900 placeholder-red-700 focus:border-red-500 focus:ring-red-500 dark:border-red-400 dark:bg-red-100 dark:focus:border-red-500 dark:focus:ring-red-500",warning:"border-yellow-500 bg-yellow-50 text-yellow-900 placeholder-yellow-700 focus:border-yellow-500 focus:ring-yellow-500 dark:border-yellow-400 dark:bg-yellow-100 dark:focus:border-yellow-500 dark:focus:ring-yellow-500",success:"border-green-500 bg-green-50 text-green-900 placeholder-green-700 focus:border-green-500 focus:ring-green-500 dark:border-green-400 dark:bg-green-100 dark:focus:border-green-500 dark:focus:ring-green-500"},withShadow:{on:"shadow-sm dark:shadow-sm-light",off:""}},Wo={root:{direction:{horizontal:"items-base sm:flex",vertical:"relative border-l border-gray-200 dark:border-gray-700"}},item:{root:{horizontal:"relative mb-6 sm:mb-0",vertical:"mb-10 ml-6"},content:{root:{base:"mt-3 sm:pr-8"},body:"mb-4 text-base font-normal text-gray-500 dark:text-gray-400",time:"mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500",title:"text-lg font-semibold text-gray-900 dark:text-white"},point:{horizontal:"flex items-center",line:"hidden h-0.5 w-full bg-gray-200 dark:bg-gray-700 sm:flex",marker:{base:{horizontal:"absolute -left-1.5 h-3 w-3 rounded-full border border-white bg-gray-200 dark:border-gray-900 dark:bg-gray-700",vertical:"absolute -left-1.5 mt-1.5 h-3 w-3 rounded-full border border-white bg-gray-200 dark:border-gray-900 dark:bg-gray-700"},icon:{base:"h-3 w-3 text-cyan-600 dark:text-cyan-300",wrapper:"absolute -left-3 flex h-6 w-6 items-center justify-center rounded-full bg-cyan-200 ring-8 ring-white dark:bg-cyan-900 dark:ring-gray-900"}},vertical:""}}},$o={root:{base:"flex w-full max-w-xs items-center rounded-lg bg-white p-4 text-gray-500 shadow dark:bg-gray-800 dark:text-gray-400",closed:"opacity-0 ease-out"},toggle:{base:"-mx-1.5 -my-1.5 ml-auto inline-flex h-8 w-8 rounded-lg bg-white p-1.5 text-gray-400 hover:bg-gray-100 hover:text-gray-900 focus:ring-2 focus:ring-gray-300 dark:bg-gray-800 dark:text-gray-500 dark:hover:bg-gray-700 dark:hover:text-white",icon:"h-5 w-5 shrink-0"}},Uo={root:{base:"group relative flex items-center rounded-lg focus:outline-none",active:{on:"cursor-pointer",off:"cursor-not-allowed opacity-50"},label:"ml-3 text-sm font-medium text-gray-900 dark:text-gray-300"},toggle:{base:"toggle-bg rounded-full border group-focus:ring-4 group-focus:ring-cyan-500/25",checked:{on:"after:translate-x-full after:border-white",off:"border-gray-200 bg-gray-200 dark:border-gray-600 dark:bg-gray-700",color:{blue:" bg-cyan-700 border-cyan-700",dark:"bg-dark-700 border-dark-900",failure:"bg-red-700 border-red-900",gray:"bg-gray-500 border-gray-600",green:"bg-green-600 border-green-700",light:"bg-light-700 border-light-900",red:"bg-red-700 border-red-900",purple:"bg-purple-700 border-purple-900",success:"bg-green-500 border-green-500",yellow:"bg-yellow-400 border-yellow-400",warning:"bg-yellow-600 border-yellow-600",cyan:"bg-cyan-500 border-cyan-500",lime:"bg-lime-400 border-lime-400",indigo:"bg-indigo-400 border-indigo-400",teal:"bg-gradient-to-r from-teal-400 via-teal-500 to-teal-600 hover:bg-gradient-to-br focus:ring-4",info:"bg-cyan-600 border-cyan-600",pink:"bg-pink-600 border-pink-600"}},sizes:{sm:"w-9 h-5 after:absolute after:top-[2px] after:left-[2px] after:h-4 after:w-4",md:"w-11 h-6 after:absolute after:top-[2px] after:left-[2px] after:h-5 after:w-5",lg:"w-14 h-7 after:absolute after:top-0.5 after:left-[4px] after:h-6 after:w-6"}}},Yo={target:"w-fit",animation:"transition-opacity",arrow:{base:"absolute z-10 h-2 w-2 rotate-45",style:{dark:"bg-gray-900 dark:bg-gray-700",light:"bg-white",auto:"bg-white dark:bg-gray-700"},placement:"-4px"},base:"absolute inline-block z-10 rounded-lg py-2 px-3 text-sm font-medium shadow-sm",hidden:"invisible opacity-0",style:{dark:"bg-gray-900 text-white dark:bg-gray-700",light:"border border-gray-200 bg-white text-gray-900",auto:"border border-gray-200 bg-white text-gray-900 dark:border-none dark:bg-gray-700 dark:text-white"},content:"relative z-20"},Xo={accordion:lo,alert:co,avatar:uo,badge:fo,blockquote:go,breadcrumb:bo,button:po,buttonGroup:ho,card:mo,carousel:yo,checkbox:xo,datepicker:ko,darkThemeToggle:vo,dropdown:wo,fileInput:Co,floatingLabel:No,footer:To,helperText:jo,kbd:Ro,label:Eo,listGroup:Io,modal:So,navbar:Mo,pagination:Oo,progress:Po,radio:Lo,rangeSlider:Ao,rating:Fo,select:Do,textInput:Ko,textarea:Go,toggleSwitch:Uo,sidebar:zo,spinner:Bo,tab:Ho,table:_o,timeline:Wo,toast:$o,tooltip:Yo},qo=c.createContext({theme:Xo}),T=()=>{const e=c.useContext(qo);if(!e)throw new Error("useTheme should be used within the ThemeContext provider!");return e};var ir=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},ke=new WeakMap,Pe=new WeakMap,Le={},Xe=0,lr=function(e){return e&&(e.host||lr(e.parentNode))},Vo=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=lr(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},dr=function(e,t,n,r){var o=Vo(t,Array.isArray(e)?e:[e]);Le[n]||(Le[n]=new WeakMap);var a=Le[n],s=[],i=new Set,d=new Set(o),f=function(u){!u||i.has(u)||(i.add(u),f(u.parentNode))};o.forEach(f);var b=function(u){!u||d.has(u)||Array.prototype.forEach.call(u.children,function(m){if(i.has(m))b(m);else try{var g=m.getAttribute(r),h=g!==null&&g!=="false",p=(ke.get(m)||0)+1,N=(a.get(m)||0)+1;ke.set(m,p),a.set(m,N),s.push(m),p===1&&h&&Pe.set(m,!0),N===1&&m.setAttribute(n,"true"),h||m.setAttribute(r,"true")}catch(R){console.error("aria-hidden: cannot operate on ",m,R)}})};return b(t),i.clear(),Xe++,function(){s.forEach(function(u){var m=ke.get(u)-1,g=a.get(u)-1;ke.set(u,m),a.set(u,g),m||(Pe.has(u)||u.removeAttribute(r),Pe.delete(u)),g||u.removeAttribute(n)}),Xe--,Xe||(ke=new WeakMap,ke=new WeakMap,Pe=new WeakMap,Le={})}},cr=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||ir(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),dr(r,o,n,"aria-hidden")):function(){return null}},Zo=function(e,t,n){n===void 0&&(n="data-inert-ed");var r=t||ir(e);return r?dr(e,r,n,"inert"):function(){return null}},ur=function(){return typeof HTMLElement<"u"&&HTMLElement.prototype.hasOwnProperty("inert")},Jo=function(e,t,n){return n===void 0&&(n="data-suppressed"),(ur()?Zo:cr)(e,t,n)};/*!
* tabbable 6.2.0
* @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
*/var Qo=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"],rt=Qo.join(","),fr=typeof Element>"u",Re=fr?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,ze=!fr&&Element.prototype.getRootNode?function(e){var t;return e==null||(t=e.getRootNode)===null||t===void 0?void 0:t.call(e)}:function(e){return e==null?void 0:e.ownerDocument},Be=function e(t,n){var r;n===void 0&&(n=!0);var o=t==null||(r=t.getAttribute)===null||r===void 0?void 0:r.call(t,"inert"),a=o===""||o==="true",s=a||n&&t&&e(t.parentNode);return s},ea=function(t){var n,r=t==null||(n=t.getAttribute)===null||n===void 0?void 0:n.call(t,"contenteditable");return r===""||r==="true"},ta=function(t,n,r){if(Be(t))return[];var o=Array.prototype.slice.apply(t.querySelectorAll(rt));return n&&Re.call(t,rt)&&o.unshift(t),o=o.filter(r),o},ra=function e(t,n,r){for(var o=[],a=Array.from(t);a.length;){var s=a.shift();if(!Be(s,!1))if(s.tagName==="SLOT"){var i=s.assignedElements(),d=i.length?i:s.children,f=e(d,!0,r);r.flatten?o.push.apply(o,f):o.push({scopeParent:s,candidates:f})}else{var b=Re.call(s,rt);b&&r.filter(s)&&(n||!t.includes(s))&&o.push(s);var u=s.shadowRoot||typeof r.getShadowRoot=="function"&&r.getShadowRoot(s),m=!Be(u,!1)&&(!r.shadowRootFilter||r.shadowRootFilter(s));if(u&&m){var g=e(u===!0?s.children:u.children,!0,r);r.flatten?o.push.apply(o,g):o.push({scopeParent:s,candidates:g})}else a.unshift.apply(a,s.children)}}return o},gr=function(t){return!isNaN(parseInt(t.getAttribute("tabindex"),10))},br=function(t){if(!t)throw new Error("No node provided");return t.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(t.tagName)||ea(t))&&!gr(t)?0:t.tabIndex},na=function(t,n){var r=br(t);return r<0&&n&&!gr(t)?0:r},oa=function(t,n){return t.tabIndex===n.tabIndex?t.documentOrder-n.documentOrder:t.tabIndex-n.tabIndex},pr=function(t){return t.tagName==="INPUT"},aa=function(t){return pr(t)&&t.type==="hidden"},sa=function(t){var n=t.tagName==="DETAILS"&&Array.prototype.slice.apply(t.children).some(function(r){return r.tagName==="SUMMARY"});return n},ia=function(t,n){for(var r=0;r<t.length;r++)if(t[r].checked&&t[r].form===n)return t[r]},la=function(t){if(!t.name)return!0;var n=t.form||ze(t),r=function(i){return n.querySelectorAll('input[type="radio"][name="'+i+'"]')},o;if(typeof window<"u"&&typeof window.CSS<"u"&&typeof window.CSS.escape=="function")o=r(window.CSS.escape(t.name));else try{o=r(t.name)}catch(s){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",s.message),!1}var a=ia(o,t.form);return!a||a===t},da=function(t){return pr(t)&&t.type==="radio"},ca=function(t){return da(t)&&!la(t)},ua=function(t){var n,r=t&&ze(t),o=(n=r)===null||n===void 0?void 0:n.host,a=!1;if(r&&r!==t){var s,i,d;for(a=!!((s=o)!==null&&s!==void 0&&(i=s.ownerDocument)!==null&&i!==void 0&&i.contains(o)||t!=null&&(d=t.ownerDocument)!==null&&d!==void 0&&d.contains(t));!a&&o;){var f,b,u;r=ze(o),o=(f=r)===null||f===void 0?void 0:f.host,a=!!((b=o)!==null&&b!==void 0&&(u=b.ownerDocument)!==null&&u!==void 0&&u.contains(o))}}return a},wt=function(t){var n=t.getBoundingClientRect(),r=n.width,o=n.height;return r===0&&o===0},fa=function(t,n){var r=n.displayCheck,o=n.getShadowRoot;if(getComputedStyle(t).visibility==="hidden")return!0;var a=Re.call(t,"details>summary:first-of-type"),s=a?t.parentElement:t;if(Re.call(s,"details:not([open]) *"))return!0;if(!r||r==="full"||r==="legacy-full"){if(typeof o=="function"){for(var i=t;t;){var d=t.parentElement,f=ze(t);if(d&&!d.shadowRoot&&o(d)===!0)return wt(t);t.assignedSlot?t=t.assignedSlot:!d&&f!==t.ownerDocument?t=f.host:t=d}t=i}if(ua(t))return!t.getClientRects().length;if(r!=="legacy-full")return!0}else if(r==="non-zero-area")return wt(t);return!1},ga=function(t){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(t.tagName))for(var n=t.parentElement;n;){if(n.tagName==="FIELDSET"&&n.disabled){for(var r=0;r<n.children.length;r++){var o=n.children.item(r);if(o.tagName==="LEGEND")return Re.call(n,"fieldset[disabled] *")?!0:!o.contains(t)}return!0}n=n.parentElement}return!1},ba=function(t,n){return!(n.disabled||Be(n)||aa(n)||fa(n,t)||sa(n)||ga(n))},Ct=function(t,n){return!(ca(n)||br(n)<0||!ba(t,n))},pa=function(t){var n=parseInt(t.getAttribute("tabindex"),10);return!!(isNaN(n)||n>=0)},ha=function e(t){var n=[],r=[];return t.forEach(function(o,a){var s=!!o.scopeParent,i=s?o.scopeParent:o,d=na(i,s),f=s?e(o.candidates):i;d===0?s?n.push.apply(n,f):n.push(i):r.push({documentOrder:a,tabIndex:d,item:o,isScope:s,content:f})}),r.sort(oa).reduce(function(o,a){return a.isScope?o.push.apply(o,a.content):o.push(a.content),o},[]).concat(n)},ot=function(t,n){n=n||{};var r;return n.getShadowRoot?r=ra([t],n.includeContainer,{filter:Ct.bind(null,n),flatten:!1,getShadowRoot:n.getShadowRoot,shadowRootFilter:pa}):r=ta(t,n.includeContainer,Ct.bind(null,n)),ha(r)};function Ee(){return Ee=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ee.apply(this,arguments)}var J=typeof document<"u"?c.useLayoutEffect:c.useEffect;let qe=!1,ma=0;const Nt=()=>"floating-ui-"+ma++;function ya(){const[e,t]=c.useState(()=>qe?Nt():void 0);return J(()=>{e==null&&t(Nt())},[]),c.useEffect(()=>{qe||(qe=!0)},[]),e}const xa=At["useId".toString()],at=xa||ya;function va(){const e=new Map;return{emit(t,n){var r;(r=e.get(t))==null||r.forEach(o=>o(n))},on(t,n){e.set(t,[...e.get(t)||[],n])},off(t,n){var r;e.set(t,((r=e.get(t))==null?void 0:r.filter(o=>o!==n))||[])}}}const ka=c.createContext(null),wa=c.createContext(null),st=()=>{var e;return((e=c.useContext(ka))==null?void 0:e.id)||null},Ie=()=>c.useContext(wa);function ie(e){return(e==null?void 0:e.ownerDocument)||document}function it(){const e=navigator.userAgentData;return e!=null&&e.platform?e.platform:navigator.platform}function Ca(){const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map(t=>{let{brand:n,version:r}=t;return n+"/"+r}).join(" "):navigator.userAgent}function Ke(e){return ie(e).defaultView||window}function le(e){return e?e instanceof Element||e instanceof Ke(e).Element:!1}function ve(e){return e?e instanceof HTMLElement||e instanceof Ke(e).HTMLElement:!1}function Na(e){if(typeof ShadowRoot>"u")return!1;const t=Ke(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function hr(e){if(e.mozInputSource===0&&e.isTrusted)return!0;const t=/Android/i;return(t.test(it())||t.test(Ca()))&&e.pointerType?e.type==="click"&&e.buttons===1:e.detail===0&&!e.pointerType}function mr(e){return e.width===0&&e.height===0||e.width===1&&e.height===1&&e.pressure===0&&e.detail===0&&e.pointerType!=="mouse"||e.width<1&&e.height<1&&e.pressure===0&&e.detail===0}function yr(){return/apple/i.test(navigator.vendor)}function Ta(){return it().toLowerCase().startsWith("mac")&&!navigator.maxTouchPoints}function He(e,t){const n=["mouse","pen"];return t||n.push("",void 0),n.includes(e)}function ja(e){return"nativeEvent"in e}function te(e,t){if(!e||!t)return!1;const n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&Na(n)){let r=t;for(;r;){if(e===r)return!0;r=r.parentNode||r.host}}return!1}function me(e){return"data-floating-ui-"+e}function ce(e){const t=c.useRef(e);return J(()=>{t.current=e}),t}const Tt=me("safe-polygon");function Ve(e,t,n){return n&&!He(n)?0:typeof e=="number"?e:e==null?void 0:e[t]}function Ra(e,t){t===void 0&&(t={});const{open:n,onOpenChange:r,dataRef:o,events:a,elements:{domReference:s,floating:i},refs:d}=e,{enabled:f=!0,delay:b=0,handleClose:u=null,mouseOnly:m=!1,restMs:g=0,move:h=!0}=t,p=Ie(),N=st(),R=ce(u),w=ce(b),W=c.useRef(),x=c.useRef(),j=c.useRef(),I=c.useRef(),V=c.useRef(!0),D=c.useRef(!1),L=c.useRef(()=>{}),S=c.useCallback(()=>{var v;const O=(v=o.current.openEvent)==null?void 0:v.type;return(O==null?void 0:O.includes("mouse"))&&O!=="mousedown"},[o]);c.useEffect(()=>{if(!f)return;function v(){clearTimeout(x.current),clearTimeout(I.current),V.current=!0}return a.on("dismiss",v),()=>{a.off("dismiss",v)}},[f,a]),c.useEffect(()=>{if(!f||!R.current||!n)return;function v(z){S()&&r(!1,z)}const O=ie(i).documentElement;return O.addEventListener("mouseleave",v),()=>{O.removeEventListener("mouseleave",v)}},[i,n,r,f,R,o,S]);const M=c.useCallback(function(v,O){O===void 0&&(O=!0);const z=Ve(w.current,"close",W.current);z&&!j.current?(clearTimeout(x.current),x.current=setTimeout(()=>r(!1,v),z)):O&&(clearTimeout(x.current),r(!1,v))},[w,r]),k=c.useCallback(()=>{L.current(),j.current=void 0},[]),C=c.useCallback(()=>{if(D.current){const v=ie(d.floating.current).body;v.style.pointerEvents="",v.removeAttribute(Tt),D.current=!1}},[d]);return c.useEffect(()=>{if(!f)return;function v(){return o.current.openEvent?["click","mousedown"].includes(o.current.openEvent.type):!1}function O(F){if(clearTimeout(x.current),V.current=!1,m&&!He(W.current)||g>0&&Ve(w.current,"open")===0)return;const Y=Ve(w.current,"open",W.current);Y?x.current=setTimeout(()=>{r(!0,F)},Y):r(!0,F)}function z(F){if(v())return;L.current();const Y=ie(i);if(clearTimeout(I.current),R.current){n||clearTimeout(x.current),j.current=R.current({...e,tree:p,x:F.clientX,y:F.clientY,onClose(){C(),k(),M(F)}});const K=j.current;Y.addEventListener("mousemove",K),L.current=()=>{Y.removeEventListener("mousemove",K)};return}(W.current==="touch"?!te(i,F.relatedTarget):!0)&&M(F)}function G(F){v()||R.current==null||R.current({...e,tree:p,x:F.clientX,y:F.clientY,onClose(){C(),k(),M(F)}})(F)}if(le(s)){const F=s;return n&&F.addEventListener("mouseleave",G),i==null||i.addEventListener("mouseleave",G),h&&F.addEventListener("mousemove",O,{once:!0}),F.addEventListener("mouseenter",O),F.addEventListener("mouseleave",z),()=>{n&&F.removeEventListener("mouseleave",G),i==null||i.removeEventListener("mouseleave",G),h&&F.removeEventListener("mousemove",O),F.removeEventListener("mouseenter",O),F.removeEventListener("mouseleave",z)}}},[s,i,f,e,m,g,h,M,k,C,r,n,p,w,R,o]),J(()=>{var v;if(f&&n&&(v=R.current)!=null&&v.__options.blockPointerEvents&&S()){const G=ie(i).body;if(G.setAttribute(Tt,""),G.style.pointerEvents="none",D.current=!0,le(s)&&i){var O,z;const F=s,Y=p==null||(O=p.nodesRef.current.find(Z=>Z.id===N))==null||(z=O.context)==null?void 0:z.elements.floating;return Y&&(Y.style.pointerEvents=""),F.style.pointerEvents="auto",i.style.pointerEvents="auto",()=>{F.style.pointerEvents="",i.style.pointerEvents=""}}}},[f,n,N,i,s,p,R,o,S]),J(()=>{n||(W.current=void 0,k(),C())},[n,k,C]),c.useEffect(()=>()=>{k(),clearTimeout(x.current),clearTimeout(I.current),C()},[f,k,C]),c.useMemo(()=>{if(!f)return{};function v(O){W.current=O.pointerType}return{reference:{onPointerDown:v,onPointerEnter:v,onMouseMove(O){n||g===0||(clearTimeout(I.current),I.current=setTimeout(()=>{V.current||r(!0,O.nativeEvent)},g))}},floating:{onMouseEnter(){clearTimeout(x.current)},onMouseLeave(O){a.emit("dismiss",{type:"mouseLeave",data:{returnFocus:!1}}),M(O.nativeEvent,!1)}}}},[a,f,g,n,r,M])}function be(e){let t=e.activeElement;for(;((n=t)==null||(r=n.shadowRoot)==null?void 0:r.activeElement)!=null;){var n,r;t=t.shadowRoot.activeElement}return t}let jt=0;function ge(e,t){t===void 0&&(t={});const{preventScroll:n=!1,cancelPrevious:r=!0,sync:o=!1}=t;r&&cancelAnimationFrame(jt);const a=()=>e==null?void 0:e.focus({preventScroll:n});o?a():jt=requestAnimationFrame(a)}function Ea(e,t){var n;let r=[],o=(n=e.find(a=>a.id===t))==null?void 0:n.parentId;for(;o;){const a=e.find(s=>s.id===o);o=a==null?void 0:a.parentId,a&&(r=r.concat(a))}return r}function we(e,t){let n=e.filter(o=>{var a;return o.parentId===t&&((a=o.context)==null?void 0:a.open)}),r=n;for(;r.length;)r=e.filter(o=>{var a;return(a=r)==null?void 0:a.some(s=>{var i;return o.parentId===s.id&&((i=o.context)==null?void 0:i.open)})}),n=n.concat(r);return n}function lt(e){return"composedPath"in e?e.composedPath()[0]:e.target}const Ia="input:not([type='hidden']):not([disabled]),[contenteditable]:not([contenteditable='false']),textarea:not([disabled])";function xr(e){return ve(e)&&e.matches(Ia)}function se(e){e.preventDefault(),e.stopPropagation()}const dt=()=>({getShadowRoot:!0,displayCheck:typeof ResizeObserver=="function"&&ResizeObserver.toString().includes("[native code]")?"full":"none"});function vr(e,t){const n=ot(e,dt());t==="prev"&&n.reverse();const r=n.indexOf(be(ie(e)));return n.slice(r+1)[0]}function kr(){return vr(document.body,"next")}function wr(){return vr(document.body,"prev")}function je(e,t){const n=t||e.currentTarget,r=e.relatedTarget;return!r||!te(n,r)}function Sa(e){ot(e,dt()).forEach(n=>{n.dataset.tabindex=n.getAttribute("tabindex")||"",n.setAttribute("tabindex","-1")})}function Ma(e){e.querySelectorAll("[data-tabindex]").forEach(n=>{const r=n.dataset.tabindex;delete n.dataset.tabindex,r?n.setAttribute("tabindex",r):n.removeAttribute("tabindex")})}const ct={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"fixed",whiteSpace:"nowrap",width:"1px",top:0,left:0};let Oa;function Rt(e){e.key==="Tab"&&(e.target,clearTimeout(Oa))}const _e=c.forwardRef(function(t,n){const[r,o]=c.useState();J(()=>(yr()&&o("button"),document.addEventListener("keydown",Rt),()=>{document.removeEventListener("keydown",Rt)}),[]);const a={ref:n,tabIndex:0,role:r,"aria-hidden":r?void 0:!0,[me("focus-guard")]:"",style:ct};return c.createElement("span",Ee({},t,a))}),Cr=c.createContext(null);function Pa(e){let{id:t,root:n}=e===void 0?{}:e;const[r,o]=c.useState(null),a=at(),s=Nr(),i=c.useMemo(()=>({id:t,root:n,portalContext:s,uniqueId:a}),[t,n,s,a]),d=c.useRef();return J(()=>()=>{r==null||r.remove()},[r,i]),J(()=>{if(d.current===i)return;d.current=i;const{id:f,root:b,portalContext:u,uniqueId:m}=i,g=f?document.getElementById(f):null,h=me("portal");if(g){const p=document.createElement("div");p.id=m,p.setAttribute(h,""),g.appendChild(p),o(p)}else{let p=b||(u==null?void 0:u.portalNode);p&&!le(p)&&(p=p.current),p=p||document.body;let N=null;f&&(N=document.createElement("div"),N.id=f,p.appendChild(N));const R=document.createElement("div");R.id=m,R.setAttribute(h,""),p=N||p,p.appendChild(R),o(R)}},[i]),r}function La(e){let{children:t,id:n,root:r=null,preserveTabOrder:o=!0}=e;const a=Pa({id:n,root:r}),[s,i]=c.useState(null),d=c.useRef(null),f=c.useRef(null),b=c.useRef(null),u=c.useRef(null),m=!!s&&!s.modal&&s.open&&o&&!!(r||a);return c.useEffect(()=>{if(!a||!o||s!=null&&s.modal)return;function g(h){a&&je(h)&&(h.type==="focusin"?Ma:Sa)(a)}return a.addEventListener("focusin",g,!0),a.addEventListener("focusout",g,!0),()=>{a.removeEventListener("focusin",g,!0),a.removeEventListener("focusout",g,!0)}},[a,o,s==null?void 0:s.modal]),c.createElement(Cr.Provider,{value:c.useMemo(()=>({preserveTabOrder:o,beforeOutsideRef:d,afterOutsideRef:f,beforeInsideRef:b,afterInsideRef:u,portalNode:a,setFocusManagerState:i}),[o,a])},m&&a&&c.createElement(_e,{"data-type":"outside",ref:d,onFocus:g=>{if(je(g,a)){var h;(h=b.current)==null||h.focus()}else{const p=wr()||(s==null?void 0:s.refs.domReference.current);p==null||p.focus()}}}),m&&a&&c.createElement("span",{"aria-owns":a.id,style:ct}),a&&$n.createPortal(t,a),m&&a&&c.createElement(_e,{"data-type":"outside",ref:f,onFocus:g=>{if(je(g,a)){var h;(h=u.current)==null||h.focus()}else{const p=kr()||(s==null?void 0:s.refs.domReference.current);p==null||p.focus(),s!=null&&s.closeOnFocusOut&&(s==null||s.onOpenChange(!1,g.nativeEvent))}}}))}const Nr=()=>c.useContext(Cr),Aa=c.forwardRef(function(t,n){return c.createElement("button",Ee({},t,{type:"button",ref:n,tabIndex:-1,style:ct}))});function Tr(e){const{context:t,children:n,disabled:r=!1,order:o=["content"],guards:a=!0,initialFocus:s=0,returnFocus:i=!0,modal:d=!0,visuallyHiddenDismiss:f=!1,closeOnFocusOut:b=!0}=e,{open:u,refs:m,nodeId:g,onOpenChange:h,events:p,dataRef:N,elements:{domReference:R,floating:w}}=t,W=ur()?a:!0,x=ce(o),j=ce(s),I=ce(i),V=Ie(),D=Nr(),L=typeof s=="number"&&s<0,S=c.useRef(null),M=c.useRef(null),k=c.useRef(!1),C=c.useRef(null),v=c.useRef(!1),O=D!=null,z=R&&R.getAttribute("role")==="combobox"&&xr(R),G=c.useCallback(function(K){return K===void 0&&(K=w),K?ot(K,dt()):[]},[w]),F=c.useCallback(K=>{const U=G(K);return x.current.map(H=>R&&H==="reference"?R:w&&H==="floating"?w:U).filter(Boolean).flat()},[R,w,x,G]);c.useEffect(()=>{if(r||!d)return;function K(H){if(H.key==="Tab"){te(w,be(ie(w)))&&G().length===0&&!z&&se(H);const $=F(),Q=lt(H);x.current[0]==="reference"&&Q===R&&(se(H),H.shiftKey?ge($[$.length-1]):ge($[1])),x.current[1]==="floating"&&Q===w&&H.shiftKey&&(se(H),ge($[0]))}}const U=ie(w);return U.addEventListener("keydown",K),()=>{U.removeEventListener("keydown",K)}},[r,R,w,d,x,m,z,G,F]),c.useEffect(()=>{if(r||!b)return;function K(){v.current=!0,setTimeout(()=>{v.current=!1})}function U(H){const $=H.relatedTarget;queueMicrotask(()=>{const Q=!(te(R,$)||te(w,$)||te($,w)||te(D==null?void 0:D.portalNode,$)||$!=null&&$.hasAttribute(me("focus-guard"))||V&&(we(V.nodesRef.current,g).find(ee=>{var A,B;return te((A=ee.context)==null?void 0:A.elements.floating,$)||te((B=ee.context)==null?void 0:B.elements.domReference,$)})||Ea(V.nodesRef.current,g).find(ee=>{var A,B;return((A=ee.context)==null?void 0:A.elements.floating)===$||((B=ee.context)==null?void 0:B.elements.domReference)===$})));$&&Q&&!v.current&&$!==C.current&&(k.current=!0,h(!1,H))})}if(w&&ve(R))return R.addEventListener("focusout",U),R.addEventListener("pointerdown",K),!d&&w.addEventListener("focusout",U),()=>{R.removeEventListener("focusout",U),R.removeEventListener("pointerdown",K),!d&&w.removeEventListener("focusout",U)}},[r,R,w,d,g,V,D,h,b]),c.useEffect(()=>{var K;if(r)return;const U=Array.from((D==null||(K=D.portalNode)==null?void 0:K.querySelectorAll("["+me("portal")+"]"))||[]);if(w&&d){const H=[w,...U,S.current,M.current].filter(ee=>ee!=null),Q=(W?cr:Jo)(x.current.includes("reference")||z?H.concat(R||[]):H,void 0,me("inert"));return()=>{Q()}}},[r,R,w,d,x,D,z,W]),J(()=>{if(r||!w)return;const K=ie(w),U=be(K);queueMicrotask(()=>{const H=F(w),$=j.current,Q=(typeof $=="number"?H[$]:$.current)||w,ee=te(w,U);!L&&!ee&&u&&ge(Q,{preventScroll:Q===w})})},[r,u,w,L,F,j]),J(()=>{if(r||!w)return;let K=!1;const U=ie(w),H=be(U),$=N.current;C.current=H;function Q(ee){if(ee.type==="escapeKey"&&m.domReference.current&&(C.current=m.domReference.current),["referencePress","escapeKey"].includes(ee.type))return;const A=ee.data.returnFocus;typeof A=="object"?(k.current=!1,K=A.preventScroll):k.current=!A}return p.on("dismiss",Q),()=>{p.off("dismiss",Q);const ee=be(U);(te(w,ee)||V&&we(V.nodesRef.current,g).some(B=>{var _;return te((_=B.context)==null?void 0:_.elements.floating,ee)})||$.openEvent&&["click","mousedown"].includes($.openEvent.type))&&m.domReference.current&&(C.current=m.domReference.current),I.current&&ve(C.current)&&!k.current&&ge(C.current,{cancelPrevious:!1,preventScroll:K})}},[r,w,I,N,m,p,V,g]),J(()=>{if(!(r||!D))return D.setFocusManagerState({...t,modal:d,closeOnFocusOut:b,open:u}),()=>{D.setFocusManagerState(null)}},[r,D,d,u,b,t]),J(()=>{if(!r&&w&&typeof MutationObserver=="function"){const K=()=>{const H=w.getAttribute("tabindex");x.current.includes("floating")||be(ie(w))!==m.domReference.current&&G().length===0?H!=="0"&&w.setAttribute("tabindex","0"):H!=="-1"&&w.setAttribute("tabindex","-1")};K();const U=new MutationObserver(K);return U.observe(w,{childList:!0,subtree:!0,attributes:!0}),()=>{U.disconnect()}}},[r,w,m,x,G]);function Y(K){return r||!f||!d?null:c.createElement(Aa,{ref:K==="start"?S:M,onClick:U=>h(!1,U.nativeEvent)},typeof f=="string"?f:"Dismiss")}const Z=!r&&W&&!z&&(O||d);return c.createElement(c.Fragment,null,Z&&c.createElement(_e,{"data-type":"inside",ref:D==null?void 0:D.beforeInsideRef,onFocus:K=>{if(d){const H=F();ge(o[0]==="reference"?H[0]:H[H.length-1])}else if(D!=null&&D.preserveTabOrder&&D.portalNode)if(k.current=!1,je(K,D.portalNode)){const H=kr()||R;H==null||H.focus()}else{var U;(U=D.beforeOutsideRef.current)==null||U.focus()}}}),!z&&Y("start"),n,Y("end"),Z&&c.createElement(_e,{"data-type":"inside",ref:D==null?void 0:D.afterInsideRef,onFocus:K=>{if(d)ge(F()[0]);else if(D!=null&&D.preserveTabOrder&&D.portalNode)if(b&&(k.current=!0),je(K,D.portalNode)){const H=wr()||R;H==null||H.focus()}else{var U;(U=D.afterOutsideRef.current)==null||U.focus()}}}))}function Fa(e,t){const n=e.compareDocumentPosition(t);return n&Node.DOCUMENT_POSITION_FOLLOWING||n&Node.DOCUMENT_POSITION_CONTAINED_BY?-1:n&Node.DOCUMENT_POSITION_PRECEDING||n&Node.DOCUMENT_POSITION_CONTAINS?1:0}function Da(e,t){if(e.size!==t.size)return!1;for(const[n,r]of e.entries())if(r!==t.get(n))return!1;return!0}const jr=c.createContext({register:()=>{},unregister:()=>{},map:new Map,elementsRef:{current:[]}});function za(e){let{children:t,elementsRef:n,labelsRef:r}=e;const[o,a]=c.useState(()=>new Map),s=c.useCallback(d=>{a(f=>new Map(f).set(d,null))},[]),i=c.useCallback(d=>{a(f=>{const b=new Map(f);return b.delete(d),b})},[]);return J(()=>{const d=new Map(o);Array.from(d.keys()).sort(Fa).forEach((b,u)=>{d.set(b,u)}),Da(o,d)||a(d)},[o]),c.createElement(jr.Provider,{value:c.useMemo(()=>({register:s,unregister:i,map:o,elementsRef:n,labelsRef:r}),[s,i,o,n,r])},t)}function Ba(e){let{label:t}=e===void 0?{}:e;const[n,r]=c.useState(null),o=c.useRef(null),{register:a,unregister:s,map:i,elementsRef:d,labelsRef:f}=c.useContext(jr),b=c.useCallback(u=>{if(o.current=u,n!==null&&(d.current[n]=u,f)){var m;const g=t!==void 0;f.current[n]=g?t:(m=u==null?void 0:u.textContent)!=null?m:null}},[n,d,f,t]);return J(()=>{const u=o.current;if(u)return a(u),()=>{s(u)}},[a,s]),J(()=>{const u=o.current?i.get(o.current):null;u!=null&&r(u)},[i]),c.useMemo(()=>({ref:b,index:n??-1}),[n,b])}const Ae=me("scroll-lock"),Ha=c.forwardRef(function(t,n){let{lockScroll:r=!1,...o}=t;return J(()=>{var a,s;if(!r||document.body.hasAttribute(Ae))return;document.body.setAttribute(Ae,"");const f=Math.round(document.documentElement.getBoundingClientRect().left)+document.documentElement.scrollLeft?"paddingLeft":"paddingRight",b=window.innerWidth-document.documentElement.clientWidth;if(!/iP(hone|ad|od)|iOS/.test(it()))return Object.assign(document.body.style,{overflow:"hidden",[f]:b+"px"}),()=>{document.body.removeAttribute(Ae),Object.assign(document.body.style,{overflow:"",[f]:""})};const u=((a=window.visualViewport)==null?void 0:a.offsetLeft)||0,m=((s=window.visualViewport)==null?void 0:s.offsetTop)||0,g=window.pageXOffset,h=window.pageYOffset;return Object.assign(document.body.style,{position:"fixed",overflow:"hidden",top:-(h-Math.floor(m))+"px",left:-(g-Math.floor(u))+"px",right:"0",[f]:b+"px"}),()=>{Object.assign(document.body.style,{position:"",overflow:"",top:"",left:"",right:"",[f]:""}),document.body.removeAttribute(Ae),window.scrollTo(g,h)}},[r]),c.createElement("div",Ee({ref:n},o,{style:{position:"fixed",overflow:"auto",top:0,right:0,bottom:0,left:0,...o.style}}))});function Et(e){return ve(e.target)&&e.target.tagName==="BUTTON"}function It(e){return xr(e)}function Rr(e,t){t===void 0&&(t={});const{open:n,onOpenChange:r,dataRef:o,elements:{domReference:a}}=e,{enabled:s=!0,event:i="click",toggle:d=!0,ignoreMouse:f=!1,keyboardHandlers:b=!0}=t,u=c.useRef(),m=c.useRef(!1);return c.useMemo(()=>s?{reference:{onPointerDown(g){u.current=g.pointerType},onMouseDown(g){g.button===0&&(He(u.current,!0)&&f||i!=="click"&&(n&&d&&(!o.current.openEvent||o.current.openEvent.type==="mousedown")?r(!1,g.nativeEvent):(g.preventDefault(),r(!0,g.nativeEvent))))},onClick(g){if(i==="mousedown"&&u.current){u.current=void 0;return}He(u.current,!0)&&f||(n&&d&&(!o.current.openEvent||o.current.openEvent.type==="click")?r(!1,g.nativeEvent):r(!0,g.nativeEvent))},onKeyDown(g){u.current=void 0,!(g.defaultPrevented||!b||Et(g))&&(g.key===" "&&!It(a)&&(g.preventDefault(),m.current=!0),g.key==="Enter"&&r(!(n&&d),g.nativeEvent))},onKeyUp(g){g.defaultPrevented||!b||Et(g)||It(a)||g.key===" "&&m.current&&(m.current=!1,r(!(n&&d),g.nativeEvent))}}}:{},[s,o,i,f,b,a,d,n,r])}const _a=At["useInsertionEffect".toString()],Ka=_a||(e=>e());function ye(e){const t=c.useRef(()=>{});return Ka(()=>{t.current=e}),c.useCallback(function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return t.current==null?void 0:t.current(...r)},[])}function De(e,t){if(t==null)return!1;if("composedPath"in e)return e.composedPath().includes(t);const n=e;return n.target!=null&&t.contains(n.target)}const Ga={pointerdown:"onPointerDown",mousedown:"onMouseDown",click:"onClick"},Wa={pointerdown:"onPointerDownCapture",mousedown:"onMouseDownCapture",click:"onClickCapture"},$a=e=>{var t,n;return{escapeKeyBubbles:typeof e=="boolean"?e:(t=e==null?void 0:e.escapeKey)!=null?t:!1,outsidePressBubbles:typeof e=="boolean"?e:(n=e==null?void 0:e.outsidePress)!=null?n:!0}};function Er(e,t){t===void 0&&(t={});const{open:n,onOpenChange:r,events:o,nodeId:a,elements:{reference:s,domReference:i,floating:d},dataRef:f}=e,{enabled:b=!0,escapeKey:u=!0,outsidePress:m=!0,outsidePressEvent:g="pointerdown",referencePress:h=!1,referencePressEvent:p="pointerdown",ancestorScroll:N=!1,bubbles:R}=t,w=Ie(),W=st()!=null,x=ye(typeof m=="function"?m:()=>!1),j=typeof m=="function"?x:m,I=c.useRef(!1),{escapeKeyBubbles:V,outsidePressBubbles:D}=$a(R),L=ye(M=>{if(!n||!b||!u||M.key!=="Escape")return;const k=w?we(w.nodesRef.current,a):[];if(!V&&(M.stopPropagation(),k.length>0)){let C=!0;if(k.forEach(v=>{var O;if((O=v.context)!=null&&O.open&&!v.context.dataRef.current.__escapeKeyBubbles){C=!1;return}}),!C)return}o.emit("dismiss",{type:"escapeKey",data:{returnFocus:{preventScroll:!1}}}),r(!1,ja(M)?M.nativeEvent:M)}),S=ye(M=>{const k=I.current;if(I.current=!1,k||typeof j=="function"&&!j(M))return;const C=lt(M);if(ve(C)&&d){const z=C.clientWidth>0&&C.scrollWidth>C.clientWidth,G=C.clientHeight>0&&C.scrollHeight>C.clientHeight;let F=G&&M.offsetX>C.clientWidth;if(G&&Ke(d).getComputedStyle(C).direction==="rtl"&&(F=M.offsetX<=C.offsetWidth-C.clientWidth),F||z&&M.offsetY>C.clientHeight)return}const v=w&&we(w.nodesRef.current,a).some(z=>{var G;return De(M,(G=z.context)==null?void 0:G.elements.floating)});if(De(M,d)||De(M,i)||v)return;const O=w?we(w.nodesRef.current,a):[];if(O.length>0){let z=!0;if(O.forEach(G=>{var F;if((F=G.context)!=null&&F.open&&!G.context.dataRef.current.__outsidePressBubbles){z=!1;return}}),!z)return}o.emit("dismiss",{type:"outsidePress",data:{returnFocus:W?{preventScroll:!0}:hr(M)||mr(M)}}),r(!1,M)});return c.useEffect(()=>{if(!n||!b)return;f.current.__escapeKeyBubbles=V,f.current.__outsidePressBubbles=D;function M(v){r(!1,v)}const k=ie(d);u&&k.addEventListener("keydown",L),j&&k.addEventListener(g,S);let C=[];return N&&(le(i)&&(C=Ue(i)),le(d)&&(C=C.concat(Ue(d))),!le(s)&&s&&s.contextElement&&(C=C.concat(Ue(s.contextElement)))),C=C.filter(v=>{var O;return v!==((O=k.defaultView)==null?void 0:O.visualViewport)}),C.forEach(v=>{v.addEventListener("scroll",M,{passive:!0})}),()=>{u&&k.removeEventListener("keydown",L),j&&k.removeEventListener(g,S),C.forEach(v=>{v.removeEventListener("scroll",M)})}},[f,d,i,s,u,j,g,n,r,N,b,V,D,L,S]),c.useEffect(()=>{I.current=!1},[j,g]),c.useMemo(()=>b?{reference:{onKeyDown:L,[Ga[p]]:M=>{h&&(o.emit("dismiss",{type:"referencePress",data:{returnFocus:!1}}),r(!1,M.nativeEvent))}},floating:{onKeyDown:L,[Wa[g]]:()=>{I.current=!0}}}:{},[b,o,h,g,p,r,L])}function Ir(e){var t;e===void 0&&(e={});const{open:n=!1,onOpenChange:r,nodeId:o}=e,[a,s]=c.useState(null),i=((t=e.elements)==null?void 0:t.reference)||a,d=Bn(e),f=Ie(),b=ye((x,j)=>{x&&(m.current.openEvent=j),r==null||r(x,j)}),u=c.useRef(null),m=c.useRef({}),g=c.useState(()=>va())[0],h=at(),p=c.useCallback(x=>{const j=le(x)?{getBoundingClientRect:()=>x.getBoundingClientRect(),contextElement:x}:x;d.refs.setReference(j)},[d.refs]),N=c.useCallback(x=>{(le(x)||x===null)&&(u.current=x,s(x)),(le(d.refs.reference.current)||d.refs.reference.current===null||x!==null&&!le(x))&&d.refs.setReference(x)},[d.refs]),R=c.useMemo(()=>({...d.refs,setReference:N,setPositionReference:p,domReference:u}),[d.refs,N,p]),w=c.useMemo(()=>({...d.elements,domReference:i}),[d.elements,i]),W=c.useMemo(()=>({...d,refs:R,elements:w,dataRef:m,nodeId:o,floatingId:h,events:g,open:n,onOpenChange:b}),[d,o,h,g,n,b,R,w]);return J(()=>{const x=f==null?void 0:f.nodesRef.current.find(j=>j.id===o);x&&(x.context=W)}),c.useMemo(()=>({...d,context:W,refs:R,elements:w}),[d,R,w,W])}function Ua(e,t){t===void 0&&(t={});const{open:n,onOpenChange:r,dataRef:o,events:a,refs:s,elements:{floating:i,domReference:d}}=e,{enabled:f=!0,keyboardOnly:b=!0}=t,u=c.useRef(""),m=c.useRef(!1),g=c.useRef();return c.useEffect(()=>{if(!f)return;const p=ie(i).defaultView||window;function N(){!n&&ve(d)&&d===be(ie(d))&&(m.current=!0)}return p.addEventListener("blur",N),()=>{p.removeEventListener("blur",N)}},[i,d,n,f]),c.useEffect(()=>{if(!f)return;function h(p){(p.type==="referencePress"||p.type==="escapeKey")&&(m.current=!0)}return a.on("dismiss",h),()=>{a.off("dismiss",h)}},[a,f]),c.useEffect(()=>()=>{clearTimeout(g.current)},[]),c.useMemo(()=>f?{reference:{onPointerDown(h){let{pointerType:p}=h;u.current=p,m.current=!!(p&&b)},onMouseLeave(){m.current=!1},onFocus(h){var p;m.current||h.type==="focus"&&((p=o.current.openEvent)==null?void 0:p.type)==="mousedown"&&De(o.current.openEvent,d)||r(!0,h.nativeEvent)},onBlur(h){m.current=!1;const p=h.relatedTarget,N=le(p)&&p.hasAttribute(me("focus-guard"))&&p.getAttribute("data-type")==="outside";g.current=setTimeout(()=>{te(s.floating.current,p)||te(d,p)||N||r(!1,h.nativeEvent)})}}}:{},[f,b,d,s,o,r])}function Ze(e,t,n){const r=new Map;return{...n==="floating"&&{tabIndex:-1},...e,...t.map(o=>o?o[n]:null).concat(e).reduce((o,a)=>(a&&Object.entries(a).forEach(s=>{let[i,d]=s;if(i.indexOf("on")===0){if(r.has(i)||r.set(i,[]),typeof d=="function"){var f;(f=r.get(i))==null||f.push(d),o[i]=function(){for(var b,u=arguments.length,m=new Array(u),g=0;g<u;g++)m[g]=arguments[g];return(b=r.get(i))==null?void 0:b.map(h=>h(...m)).find(h=>h!==void 0)}}}else o[i]=d}),o),{})}}function Sr(e){e===void 0&&(e=[]);const t=e,n=c.useCallback(a=>Ze(a,e,"reference"),t),r=c.useCallback(a=>Ze(a,e,"floating"),t),o=c.useCallback(a=>Ze(a,e,"item"),e.map(a=>a==null?void 0:a.item));return c.useMemo(()=>({getReferenceProps:n,getFloatingProps:r,getItemProps:o}),[n,r,o])}let St=!1;const ut="ArrowUp",Ge="ArrowDown",Ce="ArrowLeft",Se="ArrowRight";function Fe(e,t,n){return Math.floor(e/t)!==n}function Te(e,t){return t<0||t>=e.current.length}function oe(e,t){let{startingIndex:n=-1,decrement:r=!1,disabledIndices:o,amount:a=1}=t===void 0?{}:t;const s=e.current;let i=n;do{var d,f;i=i+(r?-a:a)}while(i>=0&&i<=s.length-1&&(o?o.includes(i):s[i]==null||(d=s[i])!=null&&d.hasAttribute("disabled")||((f=s[i])==null?void 0:f.getAttribute("aria-disabled"))==="true"));return i}function We(e,t,n){switch(e){case"vertical":return t;case"horizontal":return n;default:return t||n}}function Mt(e,t){return We(t,e===ut||e===Ge,e===Ce||e===Se)}function Je(e,t,n){return We(t,e===Ge,n?e===Ce:e===Se)||e==="Enter"||e==" "||e===""}function Ya(e,t,n){return We(t,n?e===Ce:e===Se,e===Ge)}function Xa(e,t,n){return We(t,n?e===Se:e===Ce,e===ut)}function Qe(e,t){return oe(e,{disabledIndices:t})}function Ot(e,t){return oe(e,{decrement:!0,startingIndex:e.current.length,disabledIndices:t})}function qa(e,t){const{open:n,onOpenChange:r,refs:o,elements:{domReference:a,floating:s}}=e,{listRef:i,activeIndex:d,onNavigate:f=()=>{},enabled:b=!0,selectedIndex:u=null,allowEscape:m=!1,loop:g=!1,nested:h=!1,rtl:p=!1,virtual:N=!1,focusItemOnOpen:R="auto",focusItemOnHover:w=!0,openOnArrowKeyDown:W=!0,disabledIndices:x=void 0,orientation:j="vertical",cols:I=1,scrollItemIntoView:V=!0}=t,D=st(),L=Ie(),S=ye(f),M=c.useRef(R),k=c.useRef(u??-1),C=c.useRef(null),v=c.useRef(!0),O=c.useRef(S),z=c.useRef(!!s),G=c.useRef(!1),F=c.useRef(!1),Y=ce(x),Z=ce(n),K=ce(V),[U,H]=c.useState(),$=ye(function(A,B,_){_===void 0&&(_=!1);const X=A.current[B.current];X&&(N?H(X.id):ge(X,{preventScroll:!0,sync:Ta()&&yr()?St||G.current:!1}),requestAnimationFrame(()=>{const re=K.current;re&&X&&(_||!v.current)&&(X.scrollIntoView==null||X.scrollIntoView(typeof re=="boolean"?{block:"nearest",inline:"nearest"}:re))}))});J(()=>{document.createElement("div").focus({get preventScroll(){return St=!0,!1}})},[]),J(()=>{b&&(n&&s?M.current&&u!=null&&(F.current=!0,S(u)):z.current&&(k.current=-1,O.current(null)))},[b,n,s,u,S]),J(()=>{if(b&&n&&s)if(d==null){if(G.current=!1,u!=null)return;if(z.current&&(k.current=-1,$(i,k)),!z.current&&M.current&&(C.current!=null||M.current===!0&&C.current==null)){let A=0;const B=()=>{i.current[0]==null?(A<2&&(A?requestAnimationFrame:queueMicrotask)(B),A++):(k.current=C.current==null||Je(C.current,j,p)||h?Qe(i,Y.current):Ot(i,Y.current),C.current=null,S(k.current))};B()}}else Te(i,d)||(k.current=d,$(i,k,F.current),F.current=!1)},[b,n,s,d,u,h,i,j,p,S,$,Y]),J(()=>{if(b&&z.current&&!s&&L){var A,B;const _=L.nodesRef.current,X=(A=_.find(ne=>ne.id===D))==null||(B=A.context)==null?void 0:B.elements.floating,re=be(ie(s)),P=_.some(ne=>ne.context&&te(ne.context.elements.floating,re));X&&!P&&X.focus({preventScroll:!0})}},[b,s,L,D]),J(()=>{O.current=S,z.current=!!s}),J(()=>{n||(C.current=null)},[n]);const Q=d!=null,ee=c.useMemo(()=>{function A(_){if(!n)return;const X=i.current.indexOf(_);X!==-1&&S(X)}return{onFocus(_){let{currentTarget:X}=_;A(X)},onClick:_=>{let{currentTarget:X}=_;return X.focus({preventScroll:!0})},...w&&{onMouseMove(_){let{currentTarget:X}=_;A(X)},onPointerLeave(_){let{pointerType:X}=_;!v.current||X==="touch"||(k.current=-1,$(i,k),S(null),N||ge(o.floating.current,{preventScroll:!0}))}}}},[n,o,$,w,i,S,N]);return c.useMemo(()=>{if(!b)return{};const A=Y.current;function B(P){if(v.current=!1,G.current=!0,!Z.current&&P.currentTarget===o.floating.current)return;if(h&&Xa(P.key,j,p)){se(P),r(!1,P.nativeEvent),ve(a)&&a.focus();return}const ne=k.current,ue=Qe(i,A),ae=Ot(i,A);if(P.key==="Home"&&(se(P),k.current=ue,S(k.current)),P.key==="End"&&(se(P),k.current=ae,S(k.current)),I>1){const q=k.current;if(P.key===ut){if(se(P),q===-1)k.current=ae;else if(k.current=oe(i,{startingIndex:q,amount:I,decrement:!0,disabledIndices:A}),g&&(q-I<ue||k.current<0)){const fe=q%I,Ne=ae%I,yt=ae-(Ne-fe);Ne===fe?k.current=ae:k.current=Ne>fe?yt:yt-I}Te(i,k.current)&&(k.current=q),S(k.current)}if(P.key===Ge&&(se(P),q===-1?k.current=ue:(k.current=oe(i,{startingIndex:q,amount:I,disabledIndices:A}),g&&q+I>ae&&(k.current=oe(i,{startingIndex:q%I-I,amount:I,disabledIndices:A}))),Te(i,k.current)&&(k.current=q),S(k.current)),j==="both"){const fe=Math.floor(q/I);P.key===Se&&(se(P),q%I!==I-1?(k.current=oe(i,{startingIndex:q,disabledIndices:A}),g&&Fe(k.current,I,fe)&&(k.current=oe(i,{startingIndex:q-q%I-1,disabledIndices:A}))):g&&(k.current=oe(i,{startingIndex:q-q%I-1,disabledIndices:A})),Fe(k.current,I,fe)&&(k.current=q)),P.key===Ce&&(se(P),q%I!==0?(k.current=oe(i,{startingIndex:q,disabledIndices:A,decrement:!0}),g&&Fe(k.current,I,fe)&&(k.current=oe(i,{startingIndex:q+(I-q%I),decrement:!0,disabledIndices:A}))):g&&(k.current=oe(i,{startingIndex:q+(I-q%I),decrement:!0,disabledIndices:A})),Fe(k.current,I,fe)&&(k.current=q));const Ne=Math.floor(ae/I)===fe;Te(i,k.current)&&(g&&Ne?k.current=P.key===Ce?ae:oe(i,{startingIndex:q-q%I-1,disabledIndices:A}):k.current=q),S(k.current);return}}if(Mt(P.key,j)){if(se(P),n&&!N&&be(P.currentTarget.ownerDocument)===P.currentTarget){k.current=Je(P.key,j,p)?ue:ae,S(k.current);return}Je(P.key,j,p)?g?k.current=ne>=ae?m&&ne!==i.current.length?-1:ue:oe(i,{startingIndex:ne,disabledIndices:A}):k.current=Math.min(ae,oe(i,{startingIndex:ne,disabledIndices:A})):g?k.current=ne<=ue?m&&ne!==-1?i.current.length:ae:oe(i,{startingIndex:ne,decrement:!0,disabledIndices:A}):k.current=Math.max(ue,oe(i,{startingIndex:ne,decrement:!0,disabledIndices:A})),Te(i,k.current)?S(null):S(k.current)}}function _(P){R==="auto"&&hr(P.nativeEvent)&&(M.current=!0)}function X(P){M.current=R,R==="auto"&&mr(P.nativeEvent)&&(M.current=!0)}const re=N&&n&&Q&&{"aria-activedescendant":U};return{reference:{...re,onKeyDown(P){v.current=!1;const ne=P.key.indexOf("Arrow")===0;if(N&&n)return B(P);if(!n&&!W&&ne)return;const ue=ne||P.key==="Enter"||P.key.trim()==="",ae=Mt(P.key,j),q=Ya(P.key,j,p);if(ue&&(C.current=h&&ae?null:P.key),h){q&&(se(P),n?(k.current=Qe(i,A),S(k.current)):r(!0,P.nativeEvent));return}ae&&(u!=null&&(k.current=u),se(P),!n&&W?r(!0,P.nativeEvent):B(P),n&&S(k.current))},onFocus(){n&&S(null)},onPointerDown:X,onMouseDown:_,onClick:_},floating:{"aria-orientation":j==="both"?void 0:j,...re,onKeyDown:B,onPointerMove(){v.current=!0}},item:ee}},[a,o,U,Y,Z,i,b,j,p,N,n,Q,h,u,W,m,I,g,R,S,r,ee])}function Va(e){return c.useMemo(()=>e.every(t=>t==null)?null:t=>{e.forEach(n=>{typeof n=="function"?n(t):n!=null&&(n.current=t)})},e)}function Mr(e,t){t===void 0&&(t={});const{open:n,floatingId:r}=e,{enabled:o=!0,role:a="dialog"}=t,s=at();return c.useMemo(()=>{const i={id:r,role:a};return o?a==="tooltip"?{reference:{"aria-describedby":n?r:void 0},floating:i}:{reference:{"aria-expanded":n?"true":"false","aria-haspopup":a==="alertdialog"?"dialog":a,"aria-controls":n?r:void 0,...a==="listbox"&&{role:"combobox"},...a==="menu"&&{id:s}},floating:{...i,...a==="menu"&&{"aria-labelledby":s}}}:{}},[o,a,n,r,s])}function Za(e,t){var n;const{open:r,dataRef:o}=e,{listRef:a,activeIndex:s,onMatch:i,onTypingChange:d,enabled:f=!0,findMatch:b=null,resetMs:u=750,ignoreKeys:m=[],selectedIndex:g=null}=t,h=c.useRef(),p=c.useRef(""),N=c.useRef((n=g??s)!=null?n:-1),R=c.useRef(null),w=ye(i),W=ye(d),x=ce(b),j=ce(m);return J(()=>{r&&(clearTimeout(h.current),R.current=null,p.current="")},[r]),J(()=>{if(r&&p.current===""){var I;N.current=(I=g??s)!=null?I:-1}},[r,g,s]),c.useMemo(()=>{if(!f)return{};function I(L){L?o.current.typing||(o.current.typing=L,W(L)):o.current.typing&&(o.current.typing=L,W(L))}function V(L,S,M){const k=x.current?x.current(S,M):S.find(C=>(C==null?void 0:C.toLocaleLowerCase().indexOf(M.toLocaleLowerCase()))===0);return k?L.indexOf(k):-1}function D(L){const S=a.current;if(p.current.length>0&&p.current[0]!==" "&&(V(S,S,p.current)===-1?I(!1):L.key===" "&&se(L)),S==null||j.current.includes(L.key)||L.key.length!==1||L.ctrlKey||L.metaKey||L.altKey)return;r&&L.key!==" "&&(se(L),I(!0)),S.every(v=>{var O,z;return v?((O=v[0])==null?void 0:O.toLocaleLowerCase())!==((z=v[1])==null?void 0:z.toLocaleLowerCase()):!0})&&p.current===L.key&&(p.current="",N.current=R.current),p.current+=L.key,clearTimeout(h.current),h.current=setTimeout(()=>{p.current="",N.current=R.current,I(!1)},u);const k=N.current,C=V(S,[...S.slice((k||0)+1),...S.slice(0,(k||0)+1)],p.current);C!==-1?(w(C),R.current=C):L.key!==" "&&(p.current="",I(!1))}return{reference:{onKeyDown:D},floating:{onKeyDown:D,onKeyUp(L){L.key===" "&&I(!1)}}}},[f,r,o,a,u,j,x,w,W])}function Pt(e,t){const[n,r]=e;let o=!1;const a=t.length;for(let s=0,i=a-1;s<a;i=s++){const[d,f]=t[s]||[0,0],[b,u]=t[i]||[0,0];f>=r!=u>=r&&n<=(b-d)*(r-f)/(u-f)+d&&(o=!o)}return o}function Ja(e,t){return e[0]>=t.x&&e[0]<=t.x+t.width&&e[1]>=t.y&&e[1]<=t.y+t.height}function Qa(e){e===void 0&&(e={});const{buffer:t=.5,blockPointerEvents:n=!1,requireIntent:r=!0}=e;let o,a=!1,s=null,i=null,d=performance.now();function f(u,m){const g=performance.now(),h=g-d;if(s===null||i===null||h===0)return s=u,i=m,d=g,null;const p=u-s,N=m-i,w=Math.sqrt(p*p+N*N)/h;return s=u,i=m,d=g,w}const b=u=>{let{x:m,y:g,placement:h,elements:p,onClose:N,nodeId:R,tree:w}=u;return function(x){function j(){clearTimeout(o),N()}if(clearTimeout(o),!p.domReference||!p.floating||h==null||m==null||g==null)return;const{clientX:I,clientY:V}=x,D=[I,V],L=lt(x),S=x.type==="mouseleave",M=te(p.floating,L),k=te(p.domReference,L),C=p.domReference.getBoundingClientRect(),v=p.floating.getBoundingClientRect(),O=h.split("-")[0],z=m>v.right-v.width/2,G=g>v.bottom-v.height/2,F=Ja(D,C),Y=v.width>C.width,Z=v.height>C.height,K=(Y?C:v).left,U=(Y?C:v).right,H=(Z?C:v).top,$=(Z?C:v).bottom;if(M&&(a=!0,!S))return;if(k&&(a=!1),k&&!S){a=!0;return}if(S&&le(x.relatedTarget)&&te(p.floating,x.relatedTarget)||w&&we(w.nodesRef.current,R).some(A=>{let{context:B}=A;return B==null?void 0:B.open}))return;if(O==="top"&&g>=C.bottom-1||O==="bottom"&&g<=C.top+1||O==="left"&&m>=C.right-1||O==="right"&&m<=C.left+1)return j();let Q=[];switch(O){case"top":Q=[[K,C.top+1],[K,v.bottom-1],[U,v.bottom-1],[U,C.top+1]];break;case"bottom":Q=[[K,v.top+1],[K,C.bottom-1],[U,C.bottom-1],[U,v.top+1]];break;case"left":Q=[[v.right-1,$],[v.right-1,H],[C.left+1,H],[C.left+1,$]];break;case"right":Q=[[C.right-1,$],[C.right-1,H],[v.left+1,H],[v.left+1,$]];break}function ee(A){let[B,_]=A;switch(O){case"top":{const X=[Y?B+t/2:z?B+t*4:B-t*4,_+t+1],re=[Y?B-t/2:z?B+t*4:B-t*4,_+t+1],P=[[v.left,z||Y?v.bottom-t:v.top],[v.right,z?Y?v.bottom-t:v.top:v.bottom-t]];return[X,re,...P]}case"bottom":{const X=[Y?B+t/2:z?B+t*4:B-t*4,_-t],re=[Y?B-t/2:z?B+t*4:B-t*4,_-t],P=[[v.left,z||Y?v.top+t:v.bottom],[v.right,z?Y?v.top+t:v.bottom:v.top+t]];return[X,re,...P]}case"left":{const X=[B+t+1,Z?_+t/2:G?_+t*4:_-t*4],re=[B+t+1,Z?_-t/2:G?_+t*4:_-t*4];return[...[[G||Z?v.right-t:v.left,v.top],[G?Z?v.right-t:v.left:v.right-t,v.bottom]],X,re]}case"right":{const X=[B-t,Z?_+t/2:G?_+t*4:_-t*4],re=[B-t,Z?_-t/2:G?_+t*4:_-t*4],P=[[G||Z?v.left+t:v.right,v.top],[G?Z?v.left+t:v.right:v.left+t,v.bottom]];return[X,re,...P]}}}if(!Pt([I,V],Q)){if(a&&!F)return j();if(!S&&r){const A=f(x.clientX,x.clientY);if(A!==null&&A<.1)return j()}Pt([I,V],ee([m,g]))?!a&&r&&(o=window.setTimeout(j,40)):j()}}};return b.__options={blockPointerEvents:n},b}const ft=({className:e,...t})=>{const n=T().theme.dropdown.floating.divider;return l.jsx("div",{className:y(n,e),...t})},Or=({children:e,className:t,...n})=>{const r=T().theme.dropdown.floating.header;return l.jsxs(l.Fragment,{children:[l.jsx("div",{className:y(r,t),...n,children:e}),l.jsx(ft,{})]})},es=({children:e,className:t,icon:n,onClick:r,theme:o={},...a})=>{const{ref:s,index:i}=Ba({label:typeof e=="string"?e:void 0}),{activeIndex:d,dismissOnClick:f,getItemProps:b,handleSelect:u}=c.useContext(Ar),m=d===i,g=E(T().theme.dropdown.floating.item,o),h=a;return l.jsx("li",{role:"menuitem",className:g.container,children:l.jsxs(Jt,{ref:s,className:y(g.base,t),...h,...b({onClick:()=>{r&&r(),f&&u(null)}}),tabIndex:m?0:-1,children:[n&&l.jsx(n,{className:g.icon}),e]})})},ts=({arrowRef:e,placement:t})=>{const n=[];return n.push(Hn(8)),n.push(t==="auto"?_n():Kn()),n.push(Gn({padding:8})),e!=null&&e.current&&n.push(Wn({element:e.current})),n},rs=({placement:e})=>e==="auto"?void 0:e,ns=({placement:e})=>({top:"bottom",right:"left",bottom:"top",left:"right"})[e.split("-")[0]],Pr=({open:e,arrowRef:t,placement:n="top",setOpen:r})=>Ir({placement:rs({placement:n}),open:e,onOpenChange:r,whileElementsMounted:Lt,middleware:ts({placement:n,arrowRef:t})}),Lr=({context:e,trigger:t,role:n="tooltip",interactions:r=[]})=>Sr([Rr(e,{enabled:t==="click"}),Ra(e,{enabled:t==="hover",handleClose:Qa()}),Er(e),Mr(e,{role:n}),...r]),os={top:Qn,right:Ht,bottom:Bt,left:Jn},as=({refs:e,children:t,inline:n,theme:r,disabled:o,setButtonWidth:a,getReferenceProps:s,renderTrigger:i,...d})=>{const f=e.reference,b=s();if(c.useEffect(()=>{f.current&&(a==null||a(f.current.clientWidth))},[f,a]),i){const u=i(r);return c.cloneElement(u,{ref:e.setReference,disabled:o,...b,...u.props})}return n?l.jsx("button",{type:"button",ref:e.setReference,className:r==null?void 0:r.inlineWrapper,disabled:o,...b,children:t}):l.jsx(tr,{...d,disabled:o,type:"button",ref:e.setReference,...b,children:t})},Ar=c.createContext({}),Fr=({children:e,className:t,dismissOnClick:n=!0,theme:r={},renderTrigger:o,...a})=>{const[s,i]=c.useState(!1),[d,f]=c.useState(null),[b,u]=c.useState(null),[m,g]=c.useState(void 0),h=c.useRef([]),p=c.useRef([]),N=E(T().theme.dropdown,r),R=a,w=a["data-testid"]||"flowbite-dropdown-target",{placement:W=a.inline?"bottom-start":"bottom",trigger:x="click",label:j,inline:I,arrowIcon:V=!0,...D}=R,L=c.useCallback(Z=>{u(Z),i(!1)},[]),S=c.useCallback(Z=>{s?f(Z):L(Z)},[s,L]),{context:M,floatingStyles:k,refs:C}=Pr({open:s,setOpen:i,placement:W}),v=qa(M,{listRef:h,activeIndex:d,selectedIndex:b,onNavigate:f}),O=Za(M,{listRef:p,activeIndex:d,selectedIndex:b,onMatch:S}),{getReferenceProps:z,getFloatingProps:G,getItemProps:F}=Lr({context:M,role:"menu",trigger:x,interactions:[v,O]}),Y=c.useMemo(()=>{const[Z]=W.split("-");return os[Z]??Bt},[W]);return l.jsxs(l.Fragment,{children:[l.jsxs(as,{...D,refs:C,inline:I,theme:N,"data-testid":w,className:y(N.floating.target,D.className),setButtonWidth:g,getReferenceProps:z,renderTrigger:o,children:[j,V&&l.jsx(Y,{className:N.arrowIcon})]}),l.jsx(Ar.Provider,{value:{activeIndex:d,dismissOnClick:n,getItemProps:F,handleSelect:L},children:s&&l.jsx(Tr,{context:M,modal:!1,children:l.jsx("div",{ref:C.setFloating,style:{...k,minWidth:m},"data-testid":"flowbite-dropdown","aria-expanded":s,...G({className:y(N.floating.base,N.floating.animation,"duration-100",!s&&N.floating.hidden,N.floating.style.auto,t)}),children:l.jsx(za,{elementsRef:h,labelsRef:p,children:l.jsx("ul",{className:N.content,tabIndex:-1,children:e})})})})})]})};Fr.displayName="Dropdown";Or.displayName="Dropdown.Header";ft.displayName="Dropdown.Divider";Object.assign(Fr,{Item:es,Header:Or,Divider:ft});const ss=c.forwardRef(({className:e,color:t="gray",helperText:n,sizing:r="md",theme:o={},...a},s)=>{const i=E(T().theme.fileInput,o);return l.jsxs(l.Fragment,{children:[l.jsx("div",{className:y(i.root.base,e),children:l.jsx("div",{className:i.field.base,children:l.jsx("input",{className:y(i.field.input.base,i.field.input.colors[t],i.field.input.sizes[r]),...a,type:"file",ref:s})})}),n&&l.jsx(Me,{color:t,children:n})]})});ss.displayName="FileInput";const Dr=({alt:e,className:t,children:n,href:r,name:o,src:a,theme:s={},...i})=>{const d=E(T().theme.footer.brand,s);return l.jsx("div",{children:r?l.jsxs("a",{"data-testid":"flowbite-footer-brand",href:r,className:y(d.base,t),...i,children:[l.jsx("img",{alt:e,src:a,className:d.img}),l.jsx("span",{"data-testid":"flowbite-footer-brand-span",className:d.span,children:o}),n]}):l.jsx("img",{alt:e,"data-testid":"flowbite-footer-brand",src:a,className:y(d.img,t),...i})})},zr=({by:e,className:t,href:n,theme:r={},year:o,...a})=>{const s=E(T().theme.footer.copyright,r);return l.jsxs("div",{"data-testid":"flowbite-footer-copyright",className:y(s.base,t),...a,children:["© ",o,n?l.jsx("a",{href:n,className:s.href,children:e}):l.jsx("span",{"data-testid":"flowbite-footer-copyright-span",className:s.span,children:e})]})},Br=({className:e,theme:t={},...n})=>{const r=E(T().theme.footer.divider,t);return l.jsx("hr",{"data-testid":"footer-divider",className:y(r.base,e),...n})},Hr=({ariaLabel:e,className:t,href:n,icon:r,theme:o={},...a})=>{const s=E(T().theme.footer.icon,o);return l.jsx("div",{children:n?l.jsx("a",{"aria-label":e,"data-testid":"flowbite-footer-icon",href:n,className:y(s.base,t),...a,children:l.jsx(r,{className:s.size})}):l.jsx(r,{"data-testid":"flowbite-footer-icon",className:s.size,...a})})},_r=({as:e="a",children:t,className:n,href:r,theme:o={},...a})=>{const s=E(T().theme.footer.groupLink.link,o);return l.jsx("li",{className:y(s.base,n),children:l.jsx(e,{href:r,className:s.href,...a,children:t})})},Kr=({children:e,className:t,col:n=!1,theme:r={},...o})=>{const a=E(T().theme.footer.groupLink,r);return l.jsx("ul",{"data-testid":"footer-groupLink",className:y(a.base,n&&a.col,t),...o,children:e})},Gr=({as:e="h2",className:t,theme:n={},title:r,...o})=>{const a=E(T().theme.footer.title,n);return l.jsx(e,{"data-testid":"flowbite-footer-title",className:y(a.base,t),...o,children:r})},Wr=({bgDark:e=!1,children:t,className:n,container:r=!1,theme:o={},...a})=>{const s=E(T().theme.footer,o);return l.jsx("footer",{"data-testid":"flowbite-footer",className:y(s.root.base,e&&s.root.bgDark,r&&s.root.container,n),...a,children:t})};Wr.displayName="Footer";zr.displayName="Footer.Copyright";_r.displayName="Footer.Link";Dr.displayName="Footer.Brand";Kr.displayName="Footer.LinkGroup";Hr.displayName="Footer.Icon";Gr.displayName="Footer.Title";Br.displayName="Footer.Divider";Object.assign(Wr,{Copyright:zr,Link:_r,LinkGroup:Kr,Brand:Dr,Icon:Hr,Title:Gr,Divider:Br});const Me=({children:e,className:t,color:n="default",theme:r={},value:o,...a})=>{const s=E(T().theme.helperText,r);return l.jsx("p",{className:y(s.root.base,s.root.colors[n],t),...a,children:o??e??""})};Me.displayName="HelperText";const $r=({active:e,children:t,className:n,href:r,icon:o,onClick:a,theme:s={},...i})=>{const d=E(T().theme.listGroup.item,s),f=typeof r<"u",b=f?"a":"button";return l.jsx("li",{className:y(d.base,n),children:l.jsxs(b,{href:r,onClick:a,type:f?void 0:"button",className:y(d.link.active[e?"on":"off"],d.link.base,d.link.href[f?"on":"off"]),...i,children:[o&&l.jsx(o,{"aria-hidden":!0,"data-testid":"flowbite-list-group-item-icon",className:d.link.icon}),t]})})},Ur=({children:e,className:t,theme:n={},...r})=>{const o=E(T().theme.listGroup,n);return l.jsx("ul",{className:y(o.root.base,t),...r,children:e})};Ur.displayName="ListGroup";$r.displayName="ListGroup.Item";Object.assign(Ur,{Item:$r});const Yr=c.createContext(void 0);function gt(){const e=c.useContext(Yr);if(!e)throw new Error("useModalContext should be used within the ModalContext provider!");return e}const Xr=({children:e,className:t,theme:n={},...r})=>{const o=E(T().theme.modal.body,n),{popup:a}=gt();return l.jsx("div",{className:y(o.base,a&&[o.popup],t),...r,children:e})},qr=({children:e,className:t,theme:n={},...r})=>{const o=E(T().theme.modal.footer,n),{popup:a}=gt();return l.jsx("div",{className:y(o.base,!a&&o.popup,t),...r,children:e})},Vr=({as:e="h3",children:t,className:n,theme:r={},id:o,...a})=>{const s=c.useId(),i=o||s,d=E(T().theme.modal.header,r),{popup:f,onClose:b,setHeaderId:u}=gt();return c.useLayoutEffect(()=>(u(i),()=>u(void 0)),[i,u]),l.jsxs("div",{className:y(d.base,f&&d.popup,n),...a,children:[l.jsx(e,{id:i,className:d.title,children:t}),l.jsx("button",{"aria-label":"Close",className:d.close.base,type:"button",onClick:b,children:l.jsx(eo,{"aria-hidden":!0,className:d.close.icon})})]})},Zr=c.forwardRef(({children:e,className:t,dismissible:n=!1,onClose:r,popup:o,position:a="center",root:s,show:i,size:d="2xl",theme:f={},initialFocus:b,...u},m)=>{const[g,h]=c.useState(void 0),p=E(T().theme.modal,f),{context:N}=Ir({open:i,onOpenChange:()=>r&&r()}),R=Va([N.refs.setFloating,m]),w=Rr(N),W=Er(N,{outsidePressEvent:"mousedown",enabled:n}),x=Mr(N),{getFloatingProps:j}=Sr([w,W,x]);return i?l.jsx(Yr.Provider,{value:{popup:o,onClose:r,setHeaderId:h},children:l.jsx(La,{root:s,children:l.jsx(Ha,{lockScroll:!0,"data-testid":"modal-overlay",className:y(p.root.base,p.root.positions[a],i?p.root.show.on:p.root.show.off,t),...u,children:l.jsx(Tr,{context:N,initialFocus:b,children:l.jsx("div",{ref:R,...j(u),"aria-labelledby":g,className:y(p.content.base,p.root.sizes[d]),children:l.jsx("div",{className:p.content.inner,children:e})})})})})}):null});Zr.displayName="Modal";Vr.displayName="Modal.Header";Xr.displayName="Modal.Body";qr.displayName="Modal.Footer";Object.assign(Zr,{Header:Vr,Body:Xr,Footer:qr});const Jr=({as:e="a",children:t,className:n,theme:r={},...o})=>{const a=E(T().theme.navbar.brand,r);return l.jsx(e,{className:y(a.base,n),...o,children:t})},Qr=c.createContext(void 0);function en(){const e=c.useContext(Qr);if(!e)throw new Error("useNavBarContext should be used within the NavbarContext provider!");return e}const tn=({children:e,className:t,theme:n={},...r})=>{const{isOpen:o}=en(),a=E(T().theme.navbar.collapse,n);return l.jsx("div",{"data-testid":"flowbite-navbar-collapse",className:y(a.base,a.hidden[o?"off":"on"],t),...r,children:l.jsx("ul",{className:a.list,children:e})})},rn=({active:e,as:t="a",disabled:n,children:r,className:o,theme:a={},...s})=>{const i=E(T().theme.navbar.link,a);return l.jsx("li",{children:l.jsx(t,{className:y(i.base,e&&i.active.on,!e&&!n&&i.active.off,i.disabled[n?"on":"off"],o),...s,children:r})})};function is(e){return de({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M16 132h416c8.837 0 16-7.163 16-16V76c0-8.837-7.163-16-16-16H16C7.163 60 0 67.163 0 76v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z"}}]})(e)}const nn=({barIcon:e=is,className:t,theme:n={},...r})=>{const{isOpen:o,setIsOpen:a}=en(),s=E(T().theme.navbar.toggle,n),i=()=>{a(!o)};return l.jsxs("button",{"data-testid":"flowbite-navbar-toggle",onClick:i,className:y(s.base,t),...r,children:[l.jsx("span",{className:"sr-only",children:"Open main menu"}),l.jsx(e,{"aria-hidden":!0,className:s.icon})]})},on=({border:e,children:t,className:n,fluid:r=!1,menuOpen:o,rounded:a,theme:s={},...i})=>{const[d,f]=c.useState(o),b=E(T().theme.navbar.root,s);return l.jsx(Qr.Provider,{value:{isOpen:d,setIsOpen:f},children:l.jsx("nav",{className:y(b.base,b.bordered[e?"on":"off"],b.rounded[a?"on":"off"],n),...i,children:l.jsx("div",{className:y(b.inner.base,b.inner.fluid[r?"on":"off"]),children:t})})})};on.displayName="Navbar";Jr.displayName="Navbar.Brand";tn.displayName="Navbar.Collapse";rn.displayName="Navbar.Link";nn.displayName="Navbar.Toggle";Object.assign(on,{Brand:Jr,Collapse:tn,Link:rn,Toggle:nn});const bt=({active:e,children:t,className:n,onClick:r,theme:o={},...a})=>{const s=E(T().theme.pagination,o);return l.jsx("button",{type:"button",className:y(e&&s.pages.selector.active,n),onClick:r,...a,children:t})};bt.displayName="Pagination.Button";const nt=({children:e,className:t,onClick:n,theme:r={},disabled:o=!1,...a})=>{const s=E(T().theme.pagination,r);return l.jsx("button",{type:"button",className:y(o&&s.pages.selector.disabled,t),disabled:o,onClick:n,...a,children:e})};nt.displayName="Pagination.Navigation";const ls=(e,t)=>e>=t?[]:[...Array(t-e+1).keys()].map(n=>n+e),an=({className:e,currentPage:t,layout:n="pagination",nextLabel:r="Next",onPageChange:o,previousLabel:a="Previous",renderPaginationButton:s=u=>l.jsx(bt,{...u}),showIcons:i=!1,theme:d={},totalPages:f,...b})=>{const u=E(T().theme.pagination,d),m=Math.min(Math.max(t+2,5),f),g=Math.max(1,m-4),h=()=>{o(Math.min(t+1,f))},p=()=>{o(Math.max(t-1,1))};return l.jsxs("nav",{className:y(u.base,e),...b,children:[n==="table"&&l.jsxs("div",{className:u.layout.table.base,children:["Showing ",l.jsx("span",{className:u.layout.table.span,children:g})," to ",l.jsx("span",{className:u.layout.table.span,children:m})," of ",l.jsx("span",{className:u.layout.table.span,children:f})," Entries"]}),l.jsxs("ul",{className:u.pages.base,children:[l.jsx("li",{children:l.jsxs(nt,{className:y(u.pages.previous.base,i&&u.pages.showIcon),onClick:p,disabled:t===1,children:[i&&l.jsx(Xn,{"aria-hidden":!0,className:u.pages.previous.icon}),a]})}),n==="pagination"&&ls(g,m).map(N=>l.jsx("li",{"aria-current":N===t?"page":void 0,children:s({className:y(u.pages.selector.base,t===N&&u.pages.selector.active),active:N===t,onClick:()=>o(N),children:N})},N)),l.jsx("li",{children:l.jsxs(nt,{className:y(u.pages.next.base,i&&u.pages.showIcon),onClick:h,disabled:t===f,children:[r,i&&l.jsx(qn,{"aria-hidden":!0,className:u.pages.next.icon})]})})]})]})};an.displayName="Pagination";Object.assign(an,{Button:bt});const ds=c.forwardRef(({className:e,theme:t={},...n},r)=>{const o=E(T().theme.radio,t);return l.jsx("input",{ref:r,type:"radio",className:y(o.root.base,e),...n})});ds.displayName="Radio";const cs=c.forwardRef(({className:e,sizing:t="md",theme:n={},...r},o)=>{const a=E(T().theme.rangeSlider,n);return l.jsx(l.Fragment,{children:l.jsx("div",{"data-testid":"flowbite-range-slider",className:y(a.root.base,e),children:l.jsx("div",{className:a.field.base,children:l.jsx("input",{ref:o,type:"range",className:y(a.field.input.base,a.field.input.sizes[t]),...r})})})})});cs.displayName="RangeSlider";const sn=({children:e,className:t,percentFilled:n=0,theme:r={},...o})=>{const a=E(T().theme.rating.advanced,r);return l.jsxs("div",{className:y(a.base,t),...o,children:[l.jsx("span",{className:a.label,children:e}),l.jsx("div",{className:a.progress.base,children:l.jsx("div",{className:a.progress.fill,"data-testid":"flowbite-rating-fill",style:{width:`${n}%`}})}),l.jsx("span",{className:a.progress.label,children:`${n}%`})]})},ln=c.createContext(void 0);function us(){const e=c.useContext(ln);if(!e)throw new Error("useRatingContext should be used within the RatingContext provider!");return e}const dn=({className:e,filled:t=!0,starIcon:n=Vn,theme:r={},...o})=>{const{size:a="sm"}=us(),s=E(T().theme.rating.star,r);return l.jsx(n,{"data-testid":"flowbite-rating-star",className:y(s.sizes[a],s[t?"filled":"empty"],e),...o})},cn=({children:e,className:t,size:n="sm",theme:r={},...o})=>{const a=E(T().theme.rating,r);return l.jsx(ln.Provider,{value:{size:n},children:l.jsx("div",{className:y(a.root.base,t),...o,children:e})})};cn.displayName="Rating";dn.displayName="Rating.Star";sn.displayName="Rating.Advanced";Object.assign(cn,{Star:dn,Advanced:sn});const fs=c.forwardRef(({addon:e,children:t,className:n,color:r="gray",helperText:o,icon:a,shadow:s,sizing:i="md",theme:d={},...f},b)=>{const u=E(T().theme.select,d);return l.jsxs("div",{className:y(u.base,n),children:[e&&l.jsx("span",{className:u.addon,children:e}),l.jsxs("div",{className:u.field.base,children:[a&&l.jsx("div",{className:u.field.icon.base,children:l.jsx(a,{className:u.field.icon.svg})}),l.jsx("select",{className:y(u.field.select.base,u.field.select.colors[r],u.field.select.sizes[i],u.field.select.withIcon[a?"on":"off"],u.field.select.withAddon[e?"on":"off"],u.field.select.withShadow[s?"on":"off"]),...f,ref:b,children:t}),o&&l.jsx(Me,{color:r,children:o})]})]})});fs.displayName="Select";const un=c.createContext(void 0);function $e(){const e=c.useContext(un);if(!e)throw new Error("useSidebarContext should be used within the SidebarContext provider!");return e}const fn=({children:e,color:t="info",className:n,theme:r={},...o})=>{const{isCollapsed:a}=$e(),s=E(T().theme.sidebar.cta,r);return l.jsx("div",{"data-testid":"sidebar-cta",hidden:a,className:y(s.base,s.color[t],n),...o,children:e})};fn.displayName="Sidebar.CTA";const pt=c.createContext(void 0);function gs(){const e=c.useContext(pt);if(!e)throw new Error("useSidebarItemContext should be used within the SidebarItemContext provider!");return e}const gn=({children:e,className:t,icon:n,label:r,chevronIcon:o=zt,renderChevronIcon:a,open:s=!1,theme:i={},...d})=>{const f=c.useId(),{isCollapsed:b}=$e(),[u,m]=c.useState(s),g=E(T().theme.sidebar.collapse,i);c.useEffect(()=>m(s),[s]);const h=({children:p})=>l.jsx("li",{children:b&&!u?l.jsx(mt,{content:r,placement:"right",children:p}):p});return l.jsxs(h,{children:[l.jsxs("button",{id:`flowbite-sidebar-collapse-${f}`,onClick:()=>m(!u),title:r,type:"button",className:y(g.button,t),...d,children:[n&&l.jsx(n,{"aria-hidden":!0,"data-testid":"flowbite-sidebar-collapse-icon",className:y(g.icon.base,g.icon.open[u?"on":"off"])}),b?l.jsx("span",{className:"sr-only",children:r}):l.jsxs(l.Fragment,{children:[l.jsx("span",{"data-testid":"flowbite-sidebar-collapse-label",className:g.label.base,children:r}),a?a(g,u):l.jsx(o,{"aria-hidden":!0,className:y(g.label.icon.base,g.label.icon.open[u?"on":"off"])})]})]}),l.jsx("ul",{"aria-labelledby":`flowbite-sidebar-collapse-${f}`,hidden:!u,className:g.list,children:l.jsx(pt.Provider,{value:{isInsideCollapse:!0},children:e})})]})};gn.displayName="Sidebar.Collapse";const bs=({id:e,isCollapsed:t,tooltipChildren:n,children:r,...o})=>l.jsx("li",{...o,children:t?l.jsx(mt,{content:l.jsx(ps,{id:e,children:n}),placement:"right",children:r}):r}),ps=({id:e,children:t})=>l.jsx(bn,{id:e,children:t}),bn=({id:e,children:t})=>{const n=T().theme.sidebar.item;return l.jsx("span",{"data-testid":"flowbite-sidebar-item-content",id:`flowbite-sidebar-item-${e}`,className:y(n.content.base),children:t})},pn=c.forwardRef(({active:e,as:t="a",children:n,className:r,icon:o,label:a,labelColor:s="info",theme:i={},...d},f)=>{var h,p,N,R;const b=c.useId(),{isCollapsed:u}=$e(),{isInsideCollapse:m}=gs(),g=E(T().theme.sidebar.item,i);return l.jsx(bs,{className:g.listItem,id:b,isCollapsed:u,tooltipChildren:n,children:l.jsxs(t,{"aria-labelledby":`flowbite-sidebar-item-${b}`,ref:f,className:y(g.base,e&&g.active,!u&&m&&((h=g.collapsed)==null?void 0:h.insideCollapse),r),...d,children:[o&&l.jsx(o,{"aria-hidden":!0,"data-testid":"flowbite-sidebar-item-icon",className:y((p=g.icon)==null?void 0:p.base,e&&((N=g.icon)==null?void 0:N.active))}),u&&!o&&l.jsx("span",{className:(R=g.collapsed)==null?void 0:R.noIcon,children:n.charAt(0).toLocaleUpperCase()??"?"}),!u&&l.jsx(bn,{id:b,children:n}),!u&&a&&l.jsx(Vt,{color:s,"data-testid":"flowbite-sidebar-label",hidden:u,className:g.label,children:a})]})})});pn.displayName="Sidebar.Item";const hn=({children:e,className:t,...n})=>{const r=T().theme.sidebar.itemGroup;return l.jsx("ul",{"data-testid":"flowbite-sidebar-item-group",className:y(r,t),...n,children:l.jsx(pt.Provider,{value:{isInsideCollapse:!1},children:e})})};hn.displayName="Sidebar.ItemGroup";const mn=({children:e,className:t,...n})=>{const r=T().theme.sidebar.items;return l.jsx("div",{className:y(r,t),"data-testid":"flowbite-sidebar-items",...n,children:e})};mn.displayName="Sidebar.Items";const yn=({children:e,className:t,href:n,img:r,imgAlt:o="",theme:a={},...s})=>{const i=c.useId(),{isCollapsed:d}=$e(),f=E(T().theme.sidebar.logo,a);return l.jsxs("a",{"aria-labelledby":`flowbite-sidebar-logo-${i}`,href:n,className:y(f.base,t),...s,children:[l.jsx("img",{alt:o,src:r,className:f.img}),l.jsx("span",{className:f.collapsed[d?"on":"off"],id:`flowbite-sidebar-logo-${i}`,children:e})]})};yn.displayName="Sidebar.Logo";const xn=({children:e,as:t="nav",collapseBehavior:n="collapse",collapsed:r=!1,theme:o={},className:a,...s})=>{const i=E(T().theme.sidebar,o);return l.jsx(un.Provider,{value:{isCollapsed:r},children:l.jsx(t,{"aria-label":"Sidebar",hidden:r&&n==="hide",className:y(i.root.base,i.root.collapsed[r?"on":"off"],a),...s,children:l.jsx("div",{className:i.root.inner,children:e})})})};xn.displayName="Sidebar";Object.assign(xn,{Collapse:gn,CTA:fn,Item:pn,Items:mn,ItemGroup:hn,Logo:yn});const vn=({className:e,color:t="info",light:n,size:r="md",theme:o={},...a})=>{const s=E(T().theme.spinner,o);return l.jsx("span",{role:"status",...a,children:l.jsxs("svg",{fill:"none",viewBox:"0 0 100 101",className:y(s.base,s.color[t],s.light[n?"on":"off"].base,s.light[n?"on":"off"].color[t],s.size[r],e),children:[l.jsx("path",{d:"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z",fill:"currentColor"}),l.jsx("path",{d:"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z",fill:"currentFill"})]})})};vn.displayName="Spinner";const hs=c.forwardRef(({children:e,className:t,onActiveTabChange:n,style:r="default",theme:o={},...a},s)=>{const i=E(T().theme.tab,o),d=c.useId(),f=c.useMemo(()=>c.Children.map(c.Children.toArray(e),({props:x})=>x),[e]),b=c.useRef([]),[u,m]=c.useState(Math.max(0,f.findIndex(x=>x.active))),[g,h]=c.useState(-1),p=x=>{m(x),n&&n(x)},N=({target:x})=>{p(x),h(x)},R=({event:x,target:j})=>{x.key==="ArrowLeft"&&h(Math.max(0,g-1)),x.key==="ArrowRight"&&h(Math.min(f.length-1,g+1)),x.key==="Enter"&&(p(j),h(j))},w=i.tablist.tabitem.styles[r],W=i.tabitemcontainer.styles[r];return c.useEffect(()=>{var x;(x=b.current[g])==null||x.focus()},[g]),c.useImperativeHandle(s,()=>({setActiveTab:p})),l.jsxs("div",{className:y(i.base,t),children:[l.jsx("div",{"aria-label":"Tabs",role:"tablist",className:y(i.tablist.base,i.tablist.styles[r],t),...a,children:f.map((x,j)=>l.jsxs("button",{type:"button","aria-controls":`${d}-tabpanel-${j}`,"aria-selected":j===u,className:y(i.tablist.tabitem.base,w.base,j===u&&w.active.on,j!==u&&!x.disabled&&w.active.off),disabled:x.disabled,id:`${d}-tab-${j}`,onClick:()=>N({target:j}),onKeyDown:I=>R({event:I,target:j}),ref:I=>b.current[j]=I,role:"tab",tabIndex:j===g?0:-1,style:{zIndex:j===g?2:1},children:[x.icon&&l.jsx(x.icon,{className:i.tablist.tabitem.icon}),x.title]},j))}),l.jsx("div",{className:y(i.tabitemcontainer.base,W),children:f.map((x,j)=>l.jsx("div",{"aria-labelledby":`${d}-tab-${j}`,className:i.tabpanel,hidden:j!==u,id:`${d}-tabpanel-${j}`,role:"tabpanel",tabIndex:0,children:x.children},j))})]})});hs.displayName="Tabs.Group";const kn=({children:e,className:t,theme:n={},...r})=>{const o=E(T().theme.table.body,n);return l.jsx("tbody",{className:y(o.base,t),...r,children:e})},wn=({children:e,className:t,theme:n={},...r})=>{const o=E(T().theme.table.body.cell,n);return l.jsx("td",{className:y(o.base,t),...r,children:e})},Cn=c.createContext(void 0);function ms(){const e=c.useContext(Cn);if(!e)throw new Error("useTableContext should be used within the TableContext provider!");return e}const Nn=({children:e,className:t,theme:n={},...r})=>{const o=E(T().theme.table,n);return l.jsx("thead",{className:y(o.head.base,t),...r,children:l.jsx("tr",{children:e})})},Tn=({children:e,className:t,theme:n={},...r})=>{const o=E(T().theme.table.head.cell,n);return l.jsx("th",{className:y(o.base,t),...r,children:e})},jn=({children:e,className:t,theme:n={},...r})=>{const{hoverable:o,striped:a}=ms(),s=E(T().theme.table.row,n);return l.jsx("tr",{"data-testid":"table-row-element",className:y(s.base,a&&s.striped,o&&s.hovered,t),...r,children:e})},Rn=({children:e,className:t,hoverable:n,striped:r,theme:o={},...a})=>{const s=E(T().theme.table,o);return l.jsx("div",{"data-testid":"table-element",className:y(s.root.wrapper),children:l.jsxs(Cn.Provider,{value:{striped:r,hoverable:n},children:[l.jsx("div",{className:y(s.root.shadow,t)}),l.jsx("table",{className:y(s.root.base,t),...a,children:e})]})})};Rn.displayName="Table";Nn.displayName="Table.Head";kn.displayName="Table.Body";jn.displayName="Table.Row";wn.displayName="Table.Cell";Tn.displayName="Table.HeadCell";Object.assign(Rn,{Head:Nn,Body:kn,Row:jn,Cell:wn,HeadCell:Tn});const ys=c.forwardRef(({addon:e,className:t,color:n="gray",helperText:r,icon:o,rightIcon:a,shadow:s,sizing:i="md",theme:d={},...f},b)=>{const u=E(T().theme.textInput,d);return l.jsxs(l.Fragment,{children:[l.jsxs("div",{className:y(u.base,t),children:[e&&l.jsx("span",{className:u.addon,children:e}),l.jsxs("div",{className:u.field.base,children:[o&&l.jsx("div",{className:u.field.icon.base,children:l.jsx(o,{className:u.field.icon.svg})}),a&&l.jsx("div",{"data-testid":"right-icon",className:u.field.rightIcon.base,children:l.jsx(a,{className:u.field.rightIcon.svg})}),l.jsx("input",{className:y(u.field.input.base,u.field.input.colors[n],u.field.input.sizes[i],u.field.input.withIcon[o?"on":"off"],u.field.input.withRightIcon[a?"on":"off"],u.field.input.withAddon[e?"on":"off"],u.field.input.withShadow[s?"on":"off"]),...f,ref:b})]})]}),r&&l.jsx(Me,{color:n,children:r})]})});ys.displayName="TextInput";const xs=c.forwardRef(({className:e,color:t="gray",helperText:n,shadow:r,theme:o={},...a},s)=>{const i=E(T().theme.textarea,o);return l.jsxs(l.Fragment,{children:[l.jsx("textarea",{ref:s,className:y(i.base,i.colors[t],i.withShadow[r?"on":"off"],e),...a}),n&&l.jsx(Me,{color:t,children:n})]})});xs.displayName="Textarea";const En=({children:e,className:t,theme:n={},...r})=>{const o=E(T().theme.timeline.item.content,n).body;return l.jsx("div",{className:y(o,t),...r,children:e})},In=c.createContext(void 0);function ht(){const e=c.useContext(In);if(!e)throw new Error("useTimelineContext should be used within the TimelineContext providor!");return e}const Sn=({children:e,className:t,theme:n={},...r})=>{const o=E(T().theme.timeline.item.content,n),{horizontal:a}=ht();return l.jsx("div",{"data-testid":"timeline-content",className:y(a&&o.root.base,t),...r,children:e})},Mn=({children:e,className:t,theme:n={},...r})=>{const o=E(T().theme.timeline.item,n),{horizontal:a}=ht();return l.jsx("li",{"data-testid":"timeline-item",className:y(a&&o.root.horizontal,!a&&o.root.vertical,t),...r,children:e})},On=({children:e,className:t,icon:n,theme:r={},...o})=>{const a=E(T().theme.timeline.item.point,r),{horizontal:s}=ht();return l.jsxs("div",{"data-testid":"timeline-point",className:y(s&&a.horizontal,!s&&a.vertical,t),...o,children:[e,n?l.jsx("span",{className:y(a.marker.icon.wrapper),children:l.jsx(n,{"aria-hidden":!0,className:y(a.marker.icon.base)})}):l.jsx("div",{className:y(s&&a.marker.base.horizontal,!s&&a.marker.base.vertical)}),s&&l.jsx("div",{className:y(a.line)})]})},Pn=({children:e,className:t,theme:n={},...r})=>{const o=E(T().theme.timeline.item.content,n).time;return l.jsx("time",{className:y(o,t),...r,children:e})},Ln=({as:e="h3",children:t,className:n,theme:r={},...o})=>{const a=E(T().theme.timeline.item.content,r).title;return l.jsx(e,{className:y(a,n),...o,children:t})},An=({children:e,className:t,horizontal:n,theme:r={},...o})=>{const a=E(T().theme.timeline,r);return l.jsx(In.Provider,{value:{horizontal:n},children:l.jsx("ol",{"data-testid":"timeline-component",className:y(n&&a.root.direction.horizontal,!n&&a.root.direction.vertical,t),...o,children:e})})};An.displayName="Timeline";Mn.displayName="Timeline.Item";On.displayName="Timeline.Point";Sn.displayName="Timeline.Content";Pn.displayName="Timeline.Time";Ln.displayName="Timeline.Title";En.displayName="Timeline.Body";Object.assign(An,{Item:Mn,Point:On,Content:Sn,Time:Pn,Title:Ln,Body:En});const Fn=c.createContext(void 0);function vs(){const e=c.useContext(Fn);if(!e)throw new Error("useToastContext should be used within the ToastContext provider!");return e}const Dn=({className:e,onClick:t,theme:n={},xIcon:r=Zn,onDismiss:o,...a})=>{const s=E(T().theme.toast.toggle,n),{duration:i,isClosed:d,isRemoved:f,setIsClosed:b,setIsRemoved:u}=vs(),m=g=>{if(t&&t(g),o){o();return}b(!d),setTimeout(()=>u(!f),i)};return l.jsx("button",{"aria-label":"Close",onClick:m,type:"button",className:y(s.base,e),...a,children:l.jsx(r,{"aria-hidden":!0,className:s.icon})})},ks={75:"duration-75",100:"duration-100",150:"duration-150",200:"duration-200",300:"duration-300",500:"duration-500",700:"duration-700",1e3:"duration-1000"},zn=({children:e,className:t,duration:n=300,theme:r={},...o})=>{const[a,s]=c.useState(!1),[i,d]=c.useState(!1),f=E(T().theme.toast,r);return i?null:l.jsx(Fn.Provider,{value:{duration:n,isClosed:a,isRemoved:i,setIsClosed:s,setIsRemoved:d},children:l.jsx("div",{"data-testid":"flowbite-toast",role:"alert",className:y(f.root.base,ks[n],a&&f.root.closed,t),...o,children:e})})};zn.displayName="Toast";Dn.displayName="Toast.Toggle";Object.assign(zn,{Toggle:Dn});const ws=({animation:e="duration-300",arrow:t=!0,children:n,className:r,content:o,placement:a="top",style:s="dark",theme:i,trigger:d="hover",minWidth:f,...b})=>{const u=c.useRef(null),[m,g]=c.useState(!1),h=Pr({open:m,placement:a,arrowRef:u,setOpen:g}),{context:p,middlewareData:{arrow:{x:N,y:R}={}},refs:w,strategy:W,update:x,x:j,y:I}=h,V=Ua(p),{getFloatingProps:D,getReferenceProps:L}=Lr({context:p,role:"tooltip",trigger:d,interactions:[V]});return c.useEffect(()=>{if(w.reference.current&&w.floating.current&&m)return Lt(w.reference.current,w.floating.current,x)},[m,w.floating,w.reference,x]),l.jsxs(l.Fragment,{children:[l.jsx("div",{ref:w.setReference,className:i.target,"data-testid":"flowbite-tooltip-target",...L(),children:n}),l.jsxs("div",{ref:w.setFloating,"data-testid":"flowbite-tooltip",...D({className:y(i.base,e&&`${i.animation} ${e}`,!m&&i.hidden,i.style[s],r),style:{position:W,top:I??" ",left:j??" ",minWidth:f},...b}),children:[l.jsx("div",{className:i.content,children:o}),t&&l.jsx("div",{className:y(i.arrow.base,s==="dark"&&i.arrow.style.dark,s==="light"&&i.arrow.style.light,s==="auto"&&i.arrow.style.auto),"data-testid":"flowbite-tooltip-arrow",ref:u,style:{top:R??" ",left:N??" ",right:" ",bottom:" ",[ns({placement:h.placement})]:i.arrow.placement},children:" "})]})]})},mt=({animation:e="duration-300",arrow:t=!0,children:n,className:r,content:o,placement:a="top",style:s="dark",theme:i={},trigger:d="hover",...f})=>{const b=E(T().theme.tooltip,i);return l.jsx(ws,{animation:e,arrow:t,content:o,placement:a,style:s,theme:b,trigger:d,className:r,...f,children:n})};mt.displayName="Tooltip";
