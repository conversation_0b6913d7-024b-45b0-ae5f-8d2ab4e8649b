import React from "react";
import { AuthContext } from "../authContext";
import { NavLink } from "react-router-dom";
import { GlobalContext } from "../globalContext";
export const AdminHeader = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { state } = React.useContext(GlobalContext);

  return (
    <>
      <div className={`sidebar-holder ${!state.isOpen ? "open-nav" : ""}`}>
        <div className="sticky top-0 h-fit">
          <div className="w-full p-4 bg-sky-500">
            <div className="text-white font-bold text-center text-2xl">
              Admin
            </div>
          </div>
          <div className="w-full sidebar-list">
            <ul className="flex flex-wrap">
              <li className="list-none block w-full">
                <NavLink
                  to="/admin/dashboard"
                  className={`${
                    state.path == "admin" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  Dashboard
                </NavLink>
              </li>

              <li className="list-none block w-full">
                <NavLink
                  to="/admin/cms"
                  className={`${
                    state.path == "cmss" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  Cms
                </NavLink>
              </li>
              <li className="list-none block w-full">
                <NavLink
                  to="/admin/maintenance-page/58"
                  className={`${
                    state.path == "cmss" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  Maintenance
                </NavLink>
              </li>
              {/* <li className="list-none block w-full">
                <NavLink
                  to="/admin/email-submission"
                  className={`${
                    state.path == "email-submission"
                      ? "text-black bg-gray-200"
                      : ""
                  }`}
                >
                  Email Submission
                </NavLink>
              </li> */}
              <li className="list-none block w-full">
                <NavLink
                  to="/admin/quiz"
                  className={`${
                    state.path == "quiz" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  Quiz
                </NavLink>
              </li>
              <li className="list-none block w-full">
                <NavLink
                  to="/admin/suggested-questions"
                  className={`${
                    state.path == "suggested-questions"
                      ? "text-black bg-gray-200"
                      : ""
                  }`}
                >
                  Suggested Questions
                </NavLink>
              </li>

              <li className="list-none block w-full">
                <NavLink
                  to="/admin/email"
                  className={`${
                    state.path == "emails" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  Emails
                </NavLink>
              </li>

              <li className="list-none block w-full">
                <NavLink
                  to="/admin/newsletter"
                  className={`${
                    state.path == "newsletter" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  Newsletter
                </NavLink>
              </li>
            

              <li className="list-none block w-full">
                <NavLink
                  to="/admin/payments"
                  className={`${
                    state.path == "payments" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  Payments
                </NavLink>
              </li>
              <li className="list-none block w-full">
                <NavLink
                  to="/admin/plans"
                  className={`${
                    state.path == "plans" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  Plans
                </NavLink>
              </li>
              <li className="list-none block w-full">
                <NavLink
                  to="/admin/rewards"
                  className={`${
                    state.path == "rewards" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  Gifts/Rewards
                </NavLink>
              </li>
              <li className="list-none block w-full">
                <NavLink
                  to="/admin/stripe-discount"
                  className={`${
                    state.path == "stripeDiscount"
                      ? "text-black bg-gray-200"
                      : ""
                  }`}
                >
                  Stripe Discount
                </NavLink>
              </li>
              <li className="list-none block w-full">
                <NavLink
                  to="/admin/google-analytics"
                  className={`${
                    state.path == "googleAnalytics"
                      ? "text-black bg-gray-200"
                      : ""
                  }`}
                >
                  Google Analytics
                </NavLink>
              </li>
              {/* <li className="list-none block w-full">
                <NavLink
                  to="/admin/free-message"
                  className={`${
                    state.path == "free-message" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  Free Message
                </NavLink>
              </li> */}
              {/* 
              <li className="list-none block w-full">
                <NavLink
                  to="/admin/prices"
                  className={`${
                    state.path == "prices" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  Prices
                </NavLink>
              </li> */}

              <li className="list-none block w-full">
                <NavLink
                  to="/admin/users"
                  className={`${
                    state.path == "users" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  Users
                </NavLink>
              </li>

              <li className="list-none block w-full">
                <NavLink
                  to="/admin/profile"
                  className={`${
                    state.path == "profile" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  Profile
                </NavLink>
              </li>
              <li className="list-none block w-full">
                <NavLink
                  to="/admin/lenders"
                  className={`${
                    state.path == "lenders" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  Provider
                </NavLink>
              </li>
              <li className="list-none block w-full">
                <NavLink
                  to="/admin/dynamic-headers"
                  className={`${
                    state.path == "dynamicHeaders" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  Link App
                </NavLink>
              </li>
              <li className="list-none block w-full">
                <NavLink
                  to="/admin/seo"
                  className={`${
                    state.path == "seo" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  SEO
                </NavLink>
              </li>
              <li className="list-none block w-full">
                <NavLink
                  to="/admin/collaboration"
                  className={`${
                    state.path == "collaboration" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  Collaboration
                </NavLink>
              </li>
              <li className="list-none block w-full">
                <NavLink
                  to="/admin/user-chats"
                  className={`${
                    state.path == "user-chats" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  Users Chat
                </NavLink>
              </li>
              <li className="list-none block w-full">
                <NavLink
                  to="/admin/prompt"
                  className={`${
                    state.path == "prompt" ? "text-black bg-gray-200" : ""
                  }`}
                >
                  Prompts
                </NavLink>
              </li>
              <li className="list-none block w-full">
                <NavLink
                  to="/admin/login"
                  onClick={() =>
                    dispatch({
                      type: "LOGOUT",
                    })
                  }
                >
                  Logout
                </NavLink>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </>
  );
};

export default AdminHeader;
