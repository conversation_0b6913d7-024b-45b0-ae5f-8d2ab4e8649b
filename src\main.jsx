import React, { Suspense } from "react";
import { AuthContext } from "./authContext";
import { Routes, Route } from "react-router-dom";
import SnackBar from "./components/SnackBar";
import PublicHeader from "./components/PublicHeader";
import TopHeader from "./components/TopHeader";

import AdminHeader from "./components/AdminHeader";

import NotFoundPage from "./pages/NotFoundPage";
import AdminLoginPage from "./pages/AdminLoginPage";
import AdminForgotPage from "./pages/AdminForgotPage";
import AdminResetPage from "./pages/AdminResetPage";
import AdminDashboardPage from "./pages/AdminDashboardPage";
import AdminProfilePage from "./pages/AdminProfilePage";
import SessionExpiredModal from "./components/SessionExpiredModal";

import AddAdminCmsPage from "./pages/AddAdminCmsPage";
import AddAdminEmailPage from "./pages/AddAdminEmailPage";
import AddAdminPhotoPage from "./pages/AddAdminPhotoPage";
import AdminChatPage from "./pages/AdminChatPage";
import AdminCmsListPage from "./pages/AdminCmsListPage";
import AdminEmailListPage from "./pages/AdminEmailListPage";
import AdminPhotoListPage from "./pages/AdminPhotoListPage";
import EditAdminCmsPage from "./pages/EditAdminCmsPage";
import EditAdminEmailPage from "./pages/EditAdminEmailPage";
import UserMagicLoginPage from "./pages/MagicLogin/UserMagicLoginPage";
import MagicLoginVerifyPage from "./pages/MagicLogin/MagicLoginVerifyPage";
import AddAdminPlansTablePage from "./pages/AddAdminPlansTablePage";
import EditAdminPlansTablePage from "./pages/EditAdminPlansTablePage";
import ViewAdminPlansTablePage from "./pages/ViewAdminPlansTablePage";
import ListAdminPlansTablePage from "./pages/ListAdminPlansTablePage";
import AddAdminPricesTablePage from "./pages/AddAdminPricesTablePage";
import EditAdminPricesTablePage from "./pages/EditAdminPricesTablePage";
import ViewAdminPricesTablePage from "./pages/ViewAdminPricesTablePage";
import ListAdminPricesTablePage from "./pages/ListAdminPricesTablePage";
import AdminUserListPage from "./pages/AdminUserListPage";
import AddAdminUserPage from "./pages/AddAdminUserPage";
import EditAdminUserPage from "./pages/EditAdminUserPage";
// users Components and routes
import UserHeader from "Components/UserHeader";
import UserLoginPage from "Pages/UsersPage/UserLoginPage";
import ResetConfirmationPage from "Pages/UsersPage/ResetConfirmationPage";
import UserForgotPage from "Pages/UsersPage/UserForgotPage";
import UserResetPage from "Pages/UsersPage/UserResetPage";
import UserSignUpPage from "Pages/UsersPage/UserSignUpPage";
import SignUpPage from "Pages/AdminSignUpPage";
import UserProfilePage from "Pages/UsersPage/UserProfilePage";
import UserPlanPage from "Pages/UsersPage/UserPlanPage";
import ChatComponent from "Components/ChatComponent";
import ListAdminPaymentsTablePage from "Pages/ListAdminPaymentsTablePage";
import AboutUsUserPage from "Components/AboutUsUserPage";
import ListUserPaymentsTablePage from "Pages/UsersPage/ListUserPaymentsTablePage";
import UserHomePage from "Pages/UsersPage/UserHomePage";
import UserOrderPage from "Pages/UsersPage/UserOrderPage";
import Chat from "Components/Chat";
import SocialLoginVerificationTemplate from "Pages/SocialLogin/SocialLoginVerfication";
import ListAdminLendersPage from "Pages/ListAdminLendersPage";
import AddAdminLendersPage from "Pages/AddAdminLendersPage";
import EditAdminLendersPage from "Pages/EditAdminLendersPage";
import ViewAdminLendersPage from "Pages/ViewAdminLendersPage";
import PrivacyPolicy from "Components/PrivacyPolicy";
import UserTerms from "Components/UserTerms";
import Disclaimer from "Components/Disclaimer";
import Quiz from "Pages/UsersPage/Quiz";
import TermsAndConditions from "Pages/UsersPage/TermsAndConditions";
import ViewAdminPaymentPage from "Pages/ViewAdminPaymentTablePage";
import Test from "Components/Test";
import AddAdminRewardPage from "Pages/AddAdminRewardPage";
import AddAdminQuizPage from "Pages/AddAdminQuizPage";
import AdminQuizListPage from "Pages/AdminQuizListPage";
import EditAdminQuizPage from "Pages/EditAdminQuizPage";
import ViewAdminQuizTablePage from "Pages/ViewAdminQuizTablePage";
import AdminRewardListPage from "Pages/AdminRewardListPage";
import EditAdminRewardPage from "Pages/EditAdminRewardPage";
import CountdownPage from "Pages/UsersPage/CountdownPage";
import CountdownPage2 from "Pages/UsersPage/CountdownPage2";
import EventPlan from "Components/EventPlan";
import Checkout from "Components/Checkout";
import CheckoutForm from "Components/CheckoutForm";
import Payment from "Pages/UsersPage/Payment";
import AdsVid from "Components/AdsVid";
import AddAdminSuggestedMessages from "Pages/AddAdminSuggestedQuestions";
import AddAdminSuggestedQuestions from "Pages/AddAdminSuggestedQuestions";
import EditAdminSuggestedQuestions from "Pages/EditAdminSuggestedQuestions";
import ViewAdminSuggestedQuestions from "Pages/ViewAdminSuggestedQuestions";
import ListAdminSuggestedQuestions from "Pages/ListAdminSuggestedQuestions";
import ListAdminEmailSubmissionPage from "Pages/ListAdminEmailSubmissionPage";
import AppleDoc from "Pages/AppleDoc";
import ThankYouPage from "Pages/UsersPage/ThankYouPage";
import ReportIssuesTemplate from "Pages/UsersPage/ReportIssuesTemplate";
import AdminConfirmLoginPage from "Pages/AdminConfirmLoginPage";
import ListAdminUserChatsPage from "Pages/ListAdminUserChatsPage";
import ViewAdminUserChatPage from "Pages/ViewAdminUserChatPage";
import UserConfirmLoginPage from "Pages/UsersPage/UserConfirmLoginPage";
import UserConfirmSignUpPage from "Pages/UsersPage/UserConfirmSignUpPage";
import UserRedirectSignUpPage from "Pages/UsersPage/UserRedirectSignUpPage";
import UserVerifyEmailPage from "Pages/UsersPage/UserVerifyEmailPage";
import EditAdminFreeMessagePage from "Pages/EditAdminFreeMessagePage";
import AdminStripeDiscountListPage from "Pages/AdminStripeDiscountListPage";
import AdminGoogleAnalyticsListPage from "Pages/AdminGoogleAnalyticsListPage";
import CollaborationPage from "Components/CollaborationPage";
import AgeCheckWrapper from "Pages/UsersPage/AgeCheckWrapper";
import AdminDynamicHeaderListPage from "Pages/AdminDynamicHeaderListPage";
import AddAdminDynamicHeaderPage from "Pages/AddAdminDynamicHeaderPage";
import EditAdminDynamicHeaderPage from "Pages/EditAdminDynamicHeaderPage";
import ViewAdminDynamicHeaderTablePage from "Pages/ViewAdminDynamicHeaderTablePage";
import AdminSEOListPage from "Pages/AdminSEOListPage";
import AddAdminSEOPage from "Pages/AddAdminSEOPage";
import EditAdminSEOPage from "Pages/EditAdminSEOPage";
import ViewAdminSEOTablePage from "Pages/ViewAdminSEOTablePage";
// import AdminCollaborationPage from "Pages/AdminCollaborationPage22";
import AdminMaintenancePage from "Pages/AdminMaintenancePage";
import AdminPromptListPage from "Pages/AdminPromptListPage";
import AddAdminPromptPage from "Pages/AddAdminPromptPage";
import EditAdminPromptPage from "Pages/EditAdminPromptPage";
import ViewAdminPromptTablePage from "Pages/ViewAdminPromptTablePage";
import AdminCollaborationPage from "Pages/AdminCollaborationPage";
import AddAdminDefaultPromptPage from "Pages/AddAdminDefaultPromptPage";
import EditAdminDefaultPromptPage from "Pages/EditAdminDefaultPromptPage";
import ViewAdminDefaultPromptTablePage from "Pages/ViewAdminDefaultPromptTablePage";
import AdminNewsLetterListPage from "Pages/AdminNewsLetterListPage";

function renderHeader(role) {
  switch (role) {
    case "admin":
      return <AdminHeader />;
    // case "user":
    //   return <UserHeader />;

    default:
      return <PublicHeader />;
  }
}

function renderRoutes(role) {
  switch (role) {
    case "admin":
      return (
        <Suspense fallback={<div>Loading...</div>}>
          <Routes>
            <Route
              path="/admin/dashboard"
              element={<AdminDashboardPage />}
            ></Route>
            <Route
              exact
              path="/admin/profile"
              element={<AdminProfilePage />}
            ></Route>
            <Route path="/admin/add-cms" element={<AddAdminCmsPage />}></Route>

            <Route
              path="/admin/add-email"
              element={<AddAdminEmailPage />}
            ></Route>
            <Route
              path="/admin/add-photo"
              element={<AddAdminPhotoPage />}
            ></Route>
            <Route path="/admin/chat" element={<AdminChatPage />}></Route>
            <Route path="/admin/cms" element={<AdminCmsListPage />}></Route>
            <Route path="/admin/email" element={<AdminEmailListPage />}></Route>
            <Route path="/admin/photo" element={<AdminPhotoListPage />}></Route>
            <Route
              path="/admin/edit-cms/:id"
              element={<EditAdminCmsPage />}
            ></Route>
            <Route
              path="/admin/maintenance-page/:id"
              element={<AdminMaintenancePage />}
            ></Route>
            <Route path="/admin/newsletter" element={<AdminNewsLetterListPage />}></Route>
            <Route path="/admin/quiz" element={<AdminQuizListPage />}></Route>
            <Route
              path="/admin/add-quiz"
              element={<AddAdminQuizPage />}
            ></Route>
            <Route
              path="/admin/edit-quiz/:id"
              element={<EditAdminQuizPage />}
            ></Route>
            <Route
              path="/admin/view-quiz/:id"
              element={<ViewAdminQuizTablePage />}
            ></Route>

            <Route
              path="/admin/prompt"
              element={<AdminPromptListPage />}
            ></Route>
            <Route
              path="/admin/add-prompt"
              element={<AddAdminPromptPage />}
            ></Route>
            <Route
              path="/admin/edit-prompt/:id"
              element={<EditAdminPromptPage />}
            ></Route>
            <Route
              path="/admin/view-prompt/:id"
              element={<ViewAdminPromptTablePage />}
            ></Route>

            {/* ....... */}
            <Route
              path="/admin/add-default-prompt"
              element={<AddAdminDefaultPromptPage />}
            ></Route>
            <Route
              path="/admin/edit-default-prompt/:id"
              element={<EditAdminDefaultPromptPage />}
            ></Route>
            <Route
              path="/admin/view-default-prompt/:id"
              element={<ViewAdminDefaultPromptTablePage />}
            ></Route>

            {/* ......... */}

            <Route
              path="/admin/dynamic-headers"
              element={<AdminDynamicHeaderListPage />}
            ></Route>
            <Route
              path="/admin/add-dynamic-headers"
              element={<AddAdminDynamicHeaderPage />}
            ></Route>
            <Route
              path="/admin/edit-dynamic-headers/:id"
              element={<EditAdminDynamicHeaderPage />}
            ></Route>
            <Route
              path="/admin/view-dynamic-headers/:id"
              element={<ViewAdminDynamicHeaderTablePage />}
            ></Route>

            <Route path="/admin/seo" element={<AdminSEOListPage />}></Route>
            <Route path="/admin/add-seo" element={<AddAdminSEOPage />}></Route>
            <Route
              path="/admin/edit-seo/:id"
              element={<EditAdminSEOPage />}
            ></Route>
            <Route
              path="/admin/view-seo/:id"
              element={<ViewAdminSEOTablePage />}
            ></Route>

            <Route
              path="/admin/collaboration"
              element={
                <AdminCollaborationPage />
                // <AdminCollaborationPage />
              }
            ></Route>

            <Route
              path="/admin/edit-email/:id"
              element={<EditAdminEmailPage />}
            ></Route>
            <Route
              path="/admin/add-plans"
              element={<AddAdminPlansTablePage />}
            ></Route>
            <Route
              path="/admin/edit-plans/:id"
              element={<EditAdminPlansTablePage />}
            ></Route>
            <Route
              path="/admin/view-plans/:id"
              element={<ViewAdminPlansTablePage />}
            ></Route>
            <Route
              path="/admin/plans"
              element={<ListAdminPlansTablePage />}
            ></Route>
            <Route
              path="/admin/view-payments/:id"
              element={<ViewAdminPaymentPage />}
            ></Route>
            <Route
              path="/admin/payments"
              element={<ListAdminPaymentsTablePage />}
            ></Route>
            <Route
              path="/admin/add-prices"
              element={<AddAdminPricesTablePage />}
            ></Route>
            <Route
              path="/admin/edit-prices/:id"
              element={<EditAdminPricesTablePage />}
            ></Route>
            <Route
              path="/admin/view-prices/:id"
              element={<ViewAdminPricesTablePage />}
            ></Route>
            <Route
              path="/admin/prices"
              element={<ListAdminPricesTablePage />}
            ></Route>
            <Route path="/admin/users" element={<AdminUserListPage />}></Route>
            <Route
              path="/admin/add-user"
              element={<AddAdminUserPage />}
            ></Route>
            <Route
              path="/admin/edit-user/:id"
              element={<EditAdminUserPage />}
            ></Route>

            <Route
              path="/admin/lenders"
              element={<ListAdminLendersPage />}
            ></Route>
            <Route
              path="/admin/add-lenders"
              element={<AddAdminLendersPage />}
            ></Route>
            <Route
              path="/admin/edit-lenders/:id"
              element={<EditAdminLendersPage />}
            ></Route>
            <Route
              path="/admin/view-lenders/:id"
              element={<ViewAdminLendersPage />}
            ></Route>
            {/* <Route
            path="/admin/rewards"
            element={<ListAdminLendersPage />}
          ></Route> */}

            <Route
              path="/admin/view-chat/:id"
              element={<ViewAdminUserChatPage />}
            ></Route>
            <Route
              path="/admin/user-chats"
              element={<ListAdminUserChatsPage />}
            ></Route>
            <Route
              path="/admin/stripe-discount"
              element={<AdminStripeDiscountListPage />}
            ></Route>
            <Route
              path="/admin/google-analytics"
              element={<AdminGoogleAnalyticsListPage />}
            ></Route>
            <Route
              path="/admin/rewards"
              element={<AdminRewardListPage />}
            ></Route>
            <Route
              path="/admin/free-message"
              element={<EditAdminFreeMessagePage />}
            ></Route>
            <Route
              path="/admin/add-rewards"
              element={<AddAdminRewardPage />}
            ></Route>
            <Route
              path="/admin/edit-rewards/:id"
              element={<EditAdminRewardPage />}
            ></Route>

            <Route
              path="/admin/add-suggested-questions"
              element={<AddAdminSuggestedQuestions />}
            ></Route>
            <Route
              path="/admin/edit-suggested-questions/:id"
              element={<EditAdminSuggestedQuestions />}
            ></Route>
            <Route
              path="/admin/view-suggested-questions/:id"
              element={<ViewAdminSuggestedQuestions />}
            ></Route>
            <Route
              path="/admin/suggested-questions"
              element={<ListAdminSuggestedQuestions />}
            ></Route>
            <Route
              path="/admin/email-submission"
              element={<ListAdminEmailSubmissionPage />}
            ></Route>

            {/* <Route
            path="/admin/edit-rewards/:id"
            element={<EditAdminLendersPage />}
          ></Route> */}
            {/* <Route
            path="/admin/view-rewards/:id"
            element={<ViewAdminLendersPage />}
          ></Route> */}
          </Routes>
        </Suspense>
      );
    case "user":
      return (
        <Suspense fallback={<div>Loading...</div>}>
          <Routes>
            {/* <Route path="/" element={<CountdownPage2 />}></Route> */}
            <Route
              path="/"
              element={
                <AgeCheckWrapper>
                  <UserHomePage />{" "}
                </AgeCheckWrapper>
              }
            ></Route>
            {/* <Route path="/" element={<UserHomePage />}></Route> */}

            <Route
              path="/user/order/:invoice_id"
              element={
                <AgeCheckWrapper>
                  <UserOrderPage />
                </AgeCheckWrapper>
              }
            ></Route>
            <Route
              exact
              path="/user/profile"
              element={
                <AgeCheckWrapper>
                  <UserProfilePage />
                </AgeCheckWrapper>
              }
            ></Route>
            <Route
              exact
              path="/user/buy"
              element={
                <AgeCheckWrapper>
                  <UserPlanPage />
                </AgeCheckWrapper>
              }
            ></Route>
            <Route
              exact
              path="/user/plans"
              element={
                <AgeCheckWrapper>
                  <ListUserPaymentsTablePage />
                </AgeCheckWrapper>
              }
            ></Route>

            <Route
              exact
              path="/user/chat"
              element={
                <AgeCheckWrapper>
                  <ChatComponent />
                </AgeCheckWrapper>
              }
            ></Route>

            <Route
              exact
              path="/user/aboutpage"
              element={
                <AgeCheckWrapper>
                  <AboutUsUserPage />
                </AgeCheckWrapper>
              }
            ></Route>
            <Route
              exact
              path="/user/collaboration"
              element={
                <AgeCheckWrapper>
                  <CollaborationPage />
                </AgeCheckWrapper>
              }
            ></Route>
            <Route
              exact
              path="/user/privacy"
              element={
                <AgeCheckWrapper>
                  <PrivacyPolicy />
                </AgeCheckWrapper>
              }
            ></Route>
            <Route
              exact
              path="/user/terms"
              element={
                <AgeCheckWrapper>
                  <UserTerms />
                </AgeCheckWrapper>
              }
            ></Route>
            <Route
              exact
              path="/user/disclaimer"
              element={
                <AgeCheckWrapper>
                  <Disclaimer />
                </AgeCheckWrapper>
              }
            ></Route>
            <Route exact path="/user/quiz" element={<Quiz />}></Route>
            <Route
              exact
              path="/user/termsofuse"
              element={
                <AgeCheckWrapper>
                  <TermsAndConditions />
                </AgeCheckWrapper>
              }
            ></Route>
            <Route
              exact
              path="/user/checkout/:id?/:name?/:amount?/:couponCode?/:amountWithCoupon?/:paymentIntent?/:clientSecret?/:newsletterBonus?"
              element={
                <AgeCheckWrapper>
                  <Payment />
                </AgeCheckWrapper>
              }
            ></Route>
            <Route
              exact
              path="/user/ads-video"
              element={
                <AgeCheckWrapper>
                  <AdsVid />
                </AgeCheckWrapper>
              }
            ></Route>
            <Route
              exact
              path="/user/checkout2/:id"
              element={
                <AgeCheckWrapper>
                  <CheckoutForm />
                </AgeCheckWrapper>
              }
            ></Route>
            <Route
              exact
              path="/user/report-template"
              element={
                <AgeCheckWrapper>
                  <ReportIssuesTemplate />
                </AgeCheckWrapper>
              }
            ></Route>
            <Route
              exact
              path="/user/thank-you"
              element={
                <AgeCheckWrapper>
                  <ThankYouPage />
                </AgeCheckWrapper>
              }
            ></Route>
          </Routes>
        </Suspense>
      );

    default:
      return (
        <Suspense fallback={<div>Loading...</div>}>
          <Routes>
            <Route
              path="/"
              element={
                <AgeCheckWrapper>
                  <UserHomePage />
                </AgeCheckWrapper>
              }
            ></Route>
            <Route
              exact
              path="/admin/login"
              element={<AdminLoginPage />}
            ></Route>
            <Route
              exact
              path="/admin/confirm-login"
              element={<AdminConfirmLoginPage />}
            ></Route>
            <Route
              exact
              path="/admin/forgot"
              element={<AdminForgotPage />}
            ></Route>
            <Route
              exact
              path="/admin/reset"
              element={<AdminResetPage />}
            ></Route>
            <Route exact path="/admin/sign-up" element={<SignUpPage />}></Route>

            {/* user routes */}
            <Route
              path="/login/oauth"
              element={<SocialLoginVerificationTemplate />}
            ></Route>
            <Route exact path="/user/login" element={<UserLoginPage />}></Route>
            <Route
              exact
              path="/user/forgot"
              element={<UserForgotPage />}
            ></Route>
            <Route exact path="/user/reset" element={<UserResetPage />}></Route>
            <Route
              exact
              path="/user/reset-cofirmation"
              element={<ResetConfirmationPage />}
            ></Route>

            <Route
              exact
              path="/user/aboutpage"
              element={
                <AgeCheckWrapper>
                  <AboutUsUserPage />
                </AgeCheckWrapper>
              }
            ></Route>
            {/* <Route
            exact
            path="/user/collaboration"
            element={<CollaborationPage />}
          ></Route> */}
            <Route
              exact
              path="/user/disclaimer"
              element={
                <AgeCheckWrapper>
                  <Disclaimer />
                </AgeCheckWrapper>
              }
            ></Route>
            <Route
              exact
              path="/user/privacy"
              element={
                <AgeCheckWrapper>
                  <PrivacyPolicy />{" "}
                </AgeCheckWrapper>
              }
            ></Route>
            <Route exact path="/user/quiz" element={<Quiz />}></Route>
            <Route
              exact
              path="/user/termsofuse"
              element={
                <AgeCheckWrapper>
                  <TermsAndConditions />{" "}
                </AgeCheckWrapper>
              }
            ></Route>
            <Route
              exact
              path="/user/sign-up"
              element={<UserSignUpPage />}
            ></Route>
            <Route
              exact
              path="/user/confirm-login"
              element={<UserConfirmLoginPage />}
            ></Route>
            <Route
              exact
              path="/user/confirm-signup"
              element={<UserConfirmSignUpPage />}
            ></Route>
            <Route
              exact
              path="/user/redirect-signup-page"
              element={<UserRedirectSignUpPage />}
            ></Route>
            <Route
              exact
              path="/user/verify-email"
              element={<UserVerifyEmailPage />}
            ></Route>
            <Route
              exact
              path="/user/collaboration"
              element={<CollaborationPage />}
            ></Route>

            {/* <Route exact path="/" element={<CountdownPage2 />}></Route> */}
            {/* <Route exact path="/countdown" element={<CountdownPage2 />}></Route> */}

            <Route exact path="/user/test" element={<Test />}></Route>
            <Route exact path="/user/event" element={<EventPlan />}></Route>
            <Route
              exact
              path="/user/checkout/:id"
              element={<Checkout />}
            ></Route>
            <Route
              exact
              path="/.well-known/apple-developer-merchantid-domain-association"
              element={<AppleDoc />}
            ></Route>

            {/* <Route
            path="/admin/add-rewards"
            element={<AddAdminRewardPage />}
          ></Route> */}
            {/* TEMP TEST FOR ROUTES */}
            {/* <Route path="/user/dashboard" element={<UserDashboardPage />}></Route> */}
            {/* <Route
            exact
            path="/user/profile"
            element={<UserProfilePage />}
          ></Route> */}
            {/* <Route exact path="/user/plans" element={<UserPlanPage />}></Route> */}

            {/* <Route
            exact
            path="/user/aboutpage"
            element={<AboutUsUserPage />}
          ></Route> */}

            {/* TEMP TEST FOR ROUTES END */}

            <Route path="*" exact element={<NotFoundPage />}></Route>
            <Route path="/magic-login" element={<UserMagicLoginPage />}></Route>
            <Route
              path="/magic-login/verify"
              element={<MagicLoginVerifyPage />}
            ></Route>
          </Routes>
        </Suspense>
      );
  }
}

function Main() {
  const { state } = React.useContext(AuthContext);

  return (
    <div className="h-full">
      <div className="flex w-full">
        {!state.isAuthenticated ? <PublicHeader /> : renderHeader(state.role)}
        <div className="w-full">
          {state.isAuthenticated && state.role === "admin" ? (
            <TopHeader />
          ) : null}
          <div
            className={`page-wrapper w-full ${
              state.isAuthenticated && state.role === "admin" ? "p-5" : ""
            }`}
          >
            {!state.isAuthenticated
              ? renderRoutes("none")
              : renderRoutes(state.role)}
          </div>
        </div>
      </div>
      <SessionExpiredModal />
      <SnackBar />
    </div>
  );
}

export default Main;
