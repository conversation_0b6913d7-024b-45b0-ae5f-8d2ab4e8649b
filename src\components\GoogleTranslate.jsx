import React from 'react'

const GoogleTranslate = async(text,sourcelang, targetLanguage) => {

const API_URL = 'https://translation.googleapis.com/language/translate/v2';

if (text && sourcelang === 'en' && targetLanguage === 'en') {
  // If the source and target languages are both English, no translation needed
  return text
}


const response = await fetch(`https://translation.googleapis.com/language/translate/v2?key=${import.meta.env.VITE_GOOGLE_TRANSLATE_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          q: text,
          source: sourcelang,
          target: targetLanguage,
          format: "text",
        }),
      });
   
  const result = await response.json()
  return result?.data?.translations[0].translatedText
  // return response.data.data.translations[0].translatedText;

}

export default GoogleTranslate