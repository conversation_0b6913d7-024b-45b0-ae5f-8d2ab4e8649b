import React from "react";
import { <PERSON><PERSON>, <PERSON>, But<PERSON>, Backdrop } from "@mui/material";
import { styled } from "@mui/system";

const CustomModal = ({ children, isMenuClicked, handleClose }) => {
  const CustomBackdrop = styled(Backdrop)(({ theme }) => ({
    zIndex: theme?.zIndex?.modal - 1,
    top: "100px", // Adjust this value as needed
  }));

  return (
    <Modal
      open={isMenuClicked}
      onClose={handleClose}
      style={{
         marginTop: "50px",
         display:"flex",
         flexDirection:"row",
         justifyContent:"end",
         
        
        }}
    //   BackdropComponent={CustomBackdrop}
      // BackdropComponent={CustomBackdrop}
    >
      {children}
    </Modal>
  );
};

export default CustomModal;
