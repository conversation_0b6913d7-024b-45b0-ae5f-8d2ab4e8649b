import React from 'react'
import SkeletonLoading from './SkeletonLoading'
import { useData } from 'Src/dataContext';

const ServiceSection = () => {
    const { cms } = useData();
  return (
    <section className=" flex flex-col justify-between items-center max-w-[1280px] w-full ">
    <div className=" flex flex-col justify-between items-center pt-8 max-w-[1280px]  w-[90vw]">
      <h2 className=" md:text-5xl text-[32px] font-semibold py-8 text-center">
        {!cms && <SkeletonLoading />}
        {
          cms?.find(
            (item) => item.content_key === "Service_Section_headings"
          )?.content_value
        }
      </h2>
      <p className="text-companyBlack text-opacity-60 text-[18px]  mb-[40px] md:mb-[120px] text-center max-w-[798px]">
        {!cms && <SkeletonLoading counter={2} />}
        {
          cms?.find(
            (item) => item.content_key === "Service_section_sub_headings"
          )?.content_value
        }
      </p>

      {/* contents */}
      <div className=" grid md:grid-cols-2 gap-14 items-center w-full ">
        <div className=" order-first">
          {!cms && <SkeletonLoading padding="py-20" />}
          <img
            src={
              cms?.find(
                (item) => item.content_key === "Service_Section_image_1"
              )?.content_value
            }
            alt=""
          />
        </div>

        <div className="flex flex-col justify-center items-center text-companyBlack md:text-left text-center md:w-[30vw] gap-8 order-2 max-w-[522px]">
          <h2 className=" w-full md:text-left font-medium text-center md:text-[36px] text-2xl ">
            {!cms && <SkeletonLoading />}
            {
              cms?.find(
                (item) =>
                  item.content_key === "Service_Section_service_1_heading"
              )?.content_value
            }
          </h2>
          <p className="md:text-lg text-base text-companyBlack text-opacity-60">
            {!cms && <SkeletonLoading counter={4} />}
            {
              cms?.find(
                (item) =>
                  item.content_key === "Service_Section_service_1_text"
              )?.content_value
            }
          </p>
        </div>

        <div className="flex flex-col justify-center items-center md:text-left text-center md:w-[30vw] md:order-3 order-4 max-w-[522px] gap-8">
          <h2 className=" w-full md:text-left text-center font-medium md:text-[36px] text-2xl ">
            {!cms && <SkeletonLoading />}
            {
              cms?.find(
                (item) =>
                  item.content_key === "Service_Section_service_2_heading"
              )?.content_value
            }
          </h2>
          <p className="md:text-lg text-base text-companyBlack text-opacity-60">
            {!cms && <SkeletonLoading counter={4} />}
            {
              cms?.find(
                (item) =>
                  item.content_key === "Service_Section_service_2_text"
              )?.content_value
            }
          </p>
        </div>

        <div className="md:order-4 order-3">
          {!cms && <SkeletonLoading padding="py-20" />}
          <img
            src={
              cms?.find(
                (item) => item.content_key === "Service_Section_image_2"
              )?.content_value
            }
            alt=""
          />
        </div>
        <div className=" order-5">
          {!cms && <SkeletonLoading padding="py-20" />}
          <img
            src={
              cms?.find(
                (item) => item.content_key === "Service_Section_image_3"
              )?.content_value
            }
            alt=""
          />
          {/* <img src={three} alt="" /> */}
        </div>
        <div className="flex flex-col justify-center items-center md:w-[30vw] max-w-[522px] order-last gap-8">
          <h2 className=" w-full md:text-left text-center font-medium md:text-[36px] text-2xl ">
            {!cms && <SkeletonLoading />}
            {
              cms?.find(
                (item) =>
                  item.content_key === "Service_Section_service_3_heading"
              )?.content_value
            }
          </h2>
          <p className=" md:text-left text-center md:text-lg text-base text-companyBlack text-opacity-60">
            {!cms && <SkeletonLoading counter={4} />}
            {
              cms?.find(
                (item) =>
                  item.content_key === "Service_Section_service_3_text"
              )?.content_value
            }
          </p>
        </div>
      </div>
    </div>
  </section>
  )
}

export default ServiceSection
