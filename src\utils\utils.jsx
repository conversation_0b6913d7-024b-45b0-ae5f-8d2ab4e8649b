export function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

export const getNonNullValue = (value) => {
  if (value != "") {
    return value;
  } else {
    return undefined;
  }
};

export function filterEmptyFields(object) {
  Object.keys(object).forEach((key) => {
    if (empty(object[key])) {
      delete object[key];
    }
  });
  return object;
}

export function empty(value) {
  return (
    value === "" ||
    value === null ||
    value === undefined ||
    value === "undefined"
  );
}

export const isImage = (file) => {
  const validImageTypes = ["image/gif", "image/jpeg", "image/jpg", "image/png"];
  if (validImageTypes.includes(file.file.type)) return true;
  return false;
};

export const isVideo = (file) => {
  const validVideoTypes = ["video/webm", "video/mp4"];
  if (validVideoTypes.includes(file.file.type)) return true;
  return false;
};

export const isPdf = (file) => {
  const validVideoTypes = ["application/pdf"];
  if (validVideoTypes.includes(file.file.type)) return true;
  return false;
};

export const randomString = (length) => {
  let result = "";
  let characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
};

export const capitalize = (string) => {
  const removedSpecialCharacters = string.replace(/[^a-zA-Z0-9]/g, " ");

  const splitWords = removedSpecialCharacters.split(" ");
  const capitalized = splitWords.map(
    (dt) => `${dt[0].toUpperCase()}${dt.substring(1)}`
  );

  return capitalized.join(" ");
};

export const dateHandle = (date) => {
  const newDate = date
    ? new Date(date).toISOString().split("T")[0]
    : new Date().toISOString().split("T")[0];
  return newDate;
};

export const ghrapDate = (date) => {
  const newDate = new Date(date);
  var mS = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "June",
    "July",
    "Aug",
    "Sept",
    "Oct",
    "Nov",
    "Dec",
  ];
  // console.log( newDate.getDate(), mS[ newDate.getMonth() ] );

  return `${newDate.getDate()} ${mS[newDate.getMonth()]}`;
};

export const getCmsValue = (cms, key) => {
  return cms?.find((item) => item.content_key === `${key}`)?.content_value;
};

export function convertSportsToArray(string) {
  // Split the string by commas and trim any extra whitespace
  return string.split(",").map((splitString) => splitString.trim());
}

// export function formatPrediction(text) {
//   // Define keywords that will act as split points
//   const keywords = [
//     "Predicted Final Score:",
//     "Win Probability:",
//     "Justification:",
//     "Recent Form:",
//     "Historical Data:",
//     "Injury Impact:"
//   ];

//   // Loop through the keywords and ensure the text is split by them, adding a newline before each one
//   keywords.forEach(keyword => {
//     const regex = new RegExp(keyword, "g");
//     text = text.replace(regex, `\n${keyword}\n`);
//   });

//   // Trim excess white space and return the formatted text
//   return text.trim();
// }

//   export function formatPrediction(text ) {
//     const keywords = [
//       "Predicted Final Score:",
//       "Win Probability:",
//       "Justification:",
//       "Recent Form:",
//       "Historical Data:",
//       "Injury Impact:"
//     ];
//     const regex = new RegExp(`(${keywords.join("|")})`, "g");
//     return text.split(":").filter(Boolean).join("\n");

// }
export function formatPrediction(text) {
  const keywords = [
    "Predicted Final Score:",
    "Win Probability:",
    "Justification:",
    "Recent Form:",
    "Historical Data:",
    "Injury Impact:",
  ];
  // console.log(text);
  const regex = new RegExp(`(${keywords.join("|")})`, "g");
  let t1 = text.split("Predicted Final Score:");
  if(t1?.length<2){
    return text
  }
  let t2 = t1[1]?.split("Win Probability:");
  let t3 = t2[1]?.split("Justification:");
  let t4 = t3[1]?.split("Recent Form:");

  // let t5= t4[1].split("Historical Data:")
  // let t6= t5[1].split("Injury Impact:")

  const finalText = [
    "Predicted Final Score:",
    t1[0],
    "Win Probability:",
    t2[0],
    "Justification:",
    t3[0],
    "Recent Form:",
    t4[0],
    t4[1],
    // ...t5,
    // ...t6,
  ];
  return finalText.join("\n");
  // return text.split(":").filter(Boolean).join("\n");
}

// export function breakByNewline(inputString) {
//   return inputString.split('\n').join('<br>');
// }

export function breakByNewline(inputString) {
  return inputString.split('\n').join('\n');
}

export const sportList = [
  "football",

  "tennis",
  "darts",
  "american football",
  "icehockey",
  "basketball",
  "baseball",
  "boxing",
  "UFC top 25",
  "rugby",
]