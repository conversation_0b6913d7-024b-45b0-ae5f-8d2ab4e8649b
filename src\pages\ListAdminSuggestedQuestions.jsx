import React from "react";
import { AuthContext, tokenExpireError } from "../authContext";
import MkdSDK from "../utils/MkdSDK";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { GlobalContext } from "../globalContext";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { getNonNullValue } from "../utils/utils";
import PaginationBar from "../components/PaginationBar";
import AddButton from "../components/AddButton";
import ExportButton from "../components/ExportButton";
import SelectSport from "Components/SelectSport";
import SuggestedSelectSport from "Components/SuggestedSelectSport ";

let sdk = new MkdSDK();

const columns = [
  {
    header: "Action",
    accessor: "",
  },

  // {
  //     header: 'Update At',
  //     accessor: 'update_at',
  //     isSorted: false,
  //     isSortedDesc: false,
  //     mappingExist : false,
  //     mappings: {  }
  // },
  {
    header: "question",
    accessor: "question",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "sport",
    accessor: "sport",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Priority",
    accessor: "priority",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Priority Expiry",
    accessor: "priority_expiry",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
];

const ListAdminSuggestedQuestions = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  const [query, setQuery] = React.useState("");
  const [currentTableData, setCurrentTableData] = React.useState([]);
  const [currentTableManualData, setCurrentTableManualData] = React.useState(
    []
  );
  const [pageSize, setPageSize] = React.useState(10);
  const [pageCount, setPageCount] = React.useState(0);
  const [dataTotal, setDataTotal] = React.useState(0);
  const [sport, setSport] = React.useState();
  const [manualSport, setManualSport] = React.useState();
  const [currentPage, setPage] = React.useState(0);
  const [canPreviousPage, setCanPreviousPage] = React.useState(false);
  const [canNextPage, setCanNextPage] = React.useState(false);
  const navigate = useNavigate();

  const schema = yup.object({
    update_at: yup.string(),
    suggested_questions: yup.string(),
  });
  const {
    register,
    handleSubmit,
    setError,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  function onSort(columnIndex) {
    if (columns[columnIndex].isSorted) {
      columns[columnIndex].isSortedDesc = !columns[columnIndex].isSortedDesc;
    } else {
      columns.map((i) => (i.isSorted = false));
      columns.map((i) => (i.isSortedDesc = false));
      columns[columnIndex].isSorted = true;
    }

    (async function () {
      await getData(0, pageSize);
    })();
  }

  function updatePageSize(limit) {
    (async function () {
      setPageSize(limit);
      await getData(0, limit);
    })();
  }

  function previousPage() {
    (async function () {
      await getData(currentPage - 1 > 0 ? currentPage - 1 : 0, pageSize);
    })();
  }

  function nextPage() {
    (async function () {
      await getData(
        currentPage + 1 <= pageCount ? currentPage + 1 : 0,
        pageSize
      );
    })();
  }

  async function getData(pageNum, limitNum, currentTableData) {
    try {
      sdk.setTable("suggested_question");
      let sortField = columns.filter((col) => col.isSorted);
      const result = await sdk.callRestAPI(
        {
          payload: { ...currentTableData },
          page: pageNum,
          limit: limitNum,
          sortId: sortField.length ? sortField[0].accessor : "",
          direction: sortField.length
            ? sortField[0].isSortedDesc
              ? "DESC"
              : "ASC"
            : "",
        },
        "PAGINATE"
      );

      const { list, total, limit, num_pages, page } = result;

      // console.log("list", list);

      setCurrentTableData(list);
      setPageSize(limit);
      setPageCount(num_pages);
      setPage(page);
      setDataTotal(total);
      setCanPreviousPage(page > 1);
      setCanNextPage(page + 1 <= num_pages);
    } catch (error) {
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    }
  }
  async function getManualData(pageNum, limitNum, currentTableData) {
    try {
      sdk.setTable("suggested_question");
      let sortField = columns.filter((col) => col.isSorted);
      const result = await sdk.callRestAPI(
        {
          payload: { ...currentTableData },
          page: pageNum,
          limit: limitNum,
          sortId: sortField.length ? sortField[0].accessor : "",
          direction: sortField.length
            ? sortField[0].isSortedDesc
              ? "DESC"
              : "ASC"
            : "",
        },
        "PAGINATE"
      );

      const { list, total, limit, num_pages, page } = result;

      // console.log("list", list);

      setCurrentTableManualData(list);
      setPageSize(limit);
      setPageCount(num_pages);
      setPage(page);
      setDataTotal(total);
      setCanPreviousPage(page > 1);
      setCanNextPage(page + 1 <= num_pages);
    } catch (error) {
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    }
  }

  const deleteItem = async (id) => {
    try {
      sdk.setTable("suggested_question");
      const result = await sdk.callRestAPI({ id }, "DELETE");
      setCurrentTableData((list) =>
        list.filter((x) => Number(x.id) !== Number(id))
      );
    } catch (err) {
      throw new Error(err);
    }
  };
  // const handleSendToUser = async (id) => {
  //   try {
  //     const result = await sdk.callRawAPI(`/v3/api/custom/kaizenwin/chat/match-schedule`,{ id }, "POST");

  //   } catch (error) {
  //     console.log("ERROR", error);
  //     tokenExpireError(dispatch, error.message);
  //   }
  // };

  const exportTable = async (id) => {
    try {
      sdk.setTable("payment_history");
      const result = await sdk.exportCSV();
    } catch (err) {
      throw new Error(err);
    }
  };

  const resetForm = async () => {
    reset();
    await getData(0, pageSize);
  };

  const onSubmit = (_data) => {
    let update_at = getNonNullValue(_data.update_at);
    let question = getNonNullValue(_data.question);
    let filter = {
      update_at: update_at,
      question: question,
    };
    getData(1, pageSize, filter);
  };

  React.useEffect(() => {
    if (sport) {
      let filter = {
        sport: sport,
      };
      getData(1, pageSize, filter);
    } else {
      getData(1, pageSize);
    }
  }, [sport]);
  React.useEffect(() => {
    if (manualSport) {
      let filter = {
        sport: manualSport,
      };
      getManualData(1, pageSize, filter);
    }
  }, [manualSport]);

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "suggested-questions",
      },
    });

    (async function () {
      let manualFilter = {
        type: "manual",
      };
      let autoFilter = {
        type: "auto",
      };
      await getData(1, pageSize, autoFilter);
      await getManualData(1, pageSize, manualFilter);
    })();
  }, []);

  return (
    <>
      <form
        className="p-5 bg-white shadow rounded mb-10"
        onSubmit={handleSubmit(onSubmit)}
      >
        <h4 className="text-2xl font-medium">Suggested Questions Search</h4>
        <div className="filter-form-holder mt-10 flex flex-wrap">
          <div className="mb-4  w-full md:w-1/2 pr-2 pl-2 ">
            <label
              className="block text-gray-700 text-sm font-bold mb-2"
              htmlFor="update_at"
            >
              Suggested Questions
            </label>
            <input
              type="text"
              placeholder="Suggested Questions"
              {...register("suggested_questions")}
              className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
                errors.link?.message ? "border-red-500" : ""
              }`}
            />
            <p className="text-red-500 text-xs italic">
              {errors.link?.message}
            </p>
          </div>
        </div>
        <button
          type="submit"
          className=" inline ml-2 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Search
        </button>

        <button
          onClick={() => {
            resetForm();
          }}
          type="button"
          className=" inline ml-2 bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Clear
        </button>
      </form>

      <div className="overflow-x-auto  p-5 bg-white shadow rounded">
        <div className="mb-3 text-center justify-between w-full flex  ">
          <h4 className="text-2xl font-medium">Ai Suggested Questions</h4>

          <SuggestedSelectSport
            setSport={setSport}
            search={true}
            className={"px-4 !bg-white !text-black w-full max-w-[200px]"}
          />
        </div>
        <div className="shadow overflow-x-auto border-b border-gray-200 ">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {columns.map((column, i) => (
                  <th
                    key={i}
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    onClick={() => onSort(i)}
                  >
                    {column.header}
                    <span>
                      {column.isSorted
                        ? column.isSortedDesc
                          ? " ▼"
                          : " ▲"
                        : ""}
                    </span>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentTableData.map((row, i) => {
                return (
                  <tr key={i}>
                    {columns.map((cell, index) => {
                      if (cell.accessor.indexOf("image") > -1) {
                        return (
                          <td
                            key={index}
                            className="px-6 py-4 whitespace-nowrap"
                          >
                            <img
                              src={row[cell.accessor]}
                              className="h-[100px] w-[150px]"
                            />
                          </td>
                        );
                      }
                      if (
                        cell.accessor.indexOf("pdf") > -1 ||
                        cell.accessor.indexOf("doc") > -1 ||
                        cell.accessor.indexOf("file") > -1 ||
                        cell.accessor.indexOf("video") > -1
                      ) {
                        return (
                          <td
                            key={index}
                            className="px-6 py-4 whitespace-nowrap"
                          >
                            <a
                              className="text-blue-500"
                              target="_blank"
                              href={row[cell.accessor]}
                            >
                              {" "}
                              View
                            </a>
                          </td>
                        );
                      }
                      if (cell.accessor == "") {
                        return (
                          <td
                            key={index}
                            className="px-6 py-4 whitespace-nowrap"
                          >
                            <button
                              className="text-xs bg-green-600 px-1 text-white border-2 rounded-md hover:bg-white hover:text-green-600 hover:border-green-600"
                              onClick={() => {
                                navigate(
                                  "/admin/edit-suggested-questions/" + row.id,
                                  {
                                    state: row,
                                  }
                                );
                              }}
                            >
                              {" "}
                              Change priority
                            </button>
                            <button
                              className="text-xs px-1 text-blue-500"
                              onClick={() => {
                                navigate(
                                  "/admin/view-suggested-questions/" + row.id,
                                  {
                                    state: row,
                                  }
                                );
                              }}
                            >
                              {" "}
                              View
                            </button>
                            <button
                              className="text-xs px-1 text-red-500"
                              onClick={() => deleteItem(row.id)}
                            >
                              {" "}
                              Delete
                            </button>
                          </td>
                        );
                      }
                      if (cell.accessor == "question") {
                        return (
                          <td
                            key={index}
                            className="px-6 py-4 whitespace-nowrap "
                          >
                            <span
                              // onClick={handleSendToUser(row.id)}

                              className=""
                            >
                              {" "}
                              {row[cell.accessor]}
                            </span>
                          </td>
                        );
                      }
                      if (cell.mappingExist) {
                        return (
                          <td
                            key={index}
                            className="px-6 py-4 whitespace-nowrap"
                          >
                            {cell.mappings[row[cell.accessor]]}
                          </td>
                        );
                      }
                      return (
                        <td key={index} className="px-6 py-4 whitespace-nowrap">
                          {row[cell.accessor]}
                        </td>
                      );
                    })}
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
      <PaginationBar
        currentPage={currentPage}
        pageCount={pageCount}
        pageSize={pageSize}
        canPreviousPage={canPreviousPage}
        canNextPage={canNextPage}
        updatePageSize={updatePageSize}
        previousPage={previousPage}
        nextPage={nextPage}
      />

      <div className="mt-10">
        <div className="overflow-x-auto  p-5 bg-white shadow rounded">
          <div className="mb-3 text-center justify-between w-full flex  ">
            <h4 className="text-2xl font-medium">Manual Suggested Questions</h4>
            <div className="flex items-end gap-2 w-full max-w-[250px] ">
              <div className="flex  gap-4 ">
                <AddButton link={"/admin/add-suggested-questions"} />
                {/* <ExportButton onClick={exportTable} className="mx-1" /> */}
              </div>
              <SuggestedSelectSport
                search={true}
                setSport={setManualSport}
                className={"px-4 !bg-white !text-black w-full max-w-[250px] "}
              />
            </div>
          </div>

          <div className="shadow overflow-x-auto border-b border-gray-200 ">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {columns.map((column, i) => (
                    <th
                      key={i}
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      onClick={() => onSort(i)}
                    >
                      {column.header}
                      <span>
                        {column.isSorted
                          ? column.isSortedDesc
                            ? " ▼"
                            : " ▲"
                          : ""}
                      </span>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {currentTableManualData.map((row, i) => {
                  return (
                    <tr key={i}>
                      {columns.map((cell, index) => {
                        if (cell.accessor.indexOf("image") > -1) {
                          return (
                            <td
                              key={index}
                              className="px-6 py-4 whitespace-nowrap"
                            >
                              <img
                                src={row[cell.accessor]}
                                className="h-[100px] w-[150px]"
                              />
                            </td>
                          );
                        }
                        if (
                          cell.accessor.indexOf("pdf") > -1 ||
                          cell.accessor.indexOf("doc") > -1 ||
                          cell.accessor.indexOf("file") > -1 ||
                          cell.accessor.indexOf("video") > -1
                        ) {
                          return (
                            <td
                              key={index}
                              className="px-6 py-4 whitespace-nowrap"
                            >
                              <a
                                className="text-blue-500"
                                target="_blank"
                                href={row[cell.accessor]}
                              >
                                {" "}
                                View
                              </a>
                            </td>
                          );
                        }
                        if (cell.accessor == "") {
                          return (
                            <td
                              key={index}
                              className="px-6 py-4 whitespace-nowrap"
                            >
                              <button
                                className="text-xs bg-green-600 px-1 text-white border-2 rounded-md hover:bg-white hover:text-green-600 hover:border-green-600"
                                onClick={() => {
                                  navigate(
                                    "/admin/edit-suggested-questions/" + row.id,
                                    {
                                      state: row,
                                    }
                                  );
                                }}
                              >
                                {" "}
                                Change priority
                              </button>
                              <button
                                className="text-xs px-1 text-blue-500"
                                onClick={() => {
                                  navigate(
                                    "/admin/view-suggested-questions/" + row.id,
                                    {
                                      state: row,
                                    }
                                  );
                                }}
                              >
                                {" "}
                                View
                              </button>
                              <button
                                className="text-xs px-1 text-red-500"
                                onClick={() => deleteItem(row.id)}
                              >
                                {" "}
                                Delete
                              </button>
                            </td>
                          );
                        }
                        if (cell.accessor == "question") {
                          return (
                            <td
                              key={index}
                              className="px-6 py-4 whitespace-nowrap "
                            >
                              <span
                                // onClick={handleSendToUser(row.id)}

                                className=""
                              >
                                {" "}
                                {row[cell.accessor]}
                              </span>
                            </td>
                          );
                        }
                        if (cell.mappingExist) {
                          return (
                            <td
                              key={index}
                              className="px-6 py-4 whitespace-nowrap"
                            >
                              {cell.mappings[row[cell.accessor]]}
                            </td>
                          );
                        }
                        return (
                          <td
                            key={index}
                            className="px-6 py-4 whitespace-nowrap"
                          >
                            {row[cell.accessor]}
                          </td>
                        );
                      })}
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
        <PaginationBar
          currentPage={currentPage}
          pageCount={pageCount}
          pageSize={pageSize}
          canPreviousPage={canPreviousPage}
          canNextPage={canNextPage}
          updatePageSize={updatePageSize}
          previousPage={previousPage}
          nextPage={nextPage}
        />
      </div>
    </>
  );
};

export default ListAdminSuggestedQuestions;
