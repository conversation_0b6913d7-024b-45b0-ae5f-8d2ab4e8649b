import{j as F}from"./@mui/base-0e613ae5.js";import{r as m}from"./vendor-b0222800.js";import{c as B}from"./redux-7163047e.js";const W=m.createContext({dragDropManager:void 0});function s(t,e,...r){if(X()&&e===void 0)throw new Error("invariant requires an error message argument");if(!t){let n;if(e===void 0)n=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{let i=0;n=new Error(e.replace(/%s/g,function(){return r[i++]})),n.name="Invariant Violation"}throw n.framesToPop=1,n}}function X(){return typeof process<"u"&&process.env.NODE_ENV==="production"}function Q(t,e,r){return e.split(".").reduce((n,i)=>n&&n[i]?n[i]:r||null,t)}function Y(t,e){return t.filter(r=>r!==e)}function U(t){return typeof t=="object"}function z(t,e){const r=new Map,n=o=>{r.set(o,r.has(o)?r.get(o)+1:1)};t.forEach(n),e.forEach(n);const i=[];return r.forEach((o,a)=>{o===1&&i.push(a)}),i}function J(t,e){return t.filter(r=>e.indexOf(r)>-1)}const v="dnd-core/INIT_COORDS",h="dnd-core/BEGIN_DRAG",C="dnd-core/PUBLISH_DRAG_SOURCE",y="dnd-core/HOVER",O="dnd-core/DROP",S="dnd-core/END_DRAG";function P(t,e){return{type:v,payload:{sourceClientOffset:e||null,clientOffset:t||null}}}const Z={type:v,payload:{clientOffset:null,sourceClientOffset:null}};function K(t){return function(r=[],n={publishSource:!0}){const{publishSource:i=!0,clientOffset:o,getSourceClientOffset:a}=n,c=t.getMonitor(),f=t.getRegistry();t.dispatch(P(o)),ee(r,c,f);const l=ne(r,c);if(l==null){t.dispatch(Z);return}let D=null;if(o){if(!a)throw new Error("getSourceClientOffset must be defined");te(a),D=a(l)}t.dispatch(P(o,D));const T=f.getSource(l).beginDrag(c,l);if(T==null)return;re(T),f.pinSource(l);const V=f.getSourceType(l);return{type:h,payload:{itemType:V,item:T,sourceId:l,clientOffset:o||null,sourceClientOffset:D||null,isSourcePublic:!!i}}}}function ee(t,e,r){s(!e.isDragging(),"Cannot call beginDrag while dragging."),t.forEach(function(n){s(r.getSource(n),"Expected sourceIds to be registered.")})}function te(t){s(typeof t=="function","When clientOffset is provided, getSourceClientOffset must be a function.")}function re(t){s(U(t),"Item must be an object.")}function ne(t,e){let r=null;for(let n=t.length-1;n>=0;n--)if(e.canDragSource(t[n])){r=t[n];break}return r}function ie(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function oe(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{},n=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(i){return Object.getOwnPropertyDescriptor(r,i).enumerable}))),n.forEach(function(i){ie(t,i,r[i])})}return t}function se(t){return function(r={}){const n=t.getMonitor(),i=t.getRegistry();ae(n),le(n).forEach((a,c)=>{const f=ce(a,c,i,n),l={type:O,payload:{dropResult:oe({},r,f)}};t.dispatch(l)})}}function ae(t){s(t.isDragging(),"Cannot call drop while not dragging."),s(!t.didDrop(),"Cannot call drop twice during one drag operation.")}function ce(t,e,r,n){const i=r.getTarget(t);let o=i?i.drop(n,t):void 0;return ue(o),typeof o>"u"&&(o=e===0?{}:n.getDropResult()),o}function ue(t){s(typeof t>"u"||U(t),"Drop result must either be an object or undefined.")}function le(t){const e=t.getTargetIds().filter(t.canDropOnTarget,t);return e.reverse(),e}function fe(t){return function(){const r=t.getMonitor(),n=t.getRegistry();de(r);const i=r.getSourceId();return i!=null&&(n.getSource(i,!0).endDrag(r,i),n.unpinSource()),{type:S}}}function de(t){s(t.isDragging(),"Cannot call endDrag while not dragging.")}function E(t,e){return e===null?t===null:Array.isArray(t)?t.some(r=>r===e):t===e}function ge(t){return function(r,{clientOffset:n}={}){pe(r);const i=r.slice(0),o=t.getMonitor(),a=t.getRegistry(),c=o.getItemType();return ye(i,a,c),he(i,o,a),Oe(i,o,a),{type:y,payload:{targetIds:i,clientOffset:n||null}}}}function pe(t){s(Array.isArray(t),"Expected targetIds to be an array.")}function he(t,e,r){s(e.isDragging(),"Cannot call hover while not dragging."),s(!e.didDrop(),"Cannot call hover after drop.");for(let n=0;n<t.length;n++){const i=t[n];s(t.lastIndexOf(i)===n,"Expected targetIds to be unique in the passed array.");const o=r.getTarget(i);s(o,"Expected targetIds to be registered.")}}function ye(t,e,r){for(let n=t.length-1;n>=0;n--){const i=t[n],o=e.getTargetType(i);E(o,r)||t.splice(n,1)}}function Oe(t,e,r){t.forEach(function(n){r.getTarget(n).hover(e,n)})}function Se(t){return function(){if(t.getMonitor().isDragging())return{type:C}}}function be(t){return{beginDrag:K(t),publishDragSource:Se(t),hover:ge(t),drop:se(t),endDrag:fe(t)}}class De{receiveBackend(e){this.backend=e}getMonitor(){return this.monitor}getBackend(){return this.backend}getRegistry(){return this.monitor.registry}getActions(){const e=this,{dispatch:r}=this.store;function n(o){return(...a)=>{const c=o.apply(e,a);typeof c<"u"&&r(c)}}const i=be(this);return Object.keys(i).reduce((o,a)=>{const c=i[a];return o[a]=n(c),o},{})}dispatch(e){this.store.dispatch(e)}constructor(e,r){this.isSetUp=!1,this.handleRefCountChange=()=>{const n=this.store.getState().refCount>0;this.backend&&(n&&!this.isSetUp?(this.backend.setup(),this.isSetUp=!0):!n&&this.isSetUp&&(this.backend.teardown(),this.isSetUp=!1))},this.store=e,this.monitor=r,e.subscribe(this.handleRefCountChange)}}function Te(t,e){return{x:t.x+e.x,y:t.y+e.y}}function q(t,e){return{x:t.x-e.x,y:t.y-e.y}}function me(t){const{clientOffset:e,initialClientOffset:r,initialSourceClientOffset:n}=t;return!e||!r||!n?null:q(Te(e,n),r)}function Ee(t){const{clientOffset:e,initialClientOffset:r}=t;return!e||!r?null:q(e,r)}const g=[],x=[];g.__IS_NONE__=!0;x.__IS_ALL__=!0;function Ie(t,e){return t===g?!1:t===x||typeof e>"u"?!0:J(e,t).length>0}class ve{subscribeToStateChange(e,r={}){const{handlerIds:n}=r;s(typeof e=="function","listener must be a function."),s(typeof n>"u"||Array.isArray(n),"handlerIds, when specified, must be an array of strings.");let i=this.store.getState().stateId;const o=()=>{const a=this.store.getState(),c=a.stateId;try{c===i||c===i+1&&!Ie(a.dirtyHandlerIds,n)||e()}finally{i=c}};return this.store.subscribe(o)}subscribeToOffsetChange(e){s(typeof e=="function","listener must be a function.");let r=this.store.getState().dragOffset;const n=()=>{const i=this.store.getState().dragOffset;i!==r&&(r=i,e())};return this.store.subscribe(n)}canDragSource(e){if(!e)return!1;const r=this.registry.getSource(e);return s(r,`Expected to find a valid source. sourceId=${e}`),this.isDragging()?!1:r.canDrag(this,e)}canDropOnTarget(e){if(!e)return!1;const r=this.registry.getTarget(e);if(s(r,`Expected to find a valid target. targetId=${e}`),!this.isDragging()||this.didDrop())return!1;const n=this.registry.getTargetType(e),i=this.getItemType();return E(n,i)&&r.canDrop(this,e)}isDragging(){return!!this.getItemType()}isDraggingSource(e){if(!e)return!1;const r=this.registry.getSource(e,!0);if(s(r,`Expected to find a valid source. sourceId=${e}`),!this.isDragging()||!this.isSourcePublic())return!1;const n=this.registry.getSourceType(e),i=this.getItemType();return n!==i?!1:r.isDragging(this,e)}isOverTarget(e,r={shallow:!1}){if(!e)return!1;const{shallow:n}=r;if(!this.isDragging())return!1;const i=this.registry.getTargetType(e),o=this.getItemType();if(o&&!E(i,o))return!1;const a=this.getTargetIds();if(!a.length)return!1;const c=a.indexOf(e);return n?c===a.length-1:c>-1}getItemType(){return this.store.getState().dragOperation.itemType}getItem(){return this.store.getState().dragOperation.item}getSourceId(){return this.store.getState().dragOperation.sourceId}getTargetIds(){return this.store.getState().dragOperation.targetIds}getDropResult(){return this.store.getState().dragOperation.dropResult}didDrop(){return this.store.getState().dragOperation.didDrop}isSourcePublic(){return!!this.store.getState().dragOperation.isSourcePublic}getInitialClientOffset(){return this.store.getState().dragOffset.initialClientOffset}getInitialSourceClientOffset(){return this.store.getState().dragOffset.initialSourceClientOffset}getClientOffset(){return this.store.getState().dragOffset.clientOffset}getSourceClientOffset(){return me(this.store.getState().dragOffset)}getDifferenceFromInitialOffset(){return Ee(this.store.getState().dragOffset)}constructor(e,r){this.store=e,this.registry=r}}const A=typeof global<"u"?global:self,G=A.MutationObserver||A.WebKitMutationObserver;function $(t){return function(){const r=setTimeout(i,0),n=setInterval(i,50);function i(){clearTimeout(r),clearInterval(n),t()}}}function Ce(t){let e=1;const r=new G(t),n=document.createTextNode("");return r.observe(n,{characterData:!0}),function(){e=-e,n.data=e}}const xe=typeof G=="function"?Ce:$;class Re{enqueueTask(e){const{queue:r,requestFlush:n}=this;r.length||(n(),this.flushing=!0),r[r.length]=e}constructor(){this.queue=[],this.pendingErrors=[],this.flushing=!1,this.index=0,this.capacity=1024,this.flush=()=>{const{queue:e}=this;for(;this.index<e.length;){const r=this.index;if(this.index++,e[r].call(),this.index>this.capacity){for(let n=0,i=e.length-this.index;n<i;n++)e[n]=e[n+this.index];e.length-=this.index,this.index=0}}e.length=0,this.index=0,this.flushing=!1},this.registerPendingError=e=>{this.pendingErrors.push(e),this.requestErrorThrow()},this.requestFlush=xe(this.flush),this.requestErrorThrow=$(()=>{if(this.pendingErrors.length)throw this.pendingErrors.shift()})}}class we{call(){try{this.task&&this.task()}catch(e){this.onError(e)}finally{this.task=null,this.release(this)}}constructor(e,r){this.onError=e,this.release=r,this.task=null}}class _e{create(e){const r=this.freeTasks,n=r.length?r.pop():new we(this.onError,i=>r[r.length]=i);return n.task=e,n}constructor(e){this.onError=e,this.freeTasks=[]}}const H=new Re,Pe=new _e(H.registerPendingError);function Ae(t){H.enqueueTask(Pe.create(t))}const R="dnd-core/ADD_SOURCE",w="dnd-core/ADD_TARGET",_="dnd-core/REMOVE_SOURCE",b="dnd-core/REMOVE_TARGET";function ke(t){return{type:R,payload:{sourceId:t}}}function Me(t){return{type:w,payload:{targetId:t}}}function je(t){return{type:_,payload:{sourceId:t}}}function Ne(t){return{type:b,payload:{targetId:t}}}function Ue(t){s(typeof t.canDrag=="function","Expected canDrag to be a function."),s(typeof t.beginDrag=="function","Expected beginDrag to be a function."),s(typeof t.endDrag=="function","Expected endDrag to be a function.")}function qe(t){s(typeof t.canDrop=="function","Expected canDrop to be a function."),s(typeof t.hover=="function","Expected hover to be a function."),s(typeof t.drop=="function","Expected beginDrag to be a function.")}function I(t,e){if(e&&Array.isArray(t)){t.forEach(r=>I(r,!1));return}s(typeof t=="string"||typeof t=="symbol",e?"Type can only be a string, a symbol, or an array of either.":"Type can only be a string or a symbol.")}var u;(function(t){t.SOURCE="SOURCE",t.TARGET="TARGET"})(u||(u={}));let Ge=0;function $e(){return Ge++}function He(t){const e=$e().toString();switch(t){case u.SOURCE:return`S${e}`;case u.TARGET:return`T${e}`;default:throw new Error(`Unknown Handler Role: ${t}`)}}function k(t){switch(t[0]){case"S":return u.SOURCE;case"T":return u.TARGET;default:throw new Error(`Cannot parse handler ID: ${t}`)}}function M(t,e){const r=t.entries();let n=!1;do{const{done:i,value:[,o]}=r.next();if(o===e)return!0;n=!!i}while(!n);return!1}class Le{addSource(e,r){I(e),Ue(r);const n=this.addHandler(u.SOURCE,e,r);return this.store.dispatch(ke(n)),n}addTarget(e,r){I(e,!0),qe(r);const n=this.addHandler(u.TARGET,e,r);return this.store.dispatch(Me(n)),n}containsHandler(e){return M(this.dragSources,e)||M(this.dropTargets,e)}getSource(e,r=!1){return s(this.isSourceId(e),"Expected a valid source ID."),r&&e===this.pinnedSourceId?this.pinnedSource:this.dragSources.get(e)}getTarget(e){return s(this.isTargetId(e),"Expected a valid target ID."),this.dropTargets.get(e)}getSourceType(e){return s(this.isSourceId(e),"Expected a valid source ID."),this.types.get(e)}getTargetType(e){return s(this.isTargetId(e),"Expected a valid target ID."),this.types.get(e)}isSourceId(e){return k(e)===u.SOURCE}isTargetId(e){return k(e)===u.TARGET}removeSource(e){s(this.getSource(e),"Expected an existing source."),this.store.dispatch(je(e)),Ae(()=>{this.dragSources.delete(e),this.types.delete(e)})}removeTarget(e){s(this.getTarget(e),"Expected an existing target."),this.store.dispatch(Ne(e)),this.dropTargets.delete(e),this.types.delete(e)}pinSource(e){const r=this.getSource(e);s(r,"Expected an existing source."),this.pinnedSourceId=e,this.pinnedSource=r}unpinSource(){s(this.pinnedSource,"No source is pinned at the time."),this.pinnedSourceId=null,this.pinnedSource=null}addHandler(e,r,n){const i=He(e);return this.types.set(i,r),e===u.SOURCE?this.dragSources.set(i,n):e===u.TARGET&&this.dropTargets.set(i,n),i}constructor(e){this.types=new Map,this.dragSources=new Map,this.dropTargets=new Map,this.pinnedSourceId=null,this.pinnedSource=null,this.store=e}}const Ve=(t,e)=>t===e;function Fe(t,e){return!t&&!e?!0:!t||!e?!1:t.x===e.x&&t.y===e.y}function Be(t,e,r=Ve){if(t.length!==e.length)return!1;for(let n=0;n<t.length;++n)if(!r(t[n],e[n]))return!1;return!0}function We(t=g,e){switch(e.type){case y:break;case R:case w:case b:case _:return g;case h:case C:case S:case O:default:return x}const{targetIds:r=[],prevTargetIds:n=[]}=e.payload,i=z(r,n);if(!(i.length>0||!Be(r,n)))return g;const a=n[n.length-1],c=r[r.length-1];return a!==c&&(a&&i.push(a),c&&i.push(c)),i}function Xe(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Qe(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{},n=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(i){return Object.getOwnPropertyDescriptor(r,i).enumerable}))),n.forEach(function(i){Xe(t,i,r[i])})}return t}const j={initialSourceClientOffset:null,initialClientOffset:null,clientOffset:null};function Ye(t=j,e){const{payload:r}=e;switch(e.type){case v:case h:return{initialSourceClientOffset:r.sourceClientOffset,initialClientOffset:r.clientOffset,clientOffset:r.clientOffset};case y:return Fe(t.clientOffset,r.clientOffset)?t:Qe({},t,{clientOffset:r.clientOffset});case S:case O:return j;default:return t}}function ze(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function d(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{},n=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(i){return Object.getOwnPropertyDescriptor(r,i).enumerable}))),n.forEach(function(i){ze(t,i,r[i])})}return t}const Je={itemType:null,item:null,sourceId:null,targetIds:[],dropResult:null,didDrop:!1,isSourcePublic:null};function Ze(t=Je,e){const{payload:r}=e;switch(e.type){case h:return d({},t,{itemType:r.itemType,item:r.item,sourceId:r.sourceId,isSourcePublic:r.isSourcePublic,dropResult:null,didDrop:!1});case C:return d({},t,{isSourcePublic:!0});case y:return d({},t,{targetIds:r.targetIds});case b:return t.targetIds.indexOf(r.targetId)===-1?t:d({},t,{targetIds:Y(t.targetIds,r.targetId)});case O:return d({},t,{dropResult:r.dropResult,didDrop:!0,targetIds:[]});case S:return d({},t,{itemType:null,item:null,sourceId:null,dropResult:null,didDrop:!1,isSourcePublic:null,targetIds:[]});default:return t}}function Ke(t=0,e){switch(e.type){case R:case w:return t+1;case _:case b:return t-1;default:return t}}function et(t=0){return t+1}function tt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function rt(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{},n=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(i){return Object.getOwnPropertyDescriptor(r,i).enumerable}))),n.forEach(function(i){tt(t,i,r[i])})}return t}function nt(t={},e){return{dirtyHandlerIds:We(t.dirtyHandlerIds,{type:e.type,payload:rt({},e.payload,{prevTargetIds:Q(t,"dragOperation.targetIds",[])})}),dragOffset:Ye(t.dragOffset,e),refCount:Ke(t.refCount,e),dragOperation:Ze(t.dragOperation,e),stateId:et(t.stateId)}}function it(t,e=void 0,r={},n=!1){const i=ot(n),o=new ve(i,new Le(i)),a=new De(i,o),c=t(a,e,r);return a.receiveBackend(c),a}function ot(t){const e=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION__;return B(nt,t&&e&&e({name:"dnd-core",instanceId:"dnd-core"}))}function st(t,e){if(t==null)return{};var r=at(t,e),n,i;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(i=0;i<o.length;i++)n=o[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function at(t,e){if(t==null)return{};var r={},n=Object.keys(t),i,o;for(o=0;o<n.length;o++)i=n[o],!(e.indexOf(i)>=0)&&(r[i]=t[i]);return r}let N=0;const p=Symbol.for("__REACT_DND_CONTEXT_INSTANCE__");var pt=m.memo(function(e){var{children:r}=e,n=st(e,["children"]);const[i,o]=ct(n);return m.useEffect(()=>{if(o){const a=L();return++N,()=>{--N===0&&(a[p]=null)}}},[]),F.jsx(W.Provider,{value:i,children:r})});function ct(t){if("manager"in t)return[{dragDropManager:t.manager},!1];const e=ut(t.backend,t.context,t.options,t.debugMode),r=!t.context;return[e,r]}function ut(t,e=L(),r,n){const i=e;return i[p]||(i[p]={dragDropManager:it(t,e,r,n)}),i[p]}function L(){return typeof global<"u"?global:window}export{pt as D};
