import React, { useState } from "react";
import { AuthContext } from "../authContext";
import { NavLink } from "react-router-dom";
import { GlobalContext } from "../globalContext";
import aboutimg from "../assets/images/aboutimg.png";
import { useNavigate } from "react-router-dom";
import Navbar from "./NavBar";
import FooterComp from "./FooterComp";
import SkeletonLoading from "./SkeletonLoading";
import MkdSDK from "Utils/MkdSDK";
let sdk = new MkdSDK();

const Disclaimer = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  const [cms, setCms] = useState();

  const navigate = useNavigate();

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "user",
      },
    });
  }, []);

  React.useEffect(() => {
    async function getallcms() {
      try {
        const result = await sdk.getallcms();
        // console.log("wre", result);
        if (result) {
          setCms(result?.list);
        }
      } catch (err) {
        console.log("Error:", err);
        tokenExpireError(dispatch, err.message);
      }
    }
    getallcms();
  }, []);

  let textLists;
  cms &&
    (textLists = JSON.parse(
      cms?.find((item) => item.content_key === "Disclaimer_texts")?.content_value
    ));

  return (
    <div className="relative ">
      <Navbar />
      <div className="h-[100vh] flex flex-col justify-between">
        <div className=" flex flex-col justify-center items-center md:mb-[127px] ">
          <div className=" flex flex-col justify-center items-center md:mb-[127px] max-w-[992px]">
            <h1 className="md:text-4xl text-2xl mb-6 font-semibold w-full md:text-left text-center font-manrope">
              {!cms && <SkeletonLoading />}
              {
                cms?.find((item) => item.content_key === "Disclaimer_header_texts")
                  ?.content_value
              }
            </h1>
            {!cms &&
              Array(9)
                .fill(0)
                .map((_, i) => (
                  <div key={i} className="mb-4">
                    <h2 className="text-lg font-semibold w-1/2">
                      <SkeletonLoading counter={1} />
                    </h2>
                    <p className="text-gray-400 text-sm font-normal">
                      <SkeletonLoading counter={2} />
                    </p>
                  </div>
                ))}

            {cms
              ? textLists.map((content, i) => (
                  <div key={i} className="md:text-base text-[15px] mb-4">
                    <h2 className=" md:text-[24px] text-[18px] py-2 font-normal">
                      {content.key}
                    </h2>
                    <p className="mb-2 text-base text-justify w-[70vw] max-w-[992px] text-[#999999]">
                      {content.value}
                    </p>
                  </div>
                ))
              : null}

           

          </div>
        </div>
        <FooterComp />
      </div>
    </div>
  );
};

export default Disclaimer;
