import React, { useEffect, useState } from "react";
import { AuthContext, tokenExpireError } from "../authContext";
import MkdSDK from "../utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { GlobalContext, showToast } from "../globalContext";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { getNonNullValue } from "../utils/utils";
import PaginationBar from "../components/PaginationBar";
import AddButton from "../components/AddButton";
import {
  useStripe,
  PaymentElement,
  useElements,
  Elements,
  ExpressCheckoutElement,
} from "@stripe/react-stripe-js";
// import { google } from "googleapis";
import { BetaAnalyticsDataClient } from "@google-analytics/data";
import { Bar, Line } from "react-chartjs-2";
import Line<PERSON>hart from "Components/LineChart";
import ProgressBar from "Components/ProgressBar";

let sdk = new MkdSDK();

const columns = [
  // {
  //   header: "Action",
  //   accessor: "",
  // },
  {
    header: "",
    accessor: "index",
  },
  {
    header: "Country",
    accessor: "country",
  },
  {
    header: "Active Users",
    accessor: "activeUsers",
  },
  // {
  //   header: "Status",
  //   accessor: "expired",
  //   mapping: ["Active", "Expired"],
  // },
];
const AdminGoogleAnalyticsListPage = () => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { dispatch } = React.useContext(AuthContext);
  const [pageSize, setPageSize] = React.useState(10);
  const [loading, setLoading] = React.useState(false);
  const [data, setData] = useState([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [userTotal, setUserTotal] = useState();
  // const stripe = useStripe();
  const navigate = useNavigate();


  const schema = yup.object({
    id: yup.string(),
    email: yup.string(),
    role: yup.string(),
    status: yup.string(),
    messages: yup.string(),
  });

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });



  React.useEffect(() => {
    async function getGoogleAnalytics() {
      try {
        sdk.setTable("user");
        const userResult = await sdk.callRestAPI(
          {
            payload: {
              ...data,
            },
            // page: pageNum,
            // limit: limitNum,
          },
          "PAGINATE"
        );

        const { list, total, limit, num_pages, page } = userResult;
        setUserTotal(total);

        const result = await sdk.getGoogleAnalytics();
        if (result) {
          // setCms(result?.list);
          setData(result?.data);
          const barData = result.data;
          const lineData = result.data;

         
        }
      } catch (err) {
        console.log("Error:", err);
        tokenExpireError(dispatch, err.message);
      }
    }
    getGoogleAnalytics();
  }, []);



 

  function updatePageSize(limit) {
    (async function () {
      setPageSize(limit);
      await getData(0, limit);
    })();
  }
 

  async function getData(pageNum, limitNum, data) {
    setLoading(true);
    try {
    } catch (error) {
      setLoading(false);
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    }
  }


  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "googleAnalytics",
      },
    });

    (async function () {
      await getData(0, pageSize);
    })();
  }, []);


  const startIndex = currentPage * parseInt(pageSize);

  return (
    <>
      <div className="overflow-x-auto  p-5 bg-white shadow rounded">
        <div className="mb-3 text-center justify-between w-full flex  ">
          <h4 className="text-2xl font-medium">Google analytics </h4>
          {/* <AddButton link={"/admin/add-rewards"} /> */}
        </div>

        <div className=" w-full  border-red-600 ">
          <div className=" flex flex-row mb-8">
            <div className=" border border-gray-500 p-4">
              <p className=" font-bold">Total users </p>
              <p>{userTotal}</p>
            </div>
         
          </div>
          {/* ........line chart.......... */}

          {/* <div className=" w-[500px] ">
            <LineChart chartData={_data} />
          </div> */}

          <div className=" flex gap-4">
            <div className=" ">
              <p className=" font-bold text-[20px] mb-2">Users by browsers</p>
              <div className=" w-[350px] p-4 border rounded-lg flex flex-col gap-4 max-h-[500px] overflow-y-auto">
                {data?.browser?.map((val) => (
                  <div className=" w-full ">
                    <div className=" flex justify-between">
                      <p>{val.data} </p> <span>{val.value} </span>
                    </div>
                    <div>
                      <ProgressBar percent={(val.value * 100) / 2000} />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className=" ">
              <p className=" font-bold text-[20px] mb-2">Users by city</p>
              <div className=" w-[350px] p-4 border rounded-lg flex flex-col gap-4 max-h-[500px] overflow-y-auto">
                {data?.cities?.map((val) => (
                  <div className=" w-full ">
                    <div className=" flex justify-between">
                      <p>{val.data} </p> <span>{val.value} </span>
                    </div>
                    <div>
                      <ProgressBar percent={(val.value * 100) / 2000} />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className=" ">
              <p className=" font-bold text-[20px] mb-2"> Users by country</p>
              <div className=" w-[350px] p-4 border rounded-lg flex flex-col gap-4 max-h-[500px] overflow-y-auto">
                {data?.country?.map((val) => (
                  <div className=" w-full ">
                    <div className=" flex justify-between">
                      <p>{val.data} </p> <span>{val.value} </span>
                    </div>
                    <div>
                      <ProgressBar percent={(val.value * 100) / 2000} />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className=" flex gap-4 mt-4">
            <div className=" ">
              <p className=" font-bold text-[20px] mb-2">Users by medium</p>
              <div className=" w-[350px] p-4 border rounded-lg flex flex-col gap-4 max-h-[500px] overflow-y-auto">
                {data?.medium?.map((val) => (
                  <div className=" w-full ">
                    <div className=" flex justify-between">
                      <p>{val.data} </p> <span>{val.value} </span>
                    </div>
                    <div>
                      <ProgressBar percent={(val.value * 100) / 2000} />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className=" ">
              <p className=" font-bold text-[20px] mb-2">Users by Source</p>
              <div className=" w-[350px] p-4 border rounded-lg flex flex-col gap-4 max-h-[500px] overflow-y-auto">
                {data?.source?.map((val) => (
                  <div className=" w-full ">
                    <div className=" flex justify-between">
                      <p>{val.data} </p> <span>{val.value} </span>
                    </div>
                    <div>
                      <ProgressBar percent={(val.value * 100) / 2000} />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        
      </div>
      
    </>
  );
};

export default AdminGoogleAnalyticsListPage;
