import { AuthContext } from "Src/authContext";
import { GlobalContext, showToast } from "Src/globalContext";
import MkdSDK from "Utils/MkdSDK";
import React, { useEffect } from "react";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
let sdk = new MkdSDK();

// Route /login/oauth
// <Route path="/login/oauth" element={<SocialLoginVerificationTemplate />}></Route>

const SocialLoginVerificationTemplate = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: GlobalDispatch } = React.useContext(GlobalContext);
  const location = useLocation();
  const searchParam = new URLSearchParams(location.search);
  const redirect_uri = searchParam.get("redirect_uri");
  const navigate = useNavigate();


  // https://kaizenwin.com/login/oauth?data=%7B%22error%22:false,%22role%22:%22user%22,%22token%22:%22eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjo1Nywicm9sZSI6InVzZXIiLCJpYXQiOjE3MTI4NjcyNjksImV4cCI6MTcxMjg3MDg2OX0.a37grpbdigx3WPQEhTFI0Xs-GNoVfjCT3DBX8OmuujA%22,%22expire_at%22:3600,%22user_id%22:57%7D

  // data=%7B%22error%22:false,%22role%22:%22user%22,%22access_token%22:%22eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoyNzgsInJvbGUiOiJ1c2VyIiwiaWF0IjoxNzEyODQ4NDQxLCJleHAiOjE3MTI4NTIwNDF9.2twS2kCdmOsxc8cdvKHBTNiRxaTiFNc2PrM1Z893Nks%22,%22refresh_token%22:%22eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoyNzgsInJvbGUiOiJ1c2VyIiwiaWF0IjoxNzEyODQ4NDQxLCJleHAiOjE3MTM0NTMyNDF9.KR9YfRABPb-xL2krRbJR48eVyoFDE9MlsuLglLr7ibE%22,%22expire_at%22:3600,%22user_id%22:278,%22state%22:%22a2FpemVud2luOms3ZGU3dmdyaTRubXgxczQ1ZDM0anU0OXhpaDdjbG4=~user%22,%22is_newuser%22:true%7D

  let data = searchParams.get("data");
  useEffect(() => {
    const login = async () => {
      const parsedData = JSON.parse(data);
      dispatch({
        type: "LOGIN",
        payload: parsedData,
      });
      const token = parsedData?.token
        ? parsedData?.token
        : parsedData?.access_token;
      
      // localStorage.setItem("token", token);
      const getuser = await sdk.getProfile(token);
      dispatch({
        type: "SAVENAME",
        payload: getuser,
      });
      // console.log("user", getuser);
      showToast(GlobalDispatch, "Succesfully Logged In", 4000, "success");
      navigate(redirect_uri ?? "/user/chat");
      // console.log("google", parsedData);
    };
    login();
  }, []);

  return <div>{}</div>;
};

export default SocialLoginVerificationTemplate;
