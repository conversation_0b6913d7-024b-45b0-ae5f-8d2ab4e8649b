import React from 'react';

const DownloadImage = (imageUrl) => {
  const handleDownload = async () => {
   
    const response = await fetch(imageUrl);
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = 'image.jpg';
    document.body.appendChild(link);
    link.click();

    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  return (
    <button onClick={handleDownload}>
      Download Image
    </button>
  );
};

export default DownloadImage;
