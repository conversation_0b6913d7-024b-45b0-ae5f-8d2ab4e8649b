import React, { useEffect, useState } from "react";
import { AuthContext, tokenExpireError } from "Src/authContext";
import { useForm } from "react-hook-form";
import { NavLink, useLocation, useNavigate } from "react-router-dom";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { GlobalContext } from "Src/globalContext";
import { getNonNullValue } from "Utils/utils";
import PaginationBar from "Components/PaginationBar";
import AddButton from "Components/AddButton";
import ExportButton from "Components/ExportButton";
import Navbar from "Components/NavBar";

let sdk = new MkdSDK();

const columns = [
  // {
  //   header: "Action",
  //   accessor: "",
  // },

  {
    header: "Id",
    accessor: "id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Plan Name",
    accessor: "plan_name",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Total Amount",
    accessor: "amount",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Message",
    accessor: "message_count",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Date & time",
    accessor: "create_at",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Status",
    accessor: "status",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
];

const ListUserPaymentsTablePage = () => {
  const { state, dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  const [query, setQuery] = React.useState("");
  const [currentTableData, setCurrentTableData] = React.useState([]);
  const [pageSize, setPageSize] = React.useState(10);
  const [pageCount, setPageCount] = React.useState(0);
  const [dataTotal, setDataTotal] = React.useState(0);
  const [currentPage, setPage] = React.useState(0);
  const [canPreviousPage, setCanPreviousPage] = React.useState(false);
  const [canNextPage, setCanNextPage] = React.useState(false);
  const [plans, setPlans] = useState();
  const [currentPlans, setCurrentPlans] = useState(null);
  const [totalMessages, setTotalMessages] = useState(0);
  const [couponCode, setCouponCode] = useState();
  const [message, setMessage] = useState("");
  const [verified, setVerified] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const redirect_uri = searchParams.get("redirect_uri");
  const prevRoute = location?.state?.from?.pathname;

  const schema = yup.object({
    id: yup.string(),
    plans: yup.string(),
    create_at: yup.string(),
  });
  const {
    register,
    handleSubmit,
    setError,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  function onSort(columnIndex) {
    if (columns[columnIndex].isSorted) {
      columns[columnIndex].isSortedDesc = !columns[columnIndex].isSortedDesc;
    } else {
      columns.map((i) => (i.isSorted = false));
      columns.map((i) => (i.isSortedDesc = false));
      columns[columnIndex].isSorted = true;
    }

    (async function () {
      await getData(0, pageSize);
    })();
  }

  function updatePageSize(limit) {
    (async function () {
      setPageSize(limit);
      await getData(0, limit);
    })();
  }

  function previousPage() {
    (async function () {
      await getData(currentPage - 1 > 0 ? currentPage - 1 : 0, pageSize);
    })();
  }

  function nextPage() {
    (async function () {
      await getData(
        currentPage + 1 <= pageCount ? currentPage + 1 : 0,
        pageSize
      );
    })();
  }

  async function getData(pageNum, limitNum, currentTableData) {
    try {
      sdk.setTable("payment_history");
      let sortField = columns.filter((col) => col.isSorted);
      const result = await sdk.callRestAPI(
        {
          payload: { ...currentTableData },
          page: pageNum,
          limit: limitNum,
          sortId: sortField.length ? sortField[0].accessor : "",
          direction: sortField.length
            ? sortField[0].isSortedDesc
              ? "DESC"
              : "ASC"
            : "",
        },
        "PAGINATE"
      );

      const { list, total, limit, num_pages, page } = result;
      for (let i in result?.list) {
        for (let j in plans?.list) {
          if (plans?.list[j]?.id == result?.list[i]?.plan_id) {
            result.list[i]["plan_name"] = plans?.list[j]?.name;
          }
        }
      }

      setPageSize(limit);
      setPageCount(num_pages);
      setPage(page);
      setDataTotal(total);
      setCanPreviousPage(page > 1);
      setCanNextPage(page + 1 <= num_pages);
    } catch (error) {
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    }
  }

  const deleteItem = async (id) => {
    try {
      sdk.setTable("payment_history");
      const result = await sdk.callRestAPI({ id }, "DELETE");
      setCurrentTableData((list) =>
        list.filter((x) => Number(x.id) !== Number(id))
      );
    } catch (err) {
      throw new Error(err);
    }
  };

  const exportTable = async (id) => {
    try {
      sdk.setTable("payment_history");
      const result = await sdk.exportCSV();
    } catch (err) {
      throw new Error(err);
    }
  };

  const resetForm = async () => {
    reset();
    getuserpayment();
    // await getData(0, pageSize);
  };
  const selectStatus = [
    { key: "0", value: "Inactive" },
    { key: "2", value: "Suspend" },
    { key: "1", value: "Active" },
  ];
  const selectPlan = [
    { key: "0", value: "Plan1" },
    { key: "2", value: "Plan2" },
    { key: "1", value: "Plan3" },
  ];

  const onSubmit = (_data) => {
    let id = getNonNullValue(_data.id);
    let plans = getNonNullValue(_data.plans);
    let create_at = getNonNullValue(_data.create_at);

    if (id) {
      const getuserpaymentbyid = async () => {
        const result = await sdk.getuserpaymentbyid(id, state.user);
        if (result) {
          setCurrentTableData(result.list);
        }
      };
      getuserpaymentbyid();
    }
    if (plans) {
      const getuserpaymentbyplanname = async () => {
        const result = await sdk.getuserpaymentbyplanname(plans, state.user);
        if (result) {
          setCurrentTableData(result.list);
        }
      };
      getuserpaymentbyplanname();
    }
  };

  async function getcurrentplan() {
    try {
      const result = await sdk.getcurrentplan(state.user);
      // console.log("wre", result);
      if (result) {
        // setCurrentPlans(result.list);
      }
    } catch (err) {
      console.log("Error:", err);
      tokenExpireError(dispatch, err.message);
    }
  }

  const getuserpayment = async () => {
    const paymenthistory = await sdk.getuserpayment(state.user);

    setCurrentTableData(paymenthistory.list);
  };
  const getuserplans = async () => {
    try {
      const result = await sdk.getuserplans(state.user);
      // console.log("wre", result);
      if (result) {
        setCurrentPlans(result.list);
      }
    } catch (err) {
      console.log("Error:", err);
      tokenExpireError(dispatch, err.message);
    }
  };
  React.useEffect(() => {
    getuserpayment();

    const getplans = async () => {
      const plans = await sdk.getplans();
      setPlans(plans);
    };
    getplans();

    getuserplans();

    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "payments",
      },
    });

    (async function () {
      await getData(1, pageSize);
    })();
    (async function () {
      await getcurrentplan();
    })();
  }, []);
  useEffect(() => {
    let total = 0;
    currentPlans?.map((plan, i) => {
      total += plan.messages_left;
      return setTotalMessages(total);
    });
  }, [currentPlans]);

  const verifycoupon = async () => {
    try {
      if (!state.user) {
        navigate("/user/login");
      }
      if (!localStorage.getItem("terms")) {
        navigate(redirect_uri ?? "/user/terms", { state: { from: location } });
      } else {
        const result = await sdk.verifycoupon(couponCode);
        // console.log("result", result);
        if (result.error) {
          result.message.includes("has no value")
            ? setMessage("Input the coupon code")
            : result.message.includes("Table")
            ? setMessage("An error occured please try again")
            : setMessage(result.message);
        } else {
          setMessage("verified! click here to continue to chat");
          setVerified(true);
          getuserpayment();
          getuserplans();
          setCouponCode(" ");
        }
      }
    } catch (err) {
      tokenExpireError(
        dispatch,
        err.response?.data.message ? err.response?.data.message : err.message
      );
    }
  };

  return (
    <div className=" m-auto">
      <Navbar />
      <div className="   border-red-500 mx-auto  md:px-1 px-[21px]">
        <div className="flex md:flex-row flex-col justify-between   max-w-[1240px] mx-auto items-center  mb-[20px] md:mt-[60px]  border-red-400 ">
          <h2 className="md:text-[32px] text-[28px] font-bold">
            Message Remaining ({totalMessages})
          </h2>
          <button
            onClick={() => {
              navigate("/user/buy", {
                state: { from: location },
                replace: false,
              });
            }}
            className="bg-companyRed hover:bg-red-700 text-white h-full min-h-[54px] w-full max-w-[158px] md:mt-0 mt-8 py-2 px-4 rounded-full"
          >
            Get Tokens
          </button>
        </div>

        {/* coupon */}
        <div className=" flex items-center w-full bg-[#F8F8F8] justify-start py-5">
          <div className=" flex md:gap-4 gap-2 flex-col justify-start md:flex-row  w-full md:pr-[90px] items-center mx-auto max-w-[1240px]  border-red-400">
            <p className="w-fit ">Do you have a coupon code?</p>

            <div className=" flex  items-center gap-1">
              <input
                type="text"
                placeholder="Enter coupon code "
                value={couponCode}
                className="rounded-full w-fit py-2 border-[#EDEDED]"
                onChange={(e) => {
                  setCouponCode(e.target.value);
                }}
              />
              <button
                className=" border-[1px] rounded-full p-2 px-6 ml-2 border-red-600 w-fit hover:bg-red-600 hover:text-white"
                onClick={verifycoupon}
              >
                Verify
              </button>
            </div>
            <p className="  text-sm ">
              {verified ? (
                <span
                  className=" text-companyRed text-sm mt-2 cursor-pointer font-medium"
                  onClick={() => {
                    navigate(redirect_uri ?? "/user/chat");
                  }}
                >
                  {message}
                </span>
              ) : (
                <span className=" text-companyRed text-sm mt-2 font-medium">
                  {message}
                </span>
              )}
            </p>
          </div>
        </div>

        <h2 className="max-w-[1240px] mx-auto  flex font-medium justify-left md:items-left md:place-content-start place-content-center text-companyBlack text-center mt-10 text-[24px] md:text-[28px] ">
          Simple, Transparent Pricing
        </h2>
        <h2 className="max-w-[1240px] mx-auto  flex justify-left font-normal items-left text-[#999999] text-center mt-[24px] mb-8 text-base  ">
          Gaze and attention modeling powered by AI is optimizing virtual
          reality experience
        </h2>

        {/*MAP THE CURRENT PLANS */}
        {/* md:flex-nowrap */}
        <div className="w-full max-w-[1240px] mx-auto  flex  flex-wrap justify-center items-center place-content-center text-gray-700 text-center gap-4   ">
          {currentPlans?.map((plans) => (
            <>
              <div className="flex flex-col  justify-center items-center max-w-[160px] w-full md:max-w-[290px]">
                <div className="bg-[#F9F9F9] rounded-lg shadow-lg p-10 m-4 max-w-[160px] w-full md:max-w-[290px] relative">
                  <h2
                    className={`md:text-lg text-[10px] font-semibold mb-4 text-center absolute top-[-1.5rem] right-0 left-0 m-auto border-4 border-white w-fit p-2 rounded-full  px-4  ${
                      plans.plan_name === "Deluxe"
                        ? "bg-[#FAF5E7] text-[#D9AF39] w-10"
                        : plans.plan_name === "Basic"
                        ? "bg-[#F1E6E0] text-[#D0743B]"
                        : plans.plan_name === "Premium"
                        ? "bg-[#E5E5E5] text-[#6C6C6C]"
                        : plans.plan_name === "Free"
                        ? "bg-[#22C55E] text-[#22C55E]"
                        : "bg-[#f3f2f2] text-[#a1a1a1]"
                    }`}
                  >
                    {plans.plan_name}
                  </h2>
                  <p className="text-black mb-4 font-semibold md:text-[100px] text-[56px] text-center mt-8">
                    {plans.messages_left}
                  </p>
                </div>
              </div>
            </>
          ))}
        </div>

        <h4 className="max-w-[1240px] mx-auto text-[24px] md:text-[28px] text-companyBlack font-medium md:text-left text-center md:mt-[100px] mt-[60px] md:mb-[64px] mb-[21px]  ">
          Payment History
        </h4>
        <form
          className=" max-w-[1240px] mx-auto  bg-white  rounded mb-10 w-full  border-red-400 "
          onSubmit={handleSubmit(onSubmit)}
        >
          <div className="filter-form-holder flex w-full  border-red-500  md:flex-nowrap flex-wrap items-center md:justify-between justify-center ">
            <div className="mb-4 w-full  pr-2 pl-2">
              <label
                className="block text-[#7A7A7A] text-lg font-medium mb-2 font-[GilroyMedium]"
                htmlFor="id"
              >
                ID
              </label>
              <input
                type="text"
                placeholder=""
                {...register("id")}
                className={` appearance-none border border-[#EDEDED] rounded-full w-full md:max-w-[350px] h-[61px] py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
                  errors.email?.message ? "border-red-500" : ""
                }`}
              />
            </div>
            <div className="mb-4  w-full  pr-2 pl-2 ">
              <label
                className="block text-[#7A7A7A] font-medium mb-2 text-lg font-[GilroyMedium]"
                htmlFor="plans"
              >
                Plan Name
              </label>
              <input
                type="text"
                placeholder=""
                {...register("plans")}
                className={` appearance-none border border-[#EDEDED] rounded-full w-full md:max-w-[350px] h-[61px] py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
                  errors.email?.message ? "border-red-500" : ""
                }`}
              />
            </div>

            <div className="  mb-4 w-full  pr-2 pl-2 ">
              <label
                className="block text-[#7A7A7A] font-medium mb-2 text-lg font-[GilroyMedium]"
                htmlFor="create_at"
              >
                Plan Date
              </label>
              <input
                type="date"
                placeholder="create_at"
                {...register("create_at")}
                className={` appearance-none border border-[#EDEDED] rounded-full w-full md:max-w-[350px] h-[61px] py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
                  errors.email?.message ? "border-red-500" : ""
                }`}
              />
            </div>
            <div className=" mt-4 md:flex md:items-center items-center md:flex-row flex flex-col md:w-full  md:max-w-[119px]   w-full ">
              <button
                type="submit"
                className=" inline md:ml-2 bg-[#CC1122] hover:bg-red-700 text-white font-bold py-[10px] md:py-2 px-4 rounded-full focus:outline-none focus:shadow-outline md:w-full md:max-w-[119px] w-full md:mb-0 mb-4 h-full md:min-h-[60px] min-h-[54px]  "
              >
                Search
              </button>
            </div>
          </div>
        </form>

        {/* TABLE STARTS */}

        <div className="overflow-x-auto bg-white md:w-full max-w-[1240px] mx-auto  flex flex-col items-center">
          <div className="mb-3 text-center justify-between w-full flex">
            <div className="flex"></div>
          </div>
          <div className="  w-full">
            <table className="min-w-full divide-y divide-gray-200 border-white">
              <thead className="bg-[#F8F8F8] border-white">
                <tr className=" ">
                  {columns.map((column, i) => (
                    <th
                      key={i}
                      scope="col"
                      className="md:px-6 px-2 first:pl-4 last:pr-4 h-[72px] text-center md:text-lg text-xs font-semibold text-black  tracking-wider first:rounded-tl-3xl first:rounded-bl-[30px] first:border-white last:rounded-tr-3xl last:rounded-br-[30px] "
                      onClick={() => onSort(i)}
                    >
                      {column.header}
                      <span>
                        {column.isSorted
                          ? column.isSortedDesc
                            ? " ▼"
                            : " ▲"
                          : ""}
                      </span>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {currentTableData.map((row, i) => {
                  return (
                    <tr key={i} className=" bg-[#FDFDFD] mt-8 rounded-lg">
                      {columns.map((cell, index) => {
                        if (cell.accessor.indexOf("image") > -1) {
                          return (
                            <td
                              key={index}
                              className="px-6 py-4 whitespace-nowrap"
                            >
                              <img
                                src={row[cell.accessor]}
                                className="h-[100px] w-[150px]"
                              />
                            </td>
                          );
                        }
                        if (
                          cell.accessor.indexOf("pdf") > -1 ||
                          cell.accessor.indexOf("doc") > -1 ||
                          cell.accessor.indexOf("file") > -1 ||
                          cell.accessor.indexOf("video") > -1
                        ) {
                          return (
                            <td
                              key={index}
                              className="px-6 py-4 whitespace-nowrap"
                            >
                              <a
                                className="text-blue-500"
                                target="_blank"
                                href={row[cell.accessor]}
                              >
                                View
                              </a>
                            </td>
                          );
                        }
                        if (cell.accessor == "") {
                          return (
                            <td
                              key={index}
                              className="px-6 py-4 whitespace-nowrap"
                            ></td>
                          );
                        }
                        if (cell.mappingExist) {
                          return (
                            <td
                              key={index}
                              className="px-6 py-4 whitespace-nowrap"
                            >
                              {cell.mappings[row[cell.accessor]]}
                            </td>
                          );
                        }
                        return (
                          <td
                            key={index}
                            className=" mb-4 h-[100px] bg-[#FDFDFD] whitespace-nowrap  border-white first:rounded-tl-3xl first:rounded-bl-3xl last:rounded-tr-3xl last:rounded-br-3xl border-t-[16px] text-center md:text-lg text-xs "
                          >
                            <div
                              className={`w-full flex  ${
                                cell.accessor === "plan_name"
                                  ? " w-fit border-white rounded-full"
                                  : null
                              }   place-content-center text-center`}
                            >
                              {cell.accessor == "plan_name" ? (
                                <div
                                  className={`w-fit border-6 z-50 border-4 border-white rounded-full md:px-8 px-4 py-3 text-center md:text-[18px] text-[10px]  ${
                                    row[cell.accessor] === "Deluxe"
                                      ? "bg-[#FAF5E7] text-[#D9AF39] w-10"
                                      : row[cell.accessor] === "Basic"
                                      ? "bg-[#F1E6E0] text-[#D0743B]"
                                      : row[cell.accessor] === "Premium"
                                      ? "bg-[#E5E5E5] text-[#6C6C6C]"
                                      : row[cell.accessor] === "Free"
                                      ? "bg-[#22C55E] text-[#22C55E]"
                                      : "bg-[#f3f2f2] text-[#a1a1a1]"
                                  }`}
                                >
                                  {row[cell.accessor]}{" "}
                                </div>
                              ) : cell.accessor == "amount" ? (
                                <div className=" font-semibold">
                                  {row[cell.accessor] / 100}
                                </div>
                              ) : (
                                <div className=" font-semibold">
                                  {row[cell.accessor]}
                                </div>
                              )}
                            </div>
                          </td>
                        );
                      })}
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
        {/* <PaginationBar
          currentPage={currentPage}
          pageCount={pageCount}
          pageSize={pageSize}
          canPreviousPage={canPreviousPage}
          canNextPage={canNextPage}
          updatePageSize={updatePageSize}
          previousPage={previousPage}
          nextPage={nextPage}
        /> */}
      </div>
    </div>
  );
};

export default ListUserPaymentsTablePage;
