import{g as H,c as M}from"../vendor-b0222800.js";function B(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw new TypeError("attempted to use private field on non-instance");return e}var pt=0;function at(e){return"__private_"+pt+++"_"+e}function vt(e,t,r){const s=[];return e.forEach(o=>typeof o!="string"?s.push(o):t[Symbol.split](o).forEach((u,i,a)=>{u!==""&&s.push(u),i<a.length-1&&s.push(r)})),s}/**
 * Takes a string with placeholder variables like `%{smart_count} file selected`
 * and replaces it with values from options `{smart_count: 5}`
 *
 * @license https://github.com/airbnb/polyglot.js/blob/master/LICENSE
 * taken from https://github.com/airbnb/polyglot.js/blob/master/lib/polyglot.js#L299
 *
 * @param phrase that needs interpolation, with placeholders
 * @param options with values that will be used to replace placeholders
 */function V(e,t){const r=/\$/g,s="$$$$";let o=[e];if(t==null)return o;for(const u of Object.keys(t))if(u!=="_"){let i=t[u];typeof i=="string"&&(i=r[Symbol.replace](i,s)),o=vt(o,new RegExp(`%\\{${u}\\}`,"g"),i)}return o}const bt=e=>{throw new Error(`missing string: ${e}`)};var R=at("onMissingKey"),F=at("apply");class gt{constructor(t,r){let{onMissingKey:s=bt}=r===void 0?{}:r;Object.defineProperty(this,F,{value:yt}),Object.defineProperty(this,R,{writable:!0,value:void 0}),this.locale={strings:{},pluralize(o){return o===1?0:1}},Array.isArray(t)?t.forEach(B(this,F)[F],this):B(this,F)[F](t),B(this,R)[R]=s}translate(t,r){return this.translateArray(t,r).join("")}translateArray(t,r){let s=this.locale.strings[t];if(s==null&&(B(this,R)[R](t),s=t),typeof s=="object"){if(r&&typeof r.smart_count<"u"){const u=this.locale.pluralize(r.smart_count);return V(s[u],r)}throw new Error("Attempted to use a string with plural forms, but no value was given for %{smart_count}")}if(typeof s!="string")throw new Error("string was not a string");return V(s,r)}}function yt(e){if(!(e!=null&&e.strings))return;const t=this.locale;Object.assign(this.locale,{strings:{...t.strings,...e.strings},pluralize:e.pluralize||t.pluralize})}var _t=function(){var t={},r=t._fns={};t.emit=function(i,a,l,c,f,d,v){var b=s(i);b.length&&o(i,b,[a,l,c,f,d,v])},t.on=function(i,a){r[i]||(r[i]=[]),r[i].push(a)},t.once=function(i,a){function l(){a.apply(this,arguments),t.off(i,l)}this.on(i,l)},t.off=function(i,a){var l=[];if(i&&a){var c=this._fns[i],f=0,d=c?c.length:0;for(f;f<d;f++)c[f]!==a&&l.push(c[f])}l.length?this._fns[i]=l:delete this._fns[i]};function s(u){var i=r[u]?r[u]:[],a=u.indexOf(":"),l=a===-1?[u]:[u.substring(0,a),u.substring(a+1)],c=Object.keys(r),f=0,d=c.length;for(f;f<d;f++){var v=c[f];if(v==="*"&&(i=i.concat(r[v])),l.length===2&&l[0]===v){i=i.concat(r[v]);break}}return i}function o(u,i,a){var l=0,c=i.length;for(l;l<c&&i[l];l++)i[l].event=u,i[l].apply(i[l],a)}return t};const Ie=H(_t);let Tt="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict",Ne=(e=21)=>{let t="",r=e;for(;r--;)t+=Tt[Math.random()*64|0];return t};function Ot(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var Q=Ot,wt=typeof M=="object"&&M&&M.Object===Object&&M,jt=wt,Pt=jt,Et=typeof self=="object"&&self&&self.Object===Object&&self,St=Pt||Et||Function("return this")(),ot=St,$t=ot,xt=function(){return $t.Date.now()},Lt=xt,qt=/\s/;function Rt(e){for(var t=e.length;t--&&qt.test(e.charAt(t)););return t}var Ft=Rt,It=Ft,Nt=/^\s+/;function At(e){return e&&e.slice(0,It(e)+1).replace(Nt,"")}var kt=At,Mt=ot,Bt=Mt.Symbol,ut=Bt,Z=ut,lt=Object.prototype,Ct=lt.hasOwnProperty,Gt=lt.toString,I=Z?Z.toStringTag:void 0;function Kt(e){var t=Ct.call(e,I),r=e[I];try{e[I]=void 0;var s=!0}catch{}var o=Gt.call(e);return s&&(t?e[I]=r:delete e[I]),o}var Wt=Kt,zt=Object.prototype,Ut=zt.toString;function Dt(e){return Ut.call(e)}var Ht=Dt,tt=ut,Qt=Wt,Xt=Ht,Yt="[object Null]",Jt="[object Undefined]",et=tt?tt.toStringTag:void 0;function Vt(e){return e==null?e===void 0?Jt:Yt:et&&et in Object(e)?Qt(e):Xt(e)}var Zt=Vt;function te(e){return e!=null&&typeof e=="object"}var ee=te,re=Zt,ie=ee,ne="[object Symbol]";function se(e){return typeof e=="symbol"||ie(e)&&re(e)==ne}var ae=se,oe=kt,rt=Q,ue=ae,it=0/0,le=/^[-+]0x[0-9a-f]+$/i,ce=/^0b[01]+$/i,fe=/^0o[0-7]+$/i,he=parseInt;function de(e){if(typeof e=="number")return e;if(ue(e))return it;if(rt(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=rt(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=oe(e);var r=ce.test(e);return r||fe.test(e)?he(e.slice(2),r?2:8):le.test(e)?it:+e}var me=de,pe=Q,K=Lt,nt=me,ve="Expected a function",be=Math.max,ge=Math.min;function ye(e,t,r){var s,o,u,i,a,l,c=0,f=!1,d=!1,v=!0;if(typeof e!="function")throw new TypeError(ve);t=nt(t)||0,pe(r)&&(f=!!r.leading,d="maxWait"in r,u=d?be(nt(r.maxWait)||0,t):u,v="trailing"in r?!!r.trailing:v);function b(h){var P=s,q=o;return s=o=void 0,c=h,i=e.apply(q,P),i}function ft(h){return c=h,a=setTimeout(k,t),f?b(h):i}function ht(h){var P=h-l,q=h-c,J=t-P;return d?ge(J,u-q):J}function X(h){var P=h-l,q=h-c;return l===void 0||P>=t||P<0||d&&q>=u}function k(){var h=K();if(X(h))return Y(h);a=setTimeout(k,ht(h))}function Y(h){return a=void 0,v&&s?b(h):(s=o=void 0,i)}function dt(){a!==void 0&&clearTimeout(a),c=0,s=l=o=a=void 0}function mt(){return a===void 0?i:Y(K())}function G(){var h=K(),P=X(h);if(s=arguments,o=this,l=h,P){if(a===void 0)return ft(l);if(d)return clearTimeout(a),a=setTimeout(k,t),b(l)}return a===void 0&&(a=setTimeout(k,t)),i}return G.cancel=dt,G.flush=mt,G}var ct=ye;const Ae=H(ct);var _e=ct,Te=Q,Oe="Expected a function";function we(e,t,r){var s=!0,o=!0;if(typeof e!="function")throw new TypeError(Oe);return Te(r)&&(s="leading"in r?!!r.leading:s,o="trailing"in r?!!r.trailing:o),_e(e,t,{leading:s,maxWait:t,trailing:o})}var je=we;const ke=H(je);class Me{constructor(t,r){this.uppy=t,this.opts=r??{}}getPluginState(){const{plugins:t}=this.uppy.getState();return(t==null?void 0:t[this.id])||{}}setPluginState(t){if(!t)return;const{plugins:r}=this.uppy.getState();this.uppy.setState({plugins:{...r,[this.id]:{...r[this.id],...t}}})}setOptions(t){this.opts={...this.opts,...t},this.setPluginState(void 0),this.i18nInit()}i18nInit(){const t=new gt([this.defaultLocale,this.uppy.locale,this.opts.locale]);this.i18n=t.translate.bind(t),this.i18nArray=t.translateArray.bind(t),this.setPluginState(void 0)}addTarget(t){throw new Error("Extend the addTarget method to add your plugin to another plugin's target")}install(){}uninstall(){}update(t){}afterUpdate(){}}function T(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw new TypeError("attempted to use private field on non-instance");return e}var Pe=0;function C(e){return"__private_"+Pe+++"_"+e}var O=C("aliveTimer"),E=C("isDone"),N=C("onTimedOut"),S=C("timeout");class Be{constructor(t,r){Object.defineProperty(this,O,{writable:!0,value:void 0}),Object.defineProperty(this,E,{writable:!0,value:!1}),Object.defineProperty(this,N,{writable:!0,value:void 0}),Object.defineProperty(this,S,{writable:!0,value:void 0}),T(this,S)[S]=t,T(this,N)[N]=r}progress(){T(this,E)[E]||T(this,S)[S]>0&&(clearTimeout(T(this,O)[O]),T(this,O)[O]=setTimeout(T(this,N)[N],T(this,S)[S]))}done(){T(this,E)[E]||(clearTimeout(T(this,O)[O]),T(this,O)[O]=void 0,T(this,E)[E]=!0)}}function n(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw new TypeError("attempted to use private field on non-instance");return e}var Ee=0;function g(e){return"__private_"+Ee+++"_"+e}function Se(e){return new Error("Cancelled",{cause:e})}function st(e){if(e!=null){var t;const r=()=>this.abort(e.reason);e.addEventListener("abort",r,{once:!0});const s=()=>{e.removeEventListener("abort",r)};(t=this.then)==null||t.call(this,s,s)}return this}var _=g("activeRequests"),p=g("queuedHandlers"),y=g("paused"),$=g("pauseTimer"),m=g("downLimit"),x=g("upperLimit"),w=g("rateLimitingTimer"),A=g("call"),j=g("queueNext"),U=g("next"),W=g("queue"),D=g("dequeue"),z=g("resume"),L=g("increaseLimit");class Ce{constructor(t){Object.defineProperty(this,D,{value:Re}),Object.defineProperty(this,W,{value:qe}),Object.defineProperty(this,U,{value:Le}),Object.defineProperty(this,j,{value:xe}),Object.defineProperty(this,A,{value:$e}),Object.defineProperty(this,_,{writable:!0,value:0}),Object.defineProperty(this,p,{writable:!0,value:[]}),Object.defineProperty(this,y,{writable:!0,value:!1}),Object.defineProperty(this,$,{writable:!0,value:void 0}),Object.defineProperty(this,m,{writable:!0,value:1}),Object.defineProperty(this,x,{writable:!0,value:void 0}),Object.defineProperty(this,w,{writable:!0,value:void 0}),Object.defineProperty(this,z,{writable:!0,value:()=>this.resume()}),Object.defineProperty(this,L,{writable:!0,value:()=>{if(n(this,y)[y]){n(this,w)[w]=setTimeout(n(this,L)[L],0);return}n(this,m)[m]=this.limit,this.limit=Math.ceil((n(this,x)[x]+n(this,m)[m])/2);for(let r=n(this,m)[m];r<=this.limit;r++)n(this,j)[j]();n(this,x)[x]-n(this,m)[m]>3?n(this,w)[w]=setTimeout(n(this,L)[L],2e3):n(this,m)[m]=Math.floor(n(this,m)[m]/2)}}),typeof t!="number"||t===0?this.limit=1/0:this.limit=t}run(t,r){return!n(this,y)[y]&&n(this,_)[_]<this.limit?n(this,A)[A](t):n(this,W)[W](t,r)}wrapSyncFunction(t,r){var s=this;return function(){for(var o=arguments.length,u=new Array(o),i=0;i<o;i++)u[i]=arguments[i];const a=s.run(()=>(t(...u),queueMicrotask(()=>a.done()),()=>{}),r);return{abortOn:st,abort(){a.abort()}}}}wrapPromiseFunction(t,r){var s=this;return function(){for(var o=arguments.length,u=new Array(o),i=0;i<o;i++)u[i]=arguments[i];let a;const l=new Promise((c,f)=>{a=s.run(()=>{let d,v;try{v=Promise.resolve(t(...u))}catch(b){v=Promise.reject(b)}return v.then(b=>{d?f(d):(a.done(),c(b))},b=>{d?f(d):(a.done(),f(b))}),b=>{d=Se(b)}},r)});return l.abort=c=>{a.abort(c)},l.abortOn=st,l}}resume(){n(this,y)[y]=!1,clearTimeout(n(this,$)[$]);for(let t=0;t<this.limit;t++)n(this,j)[j]()}pause(t){t===void 0&&(t=null),n(this,y)[y]=!0,clearTimeout(n(this,$)[$]),t!=null&&(n(this,$)[$]=setTimeout(n(this,z)[z],t))}rateLimit(t){clearTimeout(n(this,w)[w]),this.pause(t),this.limit>1&&Number.isFinite(this.limit)&&(n(this,x)[x]=this.limit-1,this.limit=n(this,m)[m],n(this,w)[w]=setTimeout(n(this,L)[L],t))}get isPaused(){return n(this,y)[y]}}function $e(e){n(this,_)[_]+=1;let t=!1,r;try{r=e()}catch(s){throw n(this,_)[_]-=1,s}return{abort:s=>{t||(t=!0,n(this,_)[_]-=1,r==null||r(s),n(this,j)[j]())},done:()=>{t||(t=!0,n(this,_)[_]-=1,n(this,j)[j]())}}}function xe(){queueMicrotask(()=>n(this,U)[U]())}function Le(){if(n(this,y)[y]||n(this,_)[_]>=this.limit||n(this,p)[p].length===0)return;const e=n(this,p)[p].shift(),t=n(this,A)[A](e.fn);e.abort=t.abort,e.done=t.done}function qe(e,t){t===void 0&&(t={});const r={fn:e,priority:t.priority||0,abort:()=>{n(this,D)[D](r)},done:()=>{throw new Error("Cannot mark a queued request as done: this indicates a bug")}},s=n(this,p)[p].findIndex(o=>r.priority>o.priority);return s===-1?n(this,p)[p].push(r):n(this,p)[p].splice(s,0,r),r}function Re(e){const t=n(this,p)[p].indexOf(e);t!==-1&&n(this,p)[p].splice(t,1)}const Ge=Symbol("__queue");class Ke extends Error{constructor(t,r){r===void 0&&(r=null),super("This looks like a network error, the endpoint might be blocked by an internet provider or a firewall."),this.cause=t,this.isNetworkError=!0,this.request=r}}function We(e){return e?e.readyState!==0&&e.readyState!==4||e.status===0:!1}function ze(e){const t=r=>"error"in r&&!!r.error;return e.filter(r=>!t(r))}function Ue(e){return e.filter(t=>{var r;return!((r=t.progress)!=null&&r.uploadStarted)||!t.isRestored})}export{Me as B,Ke as N,Be as P,Ce as R,gt as T,Zt as _,Ue as a,We as b,Q as c,Ae as d,Ie as e,ze as f,ot as g,ut as h,Ge as i,ee as j,jt as k,Ne as n,ke as t};
