var wn=Object.defineProperty;var Mn=(i,t,e)=>t in i?wn(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e;var S=(i,t,e)=>(Mn(i,typeof t!="symbol"?t+"":t,e),e);/*!
 * @kurkle/color v0.3.2
 * https://github.com/kurkle/color#readme
 * (c) 2023 <PERSON><PERSON>
 * Released under the MIT License
 */function ie(i){return i+.5|0}const ct=(i,t,e)=>Math.max(Math.min(i,e),t);function $t(i){return ct(ie(i*2.55),0,255)}function ut(i){return ct(ie(i*255),0,255)}function at(i){return ct(ie(i/2.55)/100,0,1)}function wi(i){return ct(ie(i*100),0,100)}const G={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},Ge=[..."0123456789ABCDEF"],Sn=i=>Ge[i&15],Cn=i=>Ge[(i&240)>>4]+Ge[i&15],ae=i=>(i&240)>>4===(i&15),Pn=i=>ae(i.r)&&ae(i.g)&&ae(i.b)&&ae(i.a);function Dn(i){var t=i.length,e;return i[0]==="#"&&(t===4||t===5?e={r:255&G[i[1]]*17,g:255&G[i[2]]*17,b:255&G[i[3]]*17,a:t===5?G[i[4]]*17:255}:(t===7||t===9)&&(e={r:G[i[1]]<<4|G[i[2]],g:G[i[3]]<<4|G[i[4]],b:G[i[5]]<<4|G[i[6]],a:t===9?G[i[7]]<<4|G[i[8]]:255})),e}const On=(i,t)=>i<255?t(i):"";function Tn(i){var t=Pn(i)?Sn:Cn;return i?"#"+t(i.r)+t(i.g)+t(i.b)+On(i.a,t):void 0}const Ln=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Hs(i,t,e){const s=t*Math.min(e,1-e),n=(o,r=(o+i/30)%12)=>e-s*Math.max(Math.min(r-3,9-r,1),-1);return[n(0),n(8),n(4)]}function An(i,t,e){const s=(n,o=(n+i/60)%6)=>e-e*t*Math.max(Math.min(o,4-o,1),0);return[s(5),s(3),s(1)]}function Fn(i,t,e){const s=Hs(i,1,.5);let n;for(t+e>1&&(n=1/(t+e),t*=n,e*=n),n=0;n<3;n++)s[n]*=1-t-e,s[n]+=t;return s}function In(i,t,e,s,n){return i===n?(t-e)/s+(t<e?6:0):t===n?(e-i)/s+2:(i-t)/s+4}function ri(i){const e=i.r/255,s=i.g/255,n=i.b/255,o=Math.max(e,s,n),r=Math.min(e,s,n),a=(o+r)/2;let l,h,c;return o!==r&&(c=o-r,h=a>.5?c/(2-o-r):c/(o+r),l=In(e,s,n,c,o),l=l*60+.5),[l|0,h||0,a]}function ai(i,t,e,s){return(Array.isArray(t)?i(t[0],t[1],t[2]):i(t,e,s)).map(ut)}function li(i,t,e){return ai(Hs,i,t,e)}function zn(i,t,e){return ai(Fn,i,t,e)}function Rn(i,t,e){return ai(An,i,t,e)}function Ws(i){return(i%360+360)%360}function En(i){const t=Ln.exec(i);let e=255,s;if(!t)return;t[5]!==s&&(e=t[6]?$t(+t[5]):ut(+t[5]));const n=Ws(+t[2]),o=+t[3]/100,r=+t[4]/100;return t[1]==="hwb"?s=zn(n,o,r):t[1]==="hsv"?s=Rn(n,o,r):s=li(n,o,r),{r:s[0],g:s[1],b:s[2],a:e}}function Bn(i,t){var e=ri(i);e[0]=Ws(e[0]+t),e=li(e),i.r=e[0],i.g=e[1],i.b=e[2]}function Hn(i){if(!i)return;const t=ri(i),e=t[0],s=wi(t[1]),n=wi(t[2]);return i.a<255?`hsla(${e}, ${s}%, ${n}%, ${at(i.a)})`:`hsl(${e}, ${s}%, ${n}%)`}const Mi={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},Si={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function Wn(){const i={},t=Object.keys(Si),e=Object.keys(Mi);let s,n,o,r,a;for(s=0;s<t.length;s++){for(r=a=t[s],n=0;n<e.length;n++)o=e[n],a=a.replace(o,Mi[o]);o=parseInt(Si[r],16),i[a]=[o>>16&255,o>>8&255,o&255]}return i}let le;function Nn(i){le||(le=Wn(),le.transparent=[0,0,0,0]);const t=le[i.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}const jn=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function Vn(i){const t=jn.exec(i);let e=255,s,n,o;if(t){if(t[7]!==s){const r=+t[7];e=t[8]?$t(r):ct(r*255,0,255)}return s=+t[1],n=+t[3],o=+t[5],s=255&(t[2]?$t(s):ct(s,0,255)),n=255&(t[4]?$t(n):ct(n,0,255)),o=255&(t[6]?$t(o):ct(o,0,255)),{r:s,g:n,b:o,a:e}}}function $n(i){return i&&(i.a<255?`rgba(${i.r}, ${i.g}, ${i.b}, ${at(i.a)})`:`rgb(${i.r}, ${i.g}, ${i.b})`)}const Ne=i=>i<=.0031308?i*12.92:Math.pow(i,1/2.4)*1.055-.055,Lt=i=>i<=.04045?i/12.92:Math.pow((i+.055)/1.055,2.4);function Yn(i,t,e){const s=Lt(at(i.r)),n=Lt(at(i.g)),o=Lt(at(i.b));return{r:ut(Ne(s+e*(Lt(at(t.r))-s))),g:ut(Ne(n+e*(Lt(at(t.g))-n))),b:ut(Ne(o+e*(Lt(at(t.b))-o))),a:i.a+e*(t.a-i.a)}}function he(i,t,e){if(i){let s=ri(i);s[t]=Math.max(0,Math.min(s[t]+s[t]*e,t===0?360:1)),s=li(s),i.r=s[0],i.g=s[1],i.b=s[2]}}function Ns(i,t){return i&&Object.assign(t||{},i)}function Ci(i){var t={r:0,g:0,b:0,a:255};return Array.isArray(i)?i.length>=3&&(t={r:i[0],g:i[1],b:i[2],a:255},i.length>3&&(t.a=ut(i[3]))):(t=Ns(i,{r:0,g:0,b:0,a:1}),t.a=ut(t.a)),t}function Un(i){return i.charAt(0)==="r"?Vn(i):En(i)}class Qt{constructor(t){if(t instanceof Qt)return t;const e=typeof t;let s;e==="object"?s=Ci(t):e==="string"&&(s=Dn(t)||Nn(t)||Un(t)),this._rgb=s,this._valid=!!s}get valid(){return this._valid}get rgb(){var t=Ns(this._rgb);return t&&(t.a=at(t.a)),t}set rgb(t){this._rgb=Ci(t)}rgbString(){return this._valid?$n(this._rgb):void 0}hexString(){return this._valid?Tn(this._rgb):void 0}hslString(){return this._valid?Hn(this._rgb):void 0}mix(t,e){if(t){const s=this.rgb,n=t.rgb;let o;const r=e===o?.5:e,a=2*r-1,l=s.a-n.a,h=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;o=1-h,s.r=255&h*s.r+o*n.r+.5,s.g=255&h*s.g+o*n.g+.5,s.b=255&h*s.b+o*n.b+.5,s.a=r*s.a+(1-r)*n.a,this.rgb=s}return this}interpolate(t,e){return t&&(this._rgb=Yn(this._rgb,t._rgb,e)),this}clone(){return new Qt(this.rgb)}alpha(t){return this._rgb.a=ut(t),this}clearer(t){const e=this._rgb;return e.a*=1-t,this}greyscale(){const t=this._rgb,e=ie(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=e,this}opaquer(t){const e=this._rgb;return e.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return he(this._rgb,2,t),this}darken(t){return he(this._rgb,2,-t),this}saturate(t){return he(this._rgb,1,t),this}desaturate(t){return he(this._rgb,1,-t),this}rotate(t){return Bn(this._rgb,t),this}}/*!
 * Chart.js v4.4.2
 * https://www.chartjs.org
 * (c) 2024 Chart.js Contributors
 * Released under the MIT License
 */function nt(){}const Xn=(()=>{let i=0;return()=>i++})();function I(i){return i===null||typeof i>"u"}function z(i){if(Array.isArray&&Array.isArray(i))return!0;const t=Object.prototype.toString.call(i);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function O(i){return i!==null&&Object.prototype.toString.call(i)==="[object Object]"}function W(i){return(typeof i=="number"||i instanceof Number)&&isFinite(+i)}function X(i,t){return W(i)?i:t}function P(i,t){return typeof i>"u"?t:i}const Kn=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100*t:+i;function F(i,t,e){if(i&&typeof i.call=="function")return i.apply(e,t)}function L(i,t,e,s){let n,o,r;if(z(i))if(o=i.length,s)for(n=o-1;n>=0;n--)t.call(e,i[n],n);else for(n=0;n<o;n++)t.call(e,i[n],n);else if(O(i))for(r=Object.keys(i),o=r.length,n=0;n<o;n++)t.call(e,i[r[n]],r[n])}function Ce(i,t){let e,s,n,o;if(!i||!t||i.length!==t.length)return!1;for(e=0,s=i.length;e<s;++e)if(n=i[e],o=t[e],n.datasetIndex!==o.datasetIndex||n.index!==o.index)return!1;return!0}function Pe(i){if(z(i))return i.map(Pe);if(O(i)){const t=Object.create(null),e=Object.keys(i),s=e.length;let n=0;for(;n<s;++n)t[e[n]]=Pe(i[e[n]]);return t}return i}function js(i){return["__proto__","prototype","constructor"].indexOf(i)===-1}function qn(i,t,e,s){if(!js(i))return;const n=t[i],o=e[i];O(n)&&O(o)?Jt(n,o,s):t[i]=Pe(o)}function Jt(i,t,e){const s=z(t)?t:[t],n=s.length;if(!O(i))return i;e=e||{};const o=e.merger||qn;let r;for(let a=0;a<n;++a){if(r=s[a],!O(r))continue;const l=Object.keys(r);for(let h=0,c=l.length;h<c;++h)o(l[h],i,r,e)}return i}function Kt(i,t){return Jt(i,t,{merger:Gn})}function Gn(i,t,e){if(!js(i))return;const s=t[i],n=e[i];O(s)&&O(n)?Kt(s,n):Object.prototype.hasOwnProperty.call(t,i)||(t[i]=Pe(n))}const Pi={"":i=>i,x:i=>i.x,y:i=>i.y};function Zn(i){const t=i.split("."),e=[];let s="";for(const n of t)s+=n,s.endsWith("\\")?s=s.slice(0,-1)+".":(e.push(s),s="");return e}function Qn(i){const t=Zn(i);return e=>{for(const s of t){if(s==="")break;e=e&&e[s]}return e}}function De(i,t){return(Pi[t]||(Pi[t]=Qn(t)))(i)}function hi(i){return i.charAt(0).toUpperCase()+i.slice(1)}const Oe=i=>typeof i<"u",gt=i=>typeof i=="function",Di=(i,t)=>{if(i.size!==t.size)return!1;for(const e of i)if(!t.has(e))return!1;return!0};function Jn(i){return i.type==="mouseup"||i.type==="click"||i.type==="contextmenu"}const B=Math.PI,Q=2*B,to=Q+B,Te=Number.POSITIVE_INFINITY,eo=B/180,K=B/2,_t=B/4,Oi=B*2/3,dt=Math.log10,It=Math.sign;function qt(i,t,e){return Math.abs(i-t)<e}function Ti(i){const t=Math.round(i);i=qt(i,t,i/1e3)?t:i;const e=Math.pow(10,Math.floor(dt(i))),s=i/e;return(s<=1?1:s<=2?2:s<=5?5:10)*e}function io(i){const t=[],e=Math.sqrt(i);let s;for(s=1;s<e;s++)i%s===0&&(t.push(s),t.push(i/s));return e===(e|0)&&t.push(e),t.sort((n,o)=>n-o).pop(),t}function Le(i){return!isNaN(parseFloat(i))&&isFinite(i)}function so(i,t){const e=Math.round(i);return e-t<=i&&e+t>=i}function Vs(i,t,e){let s,n,o;for(s=0,n=i.length;s<n;s++)o=i[s][e],isNaN(o)||(t.min=Math.min(t.min,o),t.max=Math.max(t.max,o))}function ft(i){return i*(B/180)}function ci(i){return i*(180/B)}function Li(i){if(!W(i))return;let t=1,e=0;for(;Math.round(i*t)/t!==i;)t*=10,e++;return e}function no(i,t){const e=t.x-i.x,s=t.y-i.y,n=Math.sqrt(e*e+s*s);let o=Math.atan2(s,e);return o<-.5*B&&(o+=Q),{angle:o,distance:n}}function Ze(i,t){return Math.sqrt(Math.pow(t.x-i.x,2)+Math.pow(t.y-i.y,2))}function oo(i,t){return(i-t+to)%Q-B}function J(i){return(i%Q+Q)%Q}function $s(i,t,e,s){const n=J(i),o=J(t),r=J(e),a=J(o-n),l=J(r-n),h=J(n-o),c=J(n-r);return n===o||n===r||s&&o===r||a>l&&h<c}function st(i,t,e){return Math.max(t,Math.min(e,i))}function ro(i){return st(i,-32768,32767)}function Yt(i,t,e,s=1e-6){return i>=Math.min(t,e)-s&&i<=Math.max(t,e)+s}function di(i,t,e){e=e||(r=>i[r]<t);let s=i.length-1,n=0,o;for(;s-n>1;)o=n+s>>1,e(o)?n=o:s=o;return{lo:n,hi:s}}const Qe=(i,t,e,s)=>di(i,e,s?n=>{const o=i[n][t];return o<e||o===e&&i[n+1][t]===e}:n=>i[n][t]<e),ao=(i,t,e)=>di(i,e,s=>i[s][t]>=e);function lo(i,t,e){let s=0,n=i.length;for(;s<n&&i[s]<t;)s++;for(;n>s&&i[n-1]>e;)n--;return s>0||n<i.length?i.slice(s,n):i}const Ys=["push","pop","shift","splice","unshift"];function ho(i,t){if(i._chartjs){i._chartjs.listeners.push(t);return}Object.defineProperty(i,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),Ys.forEach(e=>{const s="_onData"+hi(e),n=i[e];Object.defineProperty(i,e,{configurable:!0,enumerable:!1,value(...o){const r=n.apply(this,o);return i._chartjs.listeners.forEach(a=>{typeof a[s]=="function"&&a[s](...o)}),r}})})}function Ai(i,t){const e=i._chartjs;if(!e)return;const s=e.listeners,n=s.indexOf(t);n!==-1&&s.splice(n,1),!(s.length>0)&&(Ys.forEach(o=>{delete i[o]}),delete i._chartjs)}function co(i){const t=new Set(i);return t.size===i.length?i:Array.from(t)}const Us=function(){return typeof window>"u"?function(i){return i()}:window.requestAnimationFrame}();function Xs(i,t){let e=[],s=!1;return function(...n){e=n,s||(s=!0,Us.call(window,()=>{s=!1,i.apply(t,e)}))}}function fo(i,t){let e;return function(...s){return t?(clearTimeout(e),e=setTimeout(i,t,s)):i.apply(this,s),t}}const fi=i=>i==="start"?"left":i==="end"?"right":"center",j=(i,t,e)=>i==="start"?t:i==="end"?e:(t+e)/2,uo=(i,t,e,s)=>i===(s?"left":"right")?e:i==="center"?(t+e)/2:t,ce=i=>i===0||i===1,Fi=(i,t,e)=>-(Math.pow(2,10*(i-=1))*Math.sin((i-t)*Q/e)),Ii=(i,t,e)=>Math.pow(2,-10*i)*Math.sin((i-t)*Q/e)+1,Gt={linear:i=>i,easeInQuad:i=>i*i,easeOutQuad:i=>-i*(i-2),easeInOutQuad:i=>(i/=.5)<1?.5*i*i:-.5*(--i*(i-2)-1),easeInCubic:i=>i*i*i,easeOutCubic:i=>(i-=1)*i*i+1,easeInOutCubic:i=>(i/=.5)<1?.5*i*i*i:.5*((i-=2)*i*i+2),easeInQuart:i=>i*i*i*i,easeOutQuart:i=>-((i-=1)*i*i*i-1),easeInOutQuart:i=>(i/=.5)<1?.5*i*i*i*i:-.5*((i-=2)*i*i*i-2),easeInQuint:i=>i*i*i*i*i,easeOutQuint:i=>(i-=1)*i*i*i*i+1,easeInOutQuint:i=>(i/=.5)<1?.5*i*i*i*i*i:.5*((i-=2)*i*i*i*i+2),easeInSine:i=>-Math.cos(i*K)+1,easeOutSine:i=>Math.sin(i*K),easeInOutSine:i=>-.5*(Math.cos(B*i)-1),easeInExpo:i=>i===0?0:Math.pow(2,10*(i-1)),easeOutExpo:i=>i===1?1:-Math.pow(2,-10*i)+1,easeInOutExpo:i=>ce(i)?i:i<.5?.5*Math.pow(2,10*(i*2-1)):.5*(-Math.pow(2,-10*(i*2-1))+2),easeInCirc:i=>i>=1?i:-(Math.sqrt(1-i*i)-1),easeOutCirc:i=>Math.sqrt(1-(i-=1)*i),easeInOutCirc:i=>(i/=.5)<1?-.5*(Math.sqrt(1-i*i)-1):.5*(Math.sqrt(1-(i-=2)*i)+1),easeInElastic:i=>ce(i)?i:Fi(i,.075,.3),easeOutElastic:i=>ce(i)?i:Ii(i,.075,.3),easeInOutElastic(i){return ce(i)?i:i<.5?.5*Fi(i*2,.1125,.45):.5+.5*Ii(i*2-1,.1125,.45)},easeInBack(i){return i*i*((1.70158+1)*i-1.70158)},easeOutBack(i){return(i-=1)*i*((1.70158+1)*i********)+1},easeInOutBack(i){let t=1.70158;return(i/=.5)<1?.5*(i*i*(((t*=1.525)+1)*i-t)):.5*((i-=2)*i*(((t*=1.525)+1)*i+t)+2)},easeInBounce:i=>1-Gt.easeOutBounce(1-i),easeOutBounce(i){return i<1/2.75?7.5625*i*i:i<2/2.75?7.5625*(i-=1.5/2.75)*i+.75:i<2.5/2.75?7.5625*(i-=2.25/2.75)*i+.9375:7.5625*(i-=2.625/2.75)*i+.984375},easeInOutBounce:i=>i<.5?Gt.easeInBounce(i*2)*.5:Gt.easeOutBounce(i*2-1)*.5+.5};function ui(i){if(i&&typeof i=="object"){const t=i.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function zi(i){return ui(i)?i:new Qt(i)}function je(i){return ui(i)?i:new Qt(i).saturate(.5).darken(.1).hexString()}const go=["x","y","borderWidth","radius","tension"],po=["color","borderColor","backgroundColor"];function mo(i){i.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),i.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),i.set("animations",{colors:{type:"color",properties:po},numbers:{type:"number",properties:go}}),i.describe("animations",{_fallback:"animation"}),i.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function bo(i){i.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const Ri=new Map;function _o(i,t){t=t||{};const e=i+JSON.stringify(t);let s=Ri.get(e);return s||(s=new Intl.NumberFormat(i,t),Ri.set(e,s)),s}function gi(i,t,e){return _o(t,e).format(i)}const Ks={values(i){return z(i)?i:""+i},numeric(i,t,e){if(i===0)return"0";const s=this.chart.options.locale;let n,o=i;if(e.length>1){const h=Math.max(Math.abs(e[0].value),Math.abs(e[e.length-1].value));(h<1e-4||h>1e15)&&(n="scientific"),o=xo(i,e)}const r=dt(Math.abs(o)),a=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:n,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),gi(i,s,l)},logarithmic(i,t,e){if(i===0)return"0";const s=e[t].significand||i/Math.pow(10,Math.floor(dt(i)));return[1,2,3,5,10,15].includes(s)||t>.8*e.length?Ks.numeric.call(this,i,t,e):""}};function xo(i,t){let e=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(e)>=1&&i!==Math.floor(i)&&(e=i-Math.floor(i)),e}var Be={formatters:Ks};function yo(i){i.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Be.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),i.route("scale.ticks","color","","color"),i.route("scale.grid","color","","borderColor"),i.route("scale.border","color","","borderColor"),i.route("scale.title","color","","color"),i.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),i.describe("scales",{_fallback:"scale"}),i.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}const Ct=Object.create(null),Je=Object.create(null);function Zt(i,t){if(!t)return i;const e=t.split(".");for(let s=0,n=e.length;s<n;++s){const o=e[s];i=i[o]||(i[o]=Object.create(null))}return i}function Ve(i,t,e){return typeof t=="string"?Jt(Zt(i,t),e):Jt(Zt(i,""),t)}class vo{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=s=>s.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(s,n)=>je(n.backgroundColor),this.hoverBorderColor=(s,n)=>je(n.borderColor),this.hoverColor=(s,n)=>je(n.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return Ve(this,t,e)}get(t){return Zt(this,t)}describe(t,e){return Ve(Je,t,e)}override(t,e){return Ve(Ct,t,e)}route(t,e,s,n){const o=Zt(this,t),r=Zt(this,s),a="_"+e;Object.defineProperties(o,{[a]:{value:o[e],writable:!0},[e]:{enumerable:!0,get(){const l=this[a],h=r[n];return O(l)?Object.assign({},h,l):P(l,h)},set(l){this[a]=l}}})}apply(t){t.forEach(e=>e(this))}}var R=new vo({_scriptable:i=>!i.startsWith("on"),_indexable:i=>i!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[mo,bo,yo]);function ko(i){return!i||I(i.size)||I(i.family)?null:(i.style?i.style+" ":"")+(i.weight?i.weight+" ":"")+i.size+"px "+i.family}function Ae(i,t,e,s,n){let o=t[n];return o||(o=t[n]=i.measureText(n).width,e.push(n)),o>s&&(s=o),s}function wo(i,t,e,s){s=s||{};let n=s.data=s.data||{},o=s.garbageCollect=s.garbageCollect||[];s.font!==t&&(n=s.data={},o=s.garbageCollect=[],s.font=t),i.save(),i.font=t;let r=0;const a=e.length;let l,h,c,d,f;for(l=0;l<a;l++)if(d=e[l],d!=null&&!z(d))r=Ae(i,n,o,r,d);else if(z(d))for(h=0,c=d.length;h<c;h++)f=d[h],f!=null&&!z(f)&&(r=Ae(i,n,o,r,f));i.restore();const u=o.length/2;if(u>e.length){for(l=0;l<u;l++)delete n[o[l]];o.splice(0,u)}return r}function xt(i,t,e){const s=i.currentDevicePixelRatio,n=e!==0?Math.max(e/2,.5):0;return Math.round((t-n)*s)/s+n}function Ei(i,t){t=t||i.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,i.width,i.height),t.restore()}function ti(i,t,e,s){qs(i,t,e,s,null)}function qs(i,t,e,s,n){let o,r,a,l,h,c,d,f;const u=t.pointStyle,p=t.rotation,m=t.radius;let g=(p||0)*eo;if(u&&typeof u=="object"&&(o=u.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){i.save(),i.translate(e,s),i.rotate(g),i.drawImage(u,-u.width/2,-u.height/2,u.width,u.height),i.restore();return}if(!(isNaN(m)||m<=0)){switch(i.beginPath(),u){default:n?i.ellipse(e,s,n/2,m,0,0,Q):i.arc(e,s,m,0,Q),i.closePath();break;case"triangle":c=n?n/2:m,i.moveTo(e+Math.sin(g)*c,s-Math.cos(g)*m),g+=Oi,i.lineTo(e+Math.sin(g)*c,s-Math.cos(g)*m),g+=Oi,i.lineTo(e+Math.sin(g)*c,s-Math.cos(g)*m),i.closePath();break;case"rectRounded":h=m*.516,l=m-h,r=Math.cos(g+_t)*l,d=Math.cos(g+_t)*(n?n/2-h:l),a=Math.sin(g+_t)*l,f=Math.sin(g+_t)*(n?n/2-h:l),i.arc(e-d,s-a,h,g-B,g-K),i.arc(e+f,s-r,h,g-K,g),i.arc(e+d,s+a,h,g,g+K),i.arc(e-f,s+r,h,g+K,g+B),i.closePath();break;case"rect":if(!p){l=Math.SQRT1_2*m,c=n?n/2:l,i.rect(e-c,s-l,2*c,2*l);break}g+=_t;case"rectRot":d=Math.cos(g)*(n?n/2:m),r=Math.cos(g)*m,a=Math.sin(g)*m,f=Math.sin(g)*(n?n/2:m),i.moveTo(e-d,s-a),i.lineTo(e+f,s-r),i.lineTo(e+d,s+a),i.lineTo(e-f,s+r),i.closePath();break;case"crossRot":g+=_t;case"cross":d=Math.cos(g)*(n?n/2:m),r=Math.cos(g)*m,a=Math.sin(g)*m,f=Math.sin(g)*(n?n/2:m),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+f,s-r),i.lineTo(e-f,s+r);break;case"star":d=Math.cos(g)*(n?n/2:m),r=Math.cos(g)*m,a=Math.sin(g)*m,f=Math.sin(g)*(n?n/2:m),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+f,s-r),i.lineTo(e-f,s+r),g+=_t,d=Math.cos(g)*(n?n/2:m),r=Math.cos(g)*m,a=Math.sin(g)*m,f=Math.sin(g)*(n?n/2:m),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+f,s-r),i.lineTo(e-f,s+r);break;case"line":r=n?n/2:Math.cos(g)*m,a=Math.sin(g)*m,i.moveTo(e-r,s-a),i.lineTo(e+r,s+a);break;case"dash":i.moveTo(e,s),i.lineTo(e+Math.cos(g)*(n?n/2:m),s+Math.sin(g)*m);break;case!1:i.closePath();break}i.fill(),t.borderWidth>0&&i.stroke()}}function lt(i,t,e){return e=e||.5,!t||i&&i.x>t.left-e&&i.x<t.right+e&&i.y>t.top-e&&i.y<t.bottom+e}function pi(i,t){i.save(),i.beginPath(),i.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),i.clip()}function mi(i){i.restore()}function Mo(i,t,e,s,n){if(!t)return i.lineTo(e.x,e.y);if(n==="middle"){const o=(t.x+e.x)/2;i.lineTo(o,t.y),i.lineTo(o,e.y)}else n==="after"!=!!s?i.lineTo(t.x,e.y):i.lineTo(e.x,t.y);i.lineTo(e.x,e.y)}function So(i,t,e,s){if(!t)return i.lineTo(e.x,e.y);i.bezierCurveTo(s?t.cp1x:t.cp2x,s?t.cp1y:t.cp2y,s?e.cp2x:e.cp1x,s?e.cp2y:e.cp1y,e.x,e.y)}function Co(i,t){t.translation&&i.translate(t.translation[0],t.translation[1]),I(t.rotation)||i.rotate(t.rotation),t.color&&(i.fillStyle=t.color),t.textAlign&&(i.textAlign=t.textAlign),t.textBaseline&&(i.textBaseline=t.textBaseline)}function Po(i,t,e,s,n){if(n.strikethrough||n.underline){const o=i.measureText(s),r=t-o.actualBoundingBoxLeft,a=t+o.actualBoundingBoxRight,l=e-o.actualBoundingBoxAscent,h=e+o.actualBoundingBoxDescent,c=n.strikethrough?(l+h)/2:h;i.strokeStyle=i.fillStyle,i.beginPath(),i.lineWidth=n.decorationWidth||2,i.moveTo(r,c),i.lineTo(a,c),i.stroke()}}function Do(i,t){const e=i.fillStyle;i.fillStyle=t.color,i.fillRect(t.left,t.top,t.width,t.height),i.fillStyle=e}function Pt(i,t,e,s,n,o={}){const r=z(t)?t:[t],a=o.strokeWidth>0&&o.strokeColor!=="";let l,h;for(i.save(),i.font=n.string,Co(i,o),l=0;l<r.length;++l)h=r[l],o.backdrop&&Do(i,o.backdrop),a&&(o.strokeColor&&(i.strokeStyle=o.strokeColor),I(o.strokeWidth)||(i.lineWidth=o.strokeWidth),i.strokeText(h,e,s,o.maxWidth)),i.fillText(h,e,s,o.maxWidth),Po(i,e,s,h,o),s+=Number(n.lineHeight);i.restore()}function Fe(i,t){const{x:e,y:s,w:n,h:o,radius:r}=t;i.arc(e+r.topLeft,s+r.topLeft,r.topLeft,1.5*B,B,!0),i.lineTo(e,s+o-r.bottomLeft),i.arc(e+r.bottomLeft,s+o-r.bottomLeft,r.bottomLeft,B,K,!0),i.lineTo(e+n-r.bottomRight,s+o),i.arc(e+n-r.bottomRight,s+o-r.bottomRight,r.bottomRight,K,0,!0),i.lineTo(e+n,s+r.topRight),i.arc(e+n-r.topRight,s+r.topRight,r.topRight,0,-K,!0),i.lineTo(e+r.topLeft,s)}const Oo=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,To=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function Lo(i,t){const e=(""+i).match(Oo);if(!e||e[1]==="normal")return t*1.2;switch(i=+e[2],e[3]){case"px":return i;case"%":i/=100;break}return t*i}const Ao=i=>+i||0;function Gs(i,t){const e={},s=O(t),n=s?Object.keys(t):t,o=O(i)?s?r=>P(i[r],i[t[r]]):r=>i[r]:()=>i;for(const r of n)e[r]=Ao(o(r));return e}function Fo(i){return Gs(i,{top:"y",right:"x",bottom:"y",left:"x"})}function At(i){return Gs(i,["topLeft","topRight","bottomLeft","bottomRight"])}function V(i){const t=Fo(i);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function H(i,t){i=i||{},t=t||R.font;let e=P(i.size,t.size);typeof e=="string"&&(e=parseInt(e,10));let s=P(i.style,t.style);s&&!(""+s).match(To)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);const n={family:P(i.family,t.family),lineHeight:Lo(P(i.lineHeight,t.lineHeight),e),size:e,style:s,weight:P(i.weight,t.weight),string:""};return n.string=ko(n),n}function de(i,t,e,s){let n=!0,o,r,a;for(o=0,r=i.length;o<r;++o)if(a=i[o],a!==void 0&&(t!==void 0&&typeof a=="function"&&(a=a(t),n=!1),e!==void 0&&z(a)&&(a=a[e%a.length],n=!1),a!==void 0))return s&&!n&&(s.cacheable=!1),a}function Io(i,t,e){const{min:s,max:n}=i,o=Kn(t,(n-s)/2),r=(a,l)=>e&&a===0?0:a+l;return{min:r(s,-Math.abs(o)),max:r(n,o)}}function pt(i,t){return Object.assign(Object.create(i),t)}function bi(i,t=[""],e,s,n=()=>i[0]){const o=e||i;typeof s>"u"&&(s=tn("_fallback",i));const r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:i,_rootScopes:o,_fallback:s,_getTarget:n,override:a=>bi([a,...i],t,o,s)};return new Proxy(r,{deleteProperty(a,l){return delete a[l],delete a._keys,delete i[0][l],!0},get(a,l){return Qs(a,l,()=>jo(l,t,i,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(i[0])},has(a,l){return Hi(a).includes(l)},ownKeys(a){return Hi(a)},set(a,l,h){const c=a._storage||(a._storage=n());return a[l]=c[l]=h,delete a._keys,!0}})}function zt(i,t,e,s){const n={_cacheable:!1,_proxy:i,_context:t,_subProxy:e,_stack:new Set,_descriptors:Zs(i,s),setContext:o=>zt(i,o,e,s),override:o=>zt(i.override(o),t,e,s)};return new Proxy(n,{deleteProperty(o,r){return delete o[r],delete i[r],!0},get(o,r,a){return Qs(o,r,()=>Ro(o,r,a))},getOwnPropertyDescriptor(o,r){return o._descriptors.allKeys?Reflect.has(i,r)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(i,r)},getPrototypeOf(){return Reflect.getPrototypeOf(i)},has(o,r){return Reflect.has(i,r)},ownKeys(){return Reflect.ownKeys(i)},set(o,r,a){return i[r]=a,delete o[r],!0}})}function Zs(i,t={scriptable:!0,indexable:!0}){const{_scriptable:e=t.scriptable,_indexable:s=t.indexable,_allKeys:n=t.allKeys}=i;return{allKeys:n,scriptable:e,indexable:s,isScriptable:gt(e)?e:()=>e,isIndexable:gt(s)?s:()=>s}}const zo=(i,t)=>i?i+hi(t):t,_i=(i,t)=>O(t)&&i!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function Qs(i,t,e){if(Object.prototype.hasOwnProperty.call(i,t))return i[t];const s=e();return i[t]=s,s}function Ro(i,t,e){const{_proxy:s,_context:n,_subProxy:o,_descriptors:r}=i;let a=s[t];return gt(a)&&r.isScriptable(t)&&(a=Eo(t,a,i,e)),z(a)&&a.length&&(a=Bo(t,a,i,r.isIndexable)),_i(t,a)&&(a=zt(a,n,o&&o[t],r)),a}function Eo(i,t,e,s){const{_proxy:n,_context:o,_subProxy:r,_stack:a}=e;if(a.has(i))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+i);a.add(i);let l=t(o,r||s);return a.delete(i),_i(i,l)&&(l=xi(n._scopes,n,i,l)),l}function Bo(i,t,e,s){const{_proxy:n,_context:o,_subProxy:r,_descriptors:a}=e;if(typeof o.index<"u"&&s(i))return t[o.index%t.length];if(O(t[0])){const l=t,h=n._scopes.filter(c=>c!==l);t=[];for(const c of l){const d=xi(h,n,i,c);t.push(zt(d,o,r&&r[i],a))}}return t}function Js(i,t,e){return gt(i)?i(t,e):i}const Ho=(i,t)=>i===!0?t:typeof i=="string"?De(t,i):void 0;function Wo(i,t,e,s,n){for(const o of t){const r=Ho(e,o);if(r){i.add(r);const a=Js(r._fallback,e,n);if(typeof a<"u"&&a!==e&&a!==s)return a}else if(r===!1&&typeof s<"u"&&e!==s)return null}return!1}function xi(i,t,e,s){const n=t._rootScopes,o=Js(t._fallback,e,s),r=[...i,...n],a=new Set;a.add(s);let l=Bi(a,r,e,o||e,s);return l===null||typeof o<"u"&&o!==e&&(l=Bi(a,r,o,l,s),l===null)?!1:bi(Array.from(a),[""],n,o,()=>No(t,e,s))}function Bi(i,t,e,s,n){for(;e;)e=Wo(i,t,e,s,n);return e}function No(i,t,e){const s=i._getTarget();t in s||(s[t]={});const n=s[t];return z(n)&&O(e)?e:n||{}}function jo(i,t,e,s){let n;for(const o of t)if(n=tn(zo(o,i),e),typeof n<"u")return _i(i,n)?xi(e,s,i,n):n}function tn(i,t){for(const e of t){if(!e)continue;const s=e[i];if(typeof s<"u")return s}}function Hi(i){let t=i._keys;return t||(t=i._keys=Vo(i._scopes)),t}function Vo(i){const t=new Set;for(const e of i)for(const s of Object.keys(e).filter(n=>!n.startsWith("_")))t.add(s);return Array.from(t)}const $o=Number.EPSILON||1e-14,Rt=(i,t)=>t<i.length&&!i[t].skip&&i[t],en=i=>i==="x"?"y":"x";function Yo(i,t,e,s){const n=i.skip?t:i,o=t,r=e.skip?t:e,a=Ze(o,n),l=Ze(r,o);let h=a/(a+l),c=l/(a+l);h=isNaN(h)?0:h,c=isNaN(c)?0:c;const d=s*h,f=s*c;return{previous:{x:o.x-d*(r.x-n.x),y:o.y-d*(r.y-n.y)},next:{x:o.x+f*(r.x-n.x),y:o.y+f*(r.y-n.y)}}}function Uo(i,t,e){const s=i.length;let n,o,r,a,l,h=Rt(i,0);for(let c=0;c<s-1;++c)if(l=h,h=Rt(i,c+1),!(!l||!h)){if(qt(t[c],0,$o)){e[c]=e[c+1]=0;continue}n=e[c]/t[c],o=e[c+1]/t[c],a=Math.pow(n,2)+Math.pow(o,2),!(a<=9)&&(r=3/Math.sqrt(a),e[c]=n*r*t[c],e[c+1]=o*r*t[c])}}function Xo(i,t,e="x"){const s=en(e),n=i.length;let o,r,a,l=Rt(i,0);for(let h=0;h<n;++h){if(r=a,a=l,l=Rt(i,h+1),!a)continue;const c=a[e],d=a[s];r&&(o=(c-r[e])/3,a[`cp1${e}`]=c-o,a[`cp1${s}`]=d-o*t[h]),l&&(o=(l[e]-c)/3,a[`cp2${e}`]=c+o,a[`cp2${s}`]=d+o*t[h])}}function Ko(i,t="x"){const e=en(t),s=i.length,n=Array(s).fill(0),o=Array(s);let r,a,l,h=Rt(i,0);for(r=0;r<s;++r)if(a=l,l=h,h=Rt(i,r+1),!!l){if(h){const c=h[t]-l[t];n[r]=c!==0?(h[e]-l[e])/c:0}o[r]=a?h?It(n[r-1])!==It(n[r])?0:(n[r-1]+n[r])/2:n[r-1]:n[r]}Uo(i,n,o),Xo(i,o,t)}function fe(i,t,e){return Math.max(Math.min(i,e),t)}function qo(i,t){let e,s,n,o,r,a=lt(i[0],t);for(e=0,s=i.length;e<s;++e)r=o,o=a,a=e<s-1&&lt(i[e+1],t),o&&(n=i[e],r&&(n.cp1x=fe(n.cp1x,t.left,t.right),n.cp1y=fe(n.cp1y,t.top,t.bottom)),a&&(n.cp2x=fe(n.cp2x,t.left,t.right),n.cp2y=fe(n.cp2y,t.top,t.bottom)))}function Go(i,t,e,s,n){let o,r,a,l;if(t.spanGaps&&(i=i.filter(h=>!h.skip)),t.cubicInterpolationMode==="monotone")Ko(i,n);else{let h=s?i[i.length-1]:i[0];for(o=0,r=i.length;o<r;++o)a=i[o],l=Yo(h,a,i[Math.min(o+1,r-(s?0:1))%r],t.tension),a.cp1x=l.previous.x,a.cp1y=l.previous.y,a.cp2x=l.next.x,a.cp2y=l.next.y,h=a}t.capBezierPoints&&qo(i,e)}function yi(){return typeof window<"u"&&typeof document<"u"}function vi(i){let t=i.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function Ie(i,t,e){let s;return typeof i=="string"?(s=parseInt(i,10),i.indexOf("%")!==-1&&(s=s/100*t.parentNode[e])):s=i,s}const He=i=>i.ownerDocument.defaultView.getComputedStyle(i,null);function Zo(i,t){return He(i).getPropertyValue(t)}const Qo=["top","right","bottom","left"];function St(i,t,e){const s={};e=e?"-"+e:"";for(let n=0;n<4;n++){const o=Qo[n];s[o]=parseFloat(i[t+"-"+o+e])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}const Jo=(i,t,e)=>(i>0||t>0)&&(!e||!e.shadowRoot);function tr(i,t){const e=i.touches,s=e&&e.length?e[0]:i,{offsetX:n,offsetY:o}=s;let r=!1,a,l;if(Jo(n,o,i.target))a=n,l=o;else{const h=t.getBoundingClientRect();a=s.clientX-h.left,l=s.clientY-h.top,r=!0}return{x:a,y:l,box:r}}function kt(i,t){if("native"in i)return i;const{canvas:e,currentDevicePixelRatio:s}=t,n=He(e),o=n.boxSizing==="border-box",r=St(n,"padding"),a=St(n,"border","width"),{x:l,y:h,box:c}=tr(i,e),d=r.left+(c&&a.left),f=r.top+(c&&a.top);let{width:u,height:p}=t;return o&&(u-=r.width+a.width,p-=r.height+a.height),{x:Math.round((l-d)/u*e.width/s),y:Math.round((h-f)/p*e.height/s)}}function er(i,t,e){let s,n;if(t===void 0||e===void 0){const o=vi(i);if(!o)t=i.clientWidth,e=i.clientHeight;else{const r=o.getBoundingClientRect(),a=He(o),l=St(a,"border","width"),h=St(a,"padding");t=r.width-h.width-l.width,e=r.height-h.height-l.height,s=Ie(a.maxWidth,o,"clientWidth"),n=Ie(a.maxHeight,o,"clientHeight")}}return{width:t,height:e,maxWidth:s||Te,maxHeight:n||Te}}const ue=i=>Math.round(i*10)/10;function ir(i,t,e,s){const n=He(i),o=St(n,"margin"),r=Ie(n.maxWidth,i,"clientWidth")||Te,a=Ie(n.maxHeight,i,"clientHeight")||Te,l=er(i,t,e);let{width:h,height:c}=l;if(n.boxSizing==="content-box"){const f=St(n,"border","width"),u=St(n,"padding");h-=u.width+f.width,c-=u.height+f.height}return h=Math.max(0,h-o.width),c=Math.max(0,s?h/s:c-o.height),h=ue(Math.min(h,r,l.maxWidth)),c=ue(Math.min(c,a,l.maxHeight)),h&&!c&&(c=ue(h/2)),(t!==void 0||e!==void 0)&&s&&l.height&&c>l.height&&(c=l.height,h=ue(Math.floor(c*s))),{width:h,height:c}}function Wi(i,t,e){const s=t||1,n=Math.floor(i.height*s),o=Math.floor(i.width*s);i.height=Math.floor(i.height),i.width=Math.floor(i.width);const r=i.canvas;return r.style&&(e||!r.style.height&&!r.style.width)&&(r.style.height=`${i.height}px`,r.style.width=`${i.width}px`),i.currentDevicePixelRatio!==s||r.height!==n||r.width!==o?(i.currentDevicePixelRatio=s,r.height=n,r.width=o,i.ctx.setTransform(s,0,0,s,0,0),!0):!1}const sr=function(){let i=!1;try{const t={get passive(){return i=!0,!1}};yi()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch{}return i}();function Ni(i,t){const e=Zo(i,t),s=e&&e.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function wt(i,t,e,s){return{x:i.x+e*(t.x-i.x),y:i.y+e*(t.y-i.y)}}function nr(i,t,e,s){return{x:i.x+e*(t.x-i.x),y:s==="middle"?e<.5?i.y:t.y:s==="after"?e<1?i.y:t.y:e>0?t.y:i.y}}function or(i,t,e,s){const n={x:i.cp2x,y:i.cp2y},o={x:t.cp1x,y:t.cp1y},r=wt(i,n,e),a=wt(n,o,e),l=wt(o,t,e),h=wt(r,a,e),c=wt(a,l,e);return wt(h,c,e)}const rr=function(i,t){return{x(e){return i+i+t-e},setWidth(e){t=e},textAlign(e){return e==="center"?e:e==="right"?"left":"right"},xPlus(e,s){return e-s},leftForLtr(e,s){return e-s}}},ar=function(){return{x(i){return i},setWidth(i){},textAlign(i){return i},xPlus(i,t){return i+t},leftForLtr(i,t){return i}}};function Ft(i,t,e){return i?rr(t,e):ar()}function sn(i,t){let e,s;(t==="ltr"||t==="rtl")&&(e=i.canvas.style,s=[e.getPropertyValue("direction"),e.getPropertyPriority("direction")],e.setProperty("direction",t,"important"),i.prevTextDirection=s)}function nn(i,t){t!==void 0&&(delete i.prevTextDirection,i.canvas.style.setProperty("direction",t[0],t[1]))}function on(i){return i==="angle"?{between:$s,compare:oo,normalize:J}:{between:Yt,compare:(t,e)=>t-e,normalize:t=>t}}function ji({start:i,end:t,count:e,loop:s,style:n}){return{start:i%e,end:t%e,loop:s&&(t-i+1)%e===0,style:n}}function lr(i,t,e){const{property:s,start:n,end:o}=e,{between:r,normalize:a}=on(s),l=t.length;let{start:h,end:c,loop:d}=i,f,u;if(d){for(h+=l,c+=l,f=0,u=l;f<u&&r(a(t[h%l][s]),n,o);++f)h--,c--;h%=l,c%=l}return c<h&&(c+=l),{start:h,end:c,loop:d,style:i.style}}function hr(i,t,e){if(!e)return[i];const{property:s,start:n,end:o}=e,r=t.length,{compare:a,between:l,normalize:h}=on(s),{start:c,end:d,loop:f,style:u}=lr(i,t,e),p=[];let m=!1,g=null,b,_,y;const v=()=>l(n,y,b)&&a(n,y)!==0,x=()=>a(o,b)===0||l(o,y,b),w=()=>m||v(),M=()=>!m||x();for(let k=c,C=c;k<=d;++k)_=t[k%r],!_.skip&&(b=h(_[s]),b!==y&&(m=l(b,n,o),g===null&&w()&&(g=a(b,n)===0?k:C),g!==null&&M()&&(p.push(ji({start:g,end:k,loop:f,count:r,style:u})),g=null),C=k,y=b));return g!==null&&p.push(ji({start:g,end:d,loop:f,count:r,style:u})),p}function cr(i,t){const e=[],s=i.segments;for(let n=0;n<s.length;n++){const o=hr(s[n],i.points,t);o.length&&e.push(...o)}return e}function dr(i,t,e,s){let n=0,o=t-1;if(e&&!s)for(;n<t&&!i[n].skip;)n++;for(;n<t&&i[n].skip;)n++;for(n%=t,e&&(o+=n);o>n&&i[o%t].skip;)o--;return o%=t,{start:n,end:o}}function fr(i,t,e,s){const n=i.length,o=[];let r=t,a=i[t],l;for(l=t+1;l<=e;++l){const h=i[l%n];h.skip||h.stop?a.skip||(s=!1,o.push({start:t%n,end:(l-1)%n,loop:s}),t=r=h.stop?l:null):(r=l,a.skip&&(t=l)),a=h}return r!==null&&o.push({start:t%n,end:r%n,loop:s}),o}function ur(i,t){const e=i.points,s=i.options.spanGaps,n=e.length;if(!n)return[];const o=!!i._loop,{start:r,end:a}=dr(e,n,o,s);if(s===!0)return Vi(i,[{start:r,end:a,loop:o}],e,t);const l=a<r?a+n:a,h=!!i._fullLoop&&r===0&&a===n-1;return Vi(i,fr(e,r,l,h),e,t)}function Vi(i,t,e,s){return!s||!s.setContext||!e?t:gr(i,t,e,s)}function gr(i,t,e,s){const n=i._chart.getContext(),o=$i(i.options),{_datasetIndex:r,options:{spanGaps:a}}=i,l=e.length,h=[];let c=o,d=t[0].start,f=d;function u(p,m,g,b){const _=a?-1:1;if(p!==m){for(p+=l;e[p%l].skip;)p-=_;for(;e[m%l].skip;)m+=_;p%l!==m%l&&(h.push({start:p%l,end:m%l,loop:g,style:b}),c=b,d=m%l)}}for(const p of t){d=a?d:p.start;let m=e[d%l],g;for(f=d+1;f<=p.end;f++){const b=e[f%l];g=$i(s.setContext(pt(n,{type:"segment",p0:m,p1:b,p0DataIndex:(f-1)%l,p1DataIndex:f%l,datasetIndex:r}))),pr(g,c)&&u(d,f-1,p.loop,c),m=b,c=g}d<f-1&&u(d,f-1,p.loop,c)}return h}function $i(i){return{backgroundColor:i.backgroundColor,borderCapStyle:i.borderCapStyle,borderDash:i.borderDash,borderDashOffset:i.borderDashOffset,borderJoinStyle:i.borderJoinStyle,borderWidth:i.borderWidth,borderColor:i.borderColor}}function pr(i,t){if(!t)return!1;const e=[],s=function(n,o){return ui(o)?(e.includes(o)||e.push(o),e.indexOf(o)):o};return JSON.stringify(i,s)!==JSON.stringify(t,s)}/*!
 * Chart.js v4.4.2
 * https://www.chartjs.org
 * (c) 2024 Chart.js Contributors
 * Released under the MIT License
 */class mr{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,s,n){const o=e.listeners[n],r=e.duration;o.forEach(a=>a({chart:t,initial:e.initial,numSteps:r,currentStep:Math.min(s-e.start,r)}))}_refresh(){this._request||(this._running=!0,this._request=Us.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((s,n)=>{if(!s.running||!s.items.length)return;const o=s.items;let r=o.length-1,a=!1,l;for(;r>=0;--r)l=o[r],l._active?(l._total>s.duration&&(s.duration=l._total),l.tick(t),a=!0):(o[r]=o[o.length-1],o.pop());a&&(n.draw(),this._notify(n,s,t,"progress")),o.length||(s.running=!1,this._notify(n,s,t,"complete"),s.initial=!1),e+=o.length}),this._lastDate=t,e===0&&(this._running=!1)}_getAnims(t){const e=this._charts;let s=e.get(t);return s||(s={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,s)),s}listen(t,e,s){this._getAnims(t).listeners[e].push(s)}add(t,e){!e||!e.length||this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){const e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((s,n)=>Math.max(s,n._duration),0),this._refresh())}running(t){if(!this._running)return!1;const e=this._charts.get(t);return!(!e||!e.running||!e.items.length)}stop(t){const e=this._charts.get(t);if(!e||!e.items.length)return;const s=e.items;let n=s.length-1;for(;n>=0;--n)s[n].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var ot=new mr;const Yi="transparent",br={boolean(i,t,e){return e>.5?t:i},color(i,t,e){const s=zi(i||Yi),n=s.valid&&zi(t||Yi);return n&&n.valid?n.mix(s,e).hexString():t},number(i,t,e){return i+(t-i)*e}};class _r{constructor(t,e,s,n){const o=e[s];n=de([t.to,n,o,t.from]);const r=de([t.from,o,n]);this._active=!0,this._fn=t.fn||br[t.type||typeof r],this._easing=Gt[t.easing]||Gt.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=s,this._from=r,this._to=n,this._promises=void 0}active(){return this._active}update(t,e,s){if(this._active){this._notify(!1);const n=this._target[this._prop],o=s-this._start,r=this._duration-o;this._start=s,this._duration=Math.floor(Math.max(r,t.duration)),this._total+=o,this._loop=!!t.loop,this._to=de([t.to,e,n,t.from]),this._from=de([t.from,n,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const e=t-this._start,s=this._duration,n=this._prop,o=this._from,r=this._loop,a=this._to;let l;if(this._active=o!==a&&(r||e<s),!this._active){this._target[n]=a,this._notify(!0);return}if(e<0){this._target[n]=o;return}l=e/s%2,l=r&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[n]=this._fn(o,a,l)}wait(){const t=this._promises||(this._promises=[]);return new Promise((e,s)=>{t.push({res:e,rej:s})})}_notify(t){const e=t?"res":"rej",s=this._promises||[];for(let n=0;n<s.length;n++)s[n][e]()}}class rn{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!O(t))return;const e=Object.keys(R.animation),s=this._properties;Object.getOwnPropertyNames(t).forEach(n=>{const o=t[n];if(!O(o))return;const r={};for(const a of e)r[a]=o[a];(z(o.properties)&&o.properties||[n]).forEach(a=>{(a===n||!s.has(a))&&s.set(a,r)})})}_animateOptions(t,e){const s=e.options,n=yr(t,s);if(!n)return[];const o=this._createAnimations(n,s);return s.$shared&&xr(t.options.$animations,s).then(()=>{t.options=s},()=>{}),o}_createAnimations(t,e){const s=this._properties,n=[],o=t.$animations||(t.$animations={}),r=Object.keys(e),a=Date.now();let l;for(l=r.length-1;l>=0;--l){const h=r[l];if(h.charAt(0)==="$")continue;if(h==="options"){n.push(...this._animateOptions(t,e));continue}const c=e[h];let d=o[h];const f=s.get(h);if(d)if(f&&d.active()){d.update(f,c,a);continue}else d.cancel();if(!f||!f.duration){t[h]=c;continue}o[h]=d=new _r(f,t,h,c),n.push(d)}return n}update(t,e){if(this._properties.size===0){Object.assign(t,e);return}const s=this._createAnimations(t,e);if(s.length)return ot.add(this._chart,s),!0}}function xr(i,t){const e=[],s=Object.keys(t);for(let n=0;n<s.length;n++){const o=i[s[n]];o&&o.active()&&e.push(o.wait())}return Promise.all(e)}function yr(i,t){if(!t)return;let e=i.options;if(!e){i.options=t;return}return e.$shared&&(i.options=e=Object.assign({},e,{$shared:!1,$animations:{}})),e}function Ui(i,t){const e=i&&i.options||{},s=e.reverse,n=e.min===void 0?t:0,o=e.max===void 0?t:0;return{start:s?o:n,end:s?n:o}}function vr(i,t,e){if(e===!1)return!1;const s=Ui(i,e),n=Ui(t,e);return{top:n.end,right:s.end,bottom:n.start,left:s.start}}function kr(i){let t,e,s,n;return O(i)?(t=i.top,e=i.right,s=i.bottom,n=i.left):t=e=s=n=i,{top:t,right:e,bottom:s,left:n,disabled:i===!1}}function an(i,t){const e=[],s=i._getSortedDatasetMetas(t);let n,o;for(n=0,o=s.length;n<o;++n)e.push(s[n].index);return e}function Xi(i,t,e,s={}){const n=i.keys,o=s.mode==="single";let r,a,l,h;if(t!==null){for(r=0,a=n.length;r<a;++r){if(l=+n[r],l===e){if(s.all)continue;break}h=i.values[l],W(h)&&(o||t===0||It(t)===It(h))&&(t+=h)}return t}}function wr(i){const t=Object.keys(i),e=new Array(t.length);let s,n,o;for(s=0,n=t.length;s<n;++s)o=t[s],e[s]={x:o,y:i[o]};return e}function Ki(i,t){const e=i&&i.options.stacked;return e||e===void 0&&t.stack!==void 0}function Mr(i,t,e){return`${i.id}.${t.id}.${e.stack||e.type}`}function Sr(i){const{min:t,max:e,minDefined:s,maxDefined:n}=i.getUserBounds();return{min:s?t:Number.NEGATIVE_INFINITY,max:n?e:Number.POSITIVE_INFINITY}}function Cr(i,t,e){const s=i[t]||(i[t]={});return s[e]||(s[e]={})}function qi(i,t,e,s){for(const n of t.getMatchingVisibleMetas(s).reverse()){const o=i[n.index];if(e&&o>0||!e&&o<0)return n.index}return null}function Gi(i,t){const{chart:e,_cachedMeta:s}=i,n=e._stacks||(e._stacks={}),{iScale:o,vScale:r,index:a}=s,l=o.axis,h=r.axis,c=Mr(o,r,s),d=t.length;let f;for(let u=0;u<d;++u){const p=t[u],{[l]:m,[h]:g}=p,b=p._stacks||(p._stacks={});f=b[h]=Cr(n,c,m),f[a]=g,f._top=qi(f,r,!0,s.type),f._bottom=qi(f,r,!1,s.type);const _=f._visualValues||(f._visualValues={});_[a]=g}}function $e(i,t){const e=i.scales;return Object.keys(e).filter(s=>e[s].axis===t).shift()}function Pr(i,t){return pt(i,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function Dr(i,t,e){return pt(i,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"})}function Ht(i,t){const e=i.controller.index,s=i.vScale&&i.vScale.axis;if(s){t=t||i._parsed;for(const n of t){const o=n._stacks;if(!o||o[s]===void 0||o[s][e]===void 0)return;delete o[s][e],o[s]._visualValues!==void 0&&o[s]._visualValues[e]!==void 0&&delete o[s]._visualValues[e]}}}const Ye=i=>i==="reset"||i==="none",Zi=(i,t)=>t?i:Object.assign({},i),Or=(i,t,e)=>i&&!t.hidden&&t._stacked&&{keys:an(e,!0),values:null};class we{constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=Ki(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&Ht(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,e=this._cachedMeta,s=this.getDataset(),n=(d,f,u,p)=>d==="x"?f:d==="r"?p:u,o=e.xAxisID=P(s.xAxisID,$e(t,"x")),r=e.yAxisID=P(s.yAxisID,$e(t,"y")),a=e.rAxisID=P(s.rAxisID,$e(t,"r")),l=e.indexAxis,h=e.iAxisID=n(l,o,r,a),c=e.vAxisID=n(l,r,o,a);e.xScale=this.getScaleForId(o),e.yScale=this.getScaleForId(r),e.rScale=this.getScaleForId(a),e.iScale=this.getScaleForId(h),e.vScale=this.getScaleForId(c)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&Ai(this._data,this),t._stacked&&Ht(t)}_dataCheck(){const t=this.getDataset(),e=t.data||(t.data=[]),s=this._data;if(O(e))this._data=wr(e);else if(s!==e){if(s){Ai(s,this);const n=this._cachedMeta;Ht(n),n._parsed=[]}e&&Object.isExtensible(e)&&ho(e,this),this._syncList=[],this._data=e}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const e=this._cachedMeta,s=this.getDataset();let n=!1;this._dataCheck();const o=e._stacked;e._stacked=Ki(e.vScale,e),e.stack!==s.stack&&(n=!0,Ht(e),e.stack=s.stack),this._resyncElements(t),(n||o!==e._stacked)&&Gi(this,e._parsed)}configure(){const t=this.chart.config,e=t.datasetScopeKeys(this._type),s=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(s,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){const{_cachedMeta:s,_data:n}=this,{iScale:o,_stacked:r}=s,a=o.axis;let l=t===0&&e===n.length?!0:s._sorted,h=t>0&&s._parsed[t-1],c,d,f;if(this._parsing===!1)s._parsed=n,s._sorted=!0,f=n;else{z(n[t])?f=this.parseArrayData(s,n,t,e):O(n[t])?f=this.parseObjectData(s,n,t,e):f=this.parsePrimitiveData(s,n,t,e);const u=()=>d[a]===null||h&&d[a]<h[a];for(c=0;c<e;++c)s._parsed[c+t]=d=f[c],l&&(u()&&(l=!1),h=d);s._sorted=l}r&&Gi(this,f)}parsePrimitiveData(t,e,s,n){const{iScale:o,vScale:r}=t,a=o.axis,l=r.axis,h=o.getLabels(),c=o===r,d=new Array(n);let f,u,p;for(f=0,u=n;f<u;++f)p=f+s,d[f]={[a]:c||o.parse(h[p],p),[l]:r.parse(e[p],p)};return d}parseArrayData(t,e,s,n){const{xScale:o,yScale:r}=t,a=new Array(n);let l,h,c,d;for(l=0,h=n;l<h;++l)c=l+s,d=e[c],a[l]={x:o.parse(d[0],c),y:r.parse(d[1],c)};return a}parseObjectData(t,e,s,n){const{xScale:o,yScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,h=new Array(n);let c,d,f,u;for(c=0,d=n;c<d;++c)f=c+s,u=e[f],h[c]={x:o.parse(De(u,a),f),y:r.parse(De(u,l),f)};return h}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,s){const n=this.chart,o=this._cachedMeta,r=e[t.axis],a={keys:an(n,!0),values:e._stacks[t.axis]._visualValues};return Xi(a,r,o.index,{mode:s})}updateRangeFromParsed(t,e,s,n){const o=s[e.axis];let r=o===null?NaN:o;const a=n&&s._stacks[e.axis];n&&a&&(n.values=a,r=Xi(n,o,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(t,e){const s=this._cachedMeta,n=s._parsed,o=s._sorted&&t===s.iScale,r=n.length,a=this._getOtherScale(t),l=Or(e,s,this.chart),h={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:c,max:d}=Sr(a);let f,u;function p(){u=n[f];const m=u[a.axis];return!W(u[t.axis])||c>m||d<m}for(f=0;f<r&&!(!p()&&(this.updateRangeFromParsed(h,t,u,l),o));++f);if(o){for(f=r-1;f>=0;--f)if(!p()){this.updateRangeFromParsed(h,t,u,l);break}}return h}getAllParsedValues(t){const e=this._cachedMeta._parsed,s=[];let n,o,r;for(n=0,o=e.length;n<o;++n)r=e[n][t.axis],W(r)&&s.push(r);return s}getMaxOverflow(){return!1}getLabelAndValue(t){const e=this._cachedMeta,s=e.iScale,n=e.vScale,o=this.getParsed(t);return{label:s?""+s.getLabelForValue(o[s.axis]):"",value:n?""+n.getLabelForValue(o[n.axis]):""}}_update(t){const e=this._cachedMeta;this.update(t||"default"),e._clip=kr(P(this.options.clip,vr(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,e=this.chart,s=this._cachedMeta,n=s.data||[],o=e.chartArea,r=[],a=this._drawStart||0,l=this._drawCount||n.length-a,h=this.options.drawActiveElementsOnTop;let c;for(s.dataset&&s.dataset.draw(t,o,a,l),c=a;c<a+l;++c){const d=n[c];d.hidden||(d.active&&h?r.push(d):d.draw(t,o))}for(c=0;c<r.length;++c)r[c].draw(t,o)}getStyle(t,e){const s=e?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(s):this.resolveDataElementOptions(t||0,s)}getContext(t,e,s){const n=this.getDataset();let o;if(t>=0&&t<this._cachedMeta.data.length){const r=this._cachedMeta.data[t];o=r.$context||(r.$context=Dr(this.getContext(),t,r)),o.parsed=this.getParsed(t),o.raw=n.data[t],o.index=o.dataIndex=t}else o=this.$context||(this.$context=Pr(this.chart.getContext(),this.index)),o.dataset=n,o.index=o.datasetIndex=this.index;return o.active=!!e,o.mode=s,o}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",s){const n=e==="active",o=this._cachedDataOpts,r=t+"-"+e,a=o[r],l=this.enableOptionSharing&&Oe(s);if(a)return Zi(a,l);const h=this.chart.config,c=h.datasetElementScopeKeys(this._type,t),d=n?[`${t}Hover`,"hover",t,""]:[t,""],f=h.getOptionScopes(this.getDataset(),c),u=Object.keys(R.elements[t]),p=()=>this.getContext(s,n,e),m=h.resolveNamedOptions(f,u,p,d);return m.$shared&&(m.$shared=l,o[r]=Object.freeze(Zi(m,l))),m}_resolveAnimations(t,e,s){const n=this.chart,o=this._cachedDataOpts,r=`animation-${e}`,a=o[r];if(a)return a;let l;if(n.options.animation!==!1){const c=this.chart.config,d=c.datasetAnimationScopeKeys(this._type,e),f=c.getOptionScopes(this.getDataset(),d);l=c.createResolver(f,this.getContext(t,s,e))}const h=new rn(n,l&&l.animations);return l&&l._cacheable&&(o[r]=Object.freeze(h)),h}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||Ye(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){const s=this.resolveDataElementOptions(t,e),n=this._sharedOptions,o=this.getSharedOptions(s),r=this.includeOptions(e,o)||o!==n;return this.updateSharedOptions(o,e,s),{sharedOptions:o,includeOptions:r}}updateElement(t,e,s,n){Ye(n)?Object.assign(t,s):this._resolveAnimations(e,n).update(t,s)}updateSharedOptions(t,e,s){t&&!Ye(e)&&this._resolveAnimations(void 0,e).update(t,s)}_setStyle(t,e,s,n){t.active=n;const o=this.getStyle(e,n);this._resolveAnimations(e,s,n).update(t,{options:!n&&this.getSharedOptions(o)||o})}removeHoverStyle(t,e,s){this._setStyle(t,s,"active",!1)}setHoverStyle(t,e,s){this._setStyle(t,s,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const e=this._data,s=this._cachedMeta.data;for(const[a,l,h]of this._syncList)this[a](l,h);this._syncList=[];const n=s.length,o=e.length,r=Math.min(o,n);r&&this.parse(0,r),o>n?this._insertElements(n,o-n,t):o<n&&this._removeElements(o,n-o)}_insertElements(t,e,s=!0){const n=this._cachedMeta,o=n.data,r=t+e;let a;const l=h=>{for(h.length+=e,a=h.length-1;a>=r;a--)h[a]=h[a-e]};for(l(o),a=t;a<r;++a)o[a]=new this.dataElementType;this._parsing&&l(n._parsed),this.parse(t,e),s&&this.updateElements(o,t,e,"reset")}updateElements(t,e,s,n){}_removeElements(t,e){const s=this._cachedMeta;if(this._parsing){const n=s._parsed.splice(t,e);s._stacked&&Ht(s,n)}s.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[e,s,n]=t;this[e](s,n)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);const s=arguments.length-2;s&&this._sync(["_insertElements",t,s])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}S(we,"defaults",{}),S(we,"datasetElementType",null),S(we,"dataElementType",null);function yt(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class ki{constructor(t){S(this,"options");this.options=t||{}}static override(t){Object.assign(ki.prototype,t)}init(){}formats(){return yt()}parse(){return yt()}format(){return yt()}add(){return yt()}diff(){return yt()}startOf(){return yt()}endOf(){return yt()}}var Tr={_date:ki};function Lr(i,t,e,s){const{controller:n,data:o,_sorted:r}=i,a=n._cachedMeta.iScale;if(a&&t===a.axis&&t!=="r"&&r&&o.length){const l=a._reversePixels?ao:Qe;if(s){if(n._sharedOptions){const h=o[0],c=typeof h.getRange=="function"&&h.getRange(t);if(c){const d=l(o,t,e-c),f=l(o,t,e+c);return{lo:d.lo,hi:f.hi}}}}else return l(o,t,e)}return{lo:0,hi:o.length-1}}function se(i,t,e,s,n){const o=i.getSortedVisibleDatasetMetas(),r=e[t];for(let a=0,l=o.length;a<l;++a){const{index:h,data:c}=o[a],{lo:d,hi:f}=Lr(o[a],t,r,n);for(let u=d;u<=f;++u){const p=c[u];p.skip||s(p,h,u)}}}function Ar(i){const t=i.indexOf("x")!==-1,e=i.indexOf("y")!==-1;return function(s,n){const o=t?Math.abs(s.x-n.x):0,r=e?Math.abs(s.y-n.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(r,2))}}function Ue(i,t,e,s,n){const o=[];return!n&&!i.isPointInArea(t)||se(i,e,t,function(a,l,h){!n&&!lt(a,i.chartArea,0)||a.inRange(t.x,t.y,s)&&o.push({element:a,datasetIndex:l,index:h})},!0),o}function Fr(i,t,e,s){let n=[];function o(r,a,l){const{startAngle:h,endAngle:c}=r.getProps(["startAngle","endAngle"],s),{angle:d}=no(r,{x:t.x,y:t.y});$s(d,h,c)&&n.push({element:r,datasetIndex:a,index:l})}return se(i,e,t,o),n}function Ir(i,t,e,s,n,o){let r=[];const a=Ar(e);let l=Number.POSITIVE_INFINITY;function h(c,d,f){const u=c.inRange(t.x,t.y,n);if(s&&!u)return;const p=c.getCenterPoint(n);if(!(!!o||i.isPointInArea(p))&&!u)return;const g=a(t,p);g<l?(r=[{element:c,datasetIndex:d,index:f}],l=g):g===l&&r.push({element:c,datasetIndex:d,index:f})}return se(i,e,t,h),r}function Xe(i,t,e,s,n,o){return!o&&!i.isPointInArea(t)?[]:e==="r"&&!s?Fr(i,t,e,n):Ir(i,t,e,s,n,o)}function Qi(i,t,e,s,n){const o=[],r=e==="x"?"inXRange":"inYRange";let a=!1;return se(i,e,t,(l,h,c)=>{l[r](t[e],n)&&(o.push({element:l,datasetIndex:h,index:c}),a=a||l.inRange(t.x,t.y,n))}),s&&!a?[]:o}var zr={evaluateInteractionItems:se,modes:{index(i,t,e,s){const n=kt(t,i),o=e.axis||"x",r=e.includeInvisible||!1,a=e.intersect?Ue(i,n,o,s,r):Xe(i,n,o,!1,s,r),l=[];return a.length?(i.getSortedVisibleDatasetMetas().forEach(h=>{const c=a[0].index,d=h.data[c];d&&!d.skip&&l.push({element:d,datasetIndex:h.index,index:c})}),l):[]},dataset(i,t,e,s){const n=kt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;let a=e.intersect?Ue(i,n,o,s,r):Xe(i,n,o,!1,s,r);if(a.length>0){const l=a[0].datasetIndex,h=i.getDatasetMeta(l).data;a=[];for(let c=0;c<h.length;++c)a.push({element:h[c],datasetIndex:l,index:c})}return a},point(i,t,e,s){const n=kt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;return Ue(i,n,o,s,r)},nearest(i,t,e,s){const n=kt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;return Xe(i,n,o,e.intersect,s,r)},x(i,t,e,s){const n=kt(t,i);return Qi(i,n,"x",e.intersect,s)},y(i,t,e,s){const n=kt(t,i);return Qi(i,n,"y",e.intersect,s)}}};const ln=["left","top","right","bottom"];function Wt(i,t){return i.filter(e=>e.pos===t)}function Ji(i,t){return i.filter(e=>ln.indexOf(e.pos)===-1&&e.box.axis===t)}function Nt(i,t){return i.sort((e,s)=>{const n=t?s:e,o=t?e:s;return n.weight===o.weight?n.index-o.index:n.weight-o.weight})}function Rr(i){const t=[];let e,s,n,o,r,a;for(e=0,s=(i||[]).length;e<s;++e)n=i[e],{position:o,options:{stack:r,stackWeight:a=1}}=n,t.push({index:e,box:n,pos:o,horizontal:n.isHorizontal(),weight:n.weight,stack:r&&o+r,stackWeight:a});return t}function Er(i){const t={};for(const e of i){const{stack:s,pos:n,stackWeight:o}=e;if(!s||!ln.includes(n))continue;const r=t[s]||(t[s]={count:0,placed:0,weight:0,size:0});r.count++,r.weight+=o}return t}function Br(i,t){const e=Er(i),{vBoxMaxWidth:s,hBoxMaxHeight:n}=t;let o,r,a;for(o=0,r=i.length;o<r;++o){a=i[o];const{fullSize:l}=a.box,h=e[a.stack],c=h&&a.stackWeight/h.weight;a.horizontal?(a.width=c?c*s:l&&t.availableWidth,a.height=n):(a.width=s,a.height=c?c*n:l&&t.availableHeight)}return e}function Hr(i){const t=Rr(i),e=Nt(t.filter(h=>h.box.fullSize),!0),s=Nt(Wt(t,"left"),!0),n=Nt(Wt(t,"right")),o=Nt(Wt(t,"top"),!0),r=Nt(Wt(t,"bottom")),a=Ji(t,"x"),l=Ji(t,"y");return{fullSize:e,leftAndTop:s.concat(o),rightAndBottom:n.concat(l).concat(r).concat(a),chartArea:Wt(t,"chartArea"),vertical:s.concat(n).concat(l),horizontal:o.concat(r).concat(a)}}function ts(i,t,e,s){return Math.max(i[e],t[e])+Math.max(i[s],t[s])}function hn(i,t){i.top=Math.max(i.top,t.top),i.left=Math.max(i.left,t.left),i.bottom=Math.max(i.bottom,t.bottom),i.right=Math.max(i.right,t.right)}function Wr(i,t,e,s){const{pos:n,box:o}=e,r=i.maxPadding;if(!O(n)){e.size&&(i[n]-=e.size);const d=s[e.stack]||{size:0,count:1};d.size=Math.max(d.size,e.horizontal?o.height:o.width),e.size=d.size/d.count,i[n]+=e.size}o.getPadding&&hn(r,o.getPadding());const a=Math.max(0,t.outerWidth-ts(r,i,"left","right")),l=Math.max(0,t.outerHeight-ts(r,i,"top","bottom")),h=a!==i.w,c=l!==i.h;return i.w=a,i.h=l,e.horizontal?{same:h,other:c}:{same:c,other:h}}function Nr(i){const t=i.maxPadding;function e(s){const n=Math.max(t[s]-i[s],0);return i[s]+=n,n}i.y+=e("top"),i.x+=e("left"),e("right"),e("bottom")}function jr(i,t){const e=t.maxPadding;function s(n){const o={left:0,top:0,right:0,bottom:0};return n.forEach(r=>{o[r]=Math.max(t[r],e[r])}),o}return s(i?["left","right"]:["top","bottom"])}function Ut(i,t,e,s){const n=[];let o,r,a,l,h,c;for(o=0,r=i.length,h=0;o<r;++o){a=i[o],l=a.box,l.update(a.width||t.w,a.height||t.h,jr(a.horizontal,t));const{same:d,other:f}=Wr(t,e,a,s);h|=d&&n.length,c=c||f,l.fullSize||n.push(a)}return h&&Ut(n,t,e,s)||c}function ge(i,t,e,s,n){i.top=e,i.left=t,i.right=t+s,i.bottom=e+n,i.width=s,i.height=n}function es(i,t,e,s){const n=e.padding;let{x:o,y:r}=t;for(const a of i){const l=a.box,h=s[a.stack]||{count:1,placed:0,weight:1},c=a.stackWeight/h.weight||1;if(a.horizontal){const d=t.w*c,f=h.size||l.height;Oe(h.start)&&(r=h.start),l.fullSize?ge(l,n.left,r,e.outerWidth-n.right-n.left,f):ge(l,t.left+h.placed,r,d,f),h.start=r,h.placed+=d,r=l.bottom}else{const d=t.h*c,f=h.size||l.width;Oe(h.start)&&(o=h.start),l.fullSize?ge(l,o,n.top,f,e.outerHeight-n.bottom-n.top):ge(l,o,t.top+h.placed,f,d),h.start=o,h.placed+=d,o=l.right}}t.x=o,t.y=r}var Z={addBox(i,t){i.boxes||(i.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(e){t.draw(e)}}]},i.boxes.push(t)},removeBox(i,t){const e=i.boxes?i.boxes.indexOf(t):-1;e!==-1&&i.boxes.splice(e,1)},configure(i,t,e){t.fullSize=e.fullSize,t.position=e.position,t.weight=e.weight},update(i,t,e,s){if(!i)return;const n=V(i.options.layout.padding),o=Math.max(t-n.width,0),r=Math.max(e-n.height,0),a=Hr(i.boxes),l=a.vertical,h=a.horizontal;L(i.boxes,m=>{typeof m.beforeLayout=="function"&&m.beforeLayout()});const c=l.reduce((m,g)=>g.box.options&&g.box.options.display===!1?m:m+1,0)||1,d=Object.freeze({outerWidth:t,outerHeight:e,padding:n,availableWidth:o,availableHeight:r,vBoxMaxWidth:o/2/c,hBoxMaxHeight:r/2}),f=Object.assign({},n);hn(f,V(s));const u=Object.assign({maxPadding:f,w:o,h:r,x:n.left,y:n.top},n),p=Br(l.concat(h),d);Ut(a.fullSize,u,d,p),Ut(l,u,d,p),Ut(h,u,d,p)&&Ut(l,u,d,p),Nr(u),es(a.leftAndTop,u,d,p),u.x+=u.w,u.y+=u.h,es(a.rightAndBottom,u,d,p),i.chartArea={left:u.left,top:u.top,right:u.left+u.w,bottom:u.top+u.h,height:u.h,width:u.w},L(a.chartArea,m=>{const g=m.box;Object.assign(g,i.chartArea),g.update(u.w,u.h,{left:0,top:0,right:0,bottom:0})})}};class cn{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,s){}removeEventListener(t,e,s){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,s,n){return e=Math.max(0,e||t.width),s=s||t.height,{width:e,height:Math.max(0,n?Math.floor(e/n):s)}}isAttached(t){return!0}updateConfig(t){}}class Vr extends cn{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const Me="$chartjs",$r={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},is=i=>i===null||i==="";function Yr(i,t){const e=i.style,s=i.getAttribute("height"),n=i.getAttribute("width");if(i[Me]={initial:{height:s,width:n,style:{display:e.display,height:e.height,width:e.width}}},e.display=e.display||"block",e.boxSizing=e.boxSizing||"border-box",is(n)){const o=Ni(i,"width");o!==void 0&&(i.width=o)}if(is(s))if(i.style.height==="")i.height=i.width/(t||2);else{const o=Ni(i,"height");o!==void 0&&(i.height=o)}return i}const dn=sr?{passive:!0}:!1;function Ur(i,t,e){i&&i.addEventListener(t,e,dn)}function Xr(i,t,e){i&&i.canvas&&i.canvas.removeEventListener(t,e,dn)}function Kr(i,t){const e=$r[i.type]||i.type,{x:s,y:n}=kt(i,t);return{type:e,chart:t,native:i,x:s!==void 0?s:null,y:n!==void 0?n:null}}function ze(i,t){for(const e of i)if(e===t||e.contains(t))return!0}function qr(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||ze(a.addedNodes,s),r=r&&!ze(a.removedNodes,s);r&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}function Gr(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||ze(a.removedNodes,s),r=r&&!ze(a.addedNodes,s);r&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}const te=new Map;let ss=0;function fn(){const i=window.devicePixelRatio;i!==ss&&(ss=i,te.forEach((t,e)=>{e.currentDevicePixelRatio!==i&&t()}))}function Zr(i,t){te.size||window.addEventListener("resize",fn),te.set(i,t)}function Qr(i){te.delete(i),te.size||window.removeEventListener("resize",fn)}function Jr(i,t,e){const s=i.canvas,n=s&&vi(s);if(!n)return;const o=Xs((a,l)=>{const h=n.clientWidth;e(a,l),h<n.clientWidth&&e()},window),r=new ResizeObserver(a=>{const l=a[0],h=l.contentRect.width,c=l.contentRect.height;h===0&&c===0||o(h,c)});return r.observe(n),Zr(i,o),r}function Ke(i,t,e){e&&e.disconnect(),t==="resize"&&Qr(i)}function ta(i,t,e){const s=i.canvas,n=Xs(o=>{i.ctx!==null&&e(Kr(o,i))},i);return Ur(s,t,n),n}class ea extends cn{acquireContext(t,e){const s=t&&t.getContext&&t.getContext("2d");return s&&s.canvas===t?(Yr(t,e),s):null}releaseContext(t){const e=t.canvas;if(!e[Me])return!1;const s=e[Me].initial;["height","width"].forEach(o=>{const r=s[o];I(r)?e.removeAttribute(o):e.setAttribute(o,r)});const n=s.style||{};return Object.keys(n).forEach(o=>{e.style[o]=n[o]}),e.width=e.width,delete e[Me],!0}addEventListener(t,e,s){this.removeEventListener(t,e);const n=t.$proxies||(t.$proxies={}),r={attach:qr,detach:Gr,resize:Jr}[e]||ta;n[e]=r(t,e,s)}removeEventListener(t,e){const s=t.$proxies||(t.$proxies={}),n=s[e];if(!n)return;({attach:Ke,detach:Ke,resize:Ke}[e]||Xr)(t,e,n),s[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,s,n){return ir(t,e,s,n)}isAttached(t){const e=vi(t);return!!(e&&e.isConnected)}}function ia(i){return!yi()||typeof OffscreenCanvas<"u"&&i instanceof OffscreenCanvas?Vr:ea}class ht{constructor(){S(this,"x");S(this,"y");S(this,"active",!1);S(this,"options");S(this,"$animations")}tooltipPosition(t){const{x:e,y:s}=this.getProps(["x","y"],t);return{x:e,y:s}}hasValue(){return Le(this.x)&&Le(this.y)}getProps(t,e){const s=this.$animations;if(!e||!s)return this;const n={};return t.forEach(o=>{n[o]=s[o]&&s[o].active()?s[o]._to:this[o]}),n}}S(ht,"defaults",{}),S(ht,"defaultRoutes");function sa(i,t){const e=i.options.ticks,s=na(i),n=Math.min(e.maxTicksLimit||s,s),o=e.major.enabled?ra(t):[],r=o.length,a=o[0],l=o[r-1],h=[];if(r>n)return aa(t,h,o,r/n),h;const c=oa(o,t,n);if(r>0){let d,f;const u=r>1?Math.round((l-a)/(r-1)):null;for(pe(t,h,c,I(u)?0:a-u,a),d=0,f=r-1;d<f;d++)pe(t,h,c,o[d],o[d+1]);return pe(t,h,c,l,I(u)?t.length:l+u),h}return pe(t,h,c),h}function na(i){const t=i.options.offset,e=i._tickSize(),s=i._length/e+(t?0:1),n=i._maxLength/e;return Math.floor(Math.min(s,n))}function oa(i,t,e){const s=la(i),n=t.length/e;if(!s)return Math.max(n,1);const o=io(s);for(let r=0,a=o.length-1;r<a;r++){const l=o[r];if(l>n)return l}return Math.max(n,1)}function ra(i){const t=[];let e,s;for(e=0,s=i.length;e<s;e++)i[e].major&&t.push(e);return t}function aa(i,t,e,s){let n=0,o=e[0],r;for(s=Math.ceil(s),r=0;r<i.length;r++)r===o&&(t.push(i[r]),n++,o=e[n*s])}function pe(i,t,e,s,n){const o=P(s,0),r=Math.min(P(n,i.length),i.length);let a=0,l,h,c;for(e=Math.ceil(e),n&&(l=n-s,e=l/Math.floor(l/e)),c=o;c<0;)a++,c=Math.round(o+a*e);for(h=Math.max(o,0);h<r;h++)h===c&&(t.push(i[h]),a++,c=Math.round(o+a*e))}function la(i){const t=i.length;let e,s;if(t<2)return!1;for(s=i[0],e=1;e<t;++e)if(i[e]-i[e-1]!==s)return!1;return s}const ha=i=>i==="left"?"right":i==="right"?"left":i,ns=(i,t,e)=>t==="top"||t==="left"?i[t]+e:i[t]-e,os=(i,t)=>Math.min(t||i,i);function rs(i,t){const e=[],s=i.length/t,n=i.length;let o=0;for(;o<n;o+=s)e.push(i[Math.floor(o)]);return e}function ca(i,t,e){const s=i.ticks.length,n=Math.min(t,s-1),o=i._startPixel,r=i._endPixel,a=1e-6;let l=i.getPixelForTick(n),h;if(!(e&&(s===1?h=Math.max(l-o,r-l):t===0?h=(i.getPixelForTick(1)-l)/2:h=(l-i.getPixelForTick(n-1))/2,l+=n<t?h:-h,l<o-a||l>r+a)))return l}function da(i,t){L(i,e=>{const s=e.gc,n=s.length/2;let o;if(n>t){for(o=0;o<n;++o)delete e.data[s[o]];s.splice(0,n)}})}function jt(i){return i.drawTicks?i.tickLength:0}function as(i,t){if(!i.display)return 0;const e=H(i.font,t),s=V(i.padding);return(z(i.text)?i.text.length:1)*e.lineHeight+s.height}function fa(i,t){return pt(i,{scale:t,type:"scale"})}function ua(i,t,e){return pt(i,{tick:e,index:t,type:"tick"})}function ga(i,t,e){let s=fi(i);return(e&&t!=="right"||!e&&t==="right")&&(s=ha(s)),s}function pa(i,t,e,s){const{top:n,left:o,bottom:r,right:a,chart:l}=i,{chartArea:h,scales:c}=l;let d=0,f,u,p;const m=r-n,g=a-o;if(i.isHorizontal()){if(u=j(s,o,a),O(e)){const b=Object.keys(e)[0],_=e[b];p=c[b].getPixelForValue(_)+m-t}else e==="center"?p=(h.bottom+h.top)/2+m-t:p=ns(i,e,t);f=a-o}else{if(O(e)){const b=Object.keys(e)[0],_=e[b];u=c[b].getPixelForValue(_)-g+t}else e==="center"?u=(h.left+h.right)/2-g+t:u=ns(i,e,t);p=j(s,r,n),d=e==="left"?-K:K}return{titleX:u,titleY:p,maxWidth:f,rotation:d}}class Dt extends ht{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:s,_suggestedMax:n}=this;return t=X(t,Number.POSITIVE_INFINITY),e=X(e,Number.NEGATIVE_INFINITY),s=X(s,Number.POSITIVE_INFINITY),n=X(n,Number.NEGATIVE_INFINITY),{min:X(t,s),max:X(e,n),minDefined:W(t),maxDefined:W(e)}}getMinMax(t){let{min:e,max:s,minDefined:n,maxDefined:o}=this.getUserBounds(),r;if(n&&o)return{min:e,max:s};const a=this.getMatchingVisibleMetas();for(let l=0,h=a.length;l<h;++l)r=a[l].controller.getMinMax(this,t),n||(e=Math.min(e,r.min)),o||(s=Math.max(s,r.max));return e=o&&e>s?s:e,s=n&&e>s?e:s,{min:X(e,X(s,e)),max:X(s,X(e,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){F(this.options.beforeUpdate,[this])}update(t,e,s){const{beginAtZero:n,grace:o,ticks:r}=this.options,a=r.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=s=Object.assign({left:0,right:0,top:0,bottom:0},s),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+s.left+s.right:this.height+s.top+s.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=Io(this,o,n),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?rs(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||r.source==="auto")&&(this.ticks=sa(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,e,s;this.isHorizontal()?(e=this.left,s=this.right):(e=this.top,s=this.bottom,t=!t),this._startPixel=e,this._endPixel=s,this._reversePixels=t,this._length=s-e,this._alignToPixels=this.options.alignToPixels}afterUpdate(){F(this.options.afterUpdate,[this])}beforeSetDimensions(){F(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){F(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),F(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){F(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const e=this.options.ticks;let s,n,o;for(s=0,n=t.length;s<n;s++)o=t[s],o.label=F(e.callback,[o.value,s,t],this)}afterTickToLabelConversion(){F(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){F(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,e=t.ticks,s=os(this.ticks.length,t.ticks.maxTicksLimit),n=e.minRotation||0,o=e.maxRotation;let r=n,a,l,h;if(!this._isVisible()||!e.display||n>=o||s<=1||!this.isHorizontal()){this.labelRotation=n;return}const c=this._getLabelSizes(),d=c.widest.width,f=c.highest.height,u=st(this.chart.width-d,0,this.maxWidth);a=t.offset?this.maxWidth/s:u/(s-1),d+6>a&&(a=u/(s-(t.offset?.5:1)),l=this.maxHeight-jt(t.grid)-e.padding-as(t.title,this.chart.options.font),h=Math.sqrt(d*d+f*f),r=ci(Math.min(Math.asin(st((c.highest.height+6)/a,-1,1)),Math.asin(st(l/h,-1,1))-Math.asin(st(f/h,-1,1)))),r=Math.max(n,Math.min(o,r))),this.labelRotation=r}afterCalculateLabelRotation(){F(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){F(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:e,options:{ticks:s,title:n,grid:o}}=this,r=this._isVisible(),a=this.isHorizontal();if(r){const l=as(n,e.options.font);if(a?(t.width=this.maxWidth,t.height=jt(o)+l):(t.height=this.maxHeight,t.width=jt(o)+l),s.display&&this.ticks.length){const{first:h,last:c,widest:d,highest:f}=this._getLabelSizes(),u=s.padding*2,p=ft(this.labelRotation),m=Math.cos(p),g=Math.sin(p);if(a){const b=s.mirror?0:g*d.width+m*f.height;t.height=Math.min(this.maxHeight,t.height+b+u)}else{const b=s.mirror?0:m*d.width+g*f.height;t.width=Math.min(this.maxWidth,t.width+b+u)}this._calculatePadding(h,c,g,m)}}this._handleMargins(),a?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,s,n){const{ticks:{align:o,padding:r},position:a}=this.options,l=this.labelRotation!==0,h=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const c=this.getPixelForTick(0)-this.left,d=this.right-this.getPixelForTick(this.ticks.length-1);let f=0,u=0;l?h?(f=n*t.width,u=s*e.height):(f=s*t.height,u=n*e.width):o==="start"?u=e.width:o==="end"?f=t.width:o!=="inner"&&(f=t.width/2,u=e.width/2),this.paddingLeft=Math.max((f-c+r)*this.width/(this.width-c),0),this.paddingRight=Math.max((u-d+r)*this.width/(this.width-d),0)}else{let c=e.height/2,d=t.height/2;o==="start"?(c=0,d=t.height):o==="end"&&(c=e.height,d=0),this.paddingTop=c+r,this.paddingBottom=d+r}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){F(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:e}=this.options;return e==="top"||e==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let e,s;for(e=0,s=t.length;e<s;e++)I(t[e].label)&&(t.splice(e,1),s--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const e=this.options.ticks.sampleSize;let s=this.ticks;e<s.length&&(s=rs(s,e)),this._labelSizes=t=this._computeLabelSizes(s,s.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,s){const{ctx:n,_longestTextCache:o}=this,r=[],a=[],l=Math.floor(e/os(e,s));let h=0,c=0,d,f,u,p,m,g,b,_,y,v,x;for(d=0;d<e;d+=l){if(p=t[d].label,m=this._resolveTickFontOptions(d),n.font=g=m.string,b=o[g]=o[g]||{data:{},gc:[]},_=m.lineHeight,y=v=0,!I(p)&&!z(p))y=Ae(n,b.data,b.gc,y,p),v=_;else if(z(p))for(f=0,u=p.length;f<u;++f)x=p[f],!I(x)&&!z(x)&&(y=Ae(n,b.data,b.gc,y,x),v+=_);r.push(y),a.push(v),h=Math.max(y,h),c=Math.max(v,c)}da(o,e);const w=r.indexOf(h),M=a.indexOf(c),k=C=>({width:r[C]||0,height:a[C]||0});return{first:k(0),last:k(e-1),widest:k(w),highest:k(M),widths:r,heights:a}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const e=this._startPixel+t*this._length;return ro(this._alignToPixels?xt(this.chart,e,0):e)}getDecimalForPixel(t){const e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){const e=this.ticks||[];if(t>=0&&t<e.length){const s=e[t];return s.$context||(s.$context=ua(this.getContext(),t,s))}return this.$context||(this.$context=fa(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks,e=ft(this.labelRotation),s=Math.abs(Math.cos(e)),n=Math.abs(Math.sin(e)),o=this._getLabelSizes(),r=t.autoSkipPadding||0,a=o?o.widest.width+r:0,l=o?o.highest.height+r:0;return this.isHorizontal()?l*s>a*n?a/s:l/n:l*n<a*s?l/s:a/n}_isVisible(){const t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const e=this.axis,s=this.chart,n=this.options,{grid:o,position:r,border:a}=n,l=o.offset,h=this.isHorizontal(),d=this.ticks.length+(l?1:0),f=jt(o),u=[],p=a.setContext(this.getContext()),m=p.display?p.width:0,g=m/2,b=function(N){return xt(s,N,m)};let _,y,v,x,w,M,k,C,A,D,T,$;if(r==="top")_=b(this.bottom),M=this.bottom-f,C=_-g,D=b(t.top)+g,$=t.bottom;else if(r==="bottom")_=b(this.top),D=t.top,$=b(t.bottom)-g,M=_+g,C=this.top+f;else if(r==="left")_=b(this.right),w=this.right-f,k=_-g,A=b(t.left)+g,T=t.right;else if(r==="right")_=b(this.left),A=t.left,T=b(t.right)-g,w=_+g,k=this.left+f;else if(e==="x"){if(r==="center")_=b((t.top+t.bottom)/2+.5);else if(O(r)){const N=Object.keys(r)[0],q=r[N];_=b(this.chart.scales[N].getPixelForValue(q))}D=t.top,$=t.bottom,M=_+g,C=M+f}else if(e==="y"){if(r==="center")_=b((t.left+t.right)/2);else if(O(r)){const N=Object.keys(r)[0],q=r[N];_=b(this.chart.scales[N].getPixelForValue(q))}w=_-g,k=w-f,A=t.left,T=t.right}const tt=P(n.ticks.maxTicksLimit,d),E=Math.max(1,Math.ceil(d/tt));for(y=0;y<d;y+=E){const N=this.getContext(y),q=o.setContext(N),ne=a.setContext(N),oe=q.lineWidth,Ot=q.color,re=ne.dash||[],Tt=ne.dashOffset,Et=q.tickWidth,mt=q.tickColor,Bt=q.tickBorderDash||[],bt=q.tickBorderDashOffset;v=ca(this,y,l),v!==void 0&&(x=xt(s,v,oe),h?w=k=A=T=x:M=C=D=$=x,u.push({tx1:w,ty1:M,tx2:k,ty2:C,x1:A,y1:D,x2:T,y2:$,width:oe,color:Ot,borderDash:re,borderDashOffset:Tt,tickWidth:Et,tickColor:mt,tickBorderDash:Bt,tickBorderDashOffset:bt}))}return this._ticksLength=d,this._borderValue=_,u}_computeLabelItems(t){const e=this.axis,s=this.options,{position:n,ticks:o}=s,r=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:h,padding:c,mirror:d}=o,f=jt(s.grid),u=f+c,p=d?-c:u,m=-ft(this.labelRotation),g=[];let b,_,y,v,x,w,M,k,C,A,D,T,$="middle";if(n==="top")w=this.bottom-p,M=this._getXAxisLabelAlignment();else if(n==="bottom")w=this.top+p,M=this._getXAxisLabelAlignment();else if(n==="left"){const E=this._getYAxisLabelAlignment(f);M=E.textAlign,x=E.x}else if(n==="right"){const E=this._getYAxisLabelAlignment(f);M=E.textAlign,x=E.x}else if(e==="x"){if(n==="center")w=(t.top+t.bottom)/2+u;else if(O(n)){const E=Object.keys(n)[0],N=n[E];w=this.chart.scales[E].getPixelForValue(N)+u}M=this._getXAxisLabelAlignment()}else if(e==="y"){if(n==="center")x=(t.left+t.right)/2-u;else if(O(n)){const E=Object.keys(n)[0],N=n[E];x=this.chart.scales[E].getPixelForValue(N)}M=this._getYAxisLabelAlignment(f).textAlign}e==="y"&&(l==="start"?$="top":l==="end"&&($="bottom"));const tt=this._getLabelSizes();for(b=0,_=a.length;b<_;++b){y=a[b],v=y.label;const E=o.setContext(this.getContext(b));k=this.getPixelForTick(b)+o.labelOffset,C=this._resolveTickFontOptions(b),A=C.lineHeight,D=z(v)?v.length:1;const N=D/2,q=E.color,ne=E.textStrokeColor,oe=E.textStrokeWidth;let Ot=M;r?(x=k,M==="inner"&&(b===_-1?Ot=this.options.reverse?"left":"right":b===0?Ot=this.options.reverse?"right":"left":Ot="center"),n==="top"?h==="near"||m!==0?T=-D*A+A/2:h==="center"?T=-tt.highest.height/2-N*A+A:T=-tt.highest.height+A/2:h==="near"||m!==0?T=A/2:h==="center"?T=tt.highest.height/2-N*A:T=tt.highest.height-D*A,d&&(T*=-1),m!==0&&!E.showLabelBackdrop&&(x+=A/2*Math.sin(m))):(w=k,T=(1-D)*A/2);let re;if(E.showLabelBackdrop){const Tt=V(E.backdropPadding),Et=tt.heights[b],mt=tt.widths[b];let Bt=T-Tt.top,bt=0-Tt.left;switch($){case"middle":Bt-=Et/2;break;case"bottom":Bt-=Et;break}switch(M){case"center":bt-=mt/2;break;case"right":bt-=mt;break;case"inner":b===_-1?bt-=mt:b>0&&(bt-=mt/2);break}re={left:bt,top:Bt,width:mt+Tt.width,height:Et+Tt.height,color:E.backdropColor}}g.push({label:v,font:C,textOffset:T,options:{rotation:m,color:q,strokeColor:ne,strokeWidth:oe,textAlign:Ot,textBaseline:$,translation:[x,w],backdrop:re}})}return g}_getXAxisLabelAlignment(){const{position:t,ticks:e}=this.options;if(-ft(this.labelRotation))return t==="top"?"left":"right";let n="center";return e.align==="start"?n="left":e.align==="end"?n="right":e.align==="inner"&&(n="inner"),n}_getYAxisLabelAlignment(t){const{position:e,ticks:{crossAlign:s,mirror:n,padding:o}}=this.options,r=this._getLabelSizes(),a=t+o,l=r.widest.width;let h,c;return e==="left"?n?(c=this.right+o,s==="near"?h="left":s==="center"?(h="center",c+=l/2):(h="right",c+=l)):(c=this.right-a,s==="near"?h="right":s==="center"?(h="center",c-=l/2):(h="left",c=this.left)):e==="right"?n?(c=this.left+o,s==="near"?h="right":s==="center"?(h="center",c-=l/2):(h="left",c-=l)):(c=this.left+a,s==="near"?h="left":s==="center"?(h="center",c+=l/2):(h="right",c=this.right)):h="right",{textAlign:h,x:c}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,e=this.options.position;if(e==="left"||e==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(e==="top"||e==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){const{ctx:t,options:{backgroundColor:e},left:s,top:n,width:o,height:r}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(s,n,o,r),t.restore())}getLineWidthForValue(t){const e=this.options.grid;if(!this._isVisible()||!e.display)return 0;const n=this.ticks.findIndex(o=>o.value===t);return n>=0?e.setContext(this.getContext(n)).lineWidth:0}drawGrid(t){const e=this.options.grid,s=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let o,r;const a=(l,h,c)=>{!c.width||!c.color||(s.save(),s.lineWidth=c.width,s.strokeStyle=c.color,s.setLineDash(c.borderDash||[]),s.lineDashOffset=c.borderDashOffset,s.beginPath(),s.moveTo(l.x,l.y),s.lineTo(h.x,h.y),s.stroke(),s.restore())};if(e.display)for(o=0,r=n.length;o<r;++o){const l=n[o];e.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),e.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:e,options:{border:s,grid:n}}=this,o=s.setContext(this.getContext()),r=s.display?o.width:0;if(!r)return;const a=n.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let h,c,d,f;this.isHorizontal()?(h=xt(t,this.left,r)-r/2,c=xt(t,this.right,a)+a/2,d=f=l):(d=xt(t,this.top,r)-r/2,f=xt(t,this.bottom,a)+a/2,h=c=l),e.save(),e.lineWidth=o.width,e.strokeStyle=o.color,e.beginPath(),e.moveTo(h,d),e.lineTo(c,f),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;const s=this.ctx,n=this._computeLabelArea();n&&pi(s,n);const o=this.getLabelItems(t);for(const r of o){const a=r.options,l=r.font,h=r.label,c=r.textOffset;Pt(s,h,0,c,l,a)}n&&mi(s)}drawTitle(){const{ctx:t,options:{position:e,title:s,reverse:n}}=this;if(!s.display)return;const o=H(s.font),r=V(s.padding),a=s.align;let l=o.lineHeight/2;e==="bottom"||e==="center"||O(e)?(l+=r.bottom,z(s.text)&&(l+=o.lineHeight*(s.text.length-1))):l+=r.top;const{titleX:h,titleY:c,maxWidth:d,rotation:f}=pa(this,l,e,a);Pt(t,s.text,0,0,o,{color:s.color,maxWidth:d,rotation:f,textAlign:ga(a,e,n),textBaseline:"middle",translation:[h,c]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,e=t.ticks&&t.ticks.z||0,s=P(t.grid&&t.grid.z,-1),n=P(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==Dt.prototype.draw?[{z:e,draw:o=>{this.draw(o)}}]:[{z:s,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:n,draw:()=>{this.drawBorder()}},{z:e,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(t){const e=this.chart.getSortedVisibleDatasetMetas(),s=this.axis+"AxisID",n=[];let o,r;for(o=0,r=e.length;o<r;++o){const a=e[o];a[s]===this.id&&(!t||a.type===t)&&n.push(a)}return n}_resolveTickFontOptions(t){const e=this.options.ticks.setContext(this.getContext(t));return H(e.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class me{constructor(t,e,s){this.type=t,this.scope=e,this.override=s,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const e=Object.getPrototypeOf(t);let s;_a(e)&&(s=this.register(e));const n=this.items,o=t.id,r=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+t);return o in n||(n[o]=t,ma(t,r,s),this.override&&R.override(t.id,t.overrides)),r}get(t){return this.items[t]}unregister(t){const e=this.items,s=t.id,n=this.scope;s in e&&delete e[s],n&&s in R[n]&&(delete R[n][s],this.override&&delete Ct[s])}}function ma(i,t,e){const s=Jt(Object.create(null),[e?R.get(e):{},R.get(t),i.defaults]);R.set(t,s),i.defaultRoutes&&ba(t,i.defaultRoutes),i.descriptors&&R.describe(t,i.descriptors)}function ba(i,t){Object.keys(t).forEach(e=>{const s=e.split("."),n=s.pop(),o=[i].concat(s).join("."),r=t[e].split("."),a=r.pop(),l=r.join(".");R.route(o,n,l,a)})}function _a(i){return"id"in i&&"defaults"in i}class xa{constructor(){this.controllers=new me(we,"datasets",!0),this.elements=new me(ht,"elements"),this.plugins=new me(Object,"plugins"),this.scales=new me(Dt,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,s){[...e].forEach(n=>{const o=s||this._getRegistryForType(n);s||o.isForType(n)||o===this.plugins&&n.id?this._exec(t,o,n):L(n,r=>{const a=s||this._getRegistryForType(r);this._exec(t,a,r)})})}_exec(t,e,s){const n=hi(t);F(s["before"+n],[],s),e[t](s),F(s["after"+n],[],s)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){const s=this._typedRegistries[e];if(s.isForType(t))return s}return this.plugins}_get(t,e,s){const n=e.get(t);if(n===void 0)throw new Error('"'+t+'" is not a registered '+s+".");return n}}var it=new xa;class ya{constructor(){this._init=[]}notify(t,e,s,n){e==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const o=n?this._descriptors(t).filter(n):this._descriptors(t),r=this._notify(o,t,e,s);return e==="afterDestroy"&&(this._notify(o,t,"stop"),this._notify(this._init,t,"uninstall")),r}_notify(t,e,s,n){n=n||{};for(const o of t){const r=o.plugin,a=r[s],l=[e,n,o.options];if(F(a,l,r)===!1&&n.cancelable)return!1}return!0}invalidate(){I(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){const s=t&&t.config,n=P(s.options&&s.options.plugins,{}),o=va(s);return n===!1&&!e?[]:wa(t,o,n,e)}_notifyStateChanges(t){const e=this._oldCache||[],s=this._cache,n=(o,r)=>o.filter(a=>!r.some(l=>a.plugin.id===l.plugin.id));this._notify(n(e,s),t,"stop"),this._notify(n(s,e),t,"start")}}function va(i){const t={},e=[],s=Object.keys(it.plugins.items);for(let o=0;o<s.length;o++)e.push(it.getPlugin(s[o]));const n=i.plugins||[];for(let o=0;o<n.length;o++){const r=n[o];e.indexOf(r)===-1&&(e.push(r),t[r.id]=!0)}return{plugins:e,localIds:t}}function ka(i,t){return!t&&i===!1?null:i===!0?{}:i}function wa(i,{plugins:t,localIds:e},s,n){const o=[],r=i.getContext();for(const a of t){const l=a.id,h=ka(s[l],n);h!==null&&o.push({plugin:a,options:Ma(i.config,{plugin:a,local:e[l]},h,r)})}return o}function Ma(i,{plugin:t,local:e},s,n){const o=i.pluginScopeKeys(t),r=i.getOptionScopes(s,o);return e&&t.defaults&&r.push(t.defaults),i.createResolver(r,n,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function ei(i,t){const e=R.datasets[i]||{};return((t.datasets||{})[i]||{}).indexAxis||t.indexAxis||e.indexAxis||"x"}function Sa(i,t){let e=i;return i==="_index_"?e=t:i==="_value_"&&(e=t==="x"?"y":"x"),e}function Ca(i,t){return i===t?"_index_":"_value_"}function ls(i){if(i==="x"||i==="y"||i==="r")return i}function Pa(i){if(i==="top"||i==="bottom")return"x";if(i==="left"||i==="right")return"y"}function ii(i,...t){if(ls(i))return i;for(const e of t){const s=e.axis||Pa(e.position)||i.length>1&&ls(i[0].toLowerCase());if(s)return s}throw new Error(`Cannot determine type of '${i}' axis. Please provide 'axis' or 'position' option.`)}function hs(i,t,e){if(e[t+"AxisID"]===i)return{axis:t}}function Da(i,t){if(t.data&&t.data.datasets){const e=t.data.datasets.filter(s=>s.xAxisID===i||s.yAxisID===i);if(e.length)return hs(i,"x",e[0])||hs(i,"y",e[0])}return{}}function Oa(i,t){const e=Ct[i.type]||{scales:{}},s=t.scales||{},n=ei(i.type,t),o=Object.create(null);return Object.keys(s).forEach(r=>{const a=s[r];if(!O(a))return console.error(`Invalid scale configuration for scale: ${r}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${r}`);const l=ii(r,a,Da(r,i),R.scales[a.type]),h=Ca(l,n),c=e.scales||{};o[r]=Kt(Object.create(null),[{axis:l},a,c[l],c[h]])}),i.data.datasets.forEach(r=>{const a=r.type||i.type,l=r.indexAxis||ei(a,t),c=(Ct[a]||{}).scales||{};Object.keys(c).forEach(d=>{const f=Sa(d,l),u=r[f+"AxisID"]||f;o[u]=o[u]||Object.create(null),Kt(o[u],[{axis:f},s[u],c[d]])})}),Object.keys(o).forEach(r=>{const a=o[r];Kt(a,[R.scales[a.type],R.scale])}),o}function un(i){const t=i.options||(i.options={});t.plugins=P(t.plugins,{}),t.scales=Oa(i,t)}function gn(i){return i=i||{},i.datasets=i.datasets||[],i.labels=i.labels||[],i}function Ta(i){return i=i||{},i.data=gn(i.data),un(i),i}const cs=new Map,pn=new Set;function be(i,t){let e=cs.get(i);return e||(e=t(),cs.set(i,e),pn.add(e)),e}const Vt=(i,t,e)=>{const s=De(t,e);s!==void 0&&i.add(s)};class La{constructor(t){this._config=Ta(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=gn(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),un(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return be(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return be(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return be(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){const e=t.id,s=this.type;return be(`${s}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){const s=this._scopeCache;let n=s.get(t);return(!n||e)&&(n=new Map,s.set(t,n)),n}getOptionScopes(t,e,s){const{options:n,type:o}=this,r=this._cachedScopes(t,s),a=r.get(e);if(a)return a;const l=new Set;e.forEach(c=>{t&&(l.add(t),c.forEach(d=>Vt(l,t,d))),c.forEach(d=>Vt(l,n,d)),c.forEach(d=>Vt(l,Ct[o]||{},d)),c.forEach(d=>Vt(l,R,d)),c.forEach(d=>Vt(l,Je,d))});const h=Array.from(l);return h.length===0&&h.push(Object.create(null)),pn.has(e)&&r.set(e,h),h}chartOptionScopes(){const{options:t,type:e}=this;return[t,Ct[e]||{},R.datasets[e]||{},{type:e},R,Je]}resolveNamedOptions(t,e,s,n=[""]){const o={$shared:!0},{resolver:r,subPrefixes:a}=ds(this._resolverCache,t,n);let l=r;if(Fa(r,e)){o.$shared=!1,s=gt(s)?s():s;const h=this.createResolver(t,s,a);l=zt(r,s,h)}for(const h of e)o[h]=l[h];return o}createResolver(t,e,s=[""],n){const{resolver:o}=ds(this._resolverCache,t,s);return O(e)?zt(o,e,void 0,n):o}}function ds(i,t,e){let s=i.get(t);s||(s=new Map,i.set(t,s));const n=e.join();let o=s.get(n);return o||(o={resolver:bi(t,e),subPrefixes:e.filter(a=>!a.toLowerCase().includes("hover"))},s.set(n,o)),o}const Aa=i=>O(i)&&Object.getOwnPropertyNames(i).some(t=>gt(i[t]));function Fa(i,t){const{isScriptable:e,isIndexable:s}=Zs(i);for(const n of t){const o=e(n),r=s(n),a=(r||o)&&i[n];if(o&&(gt(a)||Aa(a))||r&&z(a))return!0}return!1}var Ia="4.4.2";const za=["top","bottom","left","right","chartArea"];function fs(i,t){return i==="top"||i==="bottom"||za.indexOf(i)===-1&&t==="x"}function us(i,t){return function(e,s){return e[i]===s[i]?e[t]-s[t]:e[i]-s[i]}}function gs(i){const t=i.chart,e=t.options.animation;t.notifyPlugins("afterRender"),F(e&&e.onComplete,[i],t)}function Ra(i){const t=i.chart,e=t.options.animation;F(e&&e.onProgress,[i],t)}function mn(i){return yi()&&typeof i=="string"?i=document.getElementById(i):i&&i.length&&(i=i[0]),i&&i.canvas&&(i=i.canvas),i}const Se={},ps=i=>{const t=mn(i);return Object.values(Se).filter(e=>e.canvas===t).pop()};function Ea(i,t,e){const s=Object.keys(i);for(const n of s){const o=+n;if(o>=t){const r=i[n];delete i[n],(e>0||o>t)&&(i[o+e]=r)}}}function Ba(i,t,e,s){return!e||i.type==="mouseout"?null:s?t:i}function _e(i,t,e){return i.options.clip?i[e]:t[e]}function Ha(i,t){const{xScale:e,yScale:s}=i;return e&&s?{left:_e(e,t,"left"),right:_e(e,t,"right"),top:_e(s,t,"top"),bottom:_e(s,t,"bottom")}:t}class Mt{static register(...t){it.add(...t),ms()}static unregister(...t){it.remove(...t),ms()}constructor(t,e){const s=this.config=new La(e),n=mn(t),o=ps(n);if(o)throw new Error("Canvas is already in use. Chart with ID '"+o.id+"' must be destroyed before the canvas with ID '"+o.canvas.id+"' can be reused.");const r=s.createResolver(s.chartOptionScopes(),this.getContext());this.platform=new(s.platform||ia(n)),this.platform.updateConfig(s);const a=this.platform.acquireContext(n,r.aspectRatio),l=a&&a.canvas,h=l&&l.height,c=l&&l.width;if(this.id=Xn(),this.ctx=a,this.canvas=l,this.width=c,this.height=h,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new ya,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=fo(d=>this.update(d),r.resizeDelay||0),this._dataChanges=[],Se[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}ot.listen(this,"complete",gs),ot.listen(this,"progress",Ra),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:s,height:n,_aspectRatio:o}=this;return I(t)?e&&o?o:n?s/n:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return it}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Wi(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return Ei(this.canvas,this.ctx),this}stop(){return ot.stop(this),this}resize(t,e){ot.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){const s=this.options,n=this.canvas,o=s.maintainAspectRatio&&this.aspectRatio,r=this.platform.getMaximumSize(n,t,e,o),a=s.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,Wi(this,a,!0)&&(this.notifyPlugins("resize",{size:r}),F(s.onResize,[this,r],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const e=this.options.scales||{};L(e,(s,n)=>{s.id=n})}buildOrUpdateScales(){const t=this.options,e=t.scales,s=this.scales,n=Object.keys(s).reduce((r,a)=>(r[a]=!1,r),{});let o=[];e&&(o=o.concat(Object.keys(e).map(r=>{const a=e[r],l=ii(r,a),h=l==="r",c=l==="x";return{options:a,dposition:h?"chartArea":c?"bottom":"left",dtype:h?"radialLinear":c?"category":"linear"}}))),L(o,r=>{const a=r.options,l=a.id,h=ii(l,a),c=P(a.type,r.dtype);(a.position===void 0||fs(a.position,h)!==fs(r.dposition))&&(a.position=r.dposition),n[l]=!0;let d=null;if(l in s&&s[l].type===c)d=s[l];else{const f=it.getScale(c);d=new f({id:l,type:c,ctx:this.ctx,chart:this}),s[d.id]=d}d.init(a,t)}),L(n,(r,a)=>{r||delete s[a]}),L(s,r=>{Z.configure(this,r,r.options),Z.addBox(this,r)})}_updateMetasets(){const t=this._metasets,e=this.data.datasets.length,s=t.length;if(t.sort((n,o)=>n.index-o.index),s>e){for(let n=e;n<s;++n)this._destroyDatasetMeta(n);t.splice(e,s-e)}this._sortedMetasets=t.slice(0).sort(us("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((s,n)=>{e.filter(o=>o===s._dataset).length===0&&this._destroyDatasetMeta(n)})}buildOrUpdateControllers(){const t=[],e=this.data.datasets;let s,n;for(this._removeUnreferencedMetasets(),s=0,n=e.length;s<n;s++){const o=e[s];let r=this.getDatasetMeta(s);const a=o.type||this.config.type;if(r.type&&r.type!==a&&(this._destroyDatasetMeta(s),r=this.getDatasetMeta(s)),r.type=a,r.indexAxis=o.indexAxis||ei(a,this.options),r.order=o.order||0,r.index=s,r.label=""+o.label,r.visible=this.isDatasetVisible(s),r.controller)r.controller.updateIndex(s),r.controller.linkScales();else{const l=it.getController(a),{datasetElementType:h,dataElementType:c}=R.datasets[a];Object.assign(l,{dataElementType:it.getElement(c),datasetElementType:h&&it.getElement(h)}),r.controller=new l(this,s),t.push(r.controller)}}return this._updateMetasets(),t}_resetElements(){L(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const e=this.config;e.update();const s=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),n=this._animationsDisabled=!s.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;const o=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let h=0,c=this.data.datasets.length;h<c;h++){const{controller:d}=this.getDatasetMeta(h),f=!n&&o.indexOf(d)===-1;d.buildOrUpdateElements(f),r=Math.max(+d.getMaxOverflow(),r)}r=this._minPadding=s.layout.autoPadding?r:0,this._updateLayout(r),n||L(o,h=>{h.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(us("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){L(this.scales,t=>{Z.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,e=new Set(Object.keys(this._listeners)),s=new Set(t.events);(!Di(e,s)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(const{method:s,start:n,count:o}of e){const r=s==="_removeElements"?-o:o;Ea(t,n,r)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const e=this.data.datasets.length,s=o=>new Set(t.filter(r=>r[0]===o).map((r,a)=>a+","+r.splice(1).join(","))),n=s(0);for(let o=1;o<e;o++)if(!Di(n,s(o)))return;return Array.from(n).map(o=>o.split(",")).map(o=>({method:o[1],start:+o[2],count:+o[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;Z.update(this,this.width,this.height,t);const e=this.chartArea,s=e.width<=0||e.height<=0;this._layers=[],L(this.boxes,n=>{s&&n.position==="chartArea"||(n.configure&&n.configure(),this._layers.push(...n._layers()))},this),this._layers.forEach((n,o)=>{n._idx=o}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let e=0,s=this.data.datasets.length;e<s;++e)this.getDatasetMeta(e).controller.configure();for(let e=0,s=this.data.datasets.length;e<s;++e)this._updateDataset(e,gt(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){const s=this.getDatasetMeta(t),n={meta:s,index:t,mode:e,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",n)!==!1&&(s.controller._update(e),n.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",n))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(ot.has(this)?this.attached&&!ot.running(this)&&ot.start(this):(this.draw(),gs({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:s,height:n}=this._resizeBeforeDraw;this._resize(s,n),this._resizeBeforeDraw=null}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const e=this._sortedMetasets,s=[];let n,o;for(n=0,o=e.length;n<o;++n){const r=e[n];(!t||r.visible)&&s.push(r)}return s}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const e=this.ctx,s=t._clip,n=!s.disabled,o=Ha(t,this.chartArea),r={meta:t,index:t.index,cancelable:!0};this.notifyPlugins("beforeDatasetDraw",r)!==!1&&(n&&pi(e,{left:s.left===!1?0:o.left-s.left,right:s.right===!1?this.width:o.right+s.right,top:s.top===!1?0:o.top-s.top,bottom:s.bottom===!1?this.height:o.bottom+s.bottom}),t.controller.draw(),n&&mi(e),r.cancelable=!1,this.notifyPlugins("afterDatasetDraw",r))}isPointInArea(t){return lt(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,s,n){const o=zr.modes[e];return typeof o=="function"?o(this,t,s,n):[]}getDatasetMeta(t){const e=this.data.datasets[t],s=this._metasets;let n=s.filter(o=>o&&o._dataset===e).pop();return n||(n={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},s.push(n)),n}getContext(){return this.$context||(this.$context=pt(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const s=this.getDatasetMeta(t);return typeof s.hidden=="boolean"?!s.hidden:!e.hidden}setDatasetVisibility(t,e){const s=this.getDatasetMeta(t);s.hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,s){const n=s?"show":"hide",o=this.getDatasetMeta(t),r=o.controller._resolveAnimations(void 0,n);Oe(e)?(o.data[e].hidden=!s,this.update()):(this.setDatasetVisibility(t,s),r.update(o,{visible:s}),this.update(a=>a.datasetIndex===t?n:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){const e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),ot.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),Ei(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete Se[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,e=this.platform,s=(o,r)=>{e.addEventListener(this,o,r),t[o]=r},n=(o,r,a)=>{o.offsetX=r,o.offsetY=a,this._eventHandler(o)};L(this.options.events,o=>s(o,n))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,e=this.platform,s=(l,h)=>{e.addEventListener(this,l,h),t[l]=h},n=(l,h)=>{t[l]&&(e.removeEventListener(this,l,h),delete t[l])},o=(l,h)=>{this.canvas&&this.resize(l,h)};let r;const a=()=>{n("attach",a),this.attached=!0,this.resize(),s("resize",o),s("detach",r)};r=()=>{this.attached=!1,n("resize",o),this._stop(),this._resize(0,0),s("attach",a)},e.isAttached(this.canvas)?a():r()}unbindEvents(){L(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},L(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,s){const n=s?"set":"remove";let o,r,a,l;for(e==="dataset"&&(o=this.getDatasetMeta(t[0].datasetIndex),o.controller["_"+n+"DatasetHoverStyle"]()),a=0,l=t.length;a<l;++a){r=t[a];const h=r&&this.getDatasetMeta(r.datasetIndex).controller;h&&h[n+"HoverStyle"](r.element,r.datasetIndex,r.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const e=this._active||[],s=t.map(({datasetIndex:o,index:r})=>{const a=this.getDatasetMeta(o);if(!a)throw new Error("No dataset found at index "+o);return{datasetIndex:o,element:a.data[r],index:r}});!Ce(s,e)&&(this._active=s,this._lastEvent=null,this._updateHoverStyles(s,e))}notifyPlugins(t,e,s){return this._plugins.notify(this,t,e,s)}isPluginEnabled(t){return this._plugins._cache.filter(e=>e.plugin.id===t).length===1}_updateHoverStyles(t,e,s){const n=this.options.hover,o=(l,h)=>l.filter(c=>!h.some(d=>c.datasetIndex===d.datasetIndex&&c.index===d.index)),r=o(e,t),a=s?t:o(t,e);r.length&&this.updateHoverStyle(r,n.mode,!1),a.length&&n.mode&&this.updateHoverStyle(a,n.mode,!0)}_eventHandler(t,e){const s={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},n=r=>(r.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",s,n)===!1)return;const o=this._handleEvent(t,e,s.inChartArea);return s.cancelable=!1,this.notifyPlugins("afterEvent",s,n),(o||s.changed)&&this.render(),this}_handleEvent(t,e,s){const{_active:n=[],options:o}=this,r=e,a=this._getActiveElements(t,n,s,r),l=Jn(t),h=Ba(t,this._lastEvent,s,l);s&&(this._lastEvent=null,F(o.onHover,[t,a,this],this),l&&F(o.onClick,[t,a,this],this));const c=!Ce(a,n);return(c||e)&&(this._active=a,this._updateHoverStyles(a,n,e)),this._lastEvent=h,c}_getActiveElements(t,e,s,n){if(t.type==="mouseout")return[];if(!s)return e;const o=this.options.hover;return this.getElementsAtEventForMode(t,o.mode,o,n)}}S(Mt,"defaults",R),S(Mt,"instances",Se),S(Mt,"overrides",Ct),S(Mt,"registry",it),S(Mt,"version",Ia),S(Mt,"getChart",ps);function ms(){return L(Mt.instances,i=>i._plugins.invalidate())}function bn(i,t,e=t){i.lineCap=P(e.borderCapStyle,t.borderCapStyle),i.setLineDash(P(e.borderDash,t.borderDash)),i.lineDashOffset=P(e.borderDashOffset,t.borderDashOffset),i.lineJoin=P(e.borderJoinStyle,t.borderJoinStyle),i.lineWidth=P(e.borderWidth,t.borderWidth),i.strokeStyle=P(e.borderColor,t.borderColor)}function Wa(i,t,e){i.lineTo(e.x,e.y)}function Na(i){return i.stepped?Mo:i.tension||i.cubicInterpolationMode==="monotone"?So:Wa}function _n(i,t,e={}){const s=i.length,{start:n=0,end:o=s-1}=e,{start:r,end:a}=t,l=Math.max(n,r),h=Math.min(o,a),c=n<r&&o<r||n>a&&o>a;return{count:s,start:l,loop:t.loop,ilen:h<l&&!c?s+h-l:h-l}}function ja(i,t,e,s){const{points:n,options:o}=t,{count:r,start:a,loop:l,ilen:h}=_n(n,e,s),c=Na(o);let{move:d=!0,reverse:f}=s||{},u,p,m;for(u=0;u<=h;++u)p=n[(a+(f?h-u:u))%r],!p.skip&&(d?(i.moveTo(p.x,p.y),d=!1):c(i,m,p,f,o.stepped),m=p);return l&&(p=n[(a+(f?h:0))%r],c(i,m,p,f,o.stepped)),!!l}function Va(i,t,e,s){const n=t.points,{count:o,start:r,ilen:a}=_n(n,e,s),{move:l=!0,reverse:h}=s||{};let c=0,d=0,f,u,p,m,g,b;const _=v=>(r+(h?a-v:v))%o,y=()=>{m!==g&&(i.lineTo(c,g),i.lineTo(c,m),i.lineTo(c,b))};for(l&&(u=n[_(0)],i.moveTo(u.x,u.y)),f=0;f<=a;++f){if(u=n[_(f)],u.skip)continue;const v=u.x,x=u.y,w=v|0;w===p?(x<m?m=x:x>g&&(g=x),c=(d*c+v)/++d):(y(),i.lineTo(v,x),p=w,d=0,m=g=x),b=x}y()}function si(i){const t=i.options,e=t.borderDash&&t.borderDash.length;return!i._decimated&&!i._loop&&!t.tension&&t.cubicInterpolationMode!=="monotone"&&!t.stepped&&!e?Va:ja}function $a(i){return i.stepped?nr:i.tension||i.cubicInterpolationMode==="monotone"?or:wt}function Ya(i,t,e,s){let n=t._path;n||(n=t._path=new Path2D,t.path(n,e,s)&&n.closePath()),bn(i,t.options),i.stroke(n)}function Ua(i,t,e,s){const{segments:n,options:o}=t,r=si(t);for(const a of n)bn(i,o,a.style),i.beginPath(),r(i,t,a,{start:e,end:e+s-1})&&i.closePath(),i.stroke()}const Xa=typeof Path2D=="function";function Ka(i,t,e,s){Xa&&!t.options.segment?Ya(i,t,e,s):Ua(i,t,e,s)}class xe extends ht{constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){const s=this.options;if((s.tension||s.cubicInterpolationMode==="monotone")&&!s.stepped&&!this._pointsUpdated){const n=s.spanGaps?this._loop:this._fullLoop;Go(this._points,s,t,n,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=ur(this,this.options.segment))}first(){const t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){const t=this.segments,e=this.points,s=t.length;return s&&e[t[s-1].end]}interpolate(t,e){const s=this.options,n=t[e],o=this.points,r=cr(this,{property:e,start:n,end:n});if(!r.length)return;const a=[],l=$a(s);let h,c;for(h=0,c=r.length;h<c;++h){const{start:d,end:f}=r[h],u=o[d],p=o[f];if(u===p){a.push(u);continue}const m=Math.abs((n-u[e])/(p[e]-u[e])),g=l(u,p,m,s.stepped);g[e]=t[e],a.push(g)}return a.length===1?a[0]:a}pathSegment(t,e,s){return si(this)(t,this,e,s)}path(t,e,s){const n=this.segments,o=si(this);let r=this._loop;e=e||0,s=s||this.points.length-e;for(const a of n)r&=o(t,this,a,{start:e,end:e+s-1});return!!r}draw(t,e,s,n){const o=this.options||{};(this.points||[]).length&&o.borderWidth&&(t.save(),Ka(t,this,s,n),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}S(xe,"id","line"),S(xe,"defaults",{borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0}),S(xe,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"}),S(xe,"descriptors",{_scriptable:!0,_indexable:t=>t!=="borderDash"&&t!=="fill"});function bs(i,t,e,s){const n=i.options,{[e]:o}=i.getProps([e],s);return Math.abs(t-o)<n.radius+n.hitRadius}class qe extends ht{constructor(e){super();S(this,"parsed");S(this,"skip");S(this,"stop");this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,e&&Object.assign(this,e)}inRange(e,s,n){const o=this.options,{x:r,y:a}=this.getProps(["x","y"],n);return Math.pow(e-r,2)+Math.pow(s-a,2)<Math.pow(o.hitRadius+o.radius,2)}inXRange(e,s){return bs(this,e,"x",s)}inYRange(e,s){return bs(this,e,"y",s)}getCenterPoint(e){const{x:s,y:n}=this.getProps(["x","y"],e);return{x:s,y:n}}size(e){e=e||this.options||{};let s=e.radius||0;s=Math.max(s,s&&e.hoverRadius||0);const n=s&&e.borderWidth||0;return(s+n)*2}draw(e,s){const n=this.options;this.skip||n.radius<.1||!lt(this,s,this.size(n)/2)||(e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.fillStyle=n.backgroundColor,ti(e,n,this.x,this.y))}getRange(){const e=this.options||{};return e.radius+e.hitRadius}}S(qe,"id","point"),S(qe,"defaults",{borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0}),S(qe,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});const _s=(i,t)=>{let{boxHeight:e=t,boxWidth:s=t}=i;return i.usePointStyle&&(e=Math.min(e,t),s=i.pointStyleWidth||Math.min(s,t)),{boxWidth:s,boxHeight:e,itemHeight:Math.max(t,e)}},qa=(i,t)=>i!==null&&t!==null&&i.datasetIndex===t.datasetIndex&&i.index===t.index;class xs extends ht{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,s){this.maxWidth=t,this.maxHeight=e,this._margins=s,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let e=F(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(s=>t.filter(s,this.chart.data))),t.sort&&(e=e.sort((s,n)=>t.sort(s,n,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){const{options:t,ctx:e}=this;if(!t.display){this.width=this.height=0;return}const s=t.labels,n=H(s.font),o=n.size,r=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=_s(s,o);let h,c;e.font=n.string,this.isHorizontal()?(h=this.maxWidth,c=this._fitRows(r,o,a,l)+10):(c=this.maxHeight,h=this._fitCols(r,n,a,l)+10),this.width=Math.min(h,t.maxWidth||this.maxWidth),this.height=Math.min(c,t.maxHeight||this.maxHeight)}_fitRows(t,e,s,n){const{ctx:o,maxWidth:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],h=this.lineWidths=[0],c=n+a;let d=t;o.textAlign="left",o.textBaseline="middle";let f=-1,u=-c;return this.legendItems.forEach((p,m)=>{const g=s+e/2+o.measureText(p.text).width;(m===0||h[h.length-1]+g+2*a>r)&&(d+=c,h[h.length-(m>0?0:1)]=0,u+=c,f++),l[m]={left:0,top:u,row:f,width:g,height:n},h[h.length-1]+=g+a}),d}_fitCols(t,e,s,n){const{ctx:o,maxHeight:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],h=this.columnSizes=[],c=r-t;let d=a,f=0,u=0,p=0,m=0;return this.legendItems.forEach((g,b)=>{const{itemWidth:_,itemHeight:y}=Ga(s,e,o,g,n);b>0&&u+y+2*a>c&&(d+=f+a,h.push({width:f,height:u}),p+=f+a,m++,f=u=0),l[b]={left:p,top:u,col:m,width:_,height:y},f=Math.max(f,_),u+=y+a}),d+=f,h.push({width:f,height:u}),d}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:s,labels:{padding:n},rtl:o}}=this,r=Ft(o,this.left,this.width);if(this.isHorizontal()){let a=0,l=j(s,this.left+n,this.right-this.lineWidths[a]);for(const h of e)a!==h.row&&(a=h.row,l=j(s,this.left+n,this.right-this.lineWidths[a])),h.top+=this.top+t+n,h.left=r.leftForLtr(r.x(l),h.width),l+=h.width+n}else{let a=0,l=j(s,this.top+t+n,this.bottom-this.columnSizes[a].height);for(const h of e)h.col!==a&&(a=h.col,l=j(s,this.top+t+n,this.bottom-this.columnSizes[a].height)),h.top=l,h.left+=this.left+n,h.left=r.leftForLtr(r.x(h.left),h.width),l+=h.height+n}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const t=this.ctx;pi(t,this),this._draw(),mi(t)}}_draw(){const{options:t,columnSizes:e,lineWidths:s,ctx:n}=this,{align:o,labels:r}=t,a=R.color,l=Ft(t.rtl,this.left,this.width),h=H(r.font),{padding:c}=r,d=h.size,f=d/2;let u;this.drawTitle(),n.textAlign=l.textAlign("left"),n.textBaseline="middle",n.lineWidth=.5,n.font=h.string;const{boxWidth:p,boxHeight:m,itemHeight:g}=_s(r,d),b=function(w,M,k){if(isNaN(p)||p<=0||isNaN(m)||m<0)return;n.save();const C=P(k.lineWidth,1);if(n.fillStyle=P(k.fillStyle,a),n.lineCap=P(k.lineCap,"butt"),n.lineDashOffset=P(k.lineDashOffset,0),n.lineJoin=P(k.lineJoin,"miter"),n.lineWidth=C,n.strokeStyle=P(k.strokeStyle,a),n.setLineDash(P(k.lineDash,[])),r.usePointStyle){const A={radius:m*Math.SQRT2/2,pointStyle:k.pointStyle,rotation:k.rotation,borderWidth:C},D=l.xPlus(w,p/2),T=M+f;qs(n,A,D,T,r.pointStyleWidth&&p)}else{const A=M+Math.max((d-m)/2,0),D=l.leftForLtr(w,p),T=At(k.borderRadius);n.beginPath(),Object.values(T).some($=>$!==0)?Fe(n,{x:D,y:A,w:p,h:m,radius:T}):n.rect(D,A,p,m),n.fill(),C!==0&&n.stroke()}n.restore()},_=function(w,M,k){Pt(n,k.text,w,M+g/2,h,{strikethrough:k.hidden,textAlign:l.textAlign(k.textAlign)})},y=this.isHorizontal(),v=this._computeTitleHeight();y?u={x:j(o,this.left+c,this.right-s[0]),y:this.top+c+v,line:0}:u={x:this.left+c,y:j(o,this.top+v+c,this.bottom-e[0].height),line:0},sn(this.ctx,t.textDirection);const x=g+c;this.legendItems.forEach((w,M)=>{n.strokeStyle=w.fontColor,n.fillStyle=w.fontColor;const k=n.measureText(w.text).width,C=l.textAlign(w.textAlign||(w.textAlign=r.textAlign)),A=p+f+k;let D=u.x,T=u.y;l.setWidth(this.width),y?M>0&&D+A+c>this.right&&(T=u.y+=x,u.line++,D=u.x=j(o,this.left+c,this.right-s[u.line])):M>0&&T+x>this.bottom&&(D=u.x=D+e[u.line].width+c,u.line++,T=u.y=j(o,this.top+v+c,this.bottom-e[u.line].height));const $=l.x(D);if(b($,T,w),D=uo(C,D+p+f,y?D+A:this.right,t.rtl),_(l.x(D),T,w),y)u.x+=A+c;else if(typeof w.text!="string"){const tt=h.lineHeight;u.y+=xn(w,tt)+c}else u.y+=x}),nn(this.ctx,t.textDirection)}drawTitle(){const t=this.options,e=t.title,s=H(e.font),n=V(e.padding);if(!e.display)return;const o=Ft(t.rtl,this.left,this.width),r=this.ctx,a=e.position,l=s.size/2,h=n.top+l;let c,d=this.left,f=this.width;if(this.isHorizontal())f=Math.max(...this.lineWidths),c=this.top+h,d=j(t.align,d,this.right-f);else{const p=this.columnSizes.reduce((m,g)=>Math.max(m,g.height),0);c=h+j(t.align,this.top,this.bottom-p-t.labels.padding-this._computeTitleHeight())}const u=j(a,d,d+f);r.textAlign=o.textAlign(fi(a)),r.textBaseline="middle",r.strokeStyle=e.color,r.fillStyle=e.color,r.font=s.string,Pt(r,e.text,u,c,s)}_computeTitleHeight(){const t=this.options.title,e=H(t.font),s=V(t.padding);return t.display?e.lineHeight+s.height:0}_getLegendItemAt(t,e){let s,n,o;if(Yt(t,this.left,this.right)&&Yt(e,this.top,this.bottom)){for(o=this.legendHitBoxes,s=0;s<o.length;++s)if(n=o[s],Yt(t,n.left,n.left+n.width)&&Yt(e,n.top,n.top+n.height))return this.legendItems[s]}return null}handleEvent(t){const e=this.options;if(!Ja(t.type,e))return;const s=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){const n=this._hoveredItem,o=qa(n,s);n&&!o&&F(e.onLeave,[t,n,this],this),this._hoveredItem=s,s&&!o&&F(e.onHover,[t,s,this],this)}else s&&F(e.onClick,[t,s,this],this)}}function Ga(i,t,e,s,n){const o=Za(s,i,t,e),r=Qa(n,s,t.lineHeight);return{itemWidth:o,itemHeight:r}}function Za(i,t,e,s){let n=i.text;return n&&typeof n!="string"&&(n=n.reduce((o,r)=>o.length>r.length?o:r)),t+e.size/2+s.measureText(n).width}function Qa(i,t,e){let s=i;return typeof t.text!="string"&&(s=xn(t,e)),s}function xn(i,t){const e=i.text?i.text.length:0;return t*e}function Ja(i,t){return!!((i==="mousemove"||i==="mouseout")&&(t.onHover||t.onLeave)||t.onClick&&(i==="click"||i==="mouseup"))}var Ll={id:"legend",_element:xs,start(i,t,e){const s=i.legend=new xs({ctx:i.ctx,options:e,chart:i});Z.configure(i,s,e),Z.addBox(i,s)},stop(i){Z.removeBox(i,i.legend),delete i.legend},beforeUpdate(i,t,e){const s=i.legend;Z.configure(i,s,e),s.options=e},afterUpdate(i){const t=i.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(i,t){t.replay||i.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(i,t,e){const s=t.datasetIndex,n=e.chart;n.isDatasetVisible(s)?(n.hide(s),t.hidden=!0):(n.show(s),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:i=>i.chart.options.color,boxWidth:40,padding:10,generateLabels(i){const t=i.data.datasets,{labels:{usePointStyle:e,pointStyle:s,textAlign:n,color:o,useBorderRadius:r,borderRadius:a}}=i.legend.options;return i._getSortedDatasetMetas().map(l=>{const h=l.controller.getStyle(e?0:void 0),c=V(h.borderWidth);return{text:t[l.index].label,fillStyle:h.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:h.borderCapStyle,lineDash:h.borderDash,lineDashOffset:h.borderDashOffset,lineJoin:h.borderJoinStyle,lineWidth:(c.width+c.height)/4,strokeStyle:h.borderColor,pointStyle:s||h.pointStyle,rotation:h.rotation,textAlign:n||h.textAlign,borderRadius:r&&(a||h.borderRadius),datasetIndex:l.index}},this)}},title:{color:i=>i.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:i=>!i.startsWith("on"),labels:{_scriptable:i=>!["generateLabels","filter","sort"].includes(i)}}};class yn extends ht{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){const s=this.options;if(this.left=0,this.top=0,!s.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;const n=z(s.text)?s.text.length:1;this._padding=V(s.padding);const o=n*H(s.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){const t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){const{top:e,left:s,bottom:n,right:o,options:r}=this,a=r.align;let l=0,h,c,d;return this.isHorizontal()?(c=j(a,s,o),d=e+t,h=o-s):(r.position==="left"?(c=s+t,d=j(a,n,e),l=B*-.5):(c=o-t,d=j(a,e,n),l=B*.5),h=n-e),{titleX:c,titleY:d,maxWidth:h,rotation:l}}draw(){const t=this.ctx,e=this.options;if(!e.display)return;const s=H(e.font),o=s.lineHeight/2+this._padding.top,{titleX:r,titleY:a,maxWidth:l,rotation:h}=this._drawArgs(o);Pt(t,e.text,0,0,s,{color:e.color,maxWidth:l,rotation:h,textAlign:fi(e.align),textBaseline:"middle",translation:[r,a]})}}function tl(i,t){const e=new yn({ctx:i.ctx,options:t,chart:i});Z.configure(i,e,t),Z.addBox(i,e),i.titleBlock=e}var Al={id:"title",_element:yn,start(i,t,e){tl(i,e)},stop(i){const t=i.titleBlock;Z.removeBox(i,t),delete i.titleBlock},beforeUpdate(i,t,e){const s=i.titleBlock;Z.configure(i,s,e),s.options=e},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const Xt={average(i){if(!i.length)return!1;let t,e,s=new Set,n=0,o=0;for(t=0,e=i.length;t<e;++t){const a=i[t].element;if(a&&a.hasValue()){const l=a.tooltipPosition();s.add(l.x),n+=l.y,++o}}return{x:[...s].reduce((a,l)=>a+l)/s.size,y:n/o}},nearest(i,t){if(!i.length)return!1;let e=t.x,s=t.y,n=Number.POSITIVE_INFINITY,o,r,a;for(o=0,r=i.length;o<r;++o){const l=i[o].element;if(l&&l.hasValue()){const h=l.getCenterPoint(),c=Ze(t,h);c<n&&(n=c,a=l)}}if(a){const l=a.tooltipPosition();e=l.x,s=l.y}return{x:e,y:s}}};function et(i,t){return t&&(z(t)?Array.prototype.push.apply(i,t):i.push(t)),i}function rt(i){return(typeof i=="string"||i instanceof String)&&i.indexOf(`
`)>-1?i.split(`
`):i}function el(i,t){const{element:e,datasetIndex:s,index:n}=t,o=i.getDatasetMeta(s).controller,{label:r,value:a}=o.getLabelAndValue(n);return{chart:i,label:r,parsed:o.getParsed(n),raw:i.data.datasets[s].data[n],formattedValue:a,dataset:o.getDataset(),dataIndex:n,datasetIndex:s,element:e}}function ys(i,t){const e=i.chart.ctx,{body:s,footer:n,title:o}=i,{boxWidth:r,boxHeight:a}=t,l=H(t.bodyFont),h=H(t.titleFont),c=H(t.footerFont),d=o.length,f=n.length,u=s.length,p=V(t.padding);let m=p.height,g=0,b=s.reduce((v,x)=>v+x.before.length+x.lines.length+x.after.length,0);if(b+=i.beforeBody.length+i.afterBody.length,d&&(m+=d*h.lineHeight+(d-1)*t.titleSpacing+t.titleMarginBottom),b){const v=t.displayColors?Math.max(a,l.lineHeight):l.lineHeight;m+=u*v+(b-u)*l.lineHeight+(b-1)*t.bodySpacing}f&&(m+=t.footerMarginTop+f*c.lineHeight+(f-1)*t.footerSpacing);let _=0;const y=function(v){g=Math.max(g,e.measureText(v).width+_)};return e.save(),e.font=h.string,L(i.title,y),e.font=l.string,L(i.beforeBody.concat(i.afterBody),y),_=t.displayColors?r+2+t.boxPadding:0,L(s,v=>{L(v.before,y),L(v.lines,y),L(v.after,y)}),_=0,e.font=c.string,L(i.footer,y),e.restore(),g+=p.width,{width:g,height:m}}function il(i,t){const{y:e,height:s}=t;return e<s/2?"top":e>i.height-s/2?"bottom":"center"}function sl(i,t,e,s){const{x:n,width:o}=s,r=e.caretSize+e.caretPadding;if(i==="left"&&n+o+r>t.width||i==="right"&&n-o-r<0)return!0}function nl(i,t,e,s){const{x:n,width:o}=e,{width:r,chartArea:{left:a,right:l}}=i;let h="center";return s==="center"?h=n<=(a+l)/2?"left":"right":n<=o/2?h="left":n>=r-o/2&&(h="right"),sl(h,i,t,e)&&(h="center"),h}function vs(i,t,e){const s=e.yAlign||t.yAlign||il(i,e);return{xAlign:e.xAlign||t.xAlign||nl(i,t,e,s),yAlign:s}}function ol(i,t){let{x:e,width:s}=i;return t==="right"?e-=s:t==="center"&&(e-=s/2),e}function rl(i,t,e){let{y:s,height:n}=i;return t==="top"?s+=e:t==="bottom"?s-=n+e:s-=n/2,s}function ks(i,t,e,s){const{caretSize:n,caretPadding:o,cornerRadius:r}=i,{xAlign:a,yAlign:l}=e,h=n+o,{topLeft:c,topRight:d,bottomLeft:f,bottomRight:u}=At(r);let p=ol(t,a);const m=rl(t,l,h);return l==="center"?a==="left"?p+=h:a==="right"&&(p-=h):a==="left"?p-=Math.max(c,f)+n:a==="right"&&(p+=Math.max(d,u)+n),{x:st(p,0,s.width-t.width),y:st(m,0,s.height-t.height)}}function ye(i,t,e){const s=V(e.padding);return t==="center"?i.x+i.width/2:t==="right"?i.x+i.width-s.right:i.x+s.left}function ws(i){return et([],rt(i))}function al(i,t,e){return pt(i,{tooltip:t,tooltipItems:e,type:"tooltip"})}function Ms(i,t){const e=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return e?i.override(e):i}const vn={beforeTitle:nt,title(i){if(i.length>0){const t=i[0],e=t.chart.data.labels,s=e?e.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(s>0&&t.dataIndex<s)return e[t.dataIndex]}return""},afterTitle:nt,beforeBody:nt,beforeLabel:nt,label(i){if(this&&this.options&&this.options.mode==="dataset")return i.label+": "+i.formattedValue||i.formattedValue;let t=i.dataset.label||"";t&&(t+=": ");const e=i.formattedValue;return I(e)||(t+=e),t},labelColor(i){const e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(i){const e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:nt,afterBody:nt,beforeFooter:nt,footer:nt,afterFooter:nt};function Y(i,t,e,s){const n=i[t].call(e,s);return typeof n>"u"?vn[t].call(e,s):n}class ni extends ht{constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const e=this.chart,s=this.options.setContext(this.getContext()),n=s.enabled&&e.options.animation&&s.animations,o=new rn(this.chart,n);return n._cacheable&&(this._cachedAnimations=Object.freeze(o)),o}getContext(){return this.$context||(this.$context=al(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,e){const{callbacks:s}=e,n=Y(s,"beforeTitle",this,t),o=Y(s,"title",this,t),r=Y(s,"afterTitle",this,t);let a=[];return a=et(a,rt(n)),a=et(a,rt(o)),a=et(a,rt(r)),a}getBeforeBody(t,e){return ws(Y(e.callbacks,"beforeBody",this,t))}getBody(t,e){const{callbacks:s}=e,n=[];return L(t,o=>{const r={before:[],lines:[],after:[]},a=Ms(s,o);et(r.before,rt(Y(a,"beforeLabel",this,o))),et(r.lines,Y(a,"label",this,o)),et(r.after,rt(Y(a,"afterLabel",this,o))),n.push(r)}),n}getAfterBody(t,e){return ws(Y(e.callbacks,"afterBody",this,t))}getFooter(t,e){const{callbacks:s}=e,n=Y(s,"beforeFooter",this,t),o=Y(s,"footer",this,t),r=Y(s,"afterFooter",this,t);let a=[];return a=et(a,rt(n)),a=et(a,rt(o)),a=et(a,rt(r)),a}_createItems(t){const e=this._active,s=this.chart.data,n=[],o=[],r=[];let a=[],l,h;for(l=0,h=e.length;l<h;++l)a.push(el(this.chart,e[l]));return t.filter&&(a=a.filter((c,d,f)=>t.filter(c,d,f,s))),t.itemSort&&(a=a.sort((c,d)=>t.itemSort(c,d,s))),L(a,c=>{const d=Ms(t.callbacks,c);n.push(Y(d,"labelColor",this,c)),o.push(Y(d,"labelPointStyle",this,c)),r.push(Y(d,"labelTextColor",this,c))}),this.labelColors=n,this.labelPointStyles=o,this.labelTextColors=r,this.dataPoints=a,a}update(t,e){const s=this.options.setContext(this.getContext()),n=this._active;let o,r=[];if(!n.length)this.opacity!==0&&(o={opacity:0});else{const a=Xt[s.position].call(this,n,this._eventPosition);r=this._createItems(s),this.title=this.getTitle(r,s),this.beforeBody=this.getBeforeBody(r,s),this.body=this.getBody(r,s),this.afterBody=this.getAfterBody(r,s),this.footer=this.getFooter(r,s);const l=this._size=ys(this,s),h=Object.assign({},a,l),c=vs(this.chart,s,h),d=ks(s,h,c,this.chart);this.xAlign=c.xAlign,this.yAlign=c.yAlign,o={opacity:1,x:d.x,y:d.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=r,this.$context=void 0,o&&this._resolveAnimations().update(this,o),t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,s,n){const o=this.getCaretPosition(t,s,n);e.lineTo(o.x1,o.y1),e.lineTo(o.x2,o.y2),e.lineTo(o.x3,o.y3)}getCaretPosition(t,e,s){const{xAlign:n,yAlign:o}=this,{caretSize:r,cornerRadius:a}=s,{topLeft:l,topRight:h,bottomLeft:c,bottomRight:d}=At(a),{x:f,y:u}=t,{width:p,height:m}=e;let g,b,_,y,v,x;return o==="center"?(v=u+m/2,n==="left"?(g=f,b=g-r,y=v+r,x=v-r):(g=f+p,b=g+r,y=v-r,x=v+r),_=g):(n==="left"?b=f+Math.max(l,c)+r:n==="right"?b=f+p-Math.max(h,d)-r:b=this.caretX,o==="top"?(y=u,v=y-r,g=b-r,_=b+r):(y=u+m,v=y+r,g=b+r,_=b-r),x=y),{x1:g,x2:b,x3:_,y1:y,y2:v,y3:x}}drawTitle(t,e,s){const n=this.title,o=n.length;let r,a,l;if(o){const h=Ft(s.rtl,this.x,this.width);for(t.x=ye(this,s.titleAlign,s),e.textAlign=h.textAlign(s.titleAlign),e.textBaseline="middle",r=H(s.titleFont),a=s.titleSpacing,e.fillStyle=s.titleColor,e.font=r.string,l=0;l<o;++l)e.fillText(n[l],h.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+a,l+1===o&&(t.y+=s.titleMarginBottom-a)}}_drawColorBox(t,e,s,n,o){const r=this.labelColors[s],a=this.labelPointStyles[s],{boxHeight:l,boxWidth:h}=o,c=H(o.bodyFont),d=ye(this,"left",o),f=n.x(d),u=l<c.lineHeight?(c.lineHeight-l)/2:0,p=e.y+u;if(o.usePointStyle){const m={radius:Math.min(h,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},g=n.leftForLtr(f,h)+h/2,b=p+l/2;t.strokeStyle=o.multiKeyBackground,t.fillStyle=o.multiKeyBackground,ti(t,m,g,b),t.strokeStyle=r.borderColor,t.fillStyle=r.backgroundColor,ti(t,m,g,b)}else{t.lineWidth=O(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,t.strokeStyle=r.borderColor,t.setLineDash(r.borderDash||[]),t.lineDashOffset=r.borderDashOffset||0;const m=n.leftForLtr(f,h),g=n.leftForLtr(n.xPlus(f,1),h-2),b=At(r.borderRadius);Object.values(b).some(_=>_!==0)?(t.beginPath(),t.fillStyle=o.multiKeyBackground,Fe(t,{x:m,y:p,w:h,h:l,radius:b}),t.fill(),t.stroke(),t.fillStyle=r.backgroundColor,t.beginPath(),Fe(t,{x:g,y:p+1,w:h-2,h:l-2,radius:b}),t.fill()):(t.fillStyle=o.multiKeyBackground,t.fillRect(m,p,h,l),t.strokeRect(m,p,h,l),t.fillStyle=r.backgroundColor,t.fillRect(g,p+1,h-2,l-2))}t.fillStyle=this.labelTextColors[s]}drawBody(t,e,s){const{body:n}=this,{bodySpacing:o,bodyAlign:r,displayColors:a,boxHeight:l,boxWidth:h,boxPadding:c}=s,d=H(s.bodyFont);let f=d.lineHeight,u=0;const p=Ft(s.rtl,this.x,this.width),m=function(k){e.fillText(k,p.x(t.x+u),t.y+f/2),t.y+=f+o},g=p.textAlign(r);let b,_,y,v,x,w,M;for(e.textAlign=r,e.textBaseline="middle",e.font=d.string,t.x=ye(this,g,s),e.fillStyle=s.bodyColor,L(this.beforeBody,m),u=a&&g!=="right"?r==="center"?h/2+c:h+2+c:0,v=0,w=n.length;v<w;++v){for(b=n[v],_=this.labelTextColors[v],e.fillStyle=_,L(b.before,m),y=b.lines,a&&y.length&&(this._drawColorBox(e,t,v,p,s),f=Math.max(d.lineHeight,l)),x=0,M=y.length;x<M;++x)m(y[x]),f=d.lineHeight;L(b.after,m)}u=0,f=d.lineHeight,L(this.afterBody,m),t.y-=o}drawFooter(t,e,s){const n=this.footer,o=n.length;let r,a;if(o){const l=Ft(s.rtl,this.x,this.width);for(t.x=ye(this,s.footerAlign,s),t.y+=s.footerMarginTop,e.textAlign=l.textAlign(s.footerAlign),e.textBaseline="middle",r=H(s.footerFont),e.fillStyle=s.footerColor,e.font=r.string,a=0;a<o;++a)e.fillText(n[a],l.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+s.footerSpacing}}drawBackground(t,e,s,n){const{xAlign:o,yAlign:r}=this,{x:a,y:l}=t,{width:h,height:c}=s,{topLeft:d,topRight:f,bottomLeft:u,bottomRight:p}=At(n.cornerRadius);e.fillStyle=n.backgroundColor,e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.beginPath(),e.moveTo(a+d,l),r==="top"&&this.drawCaret(t,e,s,n),e.lineTo(a+h-f,l),e.quadraticCurveTo(a+h,l,a+h,l+f),r==="center"&&o==="right"&&this.drawCaret(t,e,s,n),e.lineTo(a+h,l+c-p),e.quadraticCurveTo(a+h,l+c,a+h-p,l+c),r==="bottom"&&this.drawCaret(t,e,s,n),e.lineTo(a+u,l+c),e.quadraticCurveTo(a,l+c,a,l+c-u),r==="center"&&o==="left"&&this.drawCaret(t,e,s,n),e.lineTo(a,l+d),e.quadraticCurveTo(a,l,a+d,l),e.closePath(),e.fill(),n.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){const e=this.chart,s=this.$animations,n=s&&s.x,o=s&&s.y;if(n||o){const r=Xt[t.position].call(this,this._active,this._eventPosition);if(!r)return;const a=this._size=ys(this,t),l=Object.assign({},r,this._size),h=vs(e,t,l),c=ks(t,l,h,e);(n._to!==c.x||o._to!==c.y)&&(this.xAlign=h.xAlign,this.yAlign=h.yAlign,this.width=a.width,this.height=a.height,this.caretX=r.x,this.caretY=r.y,this._resolveAnimations().update(this,c))}}_willRender(){return!!this.opacity}draw(t){const e=this.options.setContext(this.getContext());let s=this.opacity;if(!s)return;this._updateAnimationTarget(e);const n={width:this.width,height:this.height},o={x:this.x,y:this.y};s=Math.abs(s)<.001?0:s;const r=V(e.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&a&&(t.save(),t.globalAlpha=s,this.drawBackground(o,t,n,e),sn(t,e.textDirection),o.y+=r.top,this.drawTitle(o,t,e),this.drawBody(o,t,e),this.drawFooter(o,t,e),nn(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){const s=this._active,n=t.map(({datasetIndex:a,index:l})=>{const h=this.chart.getDatasetMeta(a);if(!h)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:h.data[l],index:l}}),o=!Ce(s,n),r=this._positionChanged(n,e);(o||r)&&(this._active=n,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,s=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const n=this.options,o=this._active||[],r=this._getActiveElements(t,o,e,s),a=this._positionChanged(r,t),l=e||!Ce(r,o)||a;return l&&(this._active=r,(n.enabled||n.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),l}_getActiveElements(t,e,s,n){const o=this.options;if(t.type==="mouseout")return[];if(!n)return e.filter(a=>this.chart.data.datasets[a.datasetIndex]&&this.chart.getDatasetMeta(a.datasetIndex).controller.getParsed(a.index)!==void 0);const r=this.chart.getElementsAtEventForMode(t,o.mode,o,s);return o.reverse&&r.reverse(),r}_positionChanged(t,e){const{caretX:s,caretY:n,options:o}=this,r=Xt[o.position].call(this,t,e);return r!==!1&&(s!==r.x||n!==r.y)}}S(ni,"positioners",Xt);var Fl={id:"tooltip",_element:ni,positioners:Xt,afterInit(i,t,e){e&&(i.tooltip=new ni({chart:i,options:e}))},beforeUpdate(i,t,e){i.tooltip&&i.tooltip.initialize(e)},reset(i,t,e){i.tooltip&&i.tooltip.initialize(e)},afterDraw(i){const t=i.tooltip;if(t&&t._willRender()){const e={tooltip:t};if(i.notifyPlugins("beforeTooltipDraw",{...e,cancelable:!0})===!1)return;t.draw(i.ctx),i.notifyPlugins("afterTooltipDraw",e)}},afterEvent(i,t){if(i.tooltip){const e=t.replay;i.tooltip.handleEvent(t.event,e,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(i,t)=>t.bodyFont.size,boxWidth:(i,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:vn},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:i=>i!=="filter"&&i!=="itemSort"&&i!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};const ll=(i,t,e,s)=>(typeof t=="string"?(e=i.push(t)-1,s.unshift({index:e,label:t})):isNaN(t)&&(e=null),e);function hl(i,t,e,s){const n=i.indexOf(t);if(n===-1)return ll(i,t,e,s);const o=i.lastIndexOf(t);return n!==o?e:n}const cl=(i,t)=>i===null?null:st(Math.round(i),0,t);function Ss(i){const t=this.getLabels();return i>=0&&i<t.length?t[i]:i}class Cs extends Dt{constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const e=this._addedLabels;if(e.length){const s=this.getLabels();for(const{index:n,label:o}of e)s[n]===o&&s.splice(n,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(I(t))return null;const s=this.getLabels();return e=isFinite(e)&&s[e]===t?e:hl(s,t,P(e,t),this._addedLabels),cl(e,s.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:s,max:n}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(s=0),e||(n=this.getLabels().length-1)),this.min=s,this.max=n}buildTicks(){const t=this.min,e=this.max,s=this.options.offset,n=[];let o=this.getLabels();o=t===0&&e===o.length-1?o:o.slice(t,e+1),this._valueRange=Math.max(o.length-(s?0:1),1),this._startValue=this.min-(s?.5:0);for(let r=t;r<=e;r++)n.push({value:r});return n}getLabelForValue(t){return Ss.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}S(Cs,"id","category"),S(Cs,"defaults",{ticks:{callback:Ss}});function dl(i,t){const e=[],{bounds:n,step:o,min:r,max:a,precision:l,count:h,maxTicks:c,maxDigits:d,includeBounds:f}=i,u=o||1,p=c-1,{min:m,max:g}=t,b=!I(r),_=!I(a),y=!I(h),v=(g-m)/(d+1);let x=Ti((g-m)/p/u)*u,w,M,k,C;if(x<1e-14&&!b&&!_)return[{value:m},{value:g}];C=Math.ceil(g/x)-Math.floor(m/x),C>p&&(x=Ti(C*x/p/u)*u),I(l)||(w=Math.pow(10,l),x=Math.ceil(x*w)/w),n==="ticks"?(M=Math.floor(m/x)*x,k=Math.ceil(g/x)*x):(M=m,k=g),b&&_&&o&&so((a-r)/o,x/1e3)?(C=Math.round(Math.min((a-r)/x,c)),x=(a-r)/C,M=r,k=a):y?(M=b?r:M,k=_?a:k,C=h-1,x=(k-M)/C):(C=(k-M)/x,qt(C,Math.round(C),x/1e3)?C=Math.round(C):C=Math.ceil(C));const A=Math.max(Li(x),Li(M));w=Math.pow(10,I(l)?A:l),M=Math.round(M*w)/w,k=Math.round(k*w)/w;let D=0;for(b&&(f&&M!==r?(e.push({value:r}),M<r&&D++,qt(Math.round((M+D*x)*w)/w,r,Ps(r,v,i))&&D++):M<r&&D++);D<C;++D){const T=Math.round((M+D*x)*w)/w;if(_&&T>a)break;e.push({value:T})}return _&&f&&k!==a?e.length&&qt(e[e.length-1].value,a,Ps(a,v,i))?e[e.length-1].value=a:e.push({value:a}):(!_||k===a)&&e.push({value:k}),e}function Ps(i,t,{horizontal:e,minRotation:s}){const n=ft(s),o=(e?Math.sin(n):Math.cos(n))||.001,r=.75*t*(""+i).length;return Math.min(t/o,r)}class Re extends Dt{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return I(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:e,maxDefined:s}=this.getUserBounds();let{min:n,max:o}=this;const r=l=>n=e?n:l,a=l=>o=s?o:l;if(t){const l=It(n),h=It(o);l<0&&h<0?a(0):l>0&&h>0&&r(0)}if(n===o){let l=o===0?1:Math.abs(o*.05);a(o+l),t||r(n-l)}this.min=n,this.max=o}getTickLimit(){const t=this.options.ticks;let{maxTicksLimit:e,stepSize:s}=t,n;return s?(n=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,n>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${n} ticks. Limiting to 1000.`),n=1e3)):(n=this.computeTickLimit(),e=e||11),e&&(n=Math.min(e,n)),n}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,e=t.ticks;let s=this.getTickLimit();s=Math.max(2,s);const n={maxTicks:s,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:e.includeBounds!==!1},o=this._range||this,r=dl(n,o);return t.bounds==="ticks"&&Vs(r,this,"value"),t.reverse?(r.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),r}configure(){const t=this.ticks;let e=this.min,s=this.max;if(super.configure(),this.options.offset&&t.length){const n=(s-e)/Math.max(t.length-1,1)/2;e-=n,s+=n}this._startValue=e,this._endValue=s,this._valueRange=s-e}getLabelForValue(t){return gi(t,this.chart.options.locale,this.options.ticks.format)}}class Ds extends Re{determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=W(t)?t:0,this.max=W(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),e=t?this.width:this.height,s=ft(this.options.ticks.minRotation),n=(t?Math.sin(s):Math.cos(s))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,o.lineHeight/n))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}S(Ds,"id","linear"),S(Ds,"defaults",{ticks:{callback:Be.formatters.numeric}});const ee=i=>Math.floor(dt(i)),vt=(i,t)=>Math.pow(10,ee(i)+t);function Os(i){return i/Math.pow(10,ee(i))===1}function Ts(i,t,e){const s=Math.pow(10,e),n=Math.floor(i/s);return Math.ceil(t/s)-n}function fl(i,t){const e=t-i;let s=ee(e);for(;Ts(i,t,s)>10;)s++;for(;Ts(i,t,s)<10;)s--;return Math.min(s,ee(i))}function ul(i,{min:t,max:e}){t=X(i.min,t);const s=[],n=ee(t);let o=fl(t,e),r=o<0?Math.pow(10,Math.abs(o)):1;const a=Math.pow(10,o),l=n>o?Math.pow(10,n):0,h=Math.round((t-l)*r)/r,c=Math.floor((t-l)/a/10)*a*10;let d=Math.floor((h-c)/Math.pow(10,o)),f=X(i.min,Math.round((l+c+d*Math.pow(10,o))*r)/r);for(;f<e;)s.push({value:f,major:Os(f),significand:d}),d>=10?d=d<15?15:20:d++,d>=20&&(o++,d=2,r=o>=0?1:r),f=Math.round((l+c+d*Math.pow(10,o))*r)/r;const u=X(i.max,f);return s.push({value:u,major:Os(u),significand:d}),s}class Ls extends Dt{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){const s=Re.prototype.parse.apply(this,[t,e]);if(s===0){this._zero=!0;return}return W(s)&&s>0?s:null}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=W(t)?Math.max(0,t):null,this.max=W(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!W(this._userMin)&&(this.min=t===vt(this.min,0)?vt(this.min,-1):vt(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let s=this.min,n=this.max;const o=a=>s=t?s:a,r=a=>n=e?n:a;s===n&&(s<=0?(o(1),r(10)):(o(vt(s,-1)),r(vt(n,1)))),s<=0&&o(vt(n,-1)),n<=0&&r(vt(s,1)),this.min=s,this.max=n}buildTicks(){const t=this.options,e={min:this._userMin,max:this._userMax},s=ul(e,this);return t.bounds==="ticks"&&Vs(s,this,"value"),t.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}getLabelForValue(t){return t===void 0?"0":gi(t,this.chart.options.locale,this.options.ticks.format)}configure(){const t=this.min;super.configure(),this._startValue=dt(t),this._valueRange=dt(this.max)-dt(t)}getPixelForValue(t){return(t===void 0||t===0)&&(t=this.min),t===null||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(dt(t)-this._startValue)/this._valueRange)}getValueForPixel(t){const e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}}S(Ls,"id","logarithmic"),S(Ls,"defaults",{ticks:{callback:Be.formatters.logarithmic,major:{enabled:!0}}});function oi(i){const t=i.ticks;if(t.display&&i.display){const e=V(t.backdropPadding);return P(t.font&&t.font.size,R.font.size)+e.height}return 0}function gl(i,t,e){return e=z(e)?e:[e],{w:wo(i,t.string,e),h:e.length*t.lineHeight}}function As(i,t,e,s,n){return i===s||i===n?{start:t-e/2,end:t+e/2}:i<s||i>n?{start:t-e,end:t}:{start:t,end:t+e}}function pl(i){const t={l:i.left+i._padding.left,r:i.right-i._padding.right,t:i.top+i._padding.top,b:i.bottom-i._padding.bottom},e=Object.assign({},t),s=[],n=[],o=i._pointLabels.length,r=i.options.pointLabels,a=r.centerPointLabels?B/o:0;for(let l=0;l<o;l++){const h=r.setContext(i.getPointLabelContext(l));n[l]=h.padding;const c=i.getPointPosition(l,i.drawingArea+n[l],a),d=H(h.font),f=gl(i.ctx,d,i._pointLabels[l]);s[l]=f;const u=J(i.getIndexAngle(l)+a),p=Math.round(ci(u)),m=As(p,c.x,f.w,0,180),g=As(p,c.y,f.h,90,270);ml(e,t,u,m,g)}i.setCenterPoint(t.l-e.l,e.r-t.r,t.t-e.t,e.b-t.b),i._pointLabelItems=xl(i,s,n)}function ml(i,t,e,s,n){const o=Math.abs(Math.sin(e)),r=Math.abs(Math.cos(e));let a=0,l=0;s.start<t.l?(a=(t.l-s.start)/o,i.l=Math.min(i.l,t.l-a)):s.end>t.r&&(a=(s.end-t.r)/o,i.r=Math.max(i.r,t.r+a)),n.start<t.t?(l=(t.t-n.start)/r,i.t=Math.min(i.t,t.t-l)):n.end>t.b&&(l=(n.end-t.b)/r,i.b=Math.max(i.b,t.b+l))}function bl(i,t,e){const s=i.drawingArea,{extra:n,additionalAngle:o,padding:r,size:a}=e,l=i.getPointPosition(t,s+n+r,o),h=Math.round(ci(J(l.angle+K))),c=kl(l.y,a.h,h),d=yl(h),f=vl(l.x,a.w,d);return{visible:!0,x:l.x,y:c,textAlign:d,left:f,top:c,right:f+a.w,bottom:c+a.h}}function _l(i,t){if(!t)return!0;const{left:e,top:s,right:n,bottom:o}=i;return!(lt({x:e,y:s},t)||lt({x:e,y:o},t)||lt({x:n,y:s},t)||lt({x:n,y:o},t))}function xl(i,t,e){const s=[],n=i._pointLabels.length,o=i.options,{centerPointLabels:r,display:a}=o.pointLabels,l={extra:oi(o)/2,additionalAngle:r?B/n:0};let h;for(let c=0;c<n;c++){l.padding=e[c],l.size=t[c];const d=bl(i,c,l);s.push(d),a==="auto"&&(d.visible=_l(d,h),d.visible&&(h=d))}return s}function yl(i){return i===0||i===180?"center":i<180?"left":"right"}function vl(i,t,e){return e==="right"?i-=t:e==="center"&&(i-=t/2),i}function kl(i,t,e){return e===90||e===270?i-=t/2:(e>270||e<90)&&(i-=t),i}function wl(i,t,e){const{left:s,top:n,right:o,bottom:r}=e,{backdropColor:a}=t;if(!I(a)){const l=At(t.borderRadius),h=V(t.backdropPadding);i.fillStyle=a;const c=s-h.left,d=n-h.top,f=o-s+h.width,u=r-n+h.height;Object.values(l).some(p=>p!==0)?(i.beginPath(),Fe(i,{x:c,y:d,w:f,h:u,radius:l}),i.fill()):i.fillRect(c,d,f,u)}}function Ml(i,t){const{ctx:e,options:{pointLabels:s}}=i;for(let n=t-1;n>=0;n--){const o=i._pointLabelItems[n];if(!o.visible)continue;const r=s.setContext(i.getPointLabelContext(n));wl(e,r,o);const a=H(r.font),{x:l,y:h,textAlign:c}=o;Pt(e,i._pointLabels[n],l,h+a.lineHeight/2,a,{color:r.color,textAlign:c,textBaseline:"middle"})}}function kn(i,t,e,s){const{ctx:n}=i;if(e)n.arc(i.xCenter,i.yCenter,t,0,Q);else{let o=i.getPointPosition(0,t);n.moveTo(o.x,o.y);for(let r=1;r<s;r++)o=i.getPointPosition(r,t),n.lineTo(o.x,o.y)}}function Sl(i,t,e,s,n){const o=i.ctx,r=t.circular,{color:a,lineWidth:l}=t;!r&&!s||!a||!l||e<0||(o.save(),o.strokeStyle=a,o.lineWidth=l,o.setLineDash(n.dash),o.lineDashOffset=n.dashOffset,o.beginPath(),kn(i,e,r,s),o.closePath(),o.stroke(),o.restore())}function Cl(i,t,e){return pt(i,{label:e,index:t,type:"pointLabel"})}class ve extends Re{constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const t=this._padding=V(oi(this.options)/2),e=this.width=this.maxWidth-t.width,s=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+s/2+t.top),this.drawingArea=Math.floor(Math.min(e,s)/2)}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!1);this.min=W(t)&&!isNaN(t)?t:0,this.max=W(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/oi(this.options))}generateTickLabels(t){Re.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((e,s)=>{const n=F(this.options.pointLabels.callback,[e,s],this);return n||n===0?n:""}).filter((e,s)=>this.chart.getDataVisibility(s))}fit(){const t=this.options;t.display&&t.pointLabels.display?pl(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,s,n){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((s-n)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,s,n))}getIndexAngle(t){const e=Q/(this._pointLabels.length||1),s=this.options.startAngle||0;return J(t*e+ft(s))}getDistanceFromCenterForValue(t){if(I(t))return NaN;const e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(I(t))return NaN;const e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){const e=this._pointLabels||[];if(t>=0&&t<e.length){const s=e[t];return Cl(this.getContext(),t,s)}}getPointPosition(t,e,s=0){const n=this.getIndexAngle(t)-K+s;return{x:Math.cos(n)*e+this.xCenter,y:Math.sin(n)*e+this.yCenter,angle:n}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){const{left:e,top:s,right:n,bottom:o}=this._pointLabelItems[t];return{left:e,top:s,right:n,bottom:o}}drawBackground(){const{backgroundColor:t,grid:{circular:e}}=this.options;if(t){const s=this.ctx;s.save(),s.beginPath(),kn(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),s.closePath(),s.fillStyle=t,s.fill(),s.restore()}}drawGrid(){const t=this.ctx,e=this.options,{angleLines:s,grid:n,border:o}=e,r=this._pointLabels.length;let a,l,h;if(e.pointLabels.display&&Ml(this,r),n.display&&this.ticks.forEach((c,d)=>{if(d!==0||d===0&&this.min<0){l=this.getDistanceFromCenterForValue(c.value);const f=this.getContext(d),u=n.setContext(f),p=o.setContext(f);Sl(this,u,l,r,p)}}),s.display){for(t.save(),a=r-1;a>=0;a--){const c=s.setContext(this.getPointLabelContext(a)),{color:d,lineWidth:f}=c;!f||!d||(t.lineWidth=f,t.strokeStyle=d,t.setLineDash(c.borderDash),t.lineDashOffset=c.borderDashOffset,l=this.getDistanceFromCenterForValue(e.ticks.reverse?this.min:this.max),h=this.getPointPosition(a,l),t.beginPath(),t.moveTo(this.xCenter,this.yCenter),t.lineTo(h.x,h.y),t.stroke())}t.restore()}}drawBorder(){}drawLabels(){const t=this.ctx,e=this.options,s=e.ticks;if(!s.display)return;const n=this.getIndexAngle(0);let o,r;t.save(),t.translate(this.xCenter,this.yCenter),t.rotate(n),t.textAlign="center",t.textBaseline="middle",this.ticks.forEach((a,l)=>{if(l===0&&this.min>=0&&!e.reverse)return;const h=s.setContext(this.getContext(l)),c=H(h.font);if(o=this.getDistanceFromCenterForValue(this.ticks[l].value),h.showLabelBackdrop){t.font=c.string,r=t.measureText(a.label).width,t.fillStyle=h.backdropColor;const d=V(h.backdropPadding);t.fillRect(-r/2-d.left,-o-c.size/2-d.top,r+d.width,c.size+d.height)}Pt(t,a.label,0,-o,c,{color:h.color,strokeColor:h.textStrokeColor,strokeWidth:h.textStrokeWidth})}),t.restore()}drawTitle(){}}S(ve,"id","radialLinear"),S(ve,"defaults",{display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:Be.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(t){return t},padding:5,centerPointLabels:!1}}),S(ve,"defaultRoutes",{"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"}),S(ve,"descriptors",{angleLines:{_fallback:"grid"}});const We={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},U=Object.keys(We);function Fs(i,t){return i-t}function Is(i,t){if(I(t))return null;const e=i._adapter,{parser:s,round:n,isoWeekday:o}=i._parseOpts;let r=t;return typeof s=="function"&&(r=s(r)),W(r)||(r=typeof s=="string"?e.parse(r,s):e.parse(r)),r===null?null:(n&&(r=n==="week"&&(Le(o)||o===!0)?e.startOf(r,"isoWeek",o):e.startOf(r,n)),+r)}function zs(i,t,e,s){const n=U.length;for(let o=U.indexOf(i);o<n-1;++o){const r=We[U[o]],a=r.steps?r.steps:Number.MAX_SAFE_INTEGER;if(r.common&&Math.ceil((e-t)/(a*r.size))<=s)return U[o]}return U[n-1]}function Pl(i,t,e,s,n){for(let o=U.length-1;o>=U.indexOf(e);o--){const r=U[o];if(We[r].common&&i._adapter.diff(n,s,r)>=t-1)return r}return U[e?U.indexOf(e):0]}function Dl(i){for(let t=U.indexOf(i)+1,e=U.length;t<e;++t)if(We[U[t]].common)return U[t]}function Rs(i,t,e){if(!e)i[t]=!0;else if(e.length){const{lo:s,hi:n}=di(e,t),o=e[s]>=t?e[s]:e[n];i[o]=!0}}function Ol(i,t,e,s){const n=i._adapter,o=+n.startOf(t[0].value,s),r=t[t.length-1].value;let a,l;for(a=o;a<=r;a=+n.add(a,1,s))l=e[a],l>=0&&(t[l].major=!0);return t}function Es(i,t,e){const s=[],n={},o=t.length;let r,a;for(r=0;r<o;++r)a=t[r],n[a]=r,s.push({value:a,major:!1});return o===0||!e?s:Ol(i,s,n,e)}class Ee extends Dt{constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){const s=t.time||(t.time={}),n=this._adapter=new Tr._date(t.adapters.date);n.init(e),Kt(s.displayFormats,n.formats()),this._parseOpts={parser:s.parser,round:s.round,isoWeekday:s.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return t===void 0?null:Is(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,e=this._adapter,s=t.time.unit||"day";let{min:n,max:o,minDefined:r,maxDefined:a}=this.getUserBounds();function l(h){!r&&!isNaN(h.min)&&(n=Math.min(n,h.min)),!a&&!isNaN(h.max)&&(o=Math.max(o,h.max))}(!r||!a)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),n=W(n)&&!isNaN(n)?n:+e.startOf(Date.now(),s),o=W(o)&&!isNaN(o)?o:+e.endOf(Date.now(),s)+1,this.min=Math.min(n,o-1),this.max=Math.max(n+1,o)}_getLabelBounds(){const t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY,s=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],s=t[t.length-1]),{min:e,max:s}}buildTicks(){const t=this.options,e=t.time,s=t.ticks,n=s.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&n.length&&(this.min=this._userMin||n[0],this.max=this._userMax||n[n.length-1]);const o=this.min,r=this.max,a=lo(n,o,r);return this._unit=e.unit||(s.autoSkip?zs(e.minUnit,this.min,this.max,this._getLabelCapacity(o)):Pl(this,a.length,e.minUnit,this.min,this.max)),this._majorUnit=!s.major.enabled||this._unit==="year"?void 0:Dl(this._unit),this.initOffsets(n),t.reverse&&a.reverse(),Es(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e=0,s=0,n,o;this.options.offset&&t.length&&(n=this.getDecimalForValue(t[0]),t.length===1?e=1-n:e=(this.getDecimalForValue(t[1])-n)/2,o=this.getDecimalForValue(t[t.length-1]),t.length===1?s=o:s=(o-this.getDecimalForValue(t[t.length-2]))/2);const r=t.length<3?.5:.25;e=st(e,0,r),s=st(s,0,r),this._offsets={start:e,end:s,factor:1/(e+1+s)}}_generate(){const t=this._adapter,e=this.min,s=this.max,n=this.options,o=n.time,r=o.unit||zs(o.minUnit,e,s,this._getLabelCapacity(e)),a=P(n.ticks.stepSize,1),l=r==="week"?o.isoWeekday:!1,h=Le(l)||l===!0,c={};let d=e,f,u;if(h&&(d=+t.startOf(d,"isoWeek",l)),d=+t.startOf(d,h?"day":r),t.diff(s,e,r)>1e5*a)throw new Error(e+" and "+s+" are too far apart with stepSize of "+a+" "+r);const p=n.ticks.source==="data"&&this.getDataTimestamps();for(f=d,u=0;f<s;f=+t.add(f,a,r),u++)Rs(c,f,p);return(f===s||n.bounds==="ticks"||u===1)&&Rs(c,f,p),Object.keys(c).sort(Fs).map(m=>+m)}getLabelForValue(t){const e=this._adapter,s=this.options.time;return s.tooltipFormat?e.format(t,s.tooltipFormat):e.format(t,s.displayFormats.datetime)}format(t,e){const n=this.options.time.displayFormats,o=this._unit,r=e||n[o];return this._adapter.format(t,r)}_tickFormatFunction(t,e,s,n){const o=this.options,r=o.ticks.callback;if(r)return F(r,[t,e,s],this);const a=o.time.displayFormats,l=this._unit,h=this._majorUnit,c=l&&a[l],d=h&&a[h],f=s[e],u=h&&d&&f&&f.major;return this._adapter.format(t,n||(u?d:c))}generateTickLabels(t){let e,s,n;for(e=0,s=t.length;e<s;++e)n=t[e],n.label=this._tickFormatFunction(n.value,e,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const e=this._offsets,s=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+s)*e.factor)}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+s*(this.max-this.min)}_getLabelSize(t){const e=this.options.ticks,s=this.ctx.measureText(t).width,n=ft(this.isHorizontal()?e.maxRotation:e.minRotation),o=Math.cos(n),r=Math.sin(n),a=this._resolveTickFontOptions(0).size;return{w:s*o+a*r,h:s*r+a*o}}_getLabelCapacity(t){const e=this.options.time,s=e.displayFormats,n=s[e.unit]||s.millisecond,o=this._tickFormatFunction(t,0,Es(this,[t],this._majorUnit),n),r=this._getLabelSize(o),a=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return a>0?a:1}getDataTimestamps(){let t=this._cache.data||[],e,s;if(t.length)return t;const n=this.getMatchingVisibleMetas();if(this._normalized&&n.length)return this._cache.data=n[0].controller.getAllParsedValues(this);for(e=0,s=n.length;e<s;++e)t=t.concat(n[e].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let e,s;if(t.length)return t;const n=this.getLabels();for(e=0,s=n.length;e<s;++e)t.push(Is(this,n[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return co(t.sort(Fs))}}S(Ee,"id","time"),S(Ee,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function ke(i,t,e){let s=0,n=i.length-1,o,r,a,l;e?(t>=i[s].pos&&t<=i[n].pos&&({lo:s,hi:n}=Qe(i,"pos",t)),{pos:o,time:a}=i[s],{pos:r,time:l}=i[n]):(t>=i[s].time&&t<=i[n].time&&({lo:s,hi:n}=Qe(i,"time",t)),{time:o,pos:a}=i[s],{time:r,pos:l}=i[n]);const h=r-o;return h?a+(l-a)*(t-o)/h:a}class Bs extends Ee{constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=ke(e,this.min),this._tableRange=ke(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:e,max:s}=this,n=[],o=[];let r,a,l,h,c;for(r=0,a=t.length;r<a;++r)h=t[r],h>=e&&h<=s&&n.push(h);if(n.length<2)return[{time:e,pos:0},{time:s,pos:1}];for(r=0,a=n.length;r<a;++r)c=n[r+1],l=n[r-1],h=n[r],Math.round((c+l)/2)!==h&&o.push({time:h,pos:r/(a-1)});return o}_generate(){const t=this.min,e=this.max;let s=super.getDataTimestamps();return(!s.includes(t)||!s.length)&&s.splice(0,0,t),(!s.includes(e)||s.length===1)&&s.push(e),s.sort((n,o)=>n-o)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const e=this.getDataTimestamps(),s=this.getLabelTimestamps();return e.length&&s.length?t=this.normalize(e.concat(s)):t=e.length?e:s,t=this._cache.all=t,t}getDecimalForValue(t){return(ke(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return ke(this._table,s*this._tableRange+this._minPos,!0)}}S(Bs,"id","timeseries"),S(Bs,"defaults",Ee.defaults);export{Mt as C,Ds as L,qe as P,Cs as a,xe as b,Fl as c,Ll as d,Al as p};
