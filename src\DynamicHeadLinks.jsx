
import React, { useEffect } from "react";
import MkdSDK from "Utils/MkdSDK";

let sdk = new MkdSDK();

const DynamicHeadLinks = () => {
  useEffect(() => {
    const fetchAndAppendHeadLinks = async () => {
      try {
        const result = await sdk.getDynamicHead();
       

        if (!result.error) {
          result?.list?.forEach((val) => {
            appendElementToHead(val?.headlink);
          });
        }
      } catch (error) {
        console.error("Error fetching head elements:", error);
      }
    };

    const appendElementToHead = (content) => {
      const parser = new DOMParser();
      const parsedDocument = parser.parseFromString(content, "text/html");

      const scriptElement = parsedDocument.querySelector("script");
      const linkElement = parsedDocument.querySelector("link");

      if (scriptElement) {
        // Handle script elements
        const newScript = document.createElement("script");
        if (scriptElement.src) {
          // External script
          newScript.src = scriptElement.src;
          newScript.async = scriptElement.async || true;
          newScript.type = scriptElement.type || "text/javascript";
        } else {
          // Inline script
          newScript.type = scriptElement.type || "text/javascript";
          newScript.textContent = scriptElement.textContent;
        }
        document.head.appendChild(newScript);
      } else if (linkElement) {
        // Handle link elements (e.g., stylesheets)
        const newLink = document.createElement("link");
        newLink.href = linkElement.href;
        newLink.rel = linkElement.rel || "stylesheet";
        document.head.appendChild(newLink);
      }
    };

    fetchAndAppendHeadLinks();
  }, []);

  return null; // This component does not render anything in the UI
};

export default DynamicHeadLinks;
