import React, { useState } from "react";
import { AuthContext, tokenExpireError } from "../authContext";
import MkdSDK from "../utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { GlobalContext, showToast } from "../globalContext";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { getNonNullValue } from "../utils/utils";
import PaginationBar from "../components/PaginationBar";
import AddButton from "../components/AddButton";
import {
  useStripe,
  PaymentElement,
  useElements,
  Elements,
  ExpressCheckoutElement,
} from "@stripe/react-stripe-js";
import axios from "axios";

let sdk = new MkdSDK();

const columns = [
  // {
  //   header: "Action",
  //   accessor: "",
  // },
  {
    header: "Id",
    accessor: "id",
  },
  {
    header: "name",
    accessor: "name",
  },
  {
    header: "percent_off",
    accessor: "percent_off",
  },
  {
    header: "redeem_by",
    accessor: "redeem_by",
  },
  {
    header: "times_redeemed",
    accessor: "times_redeemed",
  },
  {
    header: "valid",
    accessor: "valid",
  },
  {
    header: "amount_off",
    accessor: "amount_off",
  },
  {
    header: "created",
    accessor: "created",
  },
  {
    header: "duration",
    accessor: "duration",
  },
  {
    header: "livemode",
    accessor: "livemode",
  },

  {
    header: "Status",
    accessor: "expired",
    mapping: ["Active", "Expired"],
  },
];
const AdminStripeDiscountListPage = () => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { dispatch } = React.useContext(AuthContext);
  const [query, setQuery] = React.useState("");
  const [data, setCurrentTableData] = React.useState([]);
  const [pageSize, setPageSize] = React.useState(10);
  const [pageCount, setPageCount] = React.useState(0);
  const [dataTotal, setDataTotal] = React.useState(0);
  // const [currentPage, setPage] = React.useState(0);
  const [canPreviousPage, setCanPreviousPage] = React.useState(false);
  const [canNextPage, setCanNextPage] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [coupons, setCoupons] = useState([]);
  const [currentPage, setCurrentPage] = useState(0);
  // const stripe = useStripe();
  const navigate = useNavigate();

  const schema = yup.object({
    id: yup.string(),
    email: yup.string(),
    role: yup.string(),
    status: yup.string(),
    messages: yup.string(),
  });

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const resetForm = async () => {
    reset();
    await getData(0, pageSize);
  };

  const selectRole = ["", "admin", "user"];
  const selectStatus = [
    { key: "", value: "All" },
    { key: "0", value: "Active" },
    { key: "1", value: "Expired" },
  ];
  const status = [
    { key: "0", value: "Active" },
    { key: "1", value: "Expired" },
  ];

  function updatePageSize(limit) {
    (async function () {
      setPageSize(limit);
      await getData(0, limit);
    })();
  }
  // function previousPage() {
  //   (async function () {
  //     await getData(currentPage - 1 > 0 ? currentPage - 1 : 0, pageSize);
  //   })();
  // }

  // function nextPage() {
  //   (async function () {
  //     await getData(
  //       currentPage + 1 <= pageCount ? currentPage + 1 : 0,
  //       pageSize
  //     );
  //   })();
  // }

  const nextPage = () => setCurrentPage(currentPage + 1);
  const previousPage = () => setCurrentPage(currentPage - 1);

  async function getData(pageNum, limitNum, data) {
    setLoading(true);
    const API_KEY =
      "***********************************************************************************************************";

    const headers = {
      Authorization: ` Bearer ${API_KEY}`,
    };
    try {
      const { data } = await axios.get(
        "https://api.stripe.com/v1/coupons?limit=4",
        { headers }
      );
      setCurrentTableData(data?.data);
    } catch (error) {
      setLoading(false);
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    }
  }

  const onSubmit = (data) => {
    const email = getNonNullValue(data.email);
    const role = getNonNullValue(data.role);
    const code = getNonNullValue(data.code);
    const expired = getNonNullValue(data.status);
    const id = getNonNullValue(data.id);
    const first_name = getNonNullValue(data.firstName);
    const last_name = getNonNullValue(data.lastName);
    // console.log("status", status);
    getData(0, pageSize, {
      email,
      role,
      code,
      expired,
      id,
      first_name,
      last_name,
    });
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "stripeDiscount",
      },
    });

    (async function () {
      await getData(0, pageSize);
    })();
  }, []);

  const deleteItem = async (id) => {
    try {
      sdk.setTable("coupon");
      const result = await sdk.callRestAPI({ id }, "PUT");
      // setCurrentTableData((list) =>
      //   list.filter((x) => Number(x.id) !== Number(id))
      // );
    } catch (err) {
      throw new Error(err);
    }
  };
  const onSubmitReward = async (_data) => {
    try {
      const result = await sdk.sendreward({
        email: _data.email,
        messages: parseInt(_data.messagesNum),
      });

      if (!result.error) {
        showToast(globalDispatch, "Reward Sent");
        // navigate("/admin/payments");
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error) {
      console.log("Error", error);
      setError("create_at", {
        type: "manual",
        message: error.message,
      });
      tokenExpireError(dispatch, error.message);
    }
  };

  const startIndex = currentPage * parseInt(pageSize);

  return (
    <>
      {/* Send Reward*/}
      <div className=" shadow-md rounded  mx-auto p-5 mb-8"></div>
      {/* Search Coupons*/}
      {/* <form
        className="p-5 bg-white shadow rounded mb-10"
        onSubmit={handleSubmit(onSubmit)}
      >
        <h4 className="text-2xl font-medium">Search Coupons</h4>
        <div className="filter-form-holder mt-10 flex flex-wrap">
          <div className="mb-4 w-full md:w-1/2 pr-2 pl-2">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              ID
            </label>
            <input
              type="text"
              placeholder="ID"
              {...register("id")}
              className="shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"
            />
            <p className="text-red-500 text-xs italic">{errors.id?.message}</p>
          </div>
          <div className="mb-4 w-full md:w-1/2 pr-2 pl-2">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Code
            </label>
            <input
              type="code"
              placeholder="code"
              {...register("code")}
              className="shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"
            />
            <p className="text-red-500 text-xs italic">
              {errors.code?.message}
            </p>
          </div>
          <div className="mb-4 w-full md:w-1/2 pr-2 pl-2">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Messages
            </label>
            <input
              {...register("messages")}
              name="messages"
              className={
                "shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"
              }
              id="messages"
              type="text"
              placeholder="message"
            />
            <p className="text-red-500 text-xs italic"></p>
          </div>

          <div className="mb-4 w-full md:w-1/2 pr-2 pl-2">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Status
            </label>
            <select
              className="shadow border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"
              {...register("status")}
            >
              <option name="status" defaultValue=""></option>

              {status?.map((option) => (
                <option
                  name="status"
                  value={option.key}
                  key={option.key}
                  defaultValue=""
                >
                  {option.value}
                </option>
              ))}
            </select>
            <p className="text-red-500 text-xs italic"></p>
          </div>

         
        </div>
        <button
          type="submit"
          className="inline ml-2 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Search
        </button>
        <button
          onClick={() => {
            resetForm();
          }}
          type="button"
          className=" inline ml-2 bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Clear
        </button>
      </form> */}

      <div className="overflow-x-auto  p-5 bg-white shadow rounded">
        <div className="mb-3 text-center justify-between w-full flex  ">
          <h4 className="text-2xl font-medium">Stripe Discount </h4>
          {/* <AddButton link={"/admin/add-rewards"} /> */}
        </div>

        <div>
          <h1>Discounts</h1>
          <ul>
            {coupons?.map((coupon) => (
              <li key={coupon.id}>
                {coupon.id} - {coupon.name}
              </li>
            ))}
          </ul>
        </div>

        <div className="shadow overflow-x-auto border-b border-gray-200 ">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {columns.map((column, index) => (
                  <th
                    key={index}
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    {column.header}
                    <span>
                      {column.isSorted
                        ? column.isSortedDesc
                          ? " ▼"
                          : " ▲"
                        : ""}
                    </span>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {data?.slice(startIndex, startIndex + pageSize).map((row, i) => {
                return (
                  <tr key={i}>
                    {columns.map((cell, index) => {
                      if (cell.accessor == "") {
                        return (
                          <td
                            key={index}
                            className="px-6 py-4 whitespace-nowrap"
                          >
                            <button
                              onClick={() => {
                                navigate("/admin/edit-rewards/" + row.id, {
                                  state: row,
                                });
                              }}
                            >
                              {" "}
                              Edit
                            </button>
                            {/* <button
                              className="text-xs px-1 text-red-500"
                              onClick={() => deleteItem(row.id)}
                            >
                             
                              Delete
                            </button> */}
                          </td>
                        );
                      }
                      if (cell.mapping) {
                        return (
                          <td
                            key={index}
                            className="px-6 py-4 whitespace-nowrap"
                          >
                            {cell.mapping[row[cell.accessor]]}
                          </td>
                        );
                      }
                      return (
                        <td key={index} className="px-6 py-4 whitespace-nowrap">
                          {row[cell.accessor]}
                        </td>
                      );
                    })}
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
        <PaginationBar
          pageSize={pageSize}
          canPreviousPage={currentPage > 0 ? true : false}
          canNextPage={(currentPage + 1) * pageSize < history.length}
          updatePageSize={(size) => {
            setPageSize(size);
            setCurrentPage(0);
          }}
          previousPage={previousPage}
          nextPage={nextPage}
        />
      </div>
      {/* <PaginationBar
        currentPage={currentPage}
        pageCount={pageCount}
        pageSize={pageSize}
        canPreviousPage={canPreviousPage}
        canNextPage={canNextPage}
        updatePageSize={updatePageSize}
        previousPage={previousPage}
        nextPage={nextPage}
      /> */}
    </>
  );
};

export default AdminStripeDiscountListPage;
