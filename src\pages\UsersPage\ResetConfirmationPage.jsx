import React, { useState } from "react";

const ResetConfirmationPage = () => {
  return (
    <div className=" min-h-screen flex items-center justify-center">
      <div className=" w-[80vw]  flex flex-col justify-between items-center gap-8 bg-red-50 p-12">
        <div className=" ">
          <p>
            <svg
              fill="red"
              xmlns="http://www.w3.org/2000/svg"
              width="34"
              height="34"
              viewBox="0 0 24 24"
            >
              <path d="M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm6.25 8.891l-1.421-1.409-6.105 6.218-3.078-2.937-1.396 1.436 4.5 4.319 7.5-7.627z" />
            </svg>
          </p>
        </div>

        <div className=" ">
          <p className=" font-bold text-xl self-start">Email sent</p>
          <p className="  text-lg">Check mail for reset link</p>
        </div>
      </div>
    </div>
  );
};

export default ResetConfirmationPage;
