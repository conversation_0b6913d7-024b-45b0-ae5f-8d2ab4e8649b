import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "../utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { AuthContext, tokenExpireError } from "../authContext";
import { GlobalContext, showToast } from "../globalContext";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { isImage, empty, isVideo, isPdf, sportList } from "../utils/utils";
import Papa from "papaparse";
import { BackButton } from "Components/BackButton";

let sdk = new MkdSDK();






const AddAdminPromptPage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const schema = yup
    .object({
      prompt: yup.string(),
      slug: yup.string(),
    })
    .required();

  const [fileObj, setFileObj] = React.useState({});
  const [uploadStatus, setUploadStatus] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [data, setData] = useState([]);

  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  // ADDING NEW ATTRIBUTES..................................STARTS
  const [inputFields, setInputFields] = useState([""]);
  const [slug, setSlug] = useState();

  const handleChange = (index, event) => {
    const values = [...inputFields];
    values[index] = event.target.value;
    setInputFields(values);
  };

  const handleAddFields = () => {
    setInputFields([...inputFields, ""]);
  };

  const handleRemoveFields = (index) => {
    const values = [...inputFields];
    values.splice(index, 1);
    setInputFields(values);
  };

  // console.log("inputFields",inputFields);

  // const handleSubmit = (event) => {
  //   event.preventDefault();
  //   // Handle form submission logic here
  //   console.log('Form submitted:', inputFields);
  // };

  // ADDING NEW ATTRIBUTES..................................ENDS

  const previewImage = (field, target) => {
    let tempFileObj = fileObj;
    tempFileObj[field] = {
      file: target.files[0],
      tempURL: URL.createObjectURL(target.files[0]),
    };
    setFileObj({ ...tempFileObj });
  };

  // console.log(slug);
  

  const onSubmit = async (_data) => {
    try {
      for (let item in fileObj) {
        let formData = new FormData();
        formData.append("file", fileObj[item].file);
        let uploadResult = await sdk.uploadImage(formData);
        _data[item] = uploadResult.url;
      }

      sdk.setTable("custom_prompt");

      const result = await sdk.callRestAPI(
        {
          prompt: _data.prompt,
          slug: _data.slug ?? slug,
        },
        "POST"
      );
      // console.log("result", result);
      if (!result.error) {
        showToast(globalDispatch, "Added");
        navigate("/admin/prompt");
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error) {
      console.log("Error", error);
      setError("name", {
        type: "manual",
        message: error.message,
      });
      tokenExpireError(dispatch, error.message);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "prompt",
      },
    });
  }, []);

  const handleFileChange = (event) => {
    setIsDownloading(true);
    const file = event.target.files[0];

    if (file) {
      Papa.parse(file, {
        header: true,
        complete: (results) => {
          const filteredData = results.data.filter((row) => {
            // Filter out empty rows
            return Object.values(row).some(
              (value) => value !== null && value !== ""
            );
          });
          setData(filteredData);
          // sendToBackend(filteredData);
        },
        error: (error) => {
          console.error("Error parsing the file: ", error);
        },
      });
    }
    setIsDownloading(true);
  };

  const sendToBackend = async (parsedData) => {
    sdk.setTable("prompt");
    setIsUploading(true);
    try {
      // const promises = parsedData.map((item) =>
      const promises = data.map((item) =>
        sdk.callRestAPI(
          {
            prompt: item?.prompt, // Adjust according to your API requirements
          },
          "POST"
        )
      );
      await Promise.all(promises);
      showToast(globalDispatch, "Added");
      navigate("/admin/prompt");
      // setUploadStatus("Upload successful!");
    } catch (error) {
      console.error("Error uploading data:", error);
      setUploadStatus("Upload failed. Please try again.");
    }
    setIsUploading(false);
  };

  return (
    <div className=" shadow-md rounded  mx-auto p-5">
      <div className=" mb-4"> 
        <BackButton/>
      </div>
      <h4 className="text-2xl font-medium mb-2">Add Prompt</h4>
      <form className=" w-full max-w-lg" onSubmit={handleSubmit(onSubmit)}>
        <div className="mb-4  ">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="prompt"
          >
            Prompt
          </label>
          <textarea
            placeholder="Prompt"
            {...register("prompt")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline min-h-40 ${
              errors.prompt?.message ? "border-red-500" : ""
            }`}
            row={30}
          ></textarea>
          <p className="text-red-500 text-xs italic">
            {errors.prompt?.message}
          </p>
        </div>
        <div className="mb-4  ">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="slug"
          >
            Slug
          </label>
          <select
            name="slug"
            id="slug"
            className="shadow border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline capitalize"
            value={slug}
            {...register("slug", {
              onChange: (e) => setSlug(e.target.value),
            })}
          >
            <option  value="">
                Select sport
              </option>
            {sportList.map((option) => (
              <option className="capitalize" name={option} value={option} key={option}>
                {option}
              </option>
            ))}
          </select>
         
          <p className="text-red-500 text-xs italic">
            {errors.slug?.message}
          </p>
        </div>

        <button
          type="submit"
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Submit
        </button>
      </form>

      {/* <div className="my-4  ">
        <div className=" space-y-2">
          <label
            className="block text-gray-700 text-base font-bold mb-"
            htmlFor="update_at"
          >
            Upload csv
          </label>
          <div>
            <input
              className=""
              type="file"
              accept=".csv"
              onChange={handleFileChange}
            />
            
            {isUploading && <p>uploading...</p>}
            {uploadStatus && <p>{uploadStatus}</p>}
          </div>
          <button
            type="button"
            disabled={!isDownloading}
            onClick={sendToBackend}
            className="disabled:bg-blue-200 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
          >
            Submit
          </button>
        </div>
      </div> */}
    </div>
  );
};

export default AddAdminPromptPage;
