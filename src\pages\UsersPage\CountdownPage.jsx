import React, { useState } from "react";
import loginBgImage from "../../assets/images/loginbg.png";
import filledinnerbg from "../../assets/images/filledinnerbg.png";

const CountdownPage = () => {
  return (
    <div
      className=" min-h-[95vh] flex gap-4 items-center justify-center rounded-3xl p-2 relative m-4"
      style={{
        backgroundImage: `url(${loginBgImage})`,
        backgroundSize: "cover",
        backgroundPosition: "center center",
      }}
    >
      <div className=" rounded-xl items-center text-center h-full w-full lg:flex lg:flex-row flex flex-col-reverse gap-4 relative  ">
        <div className="  border-red-400h-full w-full  ">
          {/* image */}
          <img className="w-full max-h-[80vh]" src={filledinnerbg} alt="" />
        </div>

        {/* texts */}
        {/* top-1/2 bottom-1/2 right-0 left-0 */}
        <div className="  flex flex-col justify-around items-center gap-8 bg-red-600 p-4 z-100  bg-opacity-20 h-full w-full ">
          <div className="  w-full">
            <p className=" md:text-7xl text-4xl text-white">Kaizenwin</p>
          </div>
          <div className="  w-full">
            <p className="text-red-600 md:text-7xl text-4xl font-bold">Coming soon !!!</p>
          </div>
          <div className=" bg-white skew-x-6 bg-opacity-25 flex flex-col w-full">
            <div className=" w-full">
              <p className=" md:text-6xl text-4xl font-bold">20, 40, 12</p>
              <p className=" md:text-6xl text-4xl">days, hrs, min</p>
            </div>
          <p className=" self-end md:text-4xl text-2xl">until KaizenWin launch</p>
          </div>
          <div className=" w-full">
            <p className="mb-2 text-white md:text-lg text-base">Enter your email and be the first to find out</p>
            <div className=" bg-black py-4 w-full">
              <input
                className=" rounded-full w-1/2"
                type="text"
                placeholder="enter your email"
              />
              <button type="submit" className=" bg-red-700 px-4 py-2 rounded-full ml-4">
                {" "}
                Submit
              </button>
            </div>
          </div>
          <p className=" self-end text-red-600">social links</p>
        </div>
      </div>
    </div>
  );
};

export default CountdownPage;
