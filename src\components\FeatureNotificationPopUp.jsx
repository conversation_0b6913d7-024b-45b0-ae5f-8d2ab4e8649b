import React, { useEffect, useState } from "react";
// import Modal from './Modal';
import SkeletonLoading from "./SkeletonLoading";
import { Modal } from "Components/Modal/Modal";
import { useNavigate } from "react-router";
import { useData } from "Src/dataContext";

const FeatureNotificationPopUp = ({handleGetMessage}) => {
  const [showModal, setShowModal] = useState(true);
  const { cms } = useData();
  const navigate = useNavigate();
  let dailyQuiz = {
    quiz: "hello",
  };

  // useEffect(() => {
  //   // Check if the modal has already been shown
  //   const modalStatus = localStorage.getItem("modalShown");

  //   if (!modalStatus && cms) {
  //     // Show the modal after 5 seconds if it hasn't been shown yet
  //     const timer = setTimeout(() => {
  //       setShowModal(true);
  //     }, 5000);

  //     // Cleanup the timer
  //     return () => clearTimeout(timer);
  //   }
  // }, [cms]);

  const handleClose = () => {
    setShowModal(false);
    localStorage.setItem("modalShown", "true");
  };
  // const handleGetMessage = () => {
  //   setShowModal(false);
  //   localStorage.setItem("modalShown", "true");

  //   navigate("/user/buy");
  // };

  return (
    <div>
      {/* <Modal show={showModal} onClose={handleClose} /> */}

      <>
        {showModal && (
          <>
            <h2 className=" w-full pt-4 text-center text-black  md:text-base text-sm">
              {!cms && <SkeletonLoading />}
              {
                cms?.find((item) => item.content_key === "Feature_Notification")
                  ?.content_value
              }
            </h2>

            <div className=" flex justify-center items-center mt-2 ">
              <button
                className=" border px-4 py-1 rounded-full bg-companyRed text-white"
                onClick={handleGetMessage}
              >
                Free tokens
              </button>
            </div>
          </>
        )}
      </>
    </div>
  );
};

export default FeatureNotificationPopUp;
