
  import React from "react";
import { GlobalContext } from "../globalContext";
const SnackBar = () => {
  const { state: { globalMessage, toastStatus }, dispatch } = React.useContext( GlobalContext );
  const show = globalMessage.length > 0;
  return show ? (
    <div id="mkd-toast" className={ `fixed z-[999999999] top-5 right-5 flex items-center  max-w-xs md:p-4 p-[4px] text-gray-500 bg-slate-100 rounded-lg shadow-md dark:text-gray-400 ${ toastStatus === "success" ? "bg-gradient-to-bl from-green-600 to-green-800" : toastStatus === "error" ? "bg-gradient-to-bl from-red-600 to-red-800" : "bg-gradient-to-bl from-yellow-600 to-yellow-800" }`} role="alert">
      <div className="md:text-[1.2rem] text-xs font-normal text-white">{ globalMessage }</div>
      <div className="flex items-center ml-auto space-x-2 pl-2">
        <button
          type="button"
          className="bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex md:h-8 md:w-8 h-5 w-5 dark:text-gray-500 dark:hover:text-white  dark:hover:bg-gray-700"
          aria-label="Close"
          onClick={ () => {
            dispatch( { type: "SNACKBAR", payload: { message: "" } } );
          } }
        >
          <span className="sr-only">Close</span>
          <svg className="md:w-5 md:h-5 w-3 h-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path
              fillRule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clipRule="evenodd"
            ></path>
          </svg>
        </button>
      </div>
    </div>
  ) : null;
};

export default SnackBar;

