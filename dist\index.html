<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/assets/favicon-00502e86.svg" />
    <!-- <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700&display=swap" rel="stylesheet"> -->

    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;600;700&display=swap"
      rel="stylesheet"
    />

    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/inter-ui/inter.css"
    />

    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>kaizenwin</title>

    <script
      async
      src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"
    ></script>

    <!-- Google tag (gtag.js) -->

    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());

      gtag("config", "G-829TV45PYQ");
    </script>

    <!-- Tiktok Pixel  -->
    <!-- TikTok Pixel Code Start -->
    <!-- TikTok Pixel Code Start -->
    <!-- <script>
      !(function (w, d, t) {
        w.TiktokAnalyticsObject = t;
        var ttq = (w[t] = w[t] || []);
        (ttq.methods = [
          "page",
          "track",
          "identify",
          "instances",
          "debug",
          "on",
          "off",
          "once",
          "ready",
          "alias",
          "group",
          "enableCookie",
          "disableCookie",
          "holdConsent",
          "revokeConsent",
          "grantConsent",
        ]),
          (ttq.setAndDefer = function (t, e) {
            t[e] = function () {
              t.push([e].concat(Array.prototype.slice.call(arguments, 0)));
            };
          });
        for (var i = 0; i < ttq.methods.length; i++)
          ttq.setAndDefer(ttq, ttq.methods[i]);
        (ttq.instance = function (t) {
          for (var e = ttq._i[t] || [], n = 0; n < ttq.methods.length; n++)
            ttq.setAndDefer(e, ttq.methods[n]);
          return e;
        }),
          (ttq.load = function (e, n) {
            var r = "https://analytics.tiktok.com/i18n/pixel/events.js",
              o = n && n.partner;
            (ttq._i = ttq._i || {}),
              (ttq._i[e] = []),
              (ttq._i[e]._u = r),
              (ttq._t = ttq._t || {}),
              (ttq._t[e] = +new Date()),
              (ttq._o = ttq._o || {}),
              (ttq._o[e] = n || {});
            n = document.createElement("script");
            (n.type = "text/javascript"),
              (n.async = !0),
              (n.src = r + "?sdkid=" + e + "&lib=" + t);
            e = document.getElementsByTagName("script")[0];
            e.parentNode.insertBefore(n, e);
          });

        ttq.load("COF92TRC77U0PSRTVQEG");
        ttq.page();
      })(window, document, "ttq");
    </script> -->
    <!-- TikTok Pixel Code End -->
    <!-- TikTok Pixel Code End -->
    <script type="module" crossorigin src="/assets/index-4e700035.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-b0222800.js">
    <link rel="modulepreload" crossorigin href="/assets/@emotion/react-32889a6e.js">
    <link rel="modulepreload" crossorigin href="/assets/@material-ui/core-b35124d7.js">
    <link rel="modulepreload" crossorigin href="/assets/@mui/base-0e613ae5.js">
    <link rel="modulepreload" crossorigin href="/assets/react-confirm-alert-982d01ff.js">
    <link rel="modulepreload" crossorigin href="/assets/@emotion/styled-c254866c.js">
    <link rel="modulepreload" crossorigin href="/assets/@mui/icons-material-14196d48.js">
    <link rel="modulepreload" crossorigin href="/assets/@mui/material-a827c5f8.js">
    <link rel="modulepreload" crossorigin href="/assets/framer-motion-1207d130.js">
    <link rel="modulepreload" crossorigin href="/assets/react-hook-form-9610a5ab.js">
    <link rel="modulepreload" crossorigin href="/assets/@hookform/resolvers-8073603e.js">
    <link rel="modulepreload" crossorigin href="/assets/yup-48f6b7cd.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/fontawesome-svg-core-6d3776b6.js">
    <link rel="modulepreload" crossorigin href="/assets/@ckeditor/ckeditor5-react-48fc30c1.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/react-fontawesome-9b101c5c.js">
    <link rel="modulepreload" crossorigin href="/assets/@headlessui/react-421bd466.js">
    <link rel="modulepreload" crossorigin href="/assets/react-select-country-list-019ae79c.js">
    <link rel="modulepreload" crossorigin href="/assets/@uppy/aws-s3-9d3fc29b.js">
    <link rel="modulepreload" crossorigin href="/assets/@uppy/tus-2ad25a40.js">
    <link rel="modulepreload" crossorigin href="/assets/@uppy/xhr-upload-aea524bc.js">
    <link rel="modulepreload" crossorigin href="/assets/@uppy/core-f5368a8f.js">
    <link rel="modulepreload" crossorigin href="/assets/@uppy/dashboard-54af8551.js">
    <link rel="modulepreload" crossorigin href="/assets/@uppy/drag-drop-11c773af.js">
    <link rel="modulepreload" crossorigin href="/assets/@uppy/react-b1e7ccfb.js">
    <link rel="modulepreload" crossorigin href="/assets/moment-a9aaa855.js">
    <link rel="modulepreload" crossorigin href="/assets/react-input-emoji-4f88e3e1.js">
    <link rel="modulepreload" crossorigin href="/assets/react-loading-skeleton-cc719fd0.js">
    <link rel="modulepreload" crossorigin href="/assets/react-google-maps-9cda14f0.js">
    <link rel="modulepreload" crossorigin href="/assets/react-quill-873f37b1.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/free-solid-svg-icons-c030f74c.js">
    <link rel="modulepreload" crossorigin href="/assets/tw-elements-cf010ec4.js">
    <link rel="modulepreload" crossorigin href="/assets/react-countdown-circle-timer-9ebe8293.js">
    <link rel="modulepreload" crossorigin href="/assets/@mui/system-26850b45.js">
    <link rel="modulepreload" crossorigin href="/assets/react-adsense-938c2b76.js">
    <link rel="modulepreload" crossorigin href="/assets/react-cookie-c8d6f231.js">
    <link rel="modulepreload" crossorigin href="/assets/papaparse-189c4155.js">
    <link rel="modulepreload" crossorigin href="/assets/@nextui-org/react-8c36517e.js">
    <link rel="modulepreload" crossorigin href="/assets/flowbite-react-0d0699b1.js">
    <link rel="modulepreload" crossorigin href="/assets/react-type-animation-c3b90b2c.js">
    <link rel="modulepreload" crossorigin href="/assets/antd-8f3f3a2b.js">
    <link rel="modulepreload" crossorigin href="/assets/styled-components-de78ff2f.js">
    <link rel="modulepreload" crossorigin href="/assets/reshake-940beffa.js">
    <link rel="modulepreload" crossorigin href="/assets/@stripe/react-stripe-js-9a877a09.js">
    <link rel="modulepreload" crossorigin href="/assets/@stripe/stripe-js-6b714a86.js">
    <link rel="modulepreload" crossorigin href="/assets/axios-4d564c32.js">
    <link rel="modulepreload" crossorigin href="/assets/@google-analytics/data-8925307b.js">
    <link rel="modulepreload" crossorigin href="/assets/chart.js-64efa1cb.js">
    <link rel="modulepreload" crossorigin href="/assets/react-ga-f4d74762.js">
    <link rel="modulepreload" crossorigin href="/assets/react-helmet-a073d2ae.js">
    <link rel="modulepreload" crossorigin href="/assets/react-dnd-html5-backend-c055c1b4.js">
    <link rel="modulepreload" crossorigin href="/assets/redux-7163047e.js">
    <link rel="modulepreload" crossorigin href="/assets/react-dnd-9a59c67e.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/free-brands-svg-icons-67e8b52a.js">
    <link rel="stylesheet" href="/assets/index-050eea4b.css">
  </head>
  <body>
    <div id="root"></div>
    <div id="portal"></div>

    
  </body>
  <script
    async
    src="//pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"
  ></script>
</html>
