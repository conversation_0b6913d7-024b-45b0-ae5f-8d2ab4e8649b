import React from "react";

const SkeletonLoading = ({ counter = 1, circle = false, padding="", padding_ext="" }) => {
  return (
    <div className="animate-pulse w-full ">
      <div className={`flex flex-col items-center justify-center px-4  max-w-full mx-auto  rounded-lg  w-full h-full ${padding_ext}`}>
        {circle && (
          <div className="w-20 h-20 mb-4 bg-gray-300 rounded-full"></div>
        )}
        {Array(counter)
          .fill(0)
          .map((count,i) => (
            <div key={i} className={`w-full mb-4 h-6 ${padding} bg-gray-300 rounded`}></div>
          ))}
      </div>
    </div>
  );
};

export default SkeletonLoading;
