import React from "react";

export const ProfileLogo = ({ className = "" }) => {
  return (
    <svg
      className={`${className}`}
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.1161 18.0166C14.3828 18.2333 13.5161 18.3333 12.4995 18.3333H7.49948C6.48281 18.3333 5.61615 18.2333 4.88281 18.0166C5.06615 15.85 7.29115 14.1416 9.99948 14.1416C12.7078 14.1416 14.9328 15.85 15.1161 18.0166Z"
        stroke="#1F1F1F"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M12.4993 1.66699H7.49935C3.33268 1.66699 1.66602 3.33366 1.66602 7.50033V12.5003C1.66602 15.6503 2.61602 17.3753 4.88268 18.017C5.06602 15.8503 7.29102 14.142 9.99935 14.142C12.7077 14.142 14.9327 15.8503 15.116 18.017C17.3827 17.3753 18.3327 15.6503 18.3327 12.5003V7.50033C18.3327 3.33366 16.666 1.66699 12.4993 1.66699ZM9.99935 11.8086C8.34935 11.8086 7.01601 10.467 7.01601 8.81701C7.01601 7.16701 8.34935 5.83366 9.99935 5.83366C11.6493 5.83366 12.9827 7.16701 12.9827 8.81701C12.9827 10.467 11.6493 11.8086 9.99935 11.8086Z"
        stroke="#1F1F1F"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M12.9842 8.81636C12.9842 10.4664 11.6509 11.808 10.0009 11.808C8.35091 11.808 7.01758 10.4664 7.01758 8.81636C7.01758 7.16636 8.35091 5.83301 10.0009 5.83301C11.6509 5.83301 12.9842 7.16636 12.9842 8.81636Z"
        stroke="#1F1F1F"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
