import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "../utils/MkdSDK";
import { GlobalContext, showToast } from "../globalContext";
import { useNavigate, useParams } from "react-router-dom";
import { AuthContext, tokenExpireError } from "../authContext";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { isImage, empty, isVideo, isPdf } from "../utils/utils";

let sdk = new MkdSDK();

const EditAdminQuizPage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const schema = yup
    .object({
      quiz: yup.string(),
      
    })
    .required();
  const [fileObj, setFileObj] = React.useState({});
  const navigate = useNavigate();

  const [quiz, setQuiz] = useState("");
  const [description, setDescription] = useState("");
  const [amount, setAmount] = useState("");
  const [stripe_id, setStripeId] = useState("");
  const [messages, setMessages] = useState("");
  const [id, setId] = useState(0);
  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const params = useParams();



  // ADDING NEW ATTRIBUTES..................................STARTS
  const [inputFields, setInputFields] = useState([""]);

  const handleChange = (index, event) => {
    const values = [...inputFields];
    values[index] = event.target.value;
    setInputFields(values);
  };

  const handleAddFields = () => {
    setInputFields([...inputFields, ""]);
  };

  const handleRemoveFields = (index) => {
    const values = [...inputFields];
    values.splice(index, 1);
    setInputFields(values);
  };

  // console.log("inputFields",inputFields);

  // const handleSubmit = (event) => {
  //   event.preventDefault();
  //   // Handle form submission logic here
  //   console.log('Form submitted:', inputFields);
  // };

  // ADDING NEW ATTRIBUTES..................................ENDS




  useEffect(function () {
    (async function () {
      try {
        sdk.setTable("quiz");
        const result = await sdk.callRestAPI({ id: Number(params?.id) }, "GET");
        if (!result.error) {
          setValue("quiz", result.model.quiz);
          // setValue("description", result.model.description);
          // setValue("amount", result.model.amount);
          // setValue("stripe_id", result.model.stripe_id);
          // setValue("messages", result.model.messages);
          // console.log("log", JSON.parse(result.model.attribute));
          // setInputFields(JSON.parse(result.model.attribute))
          // console.log("log",JSON.parsse(result.model.attribute));
          // setInputFields(JSON.parsse(result.model.attribute && result.model.attribute));
          setQuiz(result.model.quiz);
          // setDescription(result.model.description);
          // setAmount(result.model.amount);
          // setStripeId(result.model.stripe_id);
          // setMessages(result.model.messages);
          setId(result.model.id);
        }
      } catch (error) {
        console.log("error", error);
        tokenExpireError(dispatch, error.message);
      }
    })();
  }, []);

  const previewImage = (field, target) => {
    let tempFileObj = fileObj;
    tempFileObj[field] = {
      file: target.files[0],
      tempURL: URL.createObjectURL(target.files[0]),
    };
    setFileObj({ ...tempFileObj });
  };

  const onSubmit = async (_data) => {
    try {
      sdk.setTable("quiz");
      for (let item in fileObj) {
        let formData = new FormData();
        formData.append("file", fileObj[item].file);
        let uploadResult = await sdk.uploadImage(formData);
        _data[item] = uploadResult.url;
      }
      const result = await sdk.callRestAPI(
        {
          id: id,
          quiz: _data.quiz,
        
        },
        "PUT"
      );

      if (!result.error) {
        showToast(globalDispatch, "Updated");
        navigate("/admin/quiz");
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error) {
      console.log("Error", error);
      setError("name", {
        type: "manual",
        message: error.message,
      });
    }
  };
  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "quiz",
      },
    });
  }, []);
  // console.log("log", inputFields);

  return (
    <div className=" shadow-md rounded   mx-auto p-5">
      <h4 className="text-2xl font-medium">Edit quiz</h4>
      <form className=" w-full max-w-lg" onSubmit={handleSubmit(onSubmit)}>
        <div className="mb-4  ">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="quiz"
          >
            Quiz
          </label>
          <input
            placeholder="quiz"
            {...register("quiz")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
              errors.name?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-red-500 text-xs italic">{errors.name?.message}</p>
        </div>



        <button
          type="submit"
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Submit
        </button>
      </form>
    </div>
  );
};

export default EditAdminQuizPage;
