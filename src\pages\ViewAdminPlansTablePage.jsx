import React from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "../utils/MkdSDK";
import { useNavigate, useParams } from "react-router-dom";
import { AuthContext, tokenExpireError } from "../authContext";
import { GlobalContext, showToast } from "../globalContext";
import { isImage, empty, isVideo } from "../utils/utils";

let sdk = new MkdSDK();

const ViewAdminPlansPage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  const [viewModel, setViewModel] = React.useState({});

  const params = useParams();

  React.useEffect(function () {
    (async function () {
      try {
        sdk.setTable("plans");
        const result = await sdk.callRestAPI({ id: Number(params?.id) }, "GET");
        if (!result.error) {
          setViewModel(result.model);
        }
      } catch (error) {
        console.log("error", error);
        tokenExpireError(dispatch, error.message);
      }
    })();
  }, []);
  return (
    <div className=" shadow-md rounded  mx-auto p-5">
      <h4 className="text-2xl font-medium">View Plans</h4>

      <div className="my-4">
        <div className="flex mb-4">
          <div className="flex-1">Id</div>
          <div className="flex-1">{viewModel?.id}</div>
        </div>
      </div>

      <div className="my-4">
        <div className="flex mb-4">
          <div className="flex-1">Create At</div>
          <div className="flex-1">{viewModel?.create_at}</div>
        </div>
      </div>

      <div className="my-4">
        <div className="flex mb-4">
          <div className="flex-1">Update At</div>
          <div className="flex-1">{viewModel?.update_at}</div>
        </div>
      </div>

      <div className="my-4">
        <div className="flex mb-4">
          <div className="flex-1">Name</div>
          <div className="flex-1">{viewModel?.name}</div>
        </div>
      </div>

      <div className="my-4">
        <div className="flex mb-4">
          <div className="flex-1">Description</div>
          <div className="flex-1">{viewModel?.description}</div>
        </div>
      </div>

      <div className="my-4">
        <div className="flex mb-4">
          <div className="flex-1">Amount</div>
          <div className="flex-1">{viewModel?.amount/100}</div>
        </div>
      </div>

      <div className="my-4">
        <div className="flex mb-4">
          <div className="flex-1">Stripe Id</div>
          <div className="flex-1">{viewModel?.stripe_id}</div>
        </div>
      </div>

      <div className="my-4">
        <div className="flex mb-4">
          <div className="flex-1">Messages</div>
          <div className="flex-1">{viewModel?.messages}</div>
        </div>
      </div>
      <div className="my-4">
        <div className="flex mb-4 border-2">
          <div className="flex-1">Attribute</div>
          <div className=" flex-1">
            {viewModel?.attribute && JSON.parse(viewModel?.attribute).map((attr, i) => (
              <div key={i} className=" ">{attr}</div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewAdminPlansPage;
