import { CheckIcon } from "Assets/svgs/CheckIcon";
import { AuthContext, tokenExpireError } from "Src/authContext";
import MkdSDK from "Utils/MkdSDK";
import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router";
import SkeletonLoading from "./SkeletonLoading";
import { Modal } from "Components/Modal/Modal";
import { GlobalContext, showToast } from "Src/globalContext";

let sdk = new MkdSDK();

const Plans = () => {
  const [plans, setPlans] = useState();
  const [iscouponAvailable, setIscouponAvailable] = useState(false);
  const [hasCoupon, sethasCoupon] = useState(false);
  const [watchVideo, setWatchVideo] = useState();
  const [planId, setPlanId] = useState();
  const [planName, setPlanName] = useState(); 
  const [planAmount, setPlanAmount] = useState();
  const [watchTime, setWatchTime] = useState();
  const [couponCode, setCouponCode] = useState();
  const [watchTimeId, setWatchTimeId] = useState();
  const [countdown, setCountdown] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
  });

  const { state, dispatch } = React.useContext(AuthContext);

  const { state: globalState, dispatch: globalDispatch } =
    React.useContext(GlobalContext);
  const location = useLocation();

  const prevRoute = location?.state?.from?.pathname;
  const searchParams = new URLSearchParams(location.search);
  const redirect_uri = searchParams.get("redirect_uri");
  const navigate = useNavigate();

  const getPaymentIntent = async (
    plan_id,
    plan_name,
    plan_amount,
    couponCode
  ) => {
    try {
      const getaresult = async () => {
        if (couponCode) {
          const result = await sdk.callRawAPI(
            // "https://skillgames.mkdlabs.com/v3/api/custom/skillgames/player/payment-intent/create",
            "/v3/api/custom/kaizenwin/membership/payment/intent",

            {
              // amount: 12,
              currency: "eur",
              plan_id: plan_id,

              coupon: couponCode,
            },
            "post"
          );
          return result;
        } else {
          const result = await sdk.callRawAPI(
            
            "/v3/api/custom/kaizenwin/membership/payment/intent",

            {
              // amount: 12,
              currency: "eur",
              plan_id: plan_id,

              // coupon: couponCode,
            },
            "post"
          );
          return result;
        }
      };

      let result = await getaresult();

      if (!result.error) {
        if (couponCode) {
          showToast(globalDispatch, "Applied successfully");
        }
        let amountWithCoupon = result.amount;
        let paymentIntent = result.payment_intent_id;
        let clientSecret = result.client_secret;
        let newsletterBonus = result.newsletter_bonus
        

        

        navigate(
          `/user/checkout/?&name=${plan_name}&amount=${plan_amount}&amountWithCoupon=${amountWithCoupon}&paymentIntent=${paymentIntent}&clientSecret=${clientSecret}&plan_id=${plan_id}&newsletterBonus=${newsletterBonus}`
        );
      } else if (result.error) {
        showToast(globalDispatch, result.message, 3000, "error");
      }
    } catch (error) {
      // tokenExpireError(globalDispatch, error.message);

      showToast(globalDispatch, "Please Try Again, Later");
    }
  };

  const makepayment = async (plan_id, plan_name, plan_amount, couponCode) => {
    try {
      // console.log(plan_id);
      if (!state.user) {
        navigate("/user/login");
      }
      if (!localStorage.getItem("terms")) {
        navigate(redirect_uri ?? "/user/terms", { state: { from: location } });
      } else {
        // new call to stripe

        getPaymentIntent(plan_id, plan_name, plan_amount, couponCode);
      }
    } catch (err) {}
  };

  // .............GET Plans................
  React.useEffect(() => {
    const getplans = async () => {
      const plans = await sdk.getplans();
      setPlans(plans?.list?.reverse());
    };
    getplans();
  }, []);

  const formatTwoDigits = (value) => {
    return value < 10 ? `0${value}` : value;
  };

  const setTime = () => {
    try {
      if (!state.user) {
        navigate("/user/login");
      }
      if (!localStorage.getItem("terms")) {
        navigate(redirect_uri ?? "/user/terms", { state: { from: location } });
      } else {
        //  set modal or go to ads page
        navigate("/user/ads-video");
      }
    } catch (err) {}
  };

  const storedData = watchTime;

  const calculateTimeLeft = () => {
    const inputDate = new Date(watchTime);

    const currentTime = new Date();
    const tomorrow = new Date(inputDate);
    tomorrow.setDate(inputDate.getDate() + 1);

    if (tomorrow) {
      const eventDate = new Date(tomorrow);

      const currentDate = new Date();
      // const difference = 0;
      const difference = eventDate - currentDate;

      const twentyFourHours = 25 * 60 * 50 * 1000;

      if (difference > 0) {
        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
        const hours = Math.floor(
          (difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
        );
        const minutes = Math.floor(
          (difference % (1000 * 60 * 60)) / (1000 * 60)
        );
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        setCountdown({
          days: formatTwoDigits(days),
          hours: formatTwoDigits(hours),
          minutes: formatTwoDigits(minutes),
          seconds: formatTwoDigits(seconds),
        });
        setWatchVideo(false);
        localStorage.getItem("watchVideo") &&
          localStorage.removeItem("watchVideo");
      } else {
        // delete the last watched in the api here
        setWatchVideo(true);
        localStorage.setItem("watchVideo", JSON.stringify(true));
        setWatchTime(null);
      }
    } else {
      setWatchTime(null);
      return;
    }
  };

  useEffect(() => {
    const interval = setInterval(() => {
      const storedData = watchTime;
      if (storedData) {
        calculateTimeLeft();
      } else {
        setWatchVideo(true);
        localStorage.setItem("watchVideo", JSON.stringify(true));
        return;
      }
    }, 1000);

    return () => {
      clearInterval(interval);
    };
  }, [watchVideo, storedData]);

  //............GET LAST WATCHED..............
  useEffect(() => {
    let pageSize = 10;
    let filter = {
      user_id: state.user,
    };

    async function getData(pageNum, limitNum, currentTableData) {
      try {
        if (state?.user) {
          sdk.setTable("profile");
          const result = await sdk.callRestAPI(
            {
              payload: { ...currentTableData },
              page: pageNum,
              limit: limitNum,
              sortId: "",
              direction: "",
            },
            "PAGINATE"
          );

          const { list, total, limit, num_pages, page } = result;

          setWatchTime(list[0]?.last_watched);
          setWatchTimeId(list[0]?.id);
        }
      } catch (error) {
        console.log("ERROR", error);
        tokenExpireError(dispatch, error.message);
      }
    }

    getData(1, pageSize, filter);
  }, []);

  return (
    <div className=" flex flex-col items-center mb-4 w-full ">
      <div className="w-full flex justify-center items-center  text-[#999999] text-center flex-wrap gap-4">
        {/* MAP THE TIERS */}
        {!plans &&
          Array(4)
            .fill(0)
            .map((_, i) => (
              <div
                key={i}
                className="flex flex-col justify-evenly  h-fit  rounded-sm  m-4 min-w-[300px]"
              >
                <SkeletonLoading
                  counter={1}
                  padding=" h-[465px] shadow-lg p-6 m-4 min-w-[300px]"
                />
              </div>
            ))}
        {plans?.map((plan, i) => (
          <div
            key={i}
            className="flex flex-col justify-center items-center w-full max-w-[300px]"
          >
            <div
              className={`flex flex-col justify-evenly  h-[465px] ${
                plan?.name == "Basic" ? "bg-companyRed" : "bg-[#F8F8F8]"
              } rounded-sm shadow-lg p-6 m-4 min-w-[300px] `}
            >
              <h2
                className={`text-xl ${
                  plan?.name == "Basic" ? "text-white" : "text-[#999999]"
                } font-semibold mb-4 text-left`}
              >
                {plan?.name.includes("Free") || plan?.name.includes("free")
                  ? "Free Trial"
                  : plan?.name}
              </h2>
              <p
                className={` ${
                  plan?.name == "Basic" ? "text-white" : "text-[#222222]"
                } mb-4 font-semibold md:text-[64px] text-[48px] text-left`}
              >
                €{plan?.amount / 100}
              </p>
              <p
                className={` ${
                  plan?.name == "Basic" ? "text-white" : "text-[#999999]"
                } mb-4 w-[80%] max-w-[252px] text-left `}
              >
                {plan?.description}
              </p>
              {plan?.attribute &&
                JSON.parse(plan?.attribute)?.map((attr, i) => (
                  <div
                    key={i}
                    className={` ${
                      plan.name == "Basic" ? "text-white" : "text-[#111111]"
                    } mb-4 text-left flex`}
                  >
                    {plan?.name == "Basic" ? (
                      <p className="inline">
                        <svg
                          width="25"
                          height="25"
                          viewBox="0 0 25 25"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <circle
                            cx="12.177"
                            cy="12.3496"
                            r="12"
                            fill="white"
                          />
                          <path
                            d="M16.7487 9.49268L10.463 15.7784L7.60583 12.9212"
                            stroke="#C1272D"
                            stroke-width="1.14286"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                      </p>
                    ) : (
                      <CheckIcon className="inline" />
                    )}

                    <span className="ml-2">{attr} </span>
                  </div>
                ))}

              <div className=" flex justify-center items-center">
                <button
                  className={` ${
                    plan.name == "Basic"
                      ? "bg-white text-companyRed hover:bg-gray-200"
                      : "bg-black text-white hover:bg-gray-500"
                  } w-5/6 p-2 mt-2 rounded-full mb-4 font-medium

                  ${
                    plan?.name.includes("Free") || plan?.name.includes("free")
                      ? watchVideo
                        ? "block"
                        : ` hidden`
                      : " "
                  }

                  `}
                  onClick={() => {
                    if (
                      plan?.name.includes("Free") ||
                      plan?.name.includes("free")
                    ) {
                      setTime();
                      return;
                    } else {
                      setPlanAmount(plan?.amount);
                      setPlanName(plan?.name);
                      setPlanId(plan?.id);

                      return makepayment(plan?.id, plan?.name, plan?.amount);
                    }
                  }}
                >
                  {plan.name.includes("Free") || plan.name.includes("free")
                    ? "Watch Video"
                    : "Get Tokens"}
                </button>

                {plan.name.includes("Free") || plan.name.includes("free") ? (
                  !watchVideo ? (
                    <button
                      className={`bg-black w-5/6  text-white p-2 mt-2 rounded-full hover:bg-gray-500 mb-4 opacity-50 pointer-events-none`}
                    >
                      {countdown.hours} : {countdown.minutes} :{" "}
                      {countdown.seconds}
                    </button>
                  ) : null
                ) : null}
              </div>
            </div>
          </div>
        ))}
      </div>
      {iscouponAvailable && (
        <Modal
          isOpen={iscouponAvailable}
          title=" "
          modalHeader="heeeeugbbh"
          modalCloseClick={() => {
            setIscouponAvailable(false);
            sethasCoupon(false);
          }}
          classes={{
            modalDialog: "md:px-20 px-4 md:min-w-[50vw] min-w-[90vw] ",
          }}
          showCloseAfter={1}
        >
          <div className=" flex flex-col items-center w-full ">
            <div className=" pb-4 md:flex md:flex-row flex flex-col items-center justify-between md:gap-8 gap-4 text-center w-full ">
              {!hasCoupon && (
                <p className=" text-center w-full text-2xl">
                  Do you have a Coupon?
                </p>
              )}
            </div>
            {hasCoupon && (
              <div className=" flex items-center justify-center mt-4 gap-2 max-w-[400px] w-fit border p-4">
                <input
                  type="text"
                  className="  shadow appearance-none border rounded w-fit  px-3 text-gray-700 h-full leading-tight focus:outline-none focus:shadow-outline"
                  placeholder="Coupon code"
                  onChange={(e) => setCouponCode(e.target.value)}
                />
                <button
                  className="bg-companyRed text-white hover:bg-[#c1272ca6] w-fit py-2 px-4 h-full  rounded-lg"
                  onClick={() => {
                    makepayment(planId, planName, planAmount, couponCode);
                  }}
                >
                  Apply coupon
                </button>
              </div>
            )}

            <div className="flex items-center justify-center pb-4 pt-4 w-full ">
              {!hasCoupon && (
                <div className="flex items-center justify-between pb-4 pt-4 w-full max-w-[400px] ">
                  <button
                    className="bg-black text-white hover:bg-gray-500 w-fit p-1 px-4 mt-2 rounded-2xl mb-4"
                    onClick={() => {
                      sethasCoupon((prev) => !prev);
                    }}
                  >
                    yes
                  </button>
                  <button
                    className="bg-black text-white hover:bg-gray-500 w-fit py-1 px-4 mt-2 rounded-2xl mb-4"
                    onClick={() => makepayment(planId, planName, planAmount)}
                  >
                    No, continue
                  </button>
                </div>
              )}
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default Plans;
