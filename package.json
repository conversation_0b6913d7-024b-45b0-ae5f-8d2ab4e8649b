{"name": "adminportal", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "tw": "npx tailwindcss -i ./src/index.css -o ./src/output.css --watch", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^43.0.0", "@ckeditor/ckeditor5-react": "^9.0.0", "@ctrl/react-adsense": "^1.7.0", "@editorjs/attaches": "^1.3.0", "@editorjs/checklist": "^1.5.0", "@editorjs/code": "^2.8.0", "@editorjs/delimiter": "^1.3.0", "@editorjs/editorjs": "^2.26.5", "@editorjs/embed": "^2.5.3", "@editorjs/header": "^2.7.0", "@editorjs/image": "^2.8.1", "@editorjs/inline-code": "^1.4.0", "@editorjs/link": "^2.5.0", "@editorjs/list": "^1.8.0", "@editorjs/marker": "^1.3.0", "@editorjs/nested-list": "^1.3.0", "@editorjs/paragraph": "^2.9.0", "@editorjs/personality": "^2.0.2", "@editorjs/quote": "^2.5.0", "@editorjs/raw": "^2.4.0", "@editorjs/simple-image": "^1.5.1", "@editorjs/table": "^2.2.1", "@editorjs/underline": "^1.1.0", "@editorjs/warning": "^1.3.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fontsource/poppins": "^4.5.10", "@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-brands-svg-icons": "^6.4.0", "@fortawesome/free-regular-svg-icons": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/react-fontawesome": "^0.2.0", "@google-analytics/data": "^4.5.0", "@headlessui/react": "^1.7.14", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.1.0", "@legendapp/state": "^0.23.4", "@material-ui/core": "^4.12.4", "@mui/base": "^5.0.0-beta.40", "@mui/icons-material": "^5.15.20", "@mui/material": "^5.15.20", "@mui/system": "^5.15.20", "@nextui-org/react": "^2.2.4", "@nextui-org/select": "^2.1.16", "@splidejs/react-splide": "^0.7.12", "@stripe/react-stripe-js": "^2.1.0", "@stripe/stripe-js": "^1.52.1", "@tailwindcss/forms": "^0.5.3", "@tinymce/tinymce-react": "^5.1.1", "@tippyjs/react": "^4.2.6", "@uppy/aws-s3": "^3.1.1", "@uppy/core": "^3.2.0", "@uppy/dashboard": "^3.4.0", "@uppy/drag-drop": "^3.0.2", "@uppy/dropbox": "^3.1.1", "@uppy/google-drive": "^3.1.1", "@uppy/onedrive": "^3.1.1", "@uppy/react": "^3.1.2", "@uppy/tus": "^3.1.0", "@uppy/xhr-upload": "^3.2.0", "antd": "^5.10.2", "apexcharts": "^3.40.0", "axios": "^1.4.0", "bootstrap": "^5.2.3", "chart.js": "^4.4.2", "compressorjs": "^1.2.1", "core-js": "^3.36.0", "emoji-picker-textarea": "^1.0.1", "flowbite": "^2.0.0", "flowbite-react": "^0.6.4", "framer-motion": "^10.16.4", "googleapis": "^137.1.0", "helmet": "^7.1.0", "html-to-image": "^1.11.11", "jodit-react": "^1.3.39", "moment": "^2.29.4", "nanoid": "^4.0.2", "papaparse": "^5.4.1", "pretty-rating-react": "^2.2.0", "react": "^18.2.0", "react-adsense": "^0.1.0", "react-apexcharts": "^1.4.0", "react-calendar": "^4.2.1", "react-chartjs-2": "^5.2.0", "react-confirm-alert": "^3.0.6", "react-cookie": "^6.1.1", "react-countdown-circle-timer": "^3.2.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-ga": "^3.3.1", "react-google-autocomplete": "^2.7.3", "react-google-maps": "^9.4.5", "react-helmet": "^6.1.0", "react-hook-form": "^7.43.9", "react-infinite-scroll-component": "^6.1.0", "react-input-emoji": "^5.0.2", "react-loading-skeleton": "^3.3.1", "react-outside-click-handler": "^1.3.0", "react-quill": "^2.0.0", "react-ratings-declarative": "^3.4.1", "react-router": "^6.11.1", "react-router-dom": "^6.11.1", "react-select": "^5.7.7", "react-select-country-list": "^2.2.3", "react-slick": "^0.29.0", "react-spinners": "^0.13.8", "react-type-animation": "^3.2.0", "redux": "^4.2.1", "reshake": "^2.0.0", "safe-buffer": "^5.2.1", "slick-carousel": "^1.8.1", "styled-components": "^6.1.1", "swiper": "^9.3.1", "tw-elements": "^1.0.0-beta2", "twilio-video": "^2.27.0", "uppy": "^3.9.0", "use-debounce": "^9.0.4", "yup": "^1.1.1"}, "devDependencies": {"@editorjs/link-autocomplete": "^0.1.0", "@editorjs/opensea": "^1.0.2", "@editorjs/translate-inline": "^1.0.0-rc.0", "@types/react": "^18.2.6", "@types/react-dom": "^18.2.4", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.23", "tailwindcss": "^3.3.2", "vite": "^4.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-remove-console": "^2.2.0"}}