import { navbar } from '@nextui-org/react';
import { AuthContext } from 'Src/authContext';
import { GlobalContext, showToast } from 'Src/globalContext';
import MkdSDK from 'Utils/MkdSDK';
import React, { useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router';


const ThankYouPage = () => {
    let sdk = new MkdSDK();
    const { state, dispatch } = React.useContext(AuthContext);
    const { state:globalState, dispatch: globalDispatch } = React.useContext(GlobalContext);
    const [showThankYou, setShowThankYou]= useState()
    const navigate = useNavigate()
    const location = useLocation();
    const prevRoute = location?.state?.from?.pathname;


useEffect(()=>{
    const verify =async()=>{

    const confirm = await sdk.verifypayment(state?.paymentIntentId);

      if (confirm?.error) {
        showToast(globalDispatch, error?.message, 4000, "error");
        // setLoading(false);
        setShowThankYou(false)

        return;
      } else if (!confirm.error) {
        showToast(globalDispatch, "Payment successful", 4000);
        setShowThankYou(true)
        // localStorage.getItem("newUser")
        //   ? navigate(prevRoute ?? "/user/privacy")
        //   : navigate(prevRoute ?? "/user/chat");
      }
    }
    // verify()
},[])

const handleContinue =()=>{
    localStorage.getItem("newUser")
          ? navigate(prevRoute ?? "/user/privacy")
          : navigate(prevRoute ?? "/user/chat");
    }


// toggle the display of text based on verify using shothankyou

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-100">
          <div className="bg-white p-8 shadow-md rounded-md flex flex-col items-center">
            <h1 className="text-3xl font-bold mb-4">Thank You for Your Purchase!</h1>
            <p className="text-gray-700 mb-4">Your payment was successful.
            </p>
            <p className="text-gray-700 mb-4">
             
            </p>
            
            <button onClick={handleContinue} className="bg-blue-800 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition duration-300">
            Click to continue
            </button>
          </div>
        </div>
      );
}

export default ThankYouPage