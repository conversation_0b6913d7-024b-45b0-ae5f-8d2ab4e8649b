import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "../utils/MkdSDK";
import { GlobalContext, showToast } from "../globalContext";
import { useNavigate, useParams } from "react-router-dom";
import { AuthContext, tokenExpireError } from "../authContext";
import DynamicContentType from "../components/DynamicContentType";

let sdk = new MkdSDK();

const AdminCollaborationPage = () => {
  const schema = yup
    .object({
      page: yup.string().required(),
      key: yup.string().required(),
      type: yup.string().required(),
      value: yup.string(),
    })
    .required();

  const selectType = [
    { key: "text", value: "Text" },
    { key: "checkbox", value: "Checkbox" },
    { key: "date", value: "Date" },
    { key: "image", value: "Image" },
    { key: "video", value: "Video" },
    { key: "document", value: "Document" },
    { key: "number", value: "Number" },
    { key: "kvp", value: "Key-Value Pair" },
    { key: "image-list", value: "Image List" },
    { key: "captioned-image-list", value: "Captioned Image List" },
    { key: "team-list", value: "Team List" },
    { key: "text-list", value: "Text List" },
    { key: "video-url", value: "Video Url" },
  ];

  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const navigate = useNavigate();
  const [id, setId] = useState(0);
  const [contentType, setContentType] = React.useState(selectType[0]?.key);
  const [contentValue, setContentValue] = React.useState();
  const [showButton, setShowButton] = React.useState(false);
  const [isUploading, setIsUploading] = React.useState(false);

  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const params = useParams();

  useEffect(function () {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "cms",
      },
    });

    (async function () {
      try {
        sdk.setTable("cms");
        const result = await sdk.callRestAPI({ id: 76 }, "GET");
        if (!result.error) {
          setValue("page", result.model.page);
          setValue("key", result.model.content_key);
          setValue("type", result.model.content_type);
          setValue("value", result.model.content_value);
          setContentType(result.model.content_type);
          setContentValue(result.model.content_value);
          setId(result.model.id);
          // console.log("result", JSON.parse(result.model.content_value));
        }
      } catch (error) {
        console.log("error", error);
        tokenExpireError(dispatch, error.message);
      }
    })();
  }, []);

  useEffect(
    function () {
      if (contentValue) {
        setShowButton((prev) => !prev);
      }
    },
    [contentValue]
  );

  const onSubmit = async (data) => {
    // console.log("data", data, contentValue);
    // return
    try {
      setIsUploading(true);
      const result = await sdk.cmsEdit(
        id,
        data.page,
        data.key,
        data.type,
        contentValue
      );

      if (!result.error) {
        showToast(globalDispatch, "Updated");
        navigate("/admin/collaboration");
        setIsUploading(false);
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
        setIsUploading(false);
      }
    } catch (error) {
      console.log("Error", error);
      setError("page", {
        type: "manual",
        message: error.message,
      });
      tokenExpireError(dispatch, error.message);
      setIsUploading(false);
    }
  };

  return (
    <div className=" shadow-md rounded   mx-auto p-5">
      <h4 className="text-2xl font-medium mb-2">Collaboration Page</h4>
      <form className=" w-full  flex justify-between" onSubmit={handleSubmit(onSubmit)}>
        <div className=" w-full max-w-lg">
          <div className="mb-4">
            <label
              className="block text-gray-700 text-sm font-bold mb-2"
              htmlFor="page"
            >
              Page
            </label>
            <input
              type="text"
              placeholder="Page"
              disabled
              {...register("page")}
              className={`shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline}`}
            />
          </div>
          <div className="mb-4">
            <label
              className="block text-gray-700 text-sm font-bold mb-2"
              htmlFor="key"
            >
              Content Identifier
            </label>
            <input
              type="text"
              placeholder="Content Identifier"
              disabled
              {...register("key")}
              className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
                errors.key?.message ? "border-red-500" : ""
              }`}
            />
            <p className="text-red-500 text-xs italic">{errors.key?.message}</p>
          </div>

          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Content Type
            </label>
            <select
              name="type"
              id="type"
              disabled
              className="shadow border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"
              {...register("type", {
                onChange: (e) => setContentType(e.target.value),
              })}
            >
              {selectType.map((option) => (
                <option name={option.name} value={option.key} key={option.key}>
                  {option.value}
                </option>
              ))}
            </select>
          </div>

          <DynamicContentType
            contentType={contentType}
            contentValue={contentValue}
            setIsUploading={setIsUploading}
            setContentValue={setContentValue}
          />
        </div>
        <div className="sticky top-0 right-0 w-full flex justify-end h-fit  py-4 ">
          <p> {isUploading ? "Uploading in progress. " : " "}</p>
          <button
            type="submit"
            disabled={isUploading}
            className=" disabled:opacity-50 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
          >
            Submit
          </button>
        </div>
      </form>
    </div>
  );
};

export default AdminCollaborationPage;
