import FooterComp from "Components/FooterComp";
import Navbar from "Components/NavBar";
import SkeletonLoading from "Components/SkeletonLoading";
import { tokenExpireError } from "Src/authContext";
import MkdSDK from "Utils/MkdSDK";
import React from "react";
import { useState } from "react";
import { useLocation, useNavigate } from "react-router";

let sdk = new MkdSDK();
const ReportIssuesTemplate = () => {
  const [isOpen, setIsOpen] = useState(true);
  const [accepted, setAccepted] = useState(false);
  const [rejected, setRejected] = useState(false);
  const [cms, setCms] = useState();

  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const redirect_uri = searchParams.get("redirect_uri");

  const navigate = useNavigate();

  React.useEffect(() => {
    async function getallcms() {
      try {
        const result = await sdk.getallcms();
        // console.log("wre", result);
        if (result) {
          setCms(result?.list);
        }
      } catch (err) {
        console.log("Error:", err);
        tokenExpireError(dispatch, err.message);
      }
    }
    getallcms();
  }, []);

  let textLists;
  cms &&
    (textLists = JSON.parse(
      cms?.find((item) => item.content_key === "Terms_texts")?.content_value
    ));

  return (
    <div className="min-h-screen flex flex-col items-center justify-between bg-gray-100">
      <Navbar />
      <div className="bg-white md:p-8 p-4 rounded shadow-lg max-w-5xl w-full">
        <h1 className="md:text-4xl text-2xl font-semibold mb-6">
          Report Template
        </h1>
        <div className="mb-4 md:text-justify text-justify">
          <>
            <div className="mb-4">
              <p className=" mb-4">
                Dear Customer, We sincerely regret the recent issues you
                encountered with our football prediction service. Our heartfelt
                apologies for any inconvenience this may have caused. We strive
                to provide the best possible service and take your complaint
                very seriously.
              </p>
              <p className=" mb-4">
                We would appreciate more details about your experience so that
                we can address the problem promptly and effectively. Below is a
                form for you to fill in with specific details of your complaint.
                Only provide information relevant to your issue:
              </p>
              <p>Name: [Enter your first and last name]</p>
              <p className=" mb-4">
                Email: [Enter your email address, that is linked to the account]
              </p>

              <p className=" font-bold mb-4">Issue with Messaging:</p>
              <p className=" font-bold ">Specific Details of the Issue:</p>
              <p className=" mb-4">
                <p>Date of the issue: [Enter the date]</p>
                <p>
                  Order Number: [Attach a screenshot of the purchase
                  confirmation email]
                </p>
              </p>
              <p className=" font-bold">Description of the Experience:</p>
              <p className=" mb-4">
                [Briefly describe the problem and how it has affected your
                experience]
              </p>
              <p className=" font-bold">Message in Another Language:</p>
              <p className=" mb-4">
                [Specify the language of your question and the language of the
                response. This can also be done by attaching a screenshot of the
                question and the answer.]
              </p>
              <p className=" font-bold">Totally Incorrect Message:</p>
              <p className=" mb-4">
                [Specify what your question was and the response you received.
                This can also be done by attaching a screenshot of the question
                and the answer.]
              </p>
              <p className=" font-bold">No Response Received:</p>
              <p className=" mb-4">
                [Attach a screenshot indicating that no response was given in
                the chat.]
              </p>
              <p className=" font-bold">No Messages Received:</p>
              <p className=" mb-4">
                [Attach a screenshot indicating that you did not receive any
                credits.]
              </p>
              <p className=" font-bold">Desired Solution:</p>
              <p>
                [Clearly describe what you expect as a solution to this problem]
              </p>
              <p className=" mb-4">
                Again, our sincere apologies for the inconvenience. We aim to
                resolve this as quickly as possible and will contact you as
                within 5 days once we receive the completed form.
              </p>
              <p>Best regards,</p>
              <p>Kaizenwin Team</p>
              <pre className="flex flex-row whitespace-pre-wrap overflow-x-auto break-words ">
                <p
                  className={` font-[Manrope] md:text-lg text-lg flex gap-2 `}
                ></p>
              </pre>
            </div>
          </>
        </div>
      </div>

      <FooterComp />
    </div>
  );
};

export default ReportIssuesTemplate;
