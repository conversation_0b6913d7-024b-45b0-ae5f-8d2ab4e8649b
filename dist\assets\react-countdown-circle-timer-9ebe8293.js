import{a as T,r as m}from"./vendor-b0222800.js";var I=typeof window>"u"?m.useEffect:m.useLayoutEffect,x=({isPlaying:t,duration:e,startAt:l=0,updateInterval:r=0,onComplete:n,onUpdate:a})=>{let[i,s]=m.useState(l),d=m.useRef(0),p=m.useRef(l),h=m.useRef(l*-1e3),u=m.useRef(null),o=m.useRef(null),g=m.useRef(null),v=y=>{let c=y/1e3;if(o.current===null){o.current=c,u.current=requestAnimationFrame(v);return}let f=c-o.current,k=d.current+f;o.current=c,d.current=k;let R=p.current+(r===0?k:(k/r|0)*r),C=p.current+k,A=typeof e=="number"&&C>=e;s(A?e:R),A||(u.current=requestAnimationFrame(v))},w=()=>{u.current&&cancelAnimationFrame(u.current),g.current&&clearTimeout(g.current),o.current=null},$=m.useCallback(y=>{w(),d.current=0;let c=typeof y=="number"?y:l;p.current=c,s(c),t&&(u.current=requestAnimationFrame(v))},[t,l]);return I(()=>{if(a==null||a(i),e&&i>=e){h.current+=e*1e3;let{shouldRepeat:y=!1,delay:c=0,newStartAt:f}=(n==null?void 0:n(h.current/1e3))||{};y&&(g.current=setTimeout(()=>$(f),c*1e3))}},[i,e]),I(()=>(t&&(u.current=requestAnimationFrame(v)),w),[t,e,r]),{elapsedTime:i,reset:$}},L=(t,e,l)=>{let r=t/2,n=e/2,a=r-n,i=2*a,s=l==="clockwise"?"1,0":"0,1",d=2*Math.PI*a;return{path:`m ${r},${n} a ${a},${a} 0 ${s} 0,${i} a ${a},${a} 0 ${s} 0,-${i}`,pathLength:d}},E=(t,e)=>t===0||t===e?0:typeof e=="number"?t-e:0,S=t=>({position:"relative",width:t,height:t}),F={display:"flex",justifyContent:"center",alignItems:"center",position:"absolute",left:0,top:0,width:"100%",height:"100%"},b=(t,e,l,r,n)=>{if(r===0)return e;let a=(n?r-t:t)/r;return e+l*a},W=t=>{var e,l;return(l=(e=t.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,(r,n,a,i)=>`#${n}${n}${a}${a}${i}${i}`).substring(1).match(/.{2}/g))==null?void 0:e.map(r=>parseInt(r,16)))!=null?l:[]},D=(t,e)=>{var l;let{colors:r,colorsTime:n,isSmoothColorTransition:a=!0}=t;if(typeof r=="string")return r;let i=(l=n==null?void 0:n.findIndex((o,g)=>o>=e&&e>=n[g+1]))!=null?l:-1;if(!n||i===-1)return r[0];if(!a)return r[i];let s=n[i]-e,d=n[i]-n[i+1],p=W(r[i]),h=W(r[i+1]),u=!!t.isGrowing;return`rgb(${p.map((o,g)=>b(s,o,h[g]-o,d,u)|0).join(",")})`},P=t=>{let{duration:e,initialRemainingTime:l,updateInterval:r,size:n=180,strokeWidth:a=12,trailStrokeWidth:i,isPlaying:s=!1,isGrowing:d=!1,rotation:p="clockwise",onComplete:h,onUpdate:u}=t,o=m.useRef(),g=Math.max(a,i??0),{path:v,pathLength:w}=L(n,g,p),{elapsedTime:$}=x({isPlaying:s,duration:e,startAt:E(e,l),updateInterval:r,onUpdate:typeof u=="function"?c=>{let f=Math.ceil(e-c);f!==o.current&&(o.current=f,u(f))}:void 0,onComplete:typeof h=="function"?c=>{var f;let{shouldRepeat:k,delay:R,newInitialRemainingTime:C}=(f=h(c))!=null?f:{};if(k)return{shouldRepeat:k,delay:R,newStartAt:E(e,C)}}:void 0}),y=e-$;return{elapsedTime:$,path:v,pathLength:w,remainingTime:Math.ceil(y),rotation:p,size:n,stroke:D(t,y),strokeDashoffset:b($,0,w,e,d),strokeWidth:a}},q=t=>{let{children:e,strokeLinecap:l,trailColor:r,trailStrokeWidth:n}=t,{path:a,pathLength:i,stroke:s,strokeDashoffset:d,remainingTime:p,elapsedTime:h,size:u,strokeWidth:o}=P(t);return T.createElement("div",{style:S(u)},T.createElement("svg",{viewBox:`0 0 ${u} ${u}`,width:u,height:u,xmlns:"http://www.w3.org/2000/svg"},T.createElement("path",{d:a,fill:"none",stroke:r??"#d9d9d9",strokeWidth:n??o}),T.createElement("path",{d:a,fill:"none",stroke:s,strokeLinecap:l??"round",strokeWidth:o,strokeDasharray:i,strokeDashoffset:d})),typeof e=="function"&&T.createElement("div",{style:F},e({remainingTime:p,elapsedTime:h,color:s})))};q.displayName="CountdownCircleTimer";export{q as D};
