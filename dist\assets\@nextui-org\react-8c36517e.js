import{a as g,r as y}from"../vendor-b0222800.js";import{j as Z}from"../@mui/base-0e613ae5.js";function ze(){for(var e=0,r,t,o="";e<arguments.length;)(r=arguments[e++])&&(t=me(r))&&(o&&(o+=" "),o+=t);return o}function me(e){if(typeof e=="string")return e;for(var r,t="",o=0;o<e.length;o++)e[o]&&(r=me(e[o]))&&(t&&(t+=" "),t+=r);return t}var ee="-";function Pe(e){var r=Ie(e),t=e.conflictingClassGroups,o=e.conflictingClassGroupModifiers,a=o===void 0?{}:o;function i(l){var c=l.split(ee);return c[0]===""&&c.length!==1&&c.shift(),he(c,r)||Ee(l)}function n(l,c){var d=t[l]||[];return c&&a[l]?[].concat(d,a[l]):d}return{getClassGroupId:i,getConflictingClassGroupIds:n}}function he(e,r){var n;if(e.length===0)return r.classGroupId;var t=e[0],o=r.nextPart.get(t),a=o?he(e.slice(1),o):void 0;if(a)return a;if(r.validators.length!==0){var i=e.join(ee);return(n=r.validators.find(function(l){var c=l.validator;return c(i)}))==null?void 0:n.classGroupId}}var ue=/^\[(.+)\]$/;function Ee(e){if(ue.test(e)){var r=ue.exec(e)[1],t=r==null?void 0:r.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}}function Ie(e){var r=e.theme,t=e.prefix,o={nextPart:new Map,validators:[]},a=Le(Object.entries(e.classGroups),t);return a.forEach(function(i){var n=i[0],l=i[1];X(l,o,n,r)}),o}function X(e,r,t,o){e.forEach(function(a){if(typeof a=="string"){var i=a===""?r:fe(r,a);i.classGroupId=t;return}if(typeof a=="function"){if(Ge(a)){X(a(o),r,t,o);return}r.validators.push({validator:a,classGroupId:t});return}Object.entries(a).forEach(function(n){var l=n[0],c=n[1];X(c,fe(r,l),t,o)})})}function fe(e,r){var t=e;return r.split(ee).forEach(function(o){t.nextPart.has(o)||t.nextPart.set(o,{nextPart:new Map,validators:[]}),t=t.nextPart.get(o)}),t}function Ge(e){return e.isThemeGetter}function Le(e,r){return r?e.map(function(t){var o=t[0],a=t[1],i=a.map(function(n){return typeof n=="string"?r+n:typeof n=="object"?Object.fromEntries(Object.entries(n).map(function(l){var c=l[0],d=l[1];return[r+c,d]})):n});return[o,i]}):e}function Te(e){if(e<1)return{get:function(){},set:function(){}};var r=0,t=new Map,o=new Map;function a(i,n){t.set(i,n),r++,r>e&&(r=0,o=t,t=new Map)}return{get:function(n){var l=t.get(n);if(l!==void 0)return l;if((l=o.get(n))!==void 0)return a(n,l),l},set:function(n,l){t.has(n)?t.set(n,l):a(n,l)}}}var ye="!";function Ke(e){var r=e.separator||":",t=r.length===1,o=r[0],a=r.length;return function(n){for(var l=[],c=0,d=0,b,p=0;p<n.length;p++){var v=n[p];if(c===0){if(v===o&&(t||n.slice(p,p+a)===r)){l.push(n.slice(d,p)),d=p+a;continue}if(v==="/"){b=p;continue}}v==="["?c++:v==="]"&&c--}var x=l.length===0?n:n.substring(d),w=x.startsWith(ye),m=w?x.substring(1):x,$=b&&b>d?b-d:void 0;return{modifiers:l,hasImportantModifier:w,baseClassName:m,maybePostfixModifierPosition:$}}}function Re(e){if(e.length<=1)return e;var r=[],t=[];return e.forEach(function(o){var a=o[0]==="[";a?(r.push.apply(r,t.sort().concat([o])),t=[]):t.push(o)}),r.push.apply(r,t.sort()),r}function je(e){return{cache:Te(e.cacheSize),splitModifiers:Ke(e),...Pe(e)}}var We=/\s+/;function Ne(e,r){var t=r.splitModifiers,o=r.getClassGroupId,a=r.getConflictingClassGroupIds,i=new Set;return e.trim().split(We).map(function(n){var l=t(n),c=l.modifiers,d=l.hasImportantModifier,b=l.baseClassName,p=l.maybePostfixModifierPosition,v=o(p?b.substring(0,p):b),x=!!p;if(!v){if(!p)return{isTailwindClass:!1,originalClassName:n};if(v=o(b),!v)return{isTailwindClass:!1,originalClassName:n};x=!1}var w=Re(c).join(":"),m=d?w+ye:w;return{isTailwindClass:!0,modifierId:m,classGroupId:v,originalClassName:n,hasPostfixModifier:x}}).reverse().filter(function(n){if(!n.isTailwindClass)return!0;var l=n.modifierId,c=n.classGroupId,d=n.hasPostfixModifier,b=l+c;return i.has(b)?!1:(i.add(b),a(c,d).forEach(function(p){return i.add(l+p)}),!0)}).reverse().map(function(n){return n.originalClassName}).join(" ")}function Ve(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];var o,a,i,n=l;function l(d){var b=r[0],p=r.slice(1),v=p.reduce(function(x,w){return w(x)},b());return o=je(v),a=o.cache.get,i=o.cache.set,n=c,c(d)}function c(d){var b=a(d);if(b)return b;var p=Ne(d,o);return i(d,p),p}return function(){return n(ze.apply(null,arguments))}}function u(e){var r=function(o){return o[e]||[]};return r.isThemeGetter=!0,r}var xe=/^\[(?:([a-z-]+):)?(.+)\]$/i,He=/^\d+\/\d+$/,Ue=new Set(["px","full","screen"]),Oe=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,qe=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Be=/^-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/;function S(e){return k(e)||Ue.has(e)||He.test(e)||Q(e)}function Q(e){return z(e,"length",Ye)}function Fe(e){return z(e,"size",we)}function Ze(e){return z(e,"position",we)}function Je(e){return z(e,"url",De)}function N(e){return z(e,"number",k)}function k(e){return!Number.isNaN(Number(e))}function Xe(e){return e.endsWith("%")&&k(e.slice(0,-1))}function I(e){return pe(e)||z(e,"number",pe)}function s(e){return xe.test(e)}function G(){return!0}function A(e){return Oe.test(e)}function Qe(e){return z(e,"",_e)}function z(e,r,t){var o=xe.exec(e);return o?o[1]?o[1]===r:t(o[2]):!1}function Ye(e){return qe.test(e)}function we(){return!1}function De(e){return e.startsWith("url(")}function pe(e){return Number.isInteger(Number(e))}function _e(e){return Be.test(e)}function er(){var e=u("colors"),r=u("spacing"),t=u("blur"),o=u("brightness"),a=u("borderColor"),i=u("borderRadius"),n=u("borderSpacing"),l=u("borderWidth"),c=u("contrast"),d=u("grayscale"),b=u("hueRotate"),p=u("invert"),v=u("gap"),x=u("gradientColorStops"),w=u("gradientColorStopPositions"),m=u("inset"),$=u("margin"),M=u("opacity"),C=u("padding"),te=u("saturate"),U=u("scale"),oe=u("sepia"),ne=u("skew"),ae=u("space"),ie=u("translate"),O=function(){return["auto","contain","none"]},q=function(){return["auto","hidden","clip","visible","scroll"]},B=function(){return["auto",s,r]},f=function(){return[s,r]},le=function(){return["",S]},R=function(){return["auto",k,s]},se=function(){return["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"]},j=function(){return["solid","dashed","dotted","double","none"]},ce=function(){return["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"]},F=function(){return["start","end","center","between","around","evenly","stretch"]},P=function(){return["","0",s]},de=function(){return["auto","avoid","all","avoid-page","page","left","right","column"]},E=function(){return[k,N]},W=function(){return[k,s]};return{cacheSize:500,theme:{colors:[G],spacing:[S],blur:["none","",A,s],brightness:E(),borderColor:[e],borderRadius:["none","","full",A,s],borderSpacing:f(),borderWidth:le(),contrast:E(),grayscale:P(),hueRotate:W(),invert:P(),gap:f(),gradientColorStops:[e],gradientColorStopPositions:[Xe,Q],inset:B(),margin:B(),opacity:E(),padding:f(),saturate:E(),scale:E(),sepia:P(),skew:W(),space:f(),translate:f()},classGroups:{aspect:[{aspect:["auto","square","video",s]}],container:["container"],columns:[{columns:[A]}],"break-after":[{"break-after":de()}],"break-before":[{"break-before":de()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none"]}],clear:[{clear:["left","right","both","none"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[].concat(se(),[s])}],overflow:[{overflow:q()}],"overflow-x":[{"overflow-x":q()}],"overflow-y":[{"overflow-y":q()}],overscroll:[{overscroll:O()}],"overscroll-x":[{"overscroll-x":O()}],"overscroll-y":[{"overscroll-y":O()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[m]}],"inset-x":[{"inset-x":[m]}],"inset-y":[{"inset-y":[m]}],start:[{start:[m]}],end:[{end:[m]}],top:[{top:[m]}],right:[{right:[m]}],bottom:[{bottom:[m]}],left:[{left:[m]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",I]}],basis:[{basis:B()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",s]}],grow:[{grow:P()}],shrink:[{shrink:P()}],order:[{order:["first","last","none",I]}],"grid-cols":[{"grid-cols":[G]}],"col-start-end":[{col:["auto",{span:["full",I]},s]}],"col-start":[{"col-start":R()}],"col-end":[{"col-end":R()}],"grid-rows":[{"grid-rows":[G]}],"row-start-end":[{row:["auto",{span:[I]},s]}],"row-start":[{"row-start":R()}],"row-end":[{"row-end":R()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",s]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",s]}],gap:[{gap:[v]}],"gap-x":[{"gap-x":[v]}],"gap-y":[{"gap-y":[v]}],"justify-content":[{justify:["normal"].concat(F())}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal"].concat(F(),["baseline"])}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[].concat(F(),["baseline"])}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[C]}],px:[{px:[C]}],py:[{py:[C]}],ps:[{ps:[C]}],pe:[{pe:[C]}],pt:[{pt:[C]}],pr:[{pr:[C]}],pb:[{pb:[C]}],pl:[{pl:[C]}],m:[{m:[$]}],mx:[{mx:[$]}],my:[{my:[$]}],ms:[{ms:[$]}],me:[{me:[$]}],mt:[{mt:[$]}],mr:[{mr:[$]}],mb:[{mb:[$]}],ml:[{ml:[$]}],"space-x":[{"space-x":[ae]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[ae]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit",s,r]}],"min-w":[{"min-w":["min","max","fit",s,S]}],"max-w":[{"max-w":["0","none","full","min","max","fit","prose",{screen:[A]},A,s]}],h:[{h:[s,r,"auto","min","max","fit"]}],"min-h":[{"min-h":["min","max","fit",s,S]}],"max-h":[{"max-h":[s,r,"min","max","fit"]}],"font-size":[{text:["base",A,Q]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",N]}],"font-family":[{font:[G]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",s]}],"line-clamp":[{"line-clamp":["none",k,N]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",s,S]}],"list-image":[{"list-image":["none",s]}],"list-style-type":[{list:["none","disc","decimal",s]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[M]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[M]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[].concat(j(),["wavy"])}],"text-decoration-thickness":[{decoration:["auto","from-font",S]}],"underline-offset":[{"underline-offset":["auto",s,S]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],indent:[{indent:f()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",s]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",s]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[M]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[].concat(se(),[Ze])}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Fe]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Je]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[w]}],"gradient-via-pos":[{via:[w]}],"gradient-to-pos":[{to:[w]}],"gradient-from":[{from:[x]}],"gradient-via":[{via:[x]}],"gradient-to":[{to:[x]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[M]}],"border-style":[{border:[].concat(j(),["hidden"])}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[M]}],"divide-style":[{divide:j()}],"border-color":[{border:[a]}],"border-color-x":[{"border-x":[a]}],"border-color-y":[{"border-y":[a]}],"border-color-t":[{"border-t":[a]}],"border-color-r":[{"border-r":[a]}],"border-color-b":[{"border-b":[a]}],"border-color-l":[{"border-l":[a]}],"divide-color":[{divide:[a]}],"outline-style":[{outline:[""].concat(j())}],"outline-offset":[{"outline-offset":[s,S]}],"outline-w":[{outline:[S]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:le()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[M]}],"ring-offset-w":[{"ring-offset":[S]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",A,Qe]}],"shadow-color":[{shadow:[G]}],opacity:[{opacity:[M]}],"mix-blend":[{"mix-blend":ce()}],"bg-blend":[{"bg-blend":ce()}],filter:[{filter:["","none"]}],blur:[{blur:[t]}],brightness:[{brightness:[o]}],contrast:[{contrast:[c]}],"drop-shadow":[{"drop-shadow":["","none",A,s]}],grayscale:[{grayscale:[d]}],"hue-rotate":[{"hue-rotate":[b]}],invert:[{invert:[p]}],saturate:[{saturate:[te]}],sepia:[{sepia:[oe]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[t]}],"backdrop-brightness":[{"backdrop-brightness":[o]}],"backdrop-contrast":[{"backdrop-contrast":[c]}],"backdrop-grayscale":[{"backdrop-grayscale":[d]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[b]}],"backdrop-invert":[{"backdrop-invert":[p]}],"backdrop-opacity":[{"backdrop-opacity":[M]}],"backdrop-saturate":[{"backdrop-saturate":[te]}],"backdrop-sepia":[{"backdrop-sepia":[oe]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[n]}],"border-spacing-x":[{"border-spacing-x":[n]}],"border-spacing-y":[{"border-spacing-y":[n]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",s]}],duration:[{duration:W()}],ease:[{ease:["linear","in","out","in-out",s]}],delay:[{delay:W()}],animate:[{animate:["none","spin","ping","pulse","bounce",s]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[U]}],"scale-x":[{"scale-x":[U]}],"scale-y":[{"scale-y":[U]}],rotate:[{rotate:[I,s]}],"translate-x":[{"translate-x":[ie]}],"translate-y":[{"translate-y":[ie]}],"skew-x":[{"skew-x":[ne]}],"skew-y":[{"skew-y":[ne]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",s]}],accent:[{accent:["auto",e]}],appearance:["appearance-none"],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",s]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":f()}],"scroll-mx":[{"scroll-mx":f()}],"scroll-my":[{"scroll-my":f()}],"scroll-ms":[{"scroll-ms":f()}],"scroll-me":[{"scroll-me":f()}],"scroll-mt":[{"scroll-mt":f()}],"scroll-mr":[{"scroll-mr":f()}],"scroll-mb":[{"scroll-mb":f()}],"scroll-ml":[{"scroll-ml":f()}],"scroll-p":[{"scroll-p":f()}],"scroll-px":[{"scroll-px":f()}],"scroll-py":[{"scroll-py":f()}],"scroll-ps":[{"scroll-ps":f()}],"scroll-pe":[{"scroll-pe":f()}],"scroll-pt":[{"scroll-pt":f()}],"scroll-pr":[{"scroll-pr":f()}],"scroll-pb":[{"scroll-pb":f()}],"scroll-pl":[{"scroll-pl":f()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","pinch-zoom","manipulation",{pan:["x","left","right","y","up","down"]}]}],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",s]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[S,N]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}var Lr=Ve(er);const H={prefix:String(Math.round(Math.random()*1e10)),current:0},$e=g.createContext(H),rr=g.createContext(!1);let tr=!!(typeof window<"u"&&window.document&&window.document.createElement),J=new WeakMap;function or(e=!1){let r=y.useContext($e),t=y.useRef(null);if(t.current===null&&!e){var o,a;let i=(a=g.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED)===null||a===void 0||(o=a.ReactCurrentOwner)===null||o===void 0?void 0:o.current;if(i){let n=J.get(i);n==null?J.set(i,{id:r.current,state:i.memoizedState}):i.memoizedState!==n.state&&(r.current=n.id,J.delete(i))}t.current=++r.current}return t.current}function nr(e){let r=y.useContext($e);r===H&&!tr&&console.warn("When server rendering, you must wrap your application in an <SSRProvider> to ensure consistent ids are generated between the client and server.");let t=or(!!e),o=`react-aria${r.prefix}`;return e||`${o}-${t}`}function ar(e){let r=g.useId(),[t]=y.useState(Se()),o=t?"react-aria":`react-aria${H.prefix}`;return e||`${o}-${r}`}g.useId;function ir(){return!1}function lr(){return!0}function sr(e){return()=>{}}function Se(){return typeof g.useSyncExternalStore=="function"?g.useSyncExternalStore(sr,ir,lr):y.useContext(rr)}typeof document<"u"&&g.useLayoutEffect;function cr(e){if(dr())e.focus({preventScroll:!0});else{let r=ur(e);e.focus(),fr(r)}}let V=null;function dr(){if(V==null){V=!1;try{document.createElement("div").focus({get preventScroll(){return V=!0,!0}})}catch{}}return V}function ur(e){let r=e.parentNode,t=[],o=document.scrollingElement||document.documentElement;for(;r instanceof HTMLElement&&r!==o;)(r.offsetHeight<r.scrollHeight||r.offsetWidth<r.scrollWidth)&&t.push({element:r,scrollTop:r.scrollTop,scrollLeft:r.scrollLeft}),r=r.parentNode;return o instanceof HTMLElement&&t.push({element:o,scrollTop:o.scrollTop,scrollLeft:o.scrollLeft}),t}function fr(e){for(let{element:r,scrollTop:t,scrollLeft:o}of e)r.scrollTop=t,r.scrollLeft=o}function re(e){var r;return typeof window>"u"||window.navigator==null?!1:((r=window.navigator.userAgentData)===null||r===void 0?void 0:r.brands.some(t=>e.test(t.brand)))||e.test(window.navigator.userAgent)}function Ce(e){var r;return typeof window<"u"&&window.navigator!=null?e.test(((r=window.navigator.userAgentData)===null||r===void 0?void 0:r.platform)||window.navigator.platform):!1}function Y(){return Ce(/^Mac/i)}function pr(){return Ce(/^iPad/i)||Y()&&navigator.maxTouchPoints>1}function br(){return re(/AppleWebKit/i)&&!gr()}function gr(){return re(/Chrome/i)}function vr(){return re(/Firefox/i)}const mr=y.createContext({isNative:!0,open:xr});function hr(e){let{children:r,navigate:t}=e,o=y.useMemo(()=>({isNative:!1,open:(a,i)=>{Me(a,n=>{yr(n,i)?t(n.pathname+n.search+n.hash):K(n,i)})}}),[t]);return g.createElement(mr.Provider,{value:o},r)}function yr(e,r){let t=e.getAttribute("target");return(!t||t==="_self")&&e.origin===location.origin&&!e.hasAttribute("download")&&!r.metaKey&&!r.ctrlKey&&!r.altKey&&!r.shiftKey}function K(e,r,t=!0){var o,a;let{metaKey:i,ctrlKey:n,altKey:l,shiftKey:c}=r;vr()&&(!((a=window.event)===null||a===void 0||(o=a.type)===null||o===void 0)&&o.startsWith("key"))&&e.target==="_blank"&&(Y()?i=!0:n=!0);let d=br()&&Y()&&!pr()?new KeyboardEvent("keydown",{keyIdentifier:"Enter",metaKey:i,ctrlKey:n,altKey:l,shiftKey:c}):new MouseEvent("click",{metaKey:i,ctrlKey:n,altKey:l,shiftKey:c,bubbles:!0,cancelable:!0});K.isOpening=t,cr(e),e.dispatchEvent(d),K.isOpening=!1}K.isOpening=!1;function Me(e,r){if(e instanceof HTMLAnchorElement)r(e);else if(e.hasAttribute("data-href")){let t=document.createElement("a");t.href=e.getAttribute("data-href"),e.hasAttribute("data-target")&&(t.target=e.getAttribute("data-target")),e.hasAttribute("data-rel")&&(t.rel=e.getAttribute("data-rel")),e.hasAttribute("data-download")&&(t.download=e.getAttribute("data-download")),e.hasAttribute("data-ping")&&(t.ping=e.getAttribute("data-ping")),e.hasAttribute("data-referrer-policy")&&(t.referrerPolicy=e.getAttribute("data-referrer-policy")),e.appendChild(t),r(t),e.removeChild(t)}}function xr(e,r){Me(e,t=>K(t,r))}let L=new Map,be=new Set;function ge(){if(typeof window>"u")return;function e(o){return"propertyName"in o}let r=o=>{if(!e(o)||!o.target)return;let a=L.get(o.target);a||(a=new Set,L.set(o.target,a),o.target.addEventListener("transitioncancel",t,{once:!0})),a.add(o.propertyName)},t=o=>{if(!e(o)||!o.target)return;let a=L.get(o.target);if(a&&(a.delete(o.propertyName),a.size===0&&(o.target.removeEventListener("transitioncancel",t),L.delete(o.target)),L.size===0)){for(let i of be)i();be.clear()}};document.body.addEventListener("transitionrun",r),document.body.addEventListener("transitionend",t)}typeof document<"u"&&(document.readyState!=="loading"?ge():document.addEventListener("DOMContentLoaded",ge));const wr=new Set(["Arab","Syrc","Samr","Mand","Thaa","Mend","Nkoo","Adlm","Rohg","Hebr"]),$r=new Set(["ae","ar","arc","bcc","bqi","ckb","dv","fa","glk","he","ku","mzn","nqo","pnb","ps","sd","ug","ur","yi"]);function Ae(e){if(Intl.Locale){let t=new Intl.Locale(e).maximize(),o=typeof t.getTextInfo=="function"?t.getTextInfo():t.textInfo;if(o)return o.direction==="rtl";if(t.script)return wr.has(t.script)}let r=e.split("-")[0];return $r.has(r)}const Sr=Symbol.for("react-aria.i18n.locale");function ke(){let e=typeof window<"u"&&window[Sr]||typeof navigator<"u"&&(navigator.language||navigator.userLanguage)||"en-US";try{Intl.DateTimeFormat.supportedLocalesOf([e])}catch{e="en-US"}return{locale:e,direction:Ae(e)?"rtl":"ltr"}}let D=ke(),T=new Set;function ve(){D=ke();for(let e of T)e(D)}function Cr(){let e=Se(),[r,t]=y.useState(D);return y.useEffect(()=>(T.size===0&&window.addEventListener("languagechange",ve),T.add(t),()=>{T.delete(t),T.size===0&&window.removeEventListener("languagechange",ve)}),[]),e?{locale:"en-US",direction:"ltr"}:r}const Mr=g.createContext(null);function Ar(e){let{locale:r,children:t}=e,o=Cr(),a=r?{locale:r,direction:Ae(r)?"rtl":"ltr"}:o;return g.createElement(Mr.Provider,{value:a},t)}const _=g.createContext(null);function kr(e){let{children:r}=e,t=y.useContext(_),[o,a]=y.useState(0),i=y.useMemo(()=>({parent:t,modalCount:o,addModal(){a(n=>n+1),t&&t.addModal()},removeModal(){a(n=>n-1),t&&t.removeModal()}}),[t,o]);return g.createElement(_.Provider,{value:i},r)}function zr(){let e=y.useContext(_);return{modalProviderProps:{"aria-hidden":e&&e.modalCount>0?!0:null}}}function Pr(e){let{modalProviderProps:r}=zr();return g.createElement("div",{"data-overlay-container":!0,...e,...r})}function Er(e){return g.createElement(kr,null,g.createElement(Pr,e))}var Tr=({children:e,locale:r="en-US",navigate:t,...o})=>{let a=e;return t&&(a=Z.jsx(hr,{navigate:t,children:a})),Z.jsx(Ar,{locale:r,children:Z.jsx(Er,{...o,children:a})})};export{Tr as N,Lr as t};
