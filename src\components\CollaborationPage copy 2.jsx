import React, { useState } from "react";
import { AuthContext, tokenExpireError } from "../authContext";
import { NavLink } from "react-router-dom";
import { GlobalContext } from "../globalContext";
import aboutimg from "../assets/images/aboutimg.png";
import { useNavigate } from "react-router-dom";
import Navbar from "./NavBar";
import MkdSDK from "Utils/MkdSDK";
import SkeletonLoading from "./SkeletonLoading";
import FooterComp from "./FooterComp";
import AdSense from "react-adsense";
import { usePageContext } from "Src/pageContext";
let sdk = new MkdSDK();
const CollaborationPage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { pageLayout } = usePageContext();
  const [cms, setCms] = useState();

  const navigate = useNavigate();
  // console.log(pageLayout);

  React.useEffect(() => {
    async function getallcms() {
      try {
        const result = await sdk.getallcms();
        // console.log("wre", result);
        if (result) {
          setCms(result?.list);
        }
      } catch (err) {
        console.log("Error:", err);
        tokenExpireError(dispatch, err.message);
      }
    }
    getallcms();
  }, []);

  // console.log("cms", cms);

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "user",
      },
    });
  }, []);
  return (
    <div className="relative ">
      <Navbar />
      <div className="h-full flex flex-col justify-between items-center w-full">
        <div className="bg-white pb-8 rounded  px-4 flex flex-col justify-center items-start w-full">
          {!cms && <SkeletonLoading padding="py-[5rem] pt-[10rem]" />}
          {!cms && <SkeletonLoading padding="" counter={20} />}

          <div
            dangerouslySetInnerHTML={{
              __html: cms?.find((item) => item.content_key === "editor")
                ?.content_value,
            }}
            // className=" md:flex md:flex-row flex flex-col items-center  justify-start max-w-[2140px] w-full px-2 py-8 gap-2"
            className="  max-w-[2140px] w-full px-2 py-8 gap-2"
          />
        </div>
        <FooterComp />
      </div>
    </div>
  );
};

export default CollaborationPage;
