import { CompanyLogoBig } from "Assets/svgs/CompanyLogoBig";
import React from "react";
import { NavLink, useNavigate } from "react-router-dom";

const ChatTopLeftPanel = ({ onClick }) => {
    const navigate = useNavigate()
  return (
    <div>
      <div className="w-full lg:p-10 mt-4  flex justify-start">
        {/*COMPANY LOGO */}
        <div
          onClick={() => {
            navigate("/");
          }}
          className="text-white w-full font-bold flex lg:place-content-start place-content-start lg:text-left lg:text-2xl text-sm"
        >
          <CompanyLogoBig />
        </div>
      </div>
      {/* Centered menu items */}
      <div className="text-white text-center w-full max-w-[233px] min-w-[50px] lg:hidden my-[36px] flex items-center justify-between">
        <NavLink className="  border-gray-300 md:text-sm text-xs" to={"/"}>
          Home
        </NavLink>
        <hr className="vertical-hr bg-white bg-opacity-30" />

        <NavLink
          className="mx-4  border-gray-300 md:text-sm text-xs"
          to={"/user/aboutpage"}
        >
          About Us
        </NavLink>
        <hr className="vertical-hr bg-white bg-opacity-30" />
        <NavLink className="  md:text-sm text-xs" to={"/user/chat"}>
          Chatbot
        </NavLink>
      </div>
      <div className=" w-full p-2 flex justify-center">
        <button
          className="text-white flex justify-center  text-center lg:text-xl text-xs border border-companyRed lg:w-[90%] w-[70%] rounded-full md:p-4 p-2 bg-companyRed hover:bg-red-600"
          onClick={onClick}
        >
          + New Chat
        </button>
      </div>
    </div>
  );};

export default ChatTopLeftPanel;
