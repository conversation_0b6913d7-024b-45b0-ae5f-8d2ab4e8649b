import React, { useEffect, useRef, useState } from "react";
import SkeletonLoading from "./SkeletonLoading";
import { useData } from "Src/dataContext";
import PlayCircleIcon from "@mui/icons-material/PlayCircle";
import PauseCircleOutlineIcon from "@mui/icons-material/PauseCircleOutline";

const KaizenwinSection = () => {
  const [playing, setPlaying] = useState(false);
  const videoRef = useRef(null);
  const { cms } = useData();

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        if (entry.isIntersecting) {
          videoRef.current?.play();
          setPlaying(true);
        } else {
          videoRef.current?.pause();
          setPlaying(false);
        }
      },
      { threshold: 0.5 } // Adjust threshold to control when the video should play (e.g., 0.5 means half the video should be in view)
    );

    if (videoRef.current) {
      observer.observe(videoRef.current);
    }

    return () => {
      if (videoRef.current) {
        observer.unobserve(videoRef.current);
      }
    };
  }, []);

  const togglePlay = () => {
    const newPlayingState = !playing;
    setPlaying(newPlayingState);
    if (newPlayingState) {
      videoRef.current?.play();
    } else {
      videoRef.current?.pause();
    }
  };

  return (
    <section className="flex flex-col justify-between items-center mt-[100px] md:mt-[] max-w-[1280px] md:w-[70vw] w-[90vw]">
      <h2 className="mb-[32px] md:mb-[50px] md:text-left text-center md:text-5xl text-3xl font-semibold">
        {!cms && <SkeletonLoading />}
        {
          cms?.find((item) => item.content_key === "Kaizenwin_Section_headings")
            ?.content_value
        }
      </h2>
      <p className="text-center text-[#999999] text-[15px] md:text-companyBlack md:text-opacity-60 md:text-[22px] max-w-[942px] font-normal mb-[40px]">
        {!cms && <SkeletonLoading counter={2} />}
        {
          cms?.find(
            (item) => item.content_key === "Kaizenwin_Section_sub_headings"
          )?.content_value
        }
      </p>
      {!cms && <SkeletonLoading padding="py-[10rem]" />}
      <div className="group w-full my-4 flex justify-center relative h-full max-h-[700px]">
        <video
          ref={videoRef}
          src={
            cms?.find((item) => item.content_key === "Kaizenwin_Section_video")
              ?.content_value
          }
          autoPlay={false} // autoPlay is handled by the observer
          onEnded={() => {
            videoRef.current.pause(); // Ensures the video stops after playing once
          }}
          // muted
        />
        <div className="group-hover:opacity-100 absolute inset-0 rounded-full opacity-0 transition-opacity duration-300 flex items-center justify-center">
          <button
            className="border w-fit h-fit flex items-center justify-center bg-gray-800 text-white rounded-full transition-opacity duration-300"
            onClick={togglePlay}
          >
            {playing ? (
              <PauseCircleOutlineIcon style={{ fontSize: 70 }} />
            ) : (
              <PlayCircleIcon style={{ fontSize: 70 }} />
            )}
          </button>
        </div>
      </div>
    </section>
  );
};

export default KaizenwinSection;
