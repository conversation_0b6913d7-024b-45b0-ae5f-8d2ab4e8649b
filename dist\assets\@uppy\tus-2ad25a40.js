function n(o,e){if(!Object.prototype.hasOwnProperty.call(o,e))throw new TypeError("attempted to use private field on non-instance");return o}var u=0;function a(o){return"__private_"+u+++"_"+o}var i=a("uppy"),s=a("events");class h{constructor(e){Object.defineProperty(this,i,{writable:!0,value:void 0}),Object.defineProperty(this,s,{writable:!0,value:[]}),n(this,i)[i]=e}on(e,t){return n(this,s)[s].push([e,t]),n(this,i)[i].on(e,t)}remove(){for(const[e,t]of n(this,s)[s].splice(0))n(this,i)[i].off(e,t)}onFilePause(e,t){this.on("upload-pause",(r,l)=>{e===r&&t(l)})}onFileRemove(e,t){this.on("file-removed",r=>{e===r.id&&t(r.id)})}onPause(e,t){this.on("upload-pause",(r,l)=>{e===r&&t(l)})}onRetry(e,t){this.on("upload-retry",r=>{e===r&&t()})}onRetryAll(e,t){this.on("retry-all",()=>{n(this,i)[i].getFile(e)&&t()})}onPauseAll(e,t){this.on("pause-all",()=>{n(this,i)[i].getFile(e)&&t()})}onCancelAll(e,t){var r=this;this.on("cancel-all",function(){n(r,i)[i].getFile(e)&&t(...arguments)})}onResumeAll(e,t){this.on("resume-all",()=>{n(this,i)[i].getFile(e)&&t()})}}export{h as E};
