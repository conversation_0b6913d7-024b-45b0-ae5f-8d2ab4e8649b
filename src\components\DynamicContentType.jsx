import React, { useEffect, useState } from "react";
import MkdSDK from "../utils/MkdSDK";
import { empty } from "../utils/utils";
import { Autocomplete, Checkbox, ListItemText, TextField } from "@mui/material";
import countryList from "react-select-country-list";

const sdk = new MkdSDK();
const defaultImage = "https://via.placeholder.com/150?text=%20";
const DynamicContentType = ({
  contentType,
  contentValue,
  setContentValue,
  setIsUploading,
}) => {
  const [previewUrl, setPreviewUrl] = React.useState(defaultImage);
  const [checked, setChecked] = useState(false);

  useEffect(() => {
    setChecked(contentValue == "yes" ? true : false);
  }, [contentValue]);
  const handleImageChange = async (e) => {
    setIsUploading(true);
    const formData = new FormData();
    formData.append("file", e.target.files[0]);
    try {
      const result = await sdk.uploadImage(formData);
      setPreviewUrl(result.url);
      setContentValue(result.url);
    } catch (err) {
      console.error(err);
    }
    setIsUploading(false);
  };
  const handleVideoChange = async (e) => {
    setIsUploading(true);
    const formData = new FormData();
    formData.append("file", e.target.files[0]);
    try {
      const result = await sdk.uploadImage(formData);
      setPreviewUrl(result.url);
      setContentValue(result.url);
    } catch (err) {
      console.error(err);
    }
    setIsUploading(false);
  };

  switch (contentType) {
    case "text":
      return (
        <>
          <textarea
            className={`shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline`}
            rows={15}
            placeholder="Content"
            onChange={(e) => setContentValue(e.target.value)}
            defaultValue={contentValue}
          ></textarea>
        </>
      );
    case "checkbox":
      return (
        <>
          <CheckBox
            contentValue={contentValue}
            setContentValue={setContentValue}
          />
        </>
      );
    case "video-url":
      return (
        <>
          <input
            className={`shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline`}
            placeholder="video url"
            onChange={(e) => setContentValue(e.target.value)}
            defaultValue={contentValue}
          />
        </>
      );

    case "video":
      return (
        <>
          <img
            src={empty(contentValue) ? previewUrl : contentValue}
            alt="preview"
            height={150}
            width={150}
          />
          <input
            type="file"
            accept="video/*"
            onChange={handleVideoChange}
            className={`shadow appearance-none border block  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline`}
          />
        </>
      );

    case "image":
      return (
        <>
          <img
            src={empty(contentValue) ? previewUrl : contentValue}
            alt="preview"
            height={150}
            width={150}
          />
          <input
            type="file"
            onChange={handleImageChange}
            className={`shadow appearance-none border block  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline`}
          />
        </>
      );
    case "document":
      return (
        <>
          <img
            src={empty(contentValue) ? previewUrl : contentValue}
            alt="preview"
            height={150}
            width={150}
          />
          <input
            type="file"
            onChange={handleImageChange}
            className={`shadow appearance-none border block  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline`}
          />
        </>
      );

    case "number":
      return (
        <input
          type="number"
          className={`shadow appearance-none border block  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline`}
          onChange={(e) => setContentValue(e.target.value)}
          defaultValue={contentValue}
        />
      );
    case "date":
      return (
        <input
          type="date"
          className={`shadow appearance-none border block  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline`}
          onChange={(e) => setContentValue(e.target.value)}
          defaultValue={contentValue}
        />
      );

    case "team-list":
      return (
        <TeamList
          setContentValue={setContentValue}
          contentValue={contentValue}
        />
      );

    case "image-list":
      return (
        <ImageList
          setContentValue={setContentValue}
          contentValue={contentValue}
        />
      );

    case "captioned-image-list":
      return (
        <CaptionedImageList
          setContentValue={setContentValue}
          contentValue={contentValue}
        />
      );

    case "kvp":
      return (
        <KeyValuePair
          setContentValue={setContentValue}
          contentValue={contentValue}
        />
      );
    case "text-list":
      return (
        <TextList
          setContentValue={setContentValue}
          contentValue={contentValue}
          setIsUploading={setIsUploading}
          // handleVideoChange={handleVideoChange}
        />
      );

    default:
      break;
  }
};

export default DynamicContentType;

const ImageList = ({ contentValue, setContentValue }) => {
  let itemsObj = [{ key: "", value_type: "image", value: null }];
  if (!empty(contentValue)) {
    itemsObj = JSON.parse(contentValue);
  }
  const [items, setItems] = React.useState(itemsObj);

  const handleImageChange = async (e) => {
    const listKey = e.target.getAttribute("listkey");
    const formData = new FormData();
    formData.append("file", e.target.files[0]);
    try {
      const result = await sdk.uploadImage(formData);
      setItems((oldItems) => {
        let updatedItems = oldItems.map((item, index) => {
          if (index == listKey) {
            item.value = result.url;
            return item;
          }
          return item;
        });
        return updatedItems;
      });
      setContentValue(JSON.stringify(items));
    } catch (err) {
      console.error(err);
    }
  };

  const handleKeyChange = (e) => {
    const listKey = e.target.getAttribute("listkey");
    setItems((oldItems) => {
      let updatedItems = oldItems.map((item, index) => {
        if (index == listKey) {
          item.key = e.target.value;
          return item;
        }
        return item;
      });
      return updatedItems;
    });
    setContentValue(JSON.stringify(items));
  };

  return (
    <div className="block">
      {items.map((item, index) => (
        <div key={index * 0.23}>
          <img
            src={item.value !== null ? item.value : defaultImage}
            alt="preview"
            height={150}
            width={150}
          />
          <div className="flex">
            <input
              className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline`}
              type="text"
              placeholder="key"
              listkey={index}
              onChange={handleKeyChange}
              defaultValue={item.key}
            />
            <input
              listkey={index}
              type="file"
              accept="image/*"
              onChange={handleImageChange}
              className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline`}
            />
          </div>
        </div>
      ))}
      <button
        type="button"
        className="bg-blue-400 hover:bg-blue-700 text-white font-bold py-1 px-2 my-4 rounded focus:outline-none focus:shadow-outline"
        onClick={(e) =>
          setItems((old) => [
            ...old,
            { key: "", value_type: "image", value: null },
          ])
        }
      >
        +
      </button>
    </div>
  );
};

const CaptionedImageList = ({ setContentValue, contentValue }) => {
  let itemsObj = [{ key: "", value_type: "image", value: null, caption: "" }];

  if (!empty(contentValue)) {
    itemsObj = JSON.parse(contentValue);
  }
  const [items, setItems] = React.useState(itemsObj);

  const handleImageChange = async (e) => {
    const listKey = e.target.getAttribute("listkey");
    const formData = new FormData();
    formData.append("file", e.target.files[0]);
    try {
      const result = await sdk.uploadImage(formData);
      setItems((oldItems) => {
        let updatedItems = oldItems.map((item, index) => {
          if (index == listKey) {
            item.value = result.url;
            return item;
          }
          return item;
        });
        return updatedItems;
      });
      setContentValue(JSON.stringify(items));
    } catch (err) {
      console.error(err);
    }
  };

  const handleKeyChange = (e) => {
    const listKey = e.target.getAttribute("listkey");
    setItems((oldItems) => {
      let updatedItems = oldItems.map((item, index) => {
        if (index == listKey) {
          item.key = e.target.value;
          return item;
        }
        return item;
      });
      return updatedItems;
    });
    setContentValue(JSON.stringify(items));
  };

  const handleCaptionChange = (e) => {
    const listKey = e.target.getAttribute("listkey");
    setItems((oldItems) => {
      let updatedItems = oldItems.map((item, index) => {
        if (index == listKey) {
          item.caption = e.target.value;
          return item;
        }
        return item;
      });
      return updatedItems;
    });
    setContentValue(JSON.stringify(items));
  };

  return (
    <div className="block">
      {items.map((item, index) => (
        <div key={index * 0.23}>
          <img
            src={item.value !== null ? item.value : defaultImage}
            alt="preview"
            height={150}
            width={150}
          />
          <div className="flex">
            <input
              className={`shadow appearance-none border rounded w-full py-2 px-3 mr-2 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline`}
              type="text"
              placeholder="Key"
              listkey={index}
              onChange={handleKeyChange}
              defaultValue={item.key}
            />
            <input
              listkey={index}
              type="file"
              accept="image/*"
              onChange={handleImageChange}
              className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline`}
            />
          </div>
          <input
            className={`shadow block appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline`}
            type="text"
            placeholder="Caption"
            listkey={index}
            onChange={handleCaptionChange}
            defaultValue={item.caption}
          />
        </div>
      ))}
      <button
        type="button"
        className="bg-blue-400 hover:bg-blue-700 text-white font-bold py-1 px-2 my-4 rounded focus:outline-none focus:shadow-outline"
        onClick={(e) =>
          setItems((old) => [
            ...old,
            { key: "", value_type: "image", value: null, caption: "" },
          ])
        }
      >
        +
      </button>
    </div>
  );
};
const TeamList = ({ setContentValue, contentValue }) => {
  let itemsObj = [{ name: "", image: null, title: "" }];

  if (!empty(contentValue)) {
    itemsObj = JSON.parse(contentValue);
  }
  const [items, setItems] = React.useState(itemsObj);

  const handleImageChange = async (e) => {
    const listKey = e.target.getAttribute("listkey");
    const formData = new FormData();
    formData.append("file", e.target.files[0]);
    try {
      const result = await sdk.uploadImage(formData);
      setItems((oldItems) => {
        let updatedItems = oldItems.map((item, index) => {
          if (index == listKey) {
            item.image = result.url;
            return item;
          }
          return item;
        });
        return updatedItems;
      });
      setContentValue(JSON.stringify(items));
    } catch (err) {
      console.error(err);
    }
  };

  const handleNameChange = (e) => {
    const listKey = e.target.getAttribute("listkey");
    setItems((oldItems) => {
      let updatedItems = oldItems.map((item, index) => {
        if (index == listKey) {
          item.name = e.target.value;
          return item;
        }
        return item;
      });
      return updatedItems;
    });
    setContentValue(JSON.stringify(items));
  };

  const handleTitleChange = (e) => {
    const listKey = e.target.getAttribute("listkey");
    setItems((oldItems) => {
      let updatedItems = oldItems.map((item, index) => {
        if (index == listKey) {
          item.title = e.target.value;
          return item;
        }
        return item;
      });
      return updatedItems;
    });
    setContentValue(JSON.stringify(items));
  };

  return (
    <div className="block my-4">
      {items.map((item, index) => (
        <div key={index * 0.23} className="my-4">
          {/* <div className="flex"> */}
          <input
            className={`shadow block appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline`}
            type="text"
            placeholder="Title"
            listkey={index}
            onChange={handleTitleChange}
            defaultValue={item.title}
          />
          <input
            className={`shadow appearance-none border rounded w-full py-2 px-3 mr-2 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline`}
            type="text"
            placeholder="Name"
            listkey={index}
            onChange={handleNameChange}
            defaultValue={item.name}
          />
          <img
            src={item.image !== null ? item.image : defaultImage}
            alt="preview"
            height={150}
            width={150}
          />
          <input
            listkey={index}
            type="file"
            accept="image/*"
            onChange={handleImageChange}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline`}
          />
          {/* </div> */}
        </div>
      ))}
      <button
        type="button"
        className="bg-blue-400 hover:bg-blue-700 text-white font-bold py-1 px-2 my-4 rounded focus:outline-none focus:shadow-outline"
        onClick={(e) =>
          setItems((old) => [...old, { name: "", image: null, title: "" }])
        }
      >
        +
      </button>
    </div>
  );
};
const KeyValuePair = ({ setContentValue, contentValue }) => {
  let itemsObj = [{ key: "", value_type: "text", value: "" }];

  if (!empty(contentValue)) {
    itemsObj = JSON.parse(contentValue);
  }

  const [items, setItems] = React.useState(itemsObj);
  const valueTypeMap = [
    {
      key: "text",
      value: "Text",
    },
    {
      key: "number",
      value: "Number",
    },
    {
      key: "json",
      value: "JSON Object",
    },
    {
      key: "url",
      value: "URL",
    },
  ];

  const handleKeyChange = (e) => {
    const listKey = e.target.getAttribute("listkey");
    setItems((oldItems) => {
      let updatedItems = oldItems.map((item, index) => {
        if (index == listKey) {
          item.key = e.target.value;
          return item;
        }
        return item;
      });
      return updatedItems;
    });
    setContentValue(JSON.stringify(items));
  };

  const handleValueChange = (e) => {
    const listKey = e.target.getAttribute("listkey");
    setItems((oldItems) => {
      let updatedItems = oldItems.map((item, index) => {
        if (index == listKey) {
          item.value = e.target.value;
          return item;
        }
        return item;
      });
      return updatedItems;
    });
    setContentValue(JSON.stringify(items));
  };

  const handleValueTypeChange = (e) => {
    const listKey = e.target.getAttribute("listkey");
    setItems((oldItems) => {
      let updatedItems = oldItems.map((item, index) => {
        if (index == listKey) {
          item.value_type = e.target.value;
          return item;
        }
        return item;
      });
      return updatedItems;
    });
    setContentValue(JSON.stringify(items));
  };

  return (
    <div className="block">
      {items.map((item, index) => (
        <div key={index * 0.23} className="my-4">
          <input
            className={`shadow appearance-none border rounded w-full py-2 px-3 mr-2 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline`}
            type="text"
            placeholder="Key"
            listkey={index}
            onChange={handleKeyChange}
            defaultValue={item.key}
          />
          <select
            className={`shadow block border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline`}
            listkey={index}
            onChange={handleValueTypeChange}
            defaultValue={item.value_type}
          >
            {valueTypeMap.map((type, index) => (
              <option key={index * 122} value={type.key}>
                {type.value}
              </option>
            ))}
          </select>
          <input
            className={`shadow block appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline`}
            type="text"
            required
            placeholder="Value"
            listkey={index}
            onChange={handleValueChange}
            defaultValue={item.value}
          />
        </div>
      ))}
      <button
        type="button"
        className="bg-blue-400 hover:bg-blue-700 text-white font-bold py-1 px-2 my-4 rounded focus:outline-none focus:shadow-outline"
        onClick={(e) =>
          setItems((old) => [
            ...old,
            { key: "", value_type: "text", value: "" },
          ])
        }
      >
        +
      </button>
    </div>
  );
};

const TextList = ({ setContentValue, contentValue, setIsUploading }) => {
  let itemsObj = [{ key: "", value_type: "text", value: "", image: null }];

  if (!empty(contentValue)) {
    itemsObj = JSON.parse(contentValue);
  }

  const [items, setItems] = React.useState(itemsObj);
  const [previewUrl, setPreviewUrl] = React.useState(defaultImage);
  const [isChecked, setIsChecked] = useState(false);
  const [checkedIndex, setCheckedIndex] = useState(
    itemsObj?.map((item) => {
      if (item.video) {
        return itemsObj.indexOf(item);
      }
    }) || []
  );

  const handleCheckboxChange = (event, index) => {
    // setIsChecked(event.target.checked);
    setCheckedIndex((prev) => {
      if (prev.includes(index)) {
        return prev.filter((item) => item != index);
      } else {
        return [...prev, index];
      }
    });
  };

  const handleImageChange = async (e) => {
    const listKey = e.target.getAttribute("listkey");
    const formData = new FormData();
    formData.append("file", e.target.files[0]);
    try {
      const result = await sdk.uploadImage(formData);
      setItems((oldItems) => {
        let updatedItems = oldItems.map((item, index) => {
          if (index == listKey) {
            item.image = result.url;
            return item;
          }
          return item;
        });
        return updatedItems;
      });
      setContentValue(JSON.stringify(items));
    } catch (err) {
      console.error(err);
    }
  };
  const handleVideoChange = async (e) => {
    setIsUploading(true);
    const listKey = e.target.getAttribute("listkey");
    const formData = new FormData();
    formData.append("file", e.target.files[0]);
    try {
      const result = await sdk.uploadImage(formData);
      setPreviewUrl(result.url);
      // setContentValue(result.url);
      setItems((oldItems) => {
        let updatedItems = oldItems.map((item, index) => {
          if (index == listKey) {
            item.video = result.url;
            return item;
          }
          return item;
        });
        return updatedItems;
      });

      setContentValue(JSON.stringify(items));
    } catch (err) {
      console.error(err);
    }
    setIsUploading(false);
  };

  const handleDeleteLink = async (idx) => {
    try {
      // Update state immutably
      setItems((oldItems) =>{
        const updatedItems =  oldItems.map((item, index) =>
          index === idx ? { ...item, link: "" } : item
        )
        setContentValue(JSON.stringify(updatedItems));
        return updatedItems;
      }
      );

      // setItems((updatedItems) => {
      //   const contentValue = JSON.stringify(updatedItems);
      //   setContentValue(contentValue);

      //   return updatedItems;
      // });

    } catch (err) {
      console.error(err);
    }
  };
  const handleDeleteImage = async (idx) => {
    try {
      // Update state immutably
      setItems((oldItems) =>{
        
        const updatedItems =  oldItems.map((item, index) =>
          index === idx ? { ...item, image: null } : item
        )
        setContentValue(JSON.stringify(updatedItems));
        return updatedItems;
      }
      );

      // setItems((updatedItems) => {
      //   const contentValue = JSON.stringify(updatedItems);
      //   setContentValue(contentValue);

      //   return updatedItems;
      // });
    } catch (err) {
      console.error(err);
    }
  };
  const handleRemoveSection = (index) => {
    setItems((oldItems) => {
      const updatedItems = oldItems.filter((_, idx) => idx !== index);
      setContentValue(JSON.stringify(updatedItems));
      return updatedItems;
    });
  };
  const handleDeleteVideo = async (idx) => {
    try {
      // Update state immutably
      setItems((oldItems) =>
        oldItems.map((item, index) =>
          index === idx ? { ...item, video: null } : item
        )
      );

      setItems((updatedItems) => {
        const contentValue = JSON.stringify(updatedItems);
        setContentValue(contentValue);

        return updatedItems;
      });
    } catch (err) {
      console.error(err);
    }
  };

  const valueTypeMap = [
    {
      key: "text",
      value: "Text",
    },
    // {
    //     key: "number",
    //     value: "Number"
    // },
    // {
    //     key: "json",
    //     value: "JSON Object"
    // },
    // {
    //     key: "url",
    //     value: "URL"
    // }
  ];

  const handleKeyChange = (e) => {
    const listKey = e.target.getAttribute("listkey");
    setItems((oldItems) => {
      let updatedItems = oldItems.map((item, index) => {
        if (index == listKey) {
          item.key = e.target.value;
          return item;
        }
        return item;
      });
      return updatedItems;
    });
    setContentValue(JSON.stringify(items));
  };
  const handleLinkChange = (e) => {
    const listKey = e.target.getAttribute("listkey");
    setItems((oldItems) => {
      let updatedItems = oldItems.map((item, index) => {
        if (index == listKey) {
          item.link = e.target.value;
          return item;
        }
        return item;
      });
      return updatedItems;
    });
    setContentValue(JSON.stringify(items));
  };

  const handleValueChange = (e) => {
    const listKey = e.target.getAttribute("listkey");
    setItems((oldItems) => {
      let updatedItems = oldItems.map((item, index) => {
        if (index == listKey) {
          item.value = e.target.value;
          return item;
        }
        return item;
      });
      return updatedItems;
    });
    setContentValue(JSON.stringify(items));
  };

  const handleValueTypeChange = (e) => {
    const listKey = e.target.getAttribute("listkey");
    setItems((oldItems) => {
      let updatedItems = oldItems.map((item, index) => {
        if (index == listKey) {
          item.value_type = e.target.value;
          return item;
        }
        return item;
      });
      return updatedItems;
    });
    setContentValue(JSON.stringify(items));
  };

  return (
    <div className="block">
      {items.map((item, index) => (
        <div
          key={index * 0.23}
          className="my-4 border-2 p-2 rounded-md shadow-lg"
        >
          <p className=" font-bold w-full bg-slate-300 p-1">
            Section {index + 1}
          </p>
          <input
            className={`shadow appearance-none border rounded w-full py-2 px-3 mr-2 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline`}
            type="text"
            placeholder="Collaborator"
            listkey={index}
            onChange={handleKeyChange}
            defaultValue={item.key}
            value={item.key}
          />

          <select
            className={`shadow block border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline`}
            listkey={index}
            onChange={handleValueTypeChange}
            defaultValue={item.value_type}
          >
            {valueTypeMap.map((type, index) => (
              <option key={index * 122} value={type.key}>
                {type.value}
              </option>
            ))}
          </select>
          <textarea
            className={`shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline`}
            rows={15}
            placeholder="Content"
            listkey={index}
            onChange={handleValueChange}
            defaultValue={item.value}
            value={item.value}
          ></textarea>

          <div>
            <p>Collaborator link</p>
            <input
              className={`shadow appearance-none border rounded w-full py-2 px-3 mr-2 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline`}
              type="text"
              placeholder="collaborator link"
              listkey={index}
              onChange={handleLinkChange}
              defaultValue={item.link}
              value={item.link}
            />
            {item.link && (
              <div className="my-2">
                <span
                  onClick={(e) => {
                    handleDeleteLink(index);
                  }}
                  className=" cursor-pointer bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 my-4 rounded focus:outline-none focus:shadow-outline"
                >
                  Remove Link
                </span>
              </div>
            )}
          </div>

          <>
            <p className="font-medium mt-2 text-lg ">Image</p>
            <div className="border p-2">
              <img
                src={item.image !== null ? item.image : defaultImage}
                alt="preview"
                height={150}
                width={150}
              />
              {item.image && (
                <div className="my-2">
                  <span
                    onClick={(e) => {
                      handleDeleteImage(index);
                    }}
                    className=" cursor-pointer bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 my-4 rounded focus:outline-none focus:shadow-outline"
                  >
                    Remove Image
                  </span>
                </div>
              )}
              <input
                listkey={index}
                type="file"
                accept="image/*"
                onChange={handleImageChange}
                className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline`}
              />
            </div>
          </>

          <div className="space-x-2 mt-4">
            <label className="">
              <input
                type="checkbox"
                className="mr-2"
                checked={checkedIndex.includes(index)}
                onChange={(e) => handleCheckboxChange(e, index)}
              />
              Show video input
            </label>
            {checkedIndex.includes(index) && (
              <>
                <p className="font-medium mt-2 text-lg ">video</p>
                <div className="border p-2">
                  <img
                    src={empty(contentValue) ? previewUrl : contentValue}
                    alt="preview"
                    height={150}
                    width={150}
                  />

                  <input
                    type="file"
                    listkey={index}
                    accept="video/*"
                    onChange={handleVideoChange}
                    className={` appearance-none  block  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline`}
                  />
                </div>
                <div>
                  <span
                    onClick={(e) => {
                      handleCheckboxChange("", index);
                      handleDeleteVideo(index);
                    }}
                    className=" cursor-pointer bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 my-4 rounded focus:outline-none focus:shadow-outline"
                  >
                    Delete video
                  </span>
                </div>
              </>
            )}
          </div>

         
          <div className="my-2 mt-4 w-full ">
            <span
              onClick={(e) => {
                handleRemoveSection(index);
              }}
              className="w-full flex justify-center cursor-pointer bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2  rounded focus:outline-none focus:shadow-outline"
            >
              Remove Section
            </span>
          </div>
        </div>
      ))}
      <button
        type="button"
        className="bg-blue-400 hover:bg-blue-700 text-white font-bold py-1 px-2 my-4 rounded focus:outline-none focus:shadow-outline"
        onClick={(e) =>
          setItems((old) => [
            ...old,
            { key: "", value_type: "text", value: "" },
          ])
        }
      >
        +
      </button>
    </div>
  );
};
const CheckBox = ({ setContentValue, contentValue }) => {
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [checked, setChecked] = useState(false);
  const options = countryList().getData();

  useEffect(() => {
    if (contentValue) {
      try {
        const parsedContent = JSON.parse(contentValue);
        setContentValue(contentValue); // Store the stringified content
        setChecked(parsedContent?.status === "yes");
        setSelectedOptions(parsedContent?.options || []);
      } catch (err) {
        console.error("Error parsing contentValue:", contentValue, err);
        // Set contentValue to an empty string if parsing fails
        setContentValue("");
      }
    }
  }, [contentValue]);

  // useEffect(() => {
  //   setChecked(contentValue?.status == "yes" ? true : false);
  // }, [contentValue]);

  // const handleCheckboxChange = (event) => {
  //   const updatedContentValue = JSON.stringify({
  //     ...JSON.parse(contentValue),
  //     status: event.target.checked ? "yes" : "no",
  //   });

  //   setChecked(event.target.checked);
  //   setContentValue(updatedContentValue); // Set the stringified content value
  //   // updateContent(updatedContentValue);
  // };

  const handleCheckboxChange = (event) => {
    let currentContent = {};
    if (contentValue) {
      try {
        currentContent = JSON.parse(contentValue);
      } catch (err) {
        console.error("Error parsing contentValue:", contentValue, err);
      }
    }

    const updatedContentValue = JSON.stringify({
      ...currentContent,
      status: event.target.checked ? "yes" : "no",
    });

    setChecked(event.target.checked);
    setContentValue(updatedContentValue); // Set the stringified content value
  };

  const handleAutocompleteChange = (event, newValue) => {
    let updatedContentValue;
    try {
      const currentContent = contentValue ? JSON.parse(contentValue) : {};
      updatedContentValue = JSON.stringify({
        ...currentContent,
        options: newValue,
      });
    } catch (err) {
      console.error("Error parsing contentValue:", contentValue, err);
      updatedContentValue = JSON.stringify({ options: newValue }); // Fallback to a new object
    }

    setSelectedOptions(newValue);
    setContentValue(updatedContentValue); // Set the stringified content value
  };

  // const handleCheckboxChange = (event) => {
  //   setChecked(event.target.checked);

  //   setContentValue(
  //    JSON.stringify({
  //       ...contentValue,
  //       status: event.target.checked == true ? "yes" : "no",
  //     })
  //   );
  // };

  return (
    <div className="flex flex-col gap-4 mb-4">
      <label className=" flex items-center gap-2">
        <input
          type="checkbox"
          checked={checked}
          onChange={handleCheckboxChange}
          defaultChecked={contentValue}
        />
        {checked} Set maintenance status
        {/* {checked ? "Yes" : "No"} */}
      </label>

      {/* TO Target Different Countries */}
      {/* <>
<label htmlFor="">Select countries</label>
      <Autocomplete
        multiple
        options={options}
        value={selectedOptions}
        onChange={handleAutocompleteChange}
       
        renderInput={(params) => (
          <TextField
            {...params}
            variant="outlined"
            label="Select options"
            placeholder="Choose..."
          />
        )}
        renderOption={(props, option, { selected }) => (
          <li {...props}>
            <Checkbox style={{ marginRight: 8 }} checked={selected} />
            <ListItemText primary={option?.label} />
          </li>
        )}
      />

</> */}
    </div>
  );
};
