import { AuthContext, tokenExpireError } from "Src/authContext";
import { GlobalContext } from "Src/globalContext";
import { RxDividerVertical } from "react-icons/rx";
import MkdSDK from "Utils/MkdSDK";
import React, { useEffect, useState } from "react";
import { NavLink, useLocation, useNavigate } from "react-router-dom";
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  makeStyles,
} from "@material-ui/core";
import { CompanyLogoMid } from "Assets/svgs/CompanyLogoMid";
import MyAccount from "./MyAccount";

const useStyles = makeStyles((theme) => ({
  formControl: {
    // margin: theme.spacing(1),
    minWidth: 120,
    paddingInline: 10,
    borderRadius: "9999px", // Rounded border
    // border: '2px solid red', // Red border
    backgroundColor: "#F8F8F8", // Gray background
    "& .MuiOutlinedInput-root": {
      "& fieldset": {
        border: "none", // Remove default border
      },
      "&:hover fieldset": {
        border: "none", // Remove border on hover
      },
      "&.Mui-focused fieldset": {
        border: "none", // Remove border when focused
      },
    },
    "& .MuiOutlinedInput-input": {
      padding: "10px 14px", // Adjust input padding if needed
    },
    "& .MuiInput-underline:before": {
      borderBottom: "none", // Remove underline before selection
    },
    "& .MuiInput-underline:after": {
      borderBottom: "none", // Remove underline after selection
    },
    "&.MuiFormControl-root": {
      justifyContent: " center",
      // Transparent background
    },
  },

  select: {
    "&.MuiSelect-select": {
      backgroundColor: "transparent", // Transparent background
    },
  },
  menuItem: {
    "&:hover": {
      backgroundColor: "rgba(193,39,44,0.1)", // Light red background on hover
      "& .MuiTypography-body1": {
        color: "#8b0000", // Deep red text color on hover
      },
    },
  },
  selectedMenuItem: {
    color: "red", // Red text color for selected item
    backgroundColor: "rgba(193,39,44,0.1) !important", // Light red background for selected item
    "&:hover": {
      backgroundColor: "#ffcccc", // Maintain light red background on hover for selected item
    },
    "& .MuiTypography-body1": {
      color: "#8b0000 !important", // Deep red text color on hover
    },
  },
}));

let sdk = new MkdSDK();

const NavBarChat = ({ toggle, isOpen, isMessageSent }) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isPlanDropdownOpen, setIsPlanDropdownOpen] = useState(false);
  const [plans, setPlans] = useState(null);
  const [filteredPlans, setFilteredPlans] = useState();
  const [selectedLang, setSelectedLang] = useState("en");
  const [selectedPlan, setSelectedPlan] = useState(
    filteredPlans?.length > 0 ? filteredPlans[0].id : ""
  );
  const classes = useStyles();

  const togglePlanDropdown = () => {
    setIsPlanDropdownOpen(!isPlanDropdownOpen);
  };

  const langList = {
    en: "English",
    de: "German",
    es: "Spanish",
    ru: "Russian",
    it: "Italian",
    fr: "French",
    nl: "Dutch",
  };

  const { state: states, dispatch } = React.useContext(AuthContext);
  const { state } = React.useContext(GlobalContext);

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };
  const location = useLocation();
  const navigate = useNavigate();

  const path = location.pathname;


  React.useEffect(() => {
    getuserplans();
  }, [isMessageSent]);

  useEffect(() => {
    if (filteredPlans?.length > 0) {
      setSelectedPlan(filteredPlans[0].id);
      dispatch({
        type: "SELECTEDPLAN",
        payload: filteredPlans[0].id,
      });
    }
  }, [!filteredPlans, isMessageSent]);

  const handleSelectPlan = (e) => {
    setSelectedPlan(e.target.value);
    dispatch({
      type: "SELECTEDPLAN",
      payload: e.target.value,
    });
  };

  // ............SET LANGUAGE..............
  const handleSelectLang = async (e) => {
    setSelectedLang(e.target.value);

    let filter = {
      user_id: localStorage.getItem("user"),
    };

    sdk.setTable("profile");
    const results = await sdk.callRestAPI(
      {
        payload: { ...filter },
        page: 1,
        limit: 10,
        sortId: "",
        direction: "",
      },
      "PAGINATE"
    );

    const { list, total, limit, num_pages, page } = results;

    if (results) {
      sdk.setTable("profile");
      const result = await sdk.callRestAPI(
        {
          language: e.target.value,
          id: list[0]?.id,
        },
        "PUT"
      );
    }
    setSelectedLang(e.target.value);
    localStorage.setItem("selectedlanguage", e.target.value);
    dispatch({
      type: "SELECTEDLANG",
      payload: e.target.value,
    });
  };

  //............GET SELECTED LANGUAGE..............
  useEffect(() => {
    let filter = {
      user_id: localStorage.getItem("user"),
    };
    let pageSize = 10;

    async function getData(pageNum, limitNum, currentTableData) {
      try {
        if (states?.user) {
          sdk.setTable("profile");
          const result = await sdk.callRestAPI(
            {
              payload: { ...currentTableData },
              page: pageNum,
              limit: limitNum,
              sortId: "",
              direction: "",
            },
            "PAGINATE"
          );

          const { list, total, limit, num_pages, page } = result;

          setSelectedLang(list[0]?.language ?? "en");
          localStorage.setItem("selectedlanguage", list[0]?.language ?? "en");
        }
      } catch (error) {
        console.log("ERROR", error);
        tokenExpireError(dispatch, error.message);
      }
    }

    // getData()
    getData(1, pageSize, filter);
  }, []);

  const customStyles = {
    option: (base, { data, isDisabled, isFocused, isSelected }) => {
      return {
        ...base,
        backgroundColor: "red",
      };
    },
  };

  const getuserplans = async () => {
    try {
      const result = await sdk.getuserplans(states.user);
      if (result) {

        setPlans(result.list);
        setFilteredPlans(
          result?.list?.filter((plan, i) => plan.messages_left !== 0)
        );
      }
    } catch (err) {
      console.log("Error:", err);
      tokenExpireError(
        dispatch,
        err.response?.data.message ? err.response?.data.message : err.message
      );
    }
  };

  React.useEffect(() => {
    (async function () {})();
  }, []);
  const handleChange = (e) => {
    setAgeGroup(e.target.value);
    handleSelectPlan(e);
  };

  return (
    <nav className=" p-2 py-6 border-b-2 mb-0 flex flex-col justify-between relative md:min-h-[100px] border-[#DCDCDC]   w-full bg-white">
      <nav className="mb-0 flex flex-col justify-between relative  border-[#DCDCDC]  max-w-[1280px] mx-auto w-full bg-white">
        <div className="bg-white   mb-0 flex flex-row justify-between items-center relative ">
          {/*COMPANY LOGO  */}
          <div className="text-white lg:hidden flex ">
            <div
              className="flex flex-col max-w-md w-full relative md:items-start items-center"
              onClick={() => {
                navigate("/");
              }}
            >
              <CompanyLogoMid />
            </div>
          </div>

          {/* CENTER ITEMS */}
          <div className="container lg:flex  hidden mx-auto  justify-around items-center ">
            {/* Right items */}

            <div className="text-white flex items-center lg:justify-start gap-2 ">
              <p className=" text-[#999999] lg:px-2 py-2 lg:text-[16px] text-[12px]  mr-0 inline ">
                Response Mode:
              </p>

              <FormControl className={classes.formControl}>
                <Select
                  labelId="demo-simple-select-label"
                  id="demo-simple-select"
                  value={selectedPlan}
                  onChange={handleChange}
                  classes={{ select: classes.select }} // Apply custom styles to select
                  // displayEmpty
                >
                  {!filteredPlans?.length ? (
                    <MenuItem>No Active Plan</MenuItem>
                  ) : null}

                  {filteredPlans?.map((option) => (
                    <MenuItem
                      className={`${classes.menuItem} ${
                        selectedPlan == option.id
                          ? classes.selectedMenuItem
                          : ""
                      }`}
                      value={option.id}
                    >
                      {" "}
                      <span
                        className={` text-[#999999]  hover:text-companyRed text-[14px] ${
                          selectedPlan == option.id ? "text-companyBlack" : ""
                        }`}
                      >
                        {" "}
                        {`${
                          option?.plan_name !== "0" ? option.plan_name : ""
                        } (${option.messages_left} Message Remaining)`}{" "}
                      </span>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {/* <select
              className="shadow appearance-none border rounded-full  inline py-2 lg:px-8 px-2 lg:mr-2 mr-0 text-gray-700 leading-tight focus:outline-none focus:shadow-outline lg:text-[14px] text-[12px]"
              styles={customStyles}
              class="border-0 cursor-pointer border-red-600 rounded-full bg-[#F8F8F8] w-60 duration-300 hover:bg-[#ebebeb] focus:bg-[#F8F8F8] text-black lg:text-[14px] text-[12px] focus:outline-none focus:shadow-outline"
              onChange={handleSelectPlan}
            >
              {!filteredPlans?.length ? (
                <option class="w-96 rounded-lg drop-shadow-md bg-white duration-300 hover:bg-red-600 focus:bg-red-500 focus:ring-0 text-[#999999] sm:text-[14px] text-[12px]">
                  {" "}
                  No Active Plan
                </option>
              ) : null}
              {filteredPlans?.length &&
                filteredPlans?.map((option) => (
                  <option
                    name="status"
                    value={option.id}
                    key={option.id}
                    styles={customStyles}
                    class="w-96 rounded-lg drop-shadow-md bg-white duration-300 hover:bg-red-600 focus:bg-red-500 focus:ring-0 text-[#999999] lg:text-[14px] text-[12px]"
                  >
                    {`${option?.plan_name !== "0" ? option.plan_name : ""} (${
                      option.messages_left
                    } Message Remaining)`}
                  </option>
                ))}
            </select> */}

              <button
                className="bg-companyRed hover:bg-red-600 text-white lg:px-4 sm:px-6 px-2 py-2 lg:text-[14px] text-[12px] rounded-full font-semibold"
                onClick={() => {
                  navigate("/user/buy", { state: { from: location } });
                }}
              >
                Get Tokens
              </button>
            </div>

            {/* Left items */}
            <div className=" flex-1  w-full border-red-500  flex justify-end items-start  ">
              <div className="text-black text-center flex items-center  w-full max-w-[336px] justify-between  gap-1  grow">
                <div className="  border-gray-300 w-fit flex justify-center">
                  <NavLink
                    className="  flex justify-around items-center text-center border-gray-300 lg:text-[16px] text-[12px] font-medium"
                    to={"/"}
                  >
                    Home
                  </NavLink>
                </div>
                <hr class="vertical-hr bg-companyBlack" />

                <div className="  border-gray-300  w-fit flex justify-center">
                  <NavLink
                    className="  flex justify-around  border-gray-300 lg:text-[16px] text-[12px] font-medium"
                    to={"/user/aboutpage"}
                  >
                    About Us
                  </NavLink>
                </div>

                {path == "/user/chat" ? (
                   <MyAccount/>
                ) : (
                  <>
                    <hr class="vertical-hr bg-companyBlack" />

                    <div className="  border-gray-300  w-fit flex justify-center">
                      <NavLink
                        className="  lg:text-[16px] text-[12px] font-medium "
                        to={"/user/chat"}
                      >
                        Chatbot
                      </NavLink>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Right-end item with dropdown */}
            <div className="relative ">
              {isDropdownOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-white border rounded-lg shadow-lg">
                  <ul>
                    <li className="hover:bg-blue-100 py-2 px-4 hover:border rounded-t-lg">
                      <NavLink to="/user/profile">My Profile</NavLink>
                    </li>
                    <li className="hover:bg-blue-100 py-2 px-4">
                      <NavLink to={"/user/plans"}>My Plan</NavLink>
                    </li>
                    <li className="hover:bg-blue-100 py-2 px-4">
                      <NavLink
                        to="/user/login"
                        onClick={() =>
                          dispatch({
                            type: "LOGOUT",
                          })
                        }
                      >
                        Logout
                      </NavLink>
                    </li>
                  </ul>
                </div>
              )}
            </div>
          </div>

          {/* MENU TOGGLE */}
          <span
            className=" lg:hidden flex justify-end cursor-pointer"
            onClick={toggle}
          >
            {!isOpen ? (
              <svg
                className="block h-6 w-6"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            ) : null}
          </span>
        </div>

        {/* for the bottom overflow */}
        <div className="text-white mt-1 lg:hidden sm:flex sm:flex-row flex flex-col items-center justify-center gap-2 relative bottom-[-0rem] left-0 ">
          <div className=" flex justify-between w-full">
            <p className=" text-[#999999] lg:px-4 px-2 py-2 sm:text-[14px] text-[12px] lg:mr-2 mr-0 inline text-left sm:w-[200px] w-full ">
              Response Mode:
            </p>
            {/* ...Select Language for small screen.... */}
            {/* <div className=" flex flex-col items-center  w-full">
            <select
              className="shadow appearance-none border rounded-full  inline py-2 lg:px-8 px-2 lg:mr-2 mr-0 text-gray-700 leading-tight focus:outline-none focus:shadow-outline lg:text-[14px] text-[12px]"
              styles={customStyles}
              class="border-0 cursor-pointer border-red-600 rounded-full bg-[#F8F8F8]  duration-300 hover:bg-[#ebebeb] focus:bg-[#F8F8F8] text-black lg:text-[14px] text-[12px] focus:outline-none focus:shadow-outline"
              onChange={handleSelectLang}
              value={selectedLang}
            >
              {!langList ? (
                <option class="w-96 rounded-lg drop-shadow-md bg-white duration-300 hover:bg-red-600 focus:bg-red-500 focus:ring-0 text-[#999999] sm:text-[14px] text-[12px]">
                  {" "}
                  select language
                </option>
              ) : null}
             
              {langList &&
                Object.entries(langList)?.map(([key, value], i) => (
                  <>
                    <option
                      name="status"
                      value={key}
                      key={i}
                      styles={customStyles}
                      class="w-96 rounded-lg drop-shadow-md bg-white duration-300 hover:bg-red-600 focus:bg-red-500 focus:ring-0 text-[#999999] lg:text-[14px] text-[12px]"
                    >
                      {value}
                    </option>
                  </>
                ))}
            </select>
            <p className=" sm:text-[14px] text-[12px] text-[#999999]">
              select preferred language
            </p>
          </div> */}
          </div>
          <div className="sm:flex sm:flex-row xs:flex xs:flex-row flex flex-col md:justify-evenly xs:justify-around justify-around w-full ">
            <FormControl className={classes.formControl}>
              <Select
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                value={selectedPlan}
                onChange={handleChange}
                classes={{ select: classes.select }}
                // displayEmpty
              >
                {!filteredPlans?.length ? (
                  <MenuItem>No Active Plan</MenuItem>
                ) : null}

                {filteredPlans?.map((option) => (
                  <MenuItem
                    className={`${classes.menuItem} ${
                      selectedPlan == option.id ? classes.selectedMenuItem : ""
                    }`}
                    value={option.id}
                  >
                    <span
                      className={` text-[#999999]  h-full  hover:text-companyRed text-[12px] ${
                        selectedPlan == option.id ? "text-companyBlack" : ""
                      }`}
                    >
                      {" "}
                      {`${option?.plan_name !== "0" ? option.plan_name : ""} (${
                        option.messages_left
                      } Message Remaining)`}{" "}
                    </span>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* <select
            className="shadow appearance-none border rounded-full  inline py-2 px-4  text-gray-700 leading-tight focus:outline-none focus:shadow-outline sm:text-[14px] text-[10px]"
            styles={customStyles}
            class="border-0 cursor-pointer border-red-600 rounded-full bg-[#F8F8F8]  w-60 xs:w-48 sm:w-72 sm:text-[14px] text-[10px] duration-300 hover:bg-[#c3c2c2] focus:bg-[#F8F8F8] text-black focus:outline-none focus:shadow-outline"
            onChange={handleSelectPlan}
          >
            {!filteredPlans?.length ? (
              <option class="w-96 rounded-lg drop-shadow-md bg-white duration-300 hover:bg-red-600 focus:bg-red-500 focus:ring-0 text-[#999999] sm:text-[14px] text-[12px]">
                {" "}
                No Active Plan
              </option>
            ) : null}
            {filteredPlans?.length &&
              filteredPlans?.map((option) => (
                <option
                  name="status"
                  value={option.id}
                  key={option.id}
                  styles={customStyles}
                  class="w-96 rounded-lg drop-shadow-md bg-white duration-300 hover:bg-red-600 focus:bg-red-500 focus:ring-0 text-[#999999] sm:text-[14px] text-[12px]"
                >
                  {`${option?.plan_name !== "0" ? option.plan_name : ""} (${
                    option.messages_left
                  } Message Remaining)`}
                </option>
              ))}
          </select> */}

            <button
              className="bg-companyRed  text-white lg:px-4 px-8 py-2 sm:text-[14px] text-[10px] rounded-full xs:w-fit md:w-fit "
              onClick={() => {
                navigate("/user/buy", { state: { from: location } });
              }}
            >
              Get Tokens
            </button>
          </div>
        </div>
      </nav>
    </nav>
  );
};

export default NavBarChat;
