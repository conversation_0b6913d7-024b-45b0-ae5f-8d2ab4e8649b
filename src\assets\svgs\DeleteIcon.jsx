import React from "react";

export const DeleteIcon = ({ className = "" }) => {
  return (
    <svg
    className={`${className}`}
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g clip-path="url(#clip0_430_8706)">
                      <path
                        d="M2 4.00098H3.33333H14"
                        stroke="white"
                        stroke-width="1.33333"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M5.33398 4.00065V2.66732C5.33398 2.3137 5.47446 1.97456 5.72451 1.72451C5.97456 1.47446 6.3137 1.33398 6.66732 1.33398H9.33398C9.68761 1.33398 10.0267 1.47446 10.2768 1.72451C10.5268 1.97456 10.6673 2.3137 10.6673 2.66732V4.00065M12.6673 4.00065V13.334C12.6673 13.6876 12.5268 14.0267 12.2768 14.2768C12.0267 14.5268 11.6876 14.6673 11.334 14.6673H4.66732C4.3137 14.6673 3.97456 14.5268 3.72451 14.2768C3.47446 14.0267 3.33398 13.6876 3.33398 13.334V4.00065H12.6673Z"
                        stroke="white"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_430_8706">
                        <rect
                          width="16"
                          height="16"
                          fill="white"
                          transform="translate(0 0.000976562)"
                        />
                      </clipPath>
                    </defs>
                  </svg>
  );
};
