import React, {
  useEffect,
  useId,
  useLayoutEffect,
  useRef,
  useState,
} from "react";
import NavBarChat from "./NavBarChat";
import { GlobalContext } from "Src/globalContext";
import { AuthContext, tokenExpireError } from "Src/authContext";
import { NavLink, useLocation, useNavigate } from "react-router-dom";
import { formatTimestamp } from "Utils/custom/CustomUtils";
import MkdSDK from "Utils/MkdSDK";
import Spinner from "./Spinner";
import { Modal } from "Components/Modal/Modal";
import SkeletonLoading from "./SkeletonLoading";
import { ChatBotIcon } from "../assets/svgs/ChatBotIcon";
import SuggestionMessages from "Pages/UsersPage/SuggestionMessages";
import BetaSign from "./BetaSign";
import GoogleTranslate from "./GoogleTranslate";
import CheckIcon from "@mui/icons-material/Check";
import CloseIcon from "@mui/icons-material/Close";
import SelectLang from "./SelectLang";
import DotLoaders from "./DotLoaders";
import CircularLoader from "./CircularLoader";
import { MoreIcon } from "Assets/svgs/MoreIcon";
import ChatTopLeftPanel from "./ChatTopLeftPanel";
import ChatMenuDropDown from "./ChatMenuDropDown";
import { SideMenuCloseIcon } from "Assets/svgs/SideMenuCloseIcon";
import ChatInputComponent from "./ChatInputComponent";
import { ChatIcon } from "Assets/svgs/ChatIcon";
import { EditIcon } from "Assets/svgs/EditIcon";
import { DeleteIcon } from "Assets/svgs/DeleteIcon";
import FeatureNotificationPopUp from "./FeatureNotificationPopUp";
import SelectSport, { sportLeagues, sportList } from "./SelectSport";
import {
  breakByNewline,
  convertSportsToArray,
  formatPrediction,
} from "Utils/utils";
let sdk = new MkdSDK();

const ChatComponent = () => {
  const [messages, setMessages] = useState([]);
  const [userInput, setUserInput] = useState("");
  const [selectedChat, setSelectedChat] = useState(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isNewChat, setIsNewChat] = useState(true);
  const [newChatClicked, setNewChatClicked] = useState(false);
  const [isSelectedChat, setIsSelectedChat] = useState(false);
  const [loadingEdit, setLoadingEdit] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [edit, setEdit] = useState(false);

  const { state: states, dispatch } = React.useContext(AuthContext);
  const { state } = React.useContext(GlobalContext);
  const [plans, setPlans] = useState();
  const [isLoading, setIsLoading] = useState(false);
  const [newSent, setNewSent] = useState(false);
  const [isMessageSent, setIsMessageSent] = useState(false);
  const [currentRoomId, setCurrentRoomId] = useState();
  const [inputValue, setInputValue] = useState("");
  const [temp, setTemp] = useState("");
  const [selectedPlan, setSelectedPlan] = useState(
    plans?.length > 0 ? plans[0].id : ""
  );
  const [suggestions, setSuggestions] = useState([
    "Popular matches this weekend?",
  ]);
  // STATES FOR THE CHAT BOT .....................................
  const [rooms, setRooms] = React.useState([]);
  const [chatId, setChatId] = React.useState();
  const otherUserId = React.useRef();
  const currentRooms = React.useRef();
  const [message, setMessage] = React.useState("");
  const [roomId, setRoomId] = React.useState();
  const [screenSize, setScreenSize] = React.useState(window.innerWidth);
  const [showContacts, setShowContacts] = React.useState(true);
  const [iscreateRoom, setCreateRoom] = React.useState(false);
  const [isSuggestedQuestion, setIsSuggestedQuestion] = React.useState(false);
  const [filteredRooms, setFilteredRooms] = React.useState([]);
  const [editQues, setEditQues] = useState();
  const [isNewRoom, setIsNewRoom] = useState();
  const [showModal, setShowModal] = useState(true);
  const [title, setTitle] = useState("");
  const [sport, setSport] = useState("");
  const [selectedLang, setSelectedLang] = useState(
    localStorage.getItem("selectedlanguage") ?? "en"
  );
  const [selectedSport, setSelectedSport] = useState(
    localStorage.getItem("selectedSport") ?? "football"
  );
  const [list, setList] = useState();
  const [dailyQuiz, setDailyQuiz] = useState("");
  const [currentTableData, setCurrentTableData] = React.useState([]);
  const [pageSize, setPageSize] = React.useState(10);
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const containerRef = useRef(null);
  const navigate = useNavigate();

  // const suggestion1 = SuggestionMessages();
  const editinputRef = useRef(null);
  const [generatedNumber, setGeneratedNumber] = useState(null);

  // Add a new state for temporary messages
  const [tempMessages, setTempMessages] = useState([]);

  // Suggestion messages
  useEffect(() => {
    const test = async () => {
      const result = await sdk.getSuggestedQuestions();
      // const result = await suggestion1;
      if (!result?.error) {
        if (result?.question?.length > 0) {
          const translationPromises = result?.question?.map(async (val) => {
            const response = await GoogleTranslate(
              val.question,
              "en",

              "en"
            );
            return response;
          });

          const translatedResults = await Promise.all(
            await translationPromises
          );

          setSuggestions(translatedResults);
        } else {
          const response = await GoogleTranslate(
            result?.question?.question,
            "en",

            "en"
          );
          setSuggestions(response);
          return response;
        }
      }
    };
    test();
  }, [sport]);

  React.useEffect(() => {
    sessionStorage.removeItem("tempInput");
  }, [message]);
  React.useEffect(() => {
    const userSelectedLang =
      localStorage.getItem("selectedlanguage") &&
      localStorage.getItem("selectedlanguage");
    setSelectedLang(userSelectedLang);
  }, []);
  React.useEffect(() => {
    const userSelectedSport =
      localStorage.getItem("selectedSport") &&
      localStorage.getItem("selectedSport");
    setSelectedSport(userSelectedSport);
  }, []);

  useEffect(() => {
    const handleStorageChange = () => {
      const userSelectedLang = JSON.parse(
        localStorage.getItem("selectedlanguage")
      );
      setSelectedLang(userSelectedLang);
    };

    window.addEventListener("storage", handleStorageChange);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }, []);

  // Scroll to the bottom of the chat container when messages change or component mounts

  useLayoutEffect(() => {
    const container = containerRef.current;
    if (container) {
      container.scrollTop = container.scrollHeight;
    }
  }, [messages, isLoading]);
  // for initial selection of the selected plan
  useEffect(() => {
    if (plans?.length > 0) {
      setSelectedPlan(plans[0].id);
    }
  }, []);

  const handleInputClick = () => {
    // When the input field is clicked, set the selection range to the end of the input value
    if (editinputRef.current) {
      editinputRef.current.setSelectionRange(
        editinputRef.current.value.length,
        editinputRef.current.value.length
      );
    }
  };

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };
  const toggle = () => {
    setIsOpen((prev) => !prev);
  };

  // You can initialize chatHistory with any initial messages here.

  const handleInputChange = (e) => {
    setUserInput(e.target.value);
  };
  const handleChatSelection = (chat) => {
    setSelectedChat(chat);
    setIsSelectedChat(true);
    setIsNewChat(false);
  };
  const handleeditroomname = (room_id) => {
    editroomname(room_id, inputValue);
  };
  const handledeletequestion = (id) => {
    deletequestion(id);
  };

  const groupMessagesByDate = (messages) => {
    const groupedMessages = {};
    messages.forEach((message) => {
      const date = formatTimestamp(message?.update_at);
      if (!groupedMessages[date]) {
        groupedMessages[date] = [];
      }
      groupedMessages[date].push(message);
    });
    return groupedMessages;
  };

  const handleMessageNameClick = (room) => {
    handleChatSelection(room);
    setIsNewChat(false);
    setIsOpen(false);
    setInputValue(room?.room_name);
  };

  const onClickSuggestion = async (suggestion) => {
    // console.log(isSuggestedQuestion, "isSuggestedQuestion");
    // setIsSuggestedQuestion(true);
    localStorage.setItem("isSuggestedQuestion", true);
    const response = await GoogleTranslate(
      suggestion,
      localStorage.getItem("selectedlanguage"),
      "en"
    );

    sessionStorage.setItem("tempInput", suggestion);
    if (response) {
      sendNewMessage(roomId, response, suggestion);
      setIsSelectedChat(false);
      setUserInput("");
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    sessionStorage.setItem("tempInput", userInput);

    if (userInput.trim() === "") {
      return;
    }

    const response = await GoogleTranslate(
      userInput,
      // JSON.parse(localStorage.getItem("selectedlanguage")),
      localStorage.getItem("selectedlanguage"),
      "en"
    );

    if (response) {
      sendNewMessage(roomId, response, userInput);
      // sendNewMessage(roomId, userInput);
      setIsSelectedChat(false);
      // generateResponse(userInput);
      setUserInput("");
    }
  };

  const handleKeyDown = async (e) => {
    if (e.key === "Enter" && e.shiftKey) {
      // Insert a newline character instead of preventing default
      setUserInput((prevMessage) => prevMessage + "\n");
    } else if (e.key === "Enter") {
      e.preventDefault();
      sessionStorage.setItem("tempInput", userInput);
      const response = await GoogleTranslate(
        userInput,
        localStorage.getItem("selectedlanguage"),
        // JSON.parse(localStorage.getItem("selectedlanguage")),
        "en"
      );

      if (response) {
        sendNewMessage(roomId, response, userInput);
        // sendNewMessage(roomId, userInput);
        setIsSelectedChat(false);
        setUserInput("");
      }
    }
  };

  const timestamp = Date.now();
  const formattedTimestamp = formatTimestamp(timestamp);
  // console.log(formattedTimestamp);

  // SVG
  const renderChatByDate = () => {
    const groupedMessages = groupMessagesByDate(filteredRooms);
    // console.log("chatHistory", chatHistory);

    return Object.keys(groupedMessages).map((date, i) => (
      <div key={i}>
        <p className="text-gray-500 sm:text-sm text-xs">{date}</p>

        {groupedMessages[date].map((room, index) => (
          <div
            key={index}
            className={` group flex justify-between items-center cursor-pointer hover:bg-zinc-700 mb-1 rounded p-1 w-[95%] relative gap-[3px] 
           
            ${selectedChat === room ? "bg-zinc-700" : ""}`}
          >
            <div className="inline  border-yellow-200">
              <ChatIcon />
            </div>

            <div
              id={`user-${room.other_user_id}`}
              className={`entry truncate flex-grow cursor-pointer py-1 items-center transform  rounded flex  border-red-500 `}
              onClick={() => {
                if (!edit) {
                  getChats(room.id, room.chat_id);
                  otherUserId.current = room.other_user_id;
                  otherUserId.currentRoom = room;
                  if (screenSize < 1024) {
                    setShowContacts(false);
                  }
                  handleMessageNameClick(room);
                }
              }}
            >
              {/* Room Name */}
              <div className="truncate px-2 flex items-center">
                <div className="  flex items-center gap-2">
                  <span className="room-name font-medium md:text-[14px] text-[12px] truncate">
                    {room?.room_name ? room.room_name : "null"}
                  </span>
                </div>
              </div>

              {/* Edit input field */}
              {edit && selectedChat === room ? (
                <div className=" absolute w-full h-full flex items-center px-[3px]  z-10 left-0 text-black rounded">
                  <input
                    ref={editinputRef}
                    type="text"
                    autofocus="autofocus"
                    value={inputValue}
                    onChange={(e) => {
                      setInputValue(e.target.value);
                    }}
                    className="focus absolute w-full h-full  text-left flex items-center left-0 text-white bg-companyBlack  pl-[5px] rounded-full "
                  />
                </div>
              ) : null}
            </div>
            {/* Submit Edit and Cancel Icon */}
            {edit && selectedChat === room ? (
              <div className=" flex  items-center z-10  py-0  border-red-400">
                <div
                  onClick={() => {
                    handleeditroomname(room.id);
                    setEdit(false);
                  }}
                >
                  <CheckIcon />
                </div>
                <div onClick={() => setEdit((prev) => !prev)}>
                  <CloseIcon />
                </div>
              </div>
            ) : null}

            {/*Toggle Edit and Delete Icons */}
            {/* {selectedChat === room && !edit ? ( */}
            {!edit ? (
              <div
                className={` group flex gap-3 items-center z-10  p-3  border-red-600 ${
                  !edit && selectedChat === room
                    ? ""
                    : "hidden group-hover:flex"
                }`}
              >
                <div
                  onClick={() => {
                    handleMessageNameClick(room);
                    setEdit((prev) => !prev);
                    handleInputClick();
                  }}
                >
                  {loadingEdit ? <CircularLoader /> : <EditIcon />}
                </div>
                <div onClick={() => handledeletequestion(room.id)}>
                  <DeleteIcon />
                </div>
              </div>
            ) : null}
          </div>
        ))}
      </div>
    ));
  };

  function setDimension(e) {
    if (e.currentTarget.innerWidth > 1024) {
      setShowContacts(true);
    }
    setScreenSize(e.currentTarget.innerWidth);
  }

  async function getRooms(filter) {
    try {
      if (filter) {
        return setFilteredRooms(
          rooms.filter((user) =>
            `${user.first_name.toLowerCase()} ${user.last_name.toLowerCase()}`.includes(
              filter.toLowerCase()
            )
          )
        );
      }

      const chats = await sdk.getMyRoom(states.user);

      if (chats && chats.list && chats.list[0]) {
        setRooms(chats.list);
        setFilteredRooms(chats?.list);
        currentRooms.current = chats.list;
        let date = new Date().toISOString().split("T")[0];
      } else {
        setFilteredRooms([]);
        setMessages([]);
        setRoomId();
        setIsNewChat(true);
      }
    } catch (err) {
      // console.log("Error:", err);
      tokenExpireError(
        dispatch,
        err.response?.data?.message ? err.response?.data?.message : err.message
      );
    }
  }

  async function createNewRoom(clickedNewChat) {
    // otherUser
    try {
      setIsLoading(true);
      const createdRoom = await sdk.createNewRoom({
        user_id: states.user,
        other_user_id: 0,
        chat_id: -1,
        unread: 1,
        user_update_at: "2023-09-29 02:35:14",
        other_user_update_at: "2023-09-29 02:35:14",
        room_name: "New Chat",
      });
      if (createdRoom) {
        getRooms();
        // console.log("xx", createdRoom.message);
        setCurrentRoomId(createdRoom.message);
        if (
          localStorage.getItem("userInput") &&
          localStorage.getItem("messageTitle")
        ) {
          editroomname(
            createdRoom.message,
            localStorage.getItem("messageTitle")
          );

          function delayedSendMessage() {
            setTimeout(function () {
              sendNewMessage(
                createdRoom.message,
                localStorage.getItem("userInput"),
                localStorage.getItem("messageTitle")
              );
              localStorage.removeItem("messageTitle");
              localStorage.removeItem("userInput");
            }, 3000);
          }

          delayedSendMessage();
          setNewSent(true);
        } else {
          getChats(createdRoom.message, -1);
          getRooms();
        }

        setIsNewRoom(true);
      }

      setCreateRoom(false);
      if (clickedNewChat) {
        setIsLoading(false);
      }
    } catch (err) {
      setCreateRoom(false);
      setIsLoading(false);
      tokenExpireError(
        dispatch,
        err.response?.data.message ? err.response?.data.message : err.message
      );
    }
  }

  useEffect(() => {
    getRooms();
  }, [newSent]);
  useEffect(() => {
    // getRooms();
  }, [newSent]);

  async function getChats(room_id, chat_id) {
    const translatedMessages = [];
    const finalMessage = [];
    try {
      setRoomId(room_id);
      setChatId(chat_id);
      let date = new Date().toISOString().split("T")[0];
      const messages = await sdk.getChats(room_id, chat_id, date);

      if (messages && messages.model) {
        const translatedMessages = await Promise.all(
          messages.model.map(async (val) => {
            const response = await GoogleTranslate(
              val.chat.message,
              "en",
              localStorage.getItem("selectedlanguage")
              // JSON.parse(localStorage.getItem("selectedlanguage"))
            );
            finalMessage.push({ user_id: val.chat.user_id, message: response });
            return { user_id: val.chat.user_id, message: response };
            // return response;
          })
        );
        setMessages(translatedMessages.reverse());
        setEditQues(translatedMessages[0]);
        // setMessages(finalMessage.reverse());
        // setEditQues(finalMessage[0]);
      }
    } catch (err) {
      // console.log("Error:", err);
      tokenExpireError(
        dispatch,
        err.response?.data.message ? err.response?.data.message : err.message
      );
    }
  }

  async function editroomname(id, room_name) {
    setLoadingEdit(true);
    try {
      const result = sdk.editroomname(id, room_name);

      if (result) {
        getRooms();
        setLoadingEdit(false);
      }
    } catch (err) {
      // console.log("Error:", err);
      tokenExpireError(
        dispatch,
        err.response?.data.message ? err.response?.data.message : err.message
      );
      setLoadingEdit(false);
    }
  }
  async function deletequestion(id) {
    try {
      const result = await sdk.deletequestion(id);
      if (result) {
        getRooms();
      }
    } catch (err) {
      // console.log("Error:", err);
      tokenExpireError(
        dispatch,
        err.response?.data.message ? err.response?.data.message : err.message
      );
    }
  }

  async function sendNewMessage(
    roomId,
    userInput,
    messageTitle,
    isSuggestedQuestion
  ) {
    try {
      let date = new Date().toISOString().split("T")[0];
      setIsMessageSent(false);

      if (roomId) {
        setIsLoading(true);

        // Store current messages as temporary view
        setTempMessages([
          ...messages,
          {
            user_id: states?.user,
            message: userInput,
            chat: {
              timestamp: new Date().toISOString(),
            },
          },
        ]);

        const messageresult = await sdk.postMessage({
          room_id: roomId,
          chat_id: chatId,
          user_id: states?.user,
          message: userInput,
          date,
        });

        if (!messageresult?.error) {
          rooms[0]?.room_name === "New Chat"
            ? editroomname(roomId, messageTitle)
            : // ? editroomname(roomId, userInput)
              rooms[0]?.room_name;

          let result;

          if (localStorage.getItem("isSuggestedQuestion")) {
            setIsSuggestedQuestion(false);
            localStorage.removeItem("isSuggestedQuestion");

            result = await sdk.matchScheduleApi(
              userInput,
              roomId,
              localStorage.getItem("selectedPlan")
            );
          } else {
            // Otherwise, call the original API

            console.log(
              "called===>",
              localStorage.getItem("selectedSportName"),
              localStorage.getItem("selectedSport"),
              sportLeagues
            );
            const selectedSportNameLocal =
              localStorage.getItem("selectedSportName");

            const selectedSportLocal = localStorage.getItem("selectedSport");

            result = await sdk.chatai(
              userInput,
              roomId,

              localStorage.getItem("selectedPlan"),

              !selectedSportNameLocal || selectedSportNameLocal === "undefined"
                ? " "
                : selectedSportNameLocal,

              !selectedSportLocal || selectedSportLocal === "undefined"
                ? sportLeagues
                : convertSportsToArray(selectedSportLocal)
            );

            console.log("called===> 2", result);
          }

          if (result) {
            // Instead of calling getChats immediately, update tempMessages with AI response
            setTempMessages((prev) => [
              ...prev,
              {
                user_id: 0, // AI's message
                message: "",
                chat: {
                  timestamp: new Date().toISOString(),
                },
              },
            ]);

            // Now get the official messages from the server
            getChats(roomId, chatId, date);

            getuserplans();
            setIsNewChat(false);
            setIsMessageSent(true);
            setIsLoading(false);
            getRooms();
            localStorage.removeItem("messageTitle");
            localStorage.removeItem("userInput");
          } else {
            console.log("result", result);
          }
          setMessage("");
        } else {
          console.log("messageresult", messageresult);
        }
      } else {
        createNewRoom(false);
        localStorage.setItem("messageTitle", messageTitle);
        localStorage.setItem("userInput", userInput);
      }
    } catch (err) {
      setIsLoading(false);
      tokenExpireError(
        dispatch,
        err.response?.data.message ? err.response?.data.message : err.message
      );
    }
  }

  let plan = [];

  const getuserplans = async () => {
    try {
      const result = await sdk.getuserplans(states.user);
      if (result) {
        setPlans(result.list);
        plan.push(result.list);
      }
    } catch (err) {
      // console.log("Error:", err);
      tokenExpireError(
        dispatch,
        err.response?.data.message ? err.response?.data.message : err.message
      );
    }
  };

  React.useEffect(() => {
    getuserplans();
  }, [isLoading]);

  React.useEffect(() => {
    (async function () {
      await getRooms();
    })();
  }, []);

  React.useEffect(() => {
    // getdailyquiz();
    let filter = {
      id: localStorage.getItem("randNum"),
    };
    async function getData(pageNum, limitNum, currentTableData) {
      try {
        sdk.setTable("quiz");
        // let sortField = columns.filter((col) => col.isSorted);
        const result = await sdk.callRestAPI(
          {
            payload: { ...currentTableData },
            page: pageNum,
            limit: limitNum,
            sortId: "",
            direction: "",
          },
          "GETALL"
        );
        const { list, total, limit, num_pages, page } = result;
        setList(list);

        setCurrentTableData(list);
      } catch (error) {
        console.log("ERROR", error);
        tokenExpireError(dispatch, error.message);
      }
    }

    getData(1, pageSize, filter);
  }, []);

  // New random func

  let maxNum = list?.length;
  // let maxNum = 1;
  let minNum = 0;
  let numDiff = maxNum - minNum;

  const storedNumbers =
    JSON.parse(localStorage.getItem("generatedNumbers")) || [];

  const generateNewNumber = () => {
    let newNumber;
    do {
      if (!(storedNumbers.length == numDiff + 1)) {
        newNumber = Math.floor(Math.random() * (numDiff + 1)) + minNum;
      } else {
        localStorage.removeItem("generatedNumbers");
        return;
      }
    } while (storedNumbers.includes(newNumber));

    setGeneratedNumber(newNumber);

    localStorage.setItem("currentNumber", newNumber);
    localStorage.setItem("numberGenerated", true);
    localStorage.setItem(
      "generatedNumbers",
      JSON.stringify([...storedNumbers, newNumber])
    );
  };

  // New random func ends

  useEffect(() => {
    if (list) {
      const currentNumber =
        generatedNumber || localStorage.getItem("currentNumber");
      setDailyQuiz(list[currentNumber]);
    }
  }, [list]);

  const modalCloseClick = (id) => {
    setShowModal((prev) => !prev);
    storeDataWithTimestamp({ key: true });
  };

  const handleGetMessage = () => {
    // setShowModal(false);
    modalCloseClick();
    localStorage.setItem("modalShown", "true");

    navigate("/user/buy");
  };

  const storeDataWithTimestamp = (data) => {
    const currentTime = new Date().getTime();
    const dataToStore = {
      data: data,
      timestamp: currentTime,
    };
    localStorage.setItem("myData", JSON.stringify(dataToStore));
  };

  const getDataFromStorage = () => {
    const storedData = localStorage.getItem("myData");
    if (storedData) {
      const parsedData = JSON.parse(storedData);
      const storedTime = parsedData.timestamp;
      const currentTime = new Date().getTime();

      // const twentyFourHours = 60 * 1000; // 1 minutes in milliseconds
      const twentyFourHours = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

      if (currentTime - storedTime < twentyFourHours) {
        return parsedData.data;
      } else {
        // Data has expired, remove it from localStorage
        localStorage.removeItem("myData");
        localStorage.removeItem("numberGenerated");
        return null;
      }
    } else {
      if (!localStorage.getItem("numberGenerated")) {
        generateNewNumber();
      }
      return null;
    }
  };

  React.useEffect(() => {
    window.addEventListener("resize", setDimension);

    return () => {
      window.removeEventListener("resize", setDimension);
    };
  }, [screenSize]);

  let storedData;
  list && (storedData = getDataFromStorage());

  const handleCreateNewChat = () => {
    setIsNewChat(true);
    setIsOpen(false);
    // setCreateRoom(true)
    createNewRoom(true);
    setNewChatClicked(true);
    sessionStorage.removeItem("tempInput");
  };

  // Clear tempMessages when official messages are updated
  useEffect(() => {
    if (messages.length > 0) {
      setTempMessages([]);
    }
  }, [messages]);

  return (
    <div className="flex  ">
      {/* LEFT PANEL STARTS HERE................................. */}
      <div
        className={`lg:w-1/4  lg:border-r overflow-y-auto lg:sticky top-0   fixed lg:h-[100vh] h-[100%] lg:flex lg:flex-col flex lg:justify-between bg-companyBlack bg-opacity-10 text-white z-50 w-[100vw]   ${
          isOpen ? "" : "hidden"
        }  `}
      >
        <div
          className={`p-4 md:border-r overflow-y-auto overflow-x-hidden md:relative md:h-[100vh] h-[100%] md:flex flex-col flex justify-between bg-companyBlack bg-opacity-100 text-white z-50  lg:w-full md:w-[60%] w-[90%]  ${
            isOpen ? "" : "hidden"
          }  `}
        >
          {/* Left panel for grouped chat history */}
          <ChatTopLeftPanel onClick={handleCreateNewChat} />

          <div className="overflow-y-auto flex-grow mt-8">
            {renderChatByDate()}
          </div>
          <SelectLang />
          <SelectSport setSport={setSport} />

          <div
            className="self-end border-t-2 border-gray-500 p-2 text-left flex flex-col  justify-between w-[100%] cursor-pointer relative "
            onClick={toggleDropdown}
          >
            <div className="flex justify-between items-center">
              <p className="text-xl capitalize truncate">
                {/* {states.user? state.user:localStorage.getItem("firstName")} */}
                {localStorage.getItem("firstName")}
              </p>
              <p className="text-2xl">
                <MoreIcon />{" "}
              </p>
            </div>
            <p className=" truncate">{localStorage.getItem("email")}</p>
            {isDropdownOpen && <ChatMenuDropDown />}
          </div>
        </div>
        {/* TRANSPARENT SIDE MENU CONTAINER */}
        {isOpen ? (
          <div
            className={`lg:hidden flex justify-center  pt-2  h-screen bg-[#0505058a] bg-opacity-20 border-none lg:w-[20%] md:w-[40%] w-[10%] z-20 p-10 ${
              isOpen ? "" : "hidden"
            }`}
          >
            <div className=" mt-[20px]">
              <SideMenuCloseIcon onClick={() => setIsOpen((prev) => !prev)} />
            </div>
          </div>
        ) : null}
      </div>

      {/* RIGHT PANEL STARTS HERE................................. */}
      <div
        className={` lg:w-3/4  w-full  ${
          isOpen ? "" : ""
        } bg-[#F8F8F8] pb-4 flex flex-col justify-between md:h-auto h-screen`}
      >
        {/* RIGHT PANEL */}
        {/* Right panel for the input form and chat display */}

        {/* NAVBAR AND ITS ITEMS */}
        <NavBarChat
          isOpen={isOpen}
          toggle={toggle}
          isMessageSent={isMessageSent}
        />

        {/* CHAT SCREEN */}
        <div
          ref={containerRef}
          className={`text-center h-[200px]  overflow-y-auto flex flex-col justify-between w-full flex-grow bg-[#F8F8F8] ${
            isLoading ? "" : " "
          } `}
        >
          {console.log("isLoading", isLoading, isNewChat)}
          {/* new chat screen */}
          {isNewChat ? (
            // newChatClicked ? (
            <div className="w-full">
              {!isLoading && (
                <>
                  <h2 className=" py-10 md:text-3xl text-2xl font-bold text-[#343541]">
                    New Chat
                  </h2>

                  <div className="px-4 md:mb-[126px] mb-[62px] w-full flex justify-center">
                    <p
                      className={`text-[12px] text-[#444654]  md:max-w-[515px] max-w-[325px] font-normal ${
                        isLoading ? "text-opacity-30 text-white" : " "
                      }`}
                    >
                      Free Research Preview. Our goal is to make AI systems more
                      natural and safe to interact with. Your feedback will help
                      us improve.
                    </p>
                  </div>

                  {!isLoading && (
                    <BetaSign
                      classNames={` ${
                        isLoading ? "text-opacity-30 text-white" : " "
                      }`}
                    />
                  )}

                  <div className="p-4 flex flex-col justify-between items-center gap-4 md:mb-[40px] mb-[24px]">
                    {!isLoading && (
                      <svg
                        width="48"
                        height="48"
                        viewBox="0 0 48 48"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M18 44H30C40 44 44 40 44 30V18C44 8 40 4 30 4H18C8 4 4 8 4 18V30C4 40 8 44 18 44Z"
                          stroke="#242424"
                          stroke-width="3"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M18 4V44"
                          stroke="#242424"
                          stroke-width="3"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    )}

                    <p className="md:text-[20px] text-[14px] font-medium">
                      Trending Question
                    </p>
                  </div>
                  {/* {suggestions && (
                    <div className="flex justify-center w-full max-w-[1280px] mx-auto">
                      <p
                        onClick={() => {
                          onClickSuggestion(suggestions);
                          setIsSuggestedQuestion(true);
                        }}
                        className="border rounded-2xl p-4 md:py-6 font-medium mb-8 md:text-[16px] text-[12px] bg-[#F3F3F3] md:w-[40vw] w-[80%] cursor-pointer hover:bg-gray-200"
                      >
                        {suggestions}
                      </p>
                    </div>
                  )} */}
                  {suggestions &&
                    suggestions?.map((suggestion, index) => (
                      <div
                        key={index}
                        className="flex justify-center w-full max-w-[1280px] mx-auto"
                      >
                        <p
                          onClick={() => onClickSuggestion(suggestion)}
                          className="border rounded-2xl p-4 md:py-6 font-medium mb-8 md:text-[16px] text-[12px] bg-[#F3F3F3] md:w-[40vw] w-[80%] cursor-pointer hover:bg-gray-200"
                        >
                          {suggestion}
                        </p>
                      </div>
                    ))}
                </>
              )}

              {/* To simulate loading when a new message is sent  */}
              {isLoading && (
                <div className=" border-red-500 ">
                  <div
                    className={`md:bg-white bg-[#F8F8F8] text-left  py-8
                      text-sm flex w-full md:px-[100px] px-8`}
                  >
                    {/* {console.log("userInput", userInput, temp)} */}
                    <div className="">
                      <p className=" pb-4 text-black">
                        {sessionStorage.getItem("tempInput")}.
                      </p>
                    </div>
                  </div>

                  <div
                    className={`
                       
                        bg-[#F8F8F8] text-left place-content-left flex-grow w-full 
                     text-sm flex items-center  md:px-[100px] px-8`}
                  >
                    <div className="mr-4">
                      <ChatBotIcon />
                    </div>

                    <div className="">
                      <DotLoaders />
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : null}

          {/* isLoading */}

          {isLoading && !temp ? (
            <div className="fixed hidden h-[100%] lg:w-[75%] w-[100%]   justify-center ">
              <div className=" absolute z-999  w-fit  top-[10%]">
                <div className="  p-4">
                  <Spinner />
                </div>
              </div>
            </div>
          ) : null}

          {/* real chat screen */}
          {!isNewChat ? (
            <div className="  w-full  h-full py-8  flex flex-col justify-between flex-grow ">
              <p className="text-xl bg-none w-full py-3 ">
                {/* new */}

                {(tempMessages.length > 0 ? tempMessages : messages).map(
                  (message, index) => (
                    <div
                      key={index}
                      className={`${
                        message?.user_id === states.user
                          ? "md:bg-white bg-[#F8F8F8] text-left "
                          : "bg-[#F8F8F8] text-left place-content-left flex-grow w-full max-w-[1280px] mx-auto"
                      } `}
                    >
                      <div
                        className={`${
                          message?.user_id === states.user
                            ? "md:bg-white bg-[#F8F8F8] text-left  py-[38px] max-w-[1280px] mx-auto"
                            : "bg-[#F8F8F8] text-left place-content-left flex-grow w-full py-4 max-w-[1280px] mx-auto"
                        } md:text-[16px] text-sm text-companyBlack flex w-full md:px-[100px] px-8 `}
                      >
                        {!(message?.user_id === states.user) && (
                          <div className="mr-4">
                            <ChatBotIcon />
                          </div>
                        )}
                        <div className="">
                          {message?.message?.split("\n").map((answer, i) => (
                            // <p className=" " key={i}>
                            //   {answer.replace(/\n/g, '<br>')}.
                            // </p>
                            <p className=" " key={i}>
                              {answer}
                            </p>
                            // <p className=" " key={i}>
                            //   {formatPrediction(answer)}.
                            // </p>
                          ))}
                        </div>

                        <div className=" whitespace-nowrap ml-2 italic text-[8px] ">
                          {formatTimestamp(message?.chat?.timestamp)}
                        </div>
                      </div>
                    </div>
                  )
                )}

                {/* To simulate loading when a new message is sent  */}
                {isLoading && (
                  <div className=" border-red-500 ">
                    {/* <div
                      className={`md:bg-white bg-[#F8F8F8] text-left  py-8
                      text-sm flex w-full md:px-[100px] px-8`}
                    >
                      <div className="">
                        <p className=" pb-4 text-black">
                          {sessionStorage.getItem("tempInput")}.
                        </p>
                      </div>
                    </div> */}

                    <div
                      className={`
                       
                        bg-[#F8F8F8] text-left place-content-left flex-grow w-full 
                     text-sm flex items-center  md:px-[100px] px-8`}
                    >
                      <div className="mr-4">
                        <ChatBotIcon />
                      </div>

                      <div className="">
                        <DotLoaders />
                      </div>
                    </div>
                  </div>
                )}
              </p>
            </div>
          ) : null}
        </div>

        {/* Your input form */}

        <div className="  block items-end bottom-0 bg-[#F8F8F8] px-4 lg:w-full w-full pb-2 max-w-[1280px] mx-auto">
          <ChatInputComponent
            handleInputChange={handleInputChange}
            handleKeyDown={handleKeyDown}
            handleSubmit={handleSubmit}
            userInput={userInput}
          />

          <p
            className={`text-center  text-[#444654] md:text-xs text-[10px] w-full mb-2 ${
              isLoading ? "text-opacity-30 text-white" : " "
            }`}
          >
            Free Research Preview. Our goal is to make AI systems more natural
            and safe to interact with. Your feedback will help us improve.
          </p>
        </div>

        {/* Quiz screen */}
        {!storedData && list ? (
          <Modal
            isOpen={showModal}
            title="Free Tokens Alert! "
            modalHeader="he"
            modalCloseClick={modalCloseClick}
            classes={{
              modalDialog: "md:px-20 px-4 md:min-w-[40vw] min-w-[90vw] ",
              title: "md:text-[30px] text-[28px] font-semibold",
            }}
          >
             <FeatureNotificationPopUp handleGetMessage={handleGetMessage} />

            <h2 className=" w-full pb-6 text-center md:text-5xl font-medium ">
              {title}
            </h2>
            
            {/* {console.log("daily", dailyQuiz)} */}

            <div className=" w-full place-content-center flex  ">
              <div className="max-w-[500px]  w-full border rounded-lg bg-gray-50 p-6 pb-10 flex flex-col md:items-left text-center md:text-base text-sm font-semibold">
                {!dailyQuiz?.quiz ? (
                  <SkeletonLoading padding="py-20" />
                ) : (
                  `${dailyQuiz.quiz}?`
                )}
              </div>
            </div>
            <h2 className=" w-full pt-6 text-center text-black  md:text-base text-sm">
              {!dailyQuiz?.quiz ? (
                <SkeletonLoading />
              ) : (
                "Want even more tokens?"
              )}
            </h2>

            <div className=" flex justify-center items-center mt-4 ">
              {!dailyQuiz?.quiz ? (
                <SkeletonLoading />
              ) : (
                <p className=" text-center md:text-base text-sm w-full max-w-[300px]">
                  DM your answer to the quiz question on
                  <span className="font-bold"> Instagram @Kaizenwinnl  </span>and get bonus tokens totally free!
                </p>
              )}
            </div>

           


          </Modal>
        ) : null}

        {/* <FeatureNotificationPopUp /> */}
      </div>
    </div>
  );
};
export default ChatComponent;
