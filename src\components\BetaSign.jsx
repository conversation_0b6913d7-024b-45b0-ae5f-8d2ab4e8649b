// src/BetaSign.js
import React from "react";
import { useNavigate } from "react-router";

const BetaSign = (classNames) => {
  const navigate = useNavigate();
  return (
    <div className={`${classNames? classNames:"text-yellow-800"} bg-yellow-50 p-4 mb-4 rounded border border-yellow-300  `}>
      <p className="font-bold">Beta Version</p>
      <p>Testing in progress. Some things may go wrong.</p>
      <p className="text-sm mt-1">
        {/* Report issues to:{" "}
        <a href="mailto:<EMAIL>" className="underline">
          <EMAIL>
        </a> */}
        <a href="https://docs.google.com/forms/d/e/1FAIpQLSeX-Wqdc5cRfYQ23S-PjV-mqzLQdWlTxYovUcx3Ph61kyDaUA/viewform?usp=sf_link"
        
        className="underline"
          target="_blank"

        > Report issues here</a>
      </p>
      {/* <p className="text-sm mt-1">
        How to report issues:
        <a
          href="/user/report-template"
          className="underline"
          target="_blank"
          rel="noopener noreferrer"
        >
          view
        </a>
      </p> */}
    </div>
  );
};

export default BetaSign;
