import React, { useState } from 'react'
import ClickAwayListener from "@mui/material/ClickAwayListener";
import MenuList from "@mui/material/MenuList";
import { Grow, Paper, Popper } from "@mui/material";
import { useNavigate } from 'react-router';
import { AuthContext } from 'Src/authContext';

const MyAccount = () => {
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const { state: states, dispatch } = React.useContext(AuthContext);
    const [open, setOpen] = React.useState(false);
    const anchorRef = React.useRef(null);
    const navigate = useNavigate();

    const handleToggle = () => {
        setOpen((prevOpen) => !prevOpen);
      };
    
      const handleClose = (event) => {
        if (anchorRef.current && anchorRef.current.contains(event.target)) {
          return;
        }
    
        setOpen(false);
      };
    
      function handleListKeyDown(event) {
        if (event.key === "Tab") {
          event.preventDefault();
          setOpen(false);
        } else if (event.key === "Escape") {
          setOpen(false);
        }
      }
    
      // return focus to the button when we transitioned from !open -> open
      const prevOpen = React.useRef(open);
      React.useEffect(() => {
        if (prevOpen.current === true && open === false) {
          anchorRef?.current?.focus();
        }
    
        prevOpen.current = open;
      }, [open]);


   
      const toggleDropdown = () => {
        setIsDropdownOpen(!isDropdownOpen);
      };
    


  return (
    <div
    className="relative hidden md:flex "
    onMouseEnter={toggleDropdown}
    onMouseLeave={toggleDropdown}
  >
    <button
      // onClick={toggleDropdown}
      onClick={handleToggle}
      ref={anchorRef}
      className="text-[#CC1122] border font-semibold border-[#CC1122] rounded-full p-2 px-4 flex items-center"
    >
      My Account
      <span className="ml-2">
        <svg
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.5999 7.45898L11.1666 12.8923C10.5249 13.534 9.4749 13.534 8.83324 12.8923L3.3999 7.45898"
            stroke="#CC1122"
            stroke-width="1.25"
            stroke-miterlimit="10"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </span>
    </button>

    {/* <div className="absolute right-0 mt-12 w-48 bg-white border rounded-lg shadow-lg"> */}
    <Popper
    open={open}
    anchorEl={anchorRef.current}
    role={undefined}
    placement="bottom-start"
    transition
    disablePortal
  >
    {({ TransitionProps, placement }) => (
      <Grow
        {...TransitionProps}
        style={{
          transformOrigin:
            placement === 'bottom-start' ? 'left top' : 'left bottom',
        }}
      >
        <Paper className="custom-width">
          <ClickAwayListener onClickAway={handleClose}>
            <MenuList
              autoFocusItem={open}
              id="composition-menu"
              aria-labelledby="composition-button"
              onKeyDown={handleListKeyDown}
            >

              <ul>
                <li
                  className="hover:bg-companyRed hover:bg-opacity-10 hover:text-companyRed cursor-pointer py-2 px-4 hover:border "
                  onClick={() => {
                    navigate("/user/profile");
                  }}
                >
                  My Profile
                </li>
                <li
                  className="hover:bg-companyRed hover:bg-opacity-10 hover:text-companyRed cursor-pointer py-2 px-4"
                  onClick={() => {
                    navigate("/user/plans");
                  }}
                >
                  My Plan
                </li>
                <li
                  className="hover:bg-companyRed hover:bg-opacity-10 hover:text-companyRed cursor-pointer py-2 px-4"
                  onClick={() => {
                    navigate("/user/login");
                    dispatch({
                      type: "LOGOUT",
                    });
                  }}
                >
                  {states.user ? "Log out" : "Sign in"}
                </li>
              </ul>
              
            </MenuList>
          </ClickAwayListener>
        </Paper>
      </Grow>
    )}
  </Popper>

    {/* </div> */}
  </div>
  )
}

export default MyAccount

