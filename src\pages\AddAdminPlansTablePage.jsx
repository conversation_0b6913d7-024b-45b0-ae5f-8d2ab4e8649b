import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "../utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { AuthContext, tokenExpireError } from "../authContext";
import { GlobalContext, showToast } from "../globalContext";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { isImage, empty, isVideo, isPdf } from "../utils/utils";

const AddAdminPlansPage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const schema = yup
    .object({
      name: yup.string(),
      description: yup.string(),
      amount: yup.string(),
      stripe_id: yup.string(),
      messages: yup.string(),
    })
    .required();

  const [fileObj, setFileObj] = React.useState({});

  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  // ADDING NEW ATTRIBUTES..................................STARTS
  const [inputFields, setInputFields] = useState([""]);

  const handleChange = (index, event) => {
    const values = [...inputFields];
    values[index] = event.target.value;
    setInputFields(values);
  };

  const handleAddFields = () => {
    setInputFields([...inputFields, ""]);
  };

  const handleRemoveFields = (index) => {
    const values = [...inputFields];
    values.splice(index, 1);
    setInputFields(values);
  };

  // console.log("inputFields",inputFields);

  // const handleSubmit = (event) => {
  //   event.preventDefault();
  //   // Handle form submission logic here
  //   console.log('Form submitted:', inputFields);
  // };

  // ADDING NEW ATTRIBUTES..................................ENDS

  const previewImage = (field, target) => {
    let tempFileObj = fileObj;
    tempFileObj[field] = {
      file: target.files[0],
      tempURL: URL.createObjectURL(target.files[0]),
    };
    setFileObj({ ...tempFileObj });
  };

  const onSubmit = async (_data) => {
    let sdk = new MkdSDK();

    try {
      for (let item in fileObj) {
        let formData = new FormData();
        formData.append("file", fileObj[item].file);
        let uploadResult = await sdk.uploadImage(formData);
        _data[item] = uploadResult.url;
      }

      sdk.setTable("plans");

      const result = await sdk.callRestAPI(
        {
          name: _data.name,
          description: _data.description,
          amount: _data.amount*100,
          attribute:JSON.stringify(inputFields),
          stripe_id: _data.stripe_id,
          messages: _data.messages,
        },
        "POST"
      );
      // console.log("result", result);
      if (!result.error) {
        showToast(globalDispatch, "Added");
        navigate("/admin/plans");
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error) {
      console.log("Error", error);
      setError("name", {
        type: "manual",
        message: error.message,
      });
      tokenExpireError(dispatch, error.message);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "plans",
      },
    });
  }, []);

  return (
    <div className=" shadow-md rounded  mx-auto p-5">
      <h4 className="text-2xl font-medium">Add Plans</h4>
      <form className=" w-full max-w-lg" onSubmit={handleSubmit(onSubmit)}>
        <div className="mb-4  ">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="name"
          >
            Name
          </label>
          <input
            placeholder="Name"
            {...register("name")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
              errors.name?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-red-500 text-xs italic">{errors.name?.message}</p>
        </div>
        <div className="mb-4  ">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="description"
          >
            Description
          </label>
          <textarea
            placeholder="Description"
            {...register("description")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
              errors.description?.message ? "border-red-500" : ""
            }`}
            row={15}
          ></textarea>
          <p className="text-red-500 text-xs italic">
            {errors.description?.message}
          </p>
        </div>
        <div className="mb-4 ">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="amount"
          >
            Amount
          </label>
          <input
            placeholder="Amount"
            {...register("amount")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
              errors.amount?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-red-500 text-xs italic">
            {errors.amount?.message}
          </p>
        </div>
        <div className="mb-4  ">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="stripe_id"
          >
            Stripe Id
          </label>
          <input
            placeholder="Stripe Id"
            {...register("stripe_id")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
              errors.stripe_id?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-red-500 text-xs italic">
            {errors.stripe_id?.message}
          </p>
        </div>
        <div className="mb-4  ">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="messages"
          >
            Messages
          </label>
          <input
            placeholder="Messages"
            {...register("messages")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
              errors.messages?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-red-500 text-xs italic">
            {errors.messages?.message}
          </p>
        </div>


        {/* ADDING NEW ATTRIBUTES..................................STARTS */}
        {/* <form onSubmit={handleSubmit}> */}
        <div className="mb-4  ">
        <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="messages"
          >
            Attributes
          </label>
          {inputFields.map((inputField, index) => (
            <div key={index} className="flex  mb-2">
              <input
                type="text"
                placeholder="Attributes"
                value={inputField}
                onChange={(event) => handleChange(index, event)}
                className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline`}
              />
              <button type="button" onClick={() => handleRemoveFields(index)} className=" inline ml-2 bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-2 rounded focus:outline-none focus:shadow-outline md:text-base text-sm">
                Remove
              </button>
            </div>
          ))}
          <button type="button" onClick={handleAddFields} className=" inline ml-2 bg-gray-500 hover:bg-gray-700 text-white font-bold mt-2 px-2 rounded focus:outline-none focus:shadow-outline md:text-base text-sm">
            Add More Attributes
          </button>

          </div>
          {/* <button type="submit">Submit</button> */}
        {/* </form> */}
        {/* ADDING NEW ATTRIBUTES..................................ENDS */}



        <button
          type="submit"
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Submit
        </button>
      </form>
    </div>
  );
};

export default AddAdminPlansPage;
