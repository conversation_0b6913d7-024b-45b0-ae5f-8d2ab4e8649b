import FooterComp from "Components/FooterComp";
import Navbar from "Components/NavBar";
import SkeletonLoading from "Components/SkeletonLoading";
import { tokenExpireError } from "Src/authContext";
import MkdSDK from "Utils/MkdSDK";
import React from "react";
import { useState } from "react";
import { useLocation, useNavigate } from "react-router";

let sdk = new MkdSDK();
const TermsAndConditions = () => {
  const [isOpen, setIsOpen] = useState(true);
  const [accepted, setAccepted] = useState(false);
  const [rejected, setRejected] = useState(false);
  const [cms, setCms] = useState();

  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const redirect_uri = searchParams.get("redirect_uri");

  const navigate = useNavigate();

  React.useEffect(() => {
    async function getallcms() {
      try {
        const result = await sdk.getallcms();
        if (result) {
          setCms(result?.list);
        }
      } catch (err) {
        console.log("Error:", err);
        tokenExpireError(dispatch, err.message);
      }
    }
    getallcms();
  }, []);

  let textLists;
  cms &&
    (textLists = JSON.parse(
      cms?.find((item) => item.content_key === "Terms_texts")?.content_value
    ));

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-100">
      <Navbar />
      <div className="bg-white md:p-8 p-4 rounded shadow-lg max-w-5xl w-full">
        <h1 className="md:text-4xl text-2xl font-semibold mb-6">
          {!cms && <SkeletonLoading />}
          {
            cms?.find((item) => item.content_key === "Terms_header_text")
              ?.content_value
          }
        </h1>
        <div className="mb-4 md:text-justify text-justify">
          {!cms &&
            Array(9)
              .fill(0)
              .map((_, i) => (
                <div key={i} className="mb-4">
                  <h2 className="text-lg font-semibold w-1/2">
                    <SkeletonLoading counter={1} />
                  </h2>
                  <p className="text-gray-400 text-sm font-normal">
                    <SkeletonLoading counter={2} />
                  </p>
                </div>
              ))}

          {cms
            ? textLists.map((content, i) => (
                <>
                  <div key={i} className="mb-4">
                    <h1 className="md:text-2xl text-lg font-semibold mb-2">
                      {content.key}
                    </h1>
                    <pre className="flex flex-row whitespace-pre-wrap overflow-x-auto break-words ">
                      <span className={` font-[Manrope] md:text-lg text-lg flex gap-2 `}>
                        {content.value}  
                        {textLists?.length === i + 1 ? (
                          <a
                            className=" inline-block "
                            href={
                              cms?.find(
                                (item) => item.content_key === "Terms_document"
                              )?.content_value
                            }
                            download={true}
                          >
                               .view terms
                          </a>
                        ) : null}
                      </span>
                    </pre>
                  </div>
                </>
              ))
            : null}
        </div>

        {/* Add your terms and conditions text here */}

        {/* <div className="flex justify-between mt-6">
            <button
              onClick={handleAccept}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded focus:outline-none text-sm"
            >
              Accept and Continue
            </button>
            <button
              onClick={handleReject}
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded focus:outline-none text-sm"
            >
              Cancel
            </button>
          </div> */}
      </div>

      <FooterComp />
    </div>
  );
};

export default TermsAndConditions;
