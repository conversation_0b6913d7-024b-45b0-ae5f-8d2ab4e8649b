import React from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "../utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { AuthContext, tokenExpireError } from "../authContext";
import { GlobalContext, showToast } from "../globalContext";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { isImage, empty, isVideo, isPdf } from "../utils/utils";
import { useState } from "react";
import Papa from "papaparse";

let sdk = new MkdSDK();

const AddAdminLendersPage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [data, setData] = useState([]);
  const [uploadStatus, setUploadStatus] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [sport, setSport] = useState("");
  const schema = yup
    .object({
      create_at: yup.string(),
      update_at: yup.string(),
      link: yup.string(),
    })
    .required();

  const [fileObj, setFileObj] = React.useState({});

  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const previewImage = (field, target) => {
    let tempFileObj = fileObj;
    tempFileObj[field] = {
      file: target.files[0],
      tempURL: URL.createObjectURL(target.files[0]),
    };
    setFileObj({ ...tempFileObj });
  };

  const onSubmit = async (_data) => {
    try {
      for (let item in fileObj) {
        let formData = new FormData();
        formData.append("file", fileObj[item].file);
        let uploadResult = await sdk.uploadImage(formData);
        _data[item] = uploadResult.url;
      }

      sdk.setTable("lenders");

      const result = await sdk.callRestAPI(
        {
          link: _data.link,
        },
        "POST"
      );
      if (!result.error) {
        showToast(globalDispatch, "Added");
        navigate("/admin/lenders");
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error) {
      console.log("Error", error);
      setError("create_at", {
        type: "manual",
        message: error.message,
      });
      tokenExpireError(dispatch, error.message);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "lenders",
      },
    });
  }, []);

  const handleFileChange = (event) => {
    setIsDownloading(true);
    const file = event.target.files[0];
    setSport(file?.name.split(".")[0]);
    if (file) {
      Papa.parse(file, {
        header: true,
        complete: (results) => {
          const filteredData = results.data.filter((row) => {
            // Filter out empty rows
            return Object.values(row).some(
              (value) => value !== null && value !== ""
            );
          });
          setData(filteredData);
          // sendToBackend(filteredData);
        },
        error: (error) => {
          console.error("Error parsing the file: ", error);
        },
      });
    }
    setIsDownloading(true);
  };

  const sendToBackend = async (parsedData) => {
    sdk.setTable("lenders");
    setIsUploading(true);
    try {
      // const promises = parsedData.map((item) =>
      const promises = data.map((item) =>
        sdk.callRestAPI(
          {
            link: item?.links, // Adjust according to your API requirements
            sport: sport, // Adjust according to your API requirements
          },
          "POST"
        )
      );
      await Promise.all(promises);
      showToast(globalDispatch, "Added");
      navigate("/admin/lenders");
      // setUploadStatus("Upload successful!");
    } catch (error) {
      console.error("Error uploading data:", error);
      setUploadStatus("Upload failed. Please try again.");
    }
    setIsUploading(false);
  };

  return (
    <div className=" shadow-md rounded  mx-auto p-5">
      <h4 className="text-2xl font-medium">Add Lenders</h4>
      <form className=" w-full max-w-lg" onSubmit={handleSubmit(onSubmit)}>
        {/* <div className="mb-4  ">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="create_at"
          >
            Create At
          </label>
          <input
            type="date"
            placeholder="Create At"
            {...register("create_at")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
              errors.create_at?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-red-500 text-xs italic">
            {errors.create_at?.message}
          </p>
        </div> */}

        <div className="mb-4  ">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="update_at"
          >
            Link
          </label>
          <input
            type="text"
            placeholder="link"
            {...register("link")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
              errors.link?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-red-500 text-xs italic">{errors.link?.message}</p>
        </div>

        {/* <div className="mb-4  ">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="update_at"
          >
            Update At
          </label>
          <input
            type="datetime-local"
            placeholder="Update At"
            {...register("update_at")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
              errors.update_at?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-red-500 text-xs italic">
            {errors.update_at?.message}
          </p>
        </div> */}

        <button
          type="submit"
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Submit
        </button>
      </form>

      <div className="my-4  ">
        <div className=" space-y-2">
          <label
            className="block text-gray-700 text-base font-bold mb-"
            htmlFor="update_at"
          >
            Upload csv
          </label>
          <div>
            <input
              className=""
              type="file"
              accept=".csv"
              onChange={handleFileChange}
            />
            {/* <pre>{JSON.stringify(data, null, 2)}</pre> */}
            {isUploading && <p>uploading...</p>}
            {uploadStatus && <p>{uploadStatus}</p>}
          </div>
          {/* 
          <div className="mb-4  ">
            <label
              className="block text-gray-700 text-sm font-bold mb-2"
              htmlFor="update_at"
            >
              Sport
            </label>
            <input
              type="text"
              placeholder="sport"
              onChange={(e) => setSport(e.target.value)}
              className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline `}
            />
          </div> */}
          <button
            type="button"
            disabled={!isDownloading || sport == ""}
            onClick={sendToBackend}
            className="disabled:bg-blue-200 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
          >
            Submit
          </button>
        </div>
      </div>
    </div>
  );
};

export default AddAdminLendersPage;
