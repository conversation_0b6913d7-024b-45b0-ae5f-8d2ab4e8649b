import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "../utils/MkdSDK";
import { GlobalContext, showToast } from "../globalContext";
import { useNavigate, useParams } from "react-router-dom";
import { AuthContext, tokenExpireError } from "../authContext";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { isImage, empty, isVideo, isPdf } from "../utils/utils";
import { BackButton } from "Components/BackButton";

let sdk = new MkdSDK();

const EditAdminDynamicHeaderPage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const schema = yup
    .object({
      headlink: yup.string(),
    })
    .required();
  const [fileObj, setFileObj] = React.useState({});
  const navigate = useNavigate();

  const [id, setId] = useState(0);
  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const params = useParams();

  const [inputFields, setInputFields] = useState([""]);

  useEffect(function () {
    (async function () {
      try {
        sdk.setTable("dynamic_head");
        const result = await sdk.callRestAPI({ id: Number(params?.id) }, "GET");
        if (!result.error) {
          setValue("headlink", result.model.headlink);
          setValue("name", result.model.name);
          setId(result.model.id);
        }
      } catch (error) {
        console.log("error", error);
        tokenExpireError(dispatch, error.message);
      }
    })();
  }, []);

  const onSubmit = async (_data) => {
    try {
      sdk.setTable("dynamic_head");
      for (let item in fileObj) {
        let formData = new FormData();
        formData.append("file", fileObj[item].file);
        let uploadResult = await sdk.uploadImage(formData);
        _data[item] = uploadResult.url;
      }
      const result = await sdk.callRestAPI(
        {
          id: id,
          headlink: _data.headlink,
          name: _data.name,
        },
        "PUT"
      );

      if (!result.error) {
        showToast(globalDispatch, "Updated");
        navigate("/admin/dynamic-headers");
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error) {
      console.log("Error", error);
      setError("name", {
        type: "manual",
        message: error.message,
      });
    }
  };
  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "dynamicHeaders",
      },
    });
  }, []);
  // console.log("log", inputFields);

  return (
    <div className=" shadow-md rounded   mx-auto p-5">

      <div className=" flex flex-col gap-4 mb-4">
        <BackButton />
        <h4 className="text-2xl font-medium">Edit Link</h4>
      </div>
      <form className=" w-full max-w-lg" onSubmit={handleSubmit(onSubmit)}>

        <div className=" mb-4">


      <textarea
            placeholder="Paste your <script> or <link> here"
            name=""
            id=""
            cols={10}
            rows={10}
            {...register("headlink")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
              errors.name?.message ? "border-red-500" : ""
            }`}
          ></textarea>
          <input
            placeholder="Name"
            {...register("name")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
              errors.name?.message ? "border-red-500" : ""
            }`}
          />
        </div>


       

        <button
          type="submit"
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Submit
        </button>
      </form>
    </div>
  );
};

export default EditAdminDynamicHeaderPage;
