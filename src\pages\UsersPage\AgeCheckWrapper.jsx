import BackButton from "Components/BackButton/BackButton";
import useGeoLocation from "Components/Hooks/useGeoLocation";
import { useIsEligible } from "Src/ageContext";
import { useData } from "Src/dataContext";
import React, { useEffect, useState } from "react";

const AgeCheckWrapper = ({ children }) => {
  const { isEligible } = useIsEligible();
  const { country } = useGeoLocation();
  const { cms } = useData();
  const [maintenance, setMaintenance] = useState("no");

  useEffect(() => {
    if (cms) {
      const maintenanceStatus = cms?.find((item) => item.content_key === "maintenance_status")?.content_value;
      try {
        setMaintenance(JSON.parse(maintenanceStatus));
      } catch (error) {
        console.error("Failed to parse maintenance status:", error);
        setMaintenance("no"); // Fallback to a default value
      }
    }
  }, [cms]);

  const labelExists = maintenance?.options && maintenance?.options?.length > 0  ? maintenance?.options?.some((option) =>
    option.label.includes(country)
  ): true

  return (
    <div className={`${isEligible ? " h-screen" : " "}`}>
      {maintenance?.status == "yes" && labelExists ? (
        <div className="">
          <div className=" h-full w-full absolute inset-0 bg-black bg-opacity-55 flex justify-center items-center text-black text-lg">
            <div className=" h-full w-full bg-black bg-opacity-55 flex flex-col justify-center items-center text-white text-lg">
              Site on maintenance! check back later
              <BackButton />
            </div>
          </div>
        </div>
      ) : (
        children
      )}
      {/* {isEligible ? (
         children
      ) : (
        <div className=" h-full w-full absolute inset-0 bg-black bg-opacity-55 flex justify-center items-center text-black text-lg">
          This content is for age above 20
        </div>
      )} */}
    </div>
    
  );
};

export default AgeCheckWrapper;
