import{g as Ye,r as j,R as we}from"../vendor-b0222800.js";function de(){return de=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e},de.apply(this,arguments)}const Qr=Object.freeze(Object.defineProperty({__proto__:null,default:de},Symbol.toStringTag,{value:"Module"}));function Ze(e){if(e.sheet)return e.sheet;for(var r=0;r<document.styleSheets.length;r++)if(document.styleSheets[r].ownerNode===e)return document.styleSheets[r]}function Ue(e){var r=document.createElement("style");return r.setAttribute("data-emotion",e.key),e.nonce!==void 0&&r.setAttribute("nonce",e.nonce),r.appendChild(document.createTextNode("")),r.setAttribute("data-s",""),r}var Je=function(){function e(t){var n=this;this._insertTag=function(a){var s;n.tags.length===0?n.insertionPoint?s=n.insertionPoint.nextSibling:n.prepend?s=n.container.firstChild:s=n.before:s=n.tags[n.tags.length-1].nextSibling,n.container.insertBefore(a,s),n.tags.push(a)},this.isSpeedy=t.speedy===void 0?!0:t.speedy,this.tags=[],this.ctr=0,this.nonce=t.nonce,this.key=t.key,this.container=t.container,this.prepend=t.prepend,this.insertionPoint=t.insertionPoint,this.before=null}var r=e.prototype;return r.hydrate=function(n){n.forEach(this._insertTag)},r.insert=function(n){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(Ue(this));var a=this.tags[this.tags.length-1];if(this.isSpeedy){var s=Ze(a);try{s.insertRule(n,s.cssRules.length)}catch{}}else a.appendChild(document.createTextNode(n));this.ctr++},r.flush=function(){this.tags.forEach(function(n){return n.parentNode&&n.parentNode.removeChild(n)}),this.tags=[],this.ctr=0},e}(),w="-ms-",U="-moz-",f="-webkit-",_e="comm",pe="rule",ye="decl",Qe="@import",Ie="@keyframes",Xe="@layer",er=Math.abs,J=String.fromCharCode,rr=Object.assign;function tr(e,r){return g(e,0)^45?(((r<<2^g(e,0))<<2^g(e,1))<<2^g(e,2))<<2^g(e,3):0}function Ne(e){return e.trim()}function nr(e,r){return(e=r.exec(e))?e[0]:e}function u(e,r,t){return e.replace(r,t)}function le(e,r){return e.indexOf(r)}function g(e,r){return e.charCodeAt(r)|0}function D(e,r,t){return e.slice(r,t)}function R(e){return e.length}function me(e){return e.length}function B(e,r){return r.push(e),e}function ar(e,r){return e.map(r).join("")}var Q=1,L=1,je=0,C=0,y=0,W="";function X(e,r,t,n,a,s,i){return{value:e,root:r,parent:t,type:n,props:a,children:s,line:Q,column:L,length:i,return:""}}function z(e,r){return rr(X("",null,null,"",null,null,0),e,{length:-e.length},r)}function sr(){return y}function ir(){return y=C>0?g(W,--C):0,L--,y===10&&(L=1,Q--),y}function O(){return y=C<je?g(W,C++):0,L++,y===10&&(L=1,Q++),y}function M(){return g(W,C)}function H(){return C}function q(e,r){return D(W,e,r)}function G(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Fe(e){return Q=L=1,je=R(W=e),C=0,[]}function Le(e){return W="",e}function Y(e){return Ne(q(C-1,he(e===91?e+2:e===40?e+1:e)))}function cr(e){for(;(y=M())&&y<33;)O();return G(e)>2||G(y)>3?"":" "}function or(e,r){for(;--r&&O()&&!(y<48||y>102||y>57&&y<65||y>70&&y<97););return q(e,H()+(r<6&&M()==32&&O()==32))}function he(e){for(;O();)switch(y){case e:return C;case 34:case 39:e!==34&&e!==39&&he(y);break;case 40:e===41&&he(e);break;case 92:O();break}return C}function fr(e,r){for(;O()&&e+y!==47+10;)if(e+y===42+42&&M()===47)break;return"/*"+q(r,C-1)+"*"+J(e===47?e:O())}function ur(e){for(;!G(M());)O();return q(e,C)}function dr(e){return Le(Z("",null,null,null,[""],e=Fe(e),0,[0],e))}function Z(e,r,t,n,a,s,i,c,o){for(var v=0,m=0,x=i,_=0,I=0,E=0,h=1,$=1,p=1,S=0,P="",K=a,N=s,k=n,l=P;$;)switch(E=S,S=O()){case 40:if(E!=108&&g(l,x-1)==58){le(l+=u(Y(S),"&","&\f"),"&\f")!=-1&&(p=-1);break}case 34:case 39:case 91:l+=Y(S);break;case 9:case 10:case 13:case 32:l+=cr(E);break;case 92:l+=or(H()-1,7);continue;case 47:switch(M()){case 42:case 47:B(lr(fr(O(),H()),r,t),o);break;default:l+="/"}break;case 123*h:c[v++]=R(l)*p;case 125*h:case 59:case 0:switch(S){case 0:case 125:$=0;case 59+m:p==-1&&(l=u(l,/\f/g,"")),I>0&&R(l)-x&&B(I>32?Ce(l+";",n,t,x-1):Ce(u(l," ","")+";",n,t,x-2),o);break;case 59:l+=";";default:if(B(k=$e(l,r,t,v,m,a,c,P,K=[],N=[],x),s),S===123)if(m===0)Z(l,r,k,k,K,s,x,c,N);else switch(_===99&&g(l,3)===110?100:_){case 100:case 108:case 109:case 115:Z(e,k,k,n&&B($e(e,k,k,0,0,a,c,P,a,K=[],x),N),a,N,x,c,n?K:N);break;default:Z(l,k,k,k,[""],N,0,c,N)}}v=m=I=0,h=p=1,P=l="",x=i;break;case 58:x=1+R(l),I=E;default:if(h<1){if(S==123)--h;else if(S==125&&h++==0&&ir()==125)continue}switch(l+=J(S),S*h){case 38:p=m>0?1:(l+="\f",-1);break;case 44:c[v++]=(R(l)-1)*p,p=1;break;case 64:M()===45&&(l+=Y(O())),_=M(),m=x=R(P=l+=ur(H())),S++;break;case 45:E===45&&R(l)==2&&(h=0)}}return s}function $e(e,r,t,n,a,s,i,c,o,v,m){for(var x=a-1,_=a===0?s:[""],I=me(_),E=0,h=0,$=0;E<n;++E)for(var p=0,S=D(e,x+1,x=er(h=i[E])),P=e;p<I;++p)(P=Ne(h>0?_[p]+" "+S:u(S,/&\f/g,_[p])))&&(o[$++]=P);return X(e,r,t,a===0?pe:c,o,v,m)}function lr(e,r,t){return X(e,r,t,_e,J(sr()),D(e,2,-2),0)}function Ce(e,r,t,n){return X(e,r,t,ye,D(e,0,n),D(e,n+1,-1),n)}function F(e,r){for(var t="",n=me(e),a=0;a<n;a++)t+=r(e[a],a,e,r)||"";return t}function hr(e,r,t,n){switch(e.type){case Xe:if(e.children.length)break;case Qe:case ye:return e.return=e.return||e.value;case _e:return"";case Ie:return e.return=e.value+"{"+F(e.children,n)+"}";case pe:e.value=e.props.join(",")}return R(t=F(e.children,n))?e.return=e.value+"{"+t+"}":""}function pr(e){var r=me(e);return function(t,n,a,s){for(var i="",c=0;c<r;c++)i+=e[c](t,n,a,s)||"";return i}}function yr(e){return function(r){r.root||(r=r.return)&&e(r)}}var mr=function(r,t,n){for(var a=0,s=0;a=s,s=M(),a===38&&s===12&&(t[n]=1),!G(s);)O();return q(r,C)},br=function(r,t){var n=-1,a=44;do switch(G(a)){case 0:a===38&&M()===12&&(t[n]=1),r[n]+=mr(C-1,t,n);break;case 2:r[n]+=Y(a);break;case 4:if(a===44){r[++n]=M()===58?"&\f":"",t[n]=r[n].length;break}default:r[n]+=J(a)}while(a=O());return r},gr=function(r,t){return Le(br(Fe(r),t))},Ee=new WeakMap,vr=function(r){if(!(r.type!=="rule"||!r.parent||r.length<1)){for(var t=r.value,n=r.parent,a=r.column===n.column&&r.line===n.line;n.type!=="rule";)if(n=n.parent,!n)return;if(!(r.props.length===1&&t.charCodeAt(0)!==58&&!Ee.get(n))&&!a){Ee.set(r,!0);for(var s=[],i=gr(t,s),c=n.props,o=0,v=0;o<i.length;o++)for(var m=0;m<c.length;m++,v++)r.props[v]=s[o]?i[o].replace(/&\f/g,c[m]):c[m]+" "+i[o]}}},xr=function(r){if(r.type==="decl"){var t=r.value;t.charCodeAt(0)===108&&t.charCodeAt(2)===98&&(r.return="",r.value="")}};function We(e,r){switch(tr(e,r)){case 5103:return f+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return f+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return f+e+U+e+w+e+e;case 6828:case 4268:return f+e+w+e+e;case 6165:return f+e+w+"flex-"+e+e;case 5187:return f+e+u(e,/(\w+).+(:[^]+)/,f+"box-$1$2"+w+"flex-$1$2")+e;case 5443:return f+e+w+"flex-item-"+u(e,/flex-|-self/,"")+e;case 4675:return f+e+w+"flex-line-pack"+u(e,/align-content|flex-|-self/,"")+e;case 5548:return f+e+w+u(e,"shrink","negative")+e;case 5292:return f+e+w+u(e,"basis","preferred-size")+e;case 6060:return f+"box-"+u(e,"-grow","")+f+e+w+u(e,"grow","positive")+e;case 4554:return f+u(e,/([^-])(transform)/g,"$1"+f+"$2")+e;case 6187:return u(u(u(e,/(zoom-|grab)/,f+"$1"),/(image-set)/,f+"$1"),e,"")+e;case 5495:case 3959:return u(e,/(image-set\([^]*)/,f+"$1$`$1");case 4968:return u(u(e,/(.+:)(flex-)?(.*)/,f+"box-pack:$3"+w+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+f+e+e;case 4095:case 3583:case 4068:case 2532:return u(e,/(.+)-inline(.+)/,f+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(R(e)-1-r>6)switch(g(e,r+1)){case 109:if(g(e,r+4)!==45)break;case 102:return u(e,/(.+:)(.+)-([^]+)/,"$1"+f+"$2-$3$1"+U+(g(e,r+3)==108?"$3":"$2-$3"))+e;case 115:return~le(e,"stretch")?We(u(e,"stretch","fill-available"),r)+e:e}break;case 4949:if(g(e,r+1)!==115)break;case 6444:switch(g(e,R(e)-3-(~le(e,"!important")&&10))){case 107:return u(e,":",":"+f)+e;case 101:return u(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+f+(g(e,14)===45?"inline-":"")+"box$3$1"+f+"$2$3$1"+w+"$2box$3")+e}break;case 5936:switch(g(e,r+11)){case 114:return f+e+w+u(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return f+e+w+u(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return f+e+w+u(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return f+e+w+e+e}return e}var Sr=function(r,t,n,a){if(r.length>-1&&!r.return)switch(r.type){case ye:r.return=We(r.value,r.length);break;case Ie:return F([z(r,{value:u(r.value,"@","@"+f)})],a);case pe:if(r.length)return ar(r.props,function(s){switch(nr(s,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return F([z(r,{props:[u(s,/:(read-\w+)/,":"+U+"$1")]})],a);case"::placeholder":return F([z(r,{props:[u(s,/:(plac\w+)/,":"+f+"input-$1")]}),z(r,{props:[u(s,/:(plac\w+)/,":"+U+"$1")]}),z(r,{props:[u(s,/:(plac\w+)/,w+"input-$1")]})],a)}return""})}},wr=[Sr],$r=function(r){var t=r.key;if(t==="css"){var n=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(n,function(h){var $=h.getAttribute("data-emotion");$.indexOf(" ")!==-1&&(document.head.appendChild(h),h.setAttribute("data-s",""))})}var a=r.stylisPlugins||wr,s={},i,c=[];i=r.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+t+' "]'),function(h){for(var $=h.getAttribute("data-emotion").split(" "),p=1;p<$.length;p++)s[$[p]]=!0;c.push(h)});var o,v=[vr,xr];{var m,x=[hr,yr(function(h){m.insert(h)})],_=pr(v.concat(a,x)),I=function($){return F(dr($),_)};o=function($,p,S,P){m=S,I($?$+"{"+p.styles+"}":p.styles),P&&(E.inserted[p.name]=!0)}}var E={key:t,sheet:new Je({key:t,container:i,nonce:r.nonce,speedy:r.speedy,prepend:r.prepend,insertionPoint:r.insertionPoint}),nonce:r.nonce,inserted:s,registered:{},insert:o};return E.sheet.hydrate(c),E},ze={exports:{}},d={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var b=typeof Symbol=="function"&&Symbol.for,be=b?Symbol.for("react.element"):60103,ge=b?Symbol.for("react.portal"):60106,ee=b?Symbol.for("react.fragment"):60107,re=b?Symbol.for("react.strict_mode"):60108,te=b?Symbol.for("react.profiler"):60114,ne=b?Symbol.for("react.provider"):60109,ae=b?Symbol.for("react.context"):60110,ve=b?Symbol.for("react.async_mode"):60111,se=b?Symbol.for("react.concurrent_mode"):60111,ie=b?Symbol.for("react.forward_ref"):60112,ce=b?Symbol.for("react.suspense"):60113,Cr=b?Symbol.for("react.suspense_list"):60120,oe=b?Symbol.for("react.memo"):60115,fe=b?Symbol.for("react.lazy"):60116,Er=b?Symbol.for("react.block"):60121,Or=b?Symbol.for("react.fundamental"):60117,Ar=b?Symbol.for("react.responder"):60118,Pr=b?Symbol.for("react.scope"):60119;function A(e){if(typeof e=="object"&&e!==null){var r=e.$$typeof;switch(r){case be:switch(e=e.type,e){case ve:case se:case ee:case te:case re:case ce:return e;default:switch(e=e&&e.$$typeof,e){case ae:case ie:case fe:case oe:case ne:return e;default:return r}}case ge:return r}}}function De(e){return A(e)===se}d.AsyncMode=ve;d.ConcurrentMode=se;d.ContextConsumer=ae;d.ContextProvider=ne;d.Element=be;d.ForwardRef=ie;d.Fragment=ee;d.Lazy=fe;d.Memo=oe;d.Portal=ge;d.Profiler=te;d.StrictMode=re;d.Suspense=ce;d.isAsyncMode=function(e){return De(e)||A(e)===ve};d.isConcurrentMode=De;d.isContextConsumer=function(e){return A(e)===ae};d.isContextProvider=function(e){return A(e)===ne};d.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===be};d.isForwardRef=function(e){return A(e)===ie};d.isFragment=function(e){return A(e)===ee};d.isLazy=function(e){return A(e)===fe};d.isMemo=function(e){return A(e)===oe};d.isPortal=function(e){return A(e)===ge};d.isProfiler=function(e){return A(e)===te};d.isStrictMode=function(e){return A(e)===re};d.isSuspense=function(e){return A(e)===ce};d.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===ee||e===se||e===te||e===re||e===ce||e===Cr||typeof e=="object"&&e!==null&&(e.$$typeof===fe||e.$$typeof===oe||e.$$typeof===ne||e.$$typeof===ae||e.$$typeof===ie||e.$$typeof===Or||e.$$typeof===Ar||e.$$typeof===Pr||e.$$typeof===Er)};d.typeOf=A;ze.exports=d;var kr=ze.exports,xe=kr,Rr={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},Tr={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Mr={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},Ge={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},Se={};Se[xe.ForwardRef]=Mr;Se[xe.Memo]=Ge;function Oe(e){return xe.isMemo(e)?Ge:Se[e.$$typeof]||Rr}var _r=Object.defineProperty,Ir=Object.getOwnPropertyNames,Ae=Object.getOwnPropertySymbols,Nr=Object.getOwnPropertyDescriptor,jr=Object.getPrototypeOf,Pe=Object.prototype;function Ve(e,r,t){if(typeof r!="string"){if(Pe){var n=jr(r);n&&n!==Pe&&Ve(e,n,t)}var a=Ir(r);Ae&&(a=a.concat(Ae(r)));for(var s=Oe(e),i=Oe(r),c=0;c<a.length;++c){var o=a[c];if(!Tr[o]&&!(t&&t[o])&&!(i&&i[o])&&!(s&&s[o])){var v=Nr(r,o);try{_r(e,o,v)}catch{}}}}return e}var Fr=Ve;const Xr=Ye(Fr);var Lr=!0;function et(e,r,t){var n="";return t.split(" ").forEach(function(a){e[a]!==void 0?r.push(e[a]+";"):n+=a+" "}),n}var Wr=function(r,t,n){var a=r.key+"-"+t.name;(n===!1||Lr===!1)&&r.registered[a]===void 0&&(r.registered[a]=t.styles)},zr=function(r,t,n){Wr(r,t,n);var a=r.key+"-"+t.name;if(r.inserted[t.name]===void 0){var s=t;do r.insert(t===s?"."+a:"",s,r.sheet,!0),s=s.next;while(s!==void 0)}};function Dr(e){for(var r=0,t,n=0,a=e.length;a>=4;++n,a-=4)t=e.charCodeAt(n)&255|(e.charCodeAt(++n)&255)<<8|(e.charCodeAt(++n)&255)<<16|(e.charCodeAt(++n)&255)<<24,t=(t&65535)*1540483477+((t>>>16)*59797<<16),t^=t>>>24,r=(t&65535)*1540483477+((t>>>16)*59797<<16)^(r&65535)*1540483477+((r>>>16)*59797<<16);switch(a){case 3:r^=(e.charCodeAt(n+2)&255)<<16;case 2:r^=(e.charCodeAt(n+1)&255)<<8;case 1:r^=e.charCodeAt(n)&255,r=(r&65535)*1540483477+((r>>>16)*59797<<16)}return r^=r>>>13,r=(r&65535)*1540483477+((r>>>16)*59797<<16),((r^r>>>15)>>>0).toString(36)}var Gr={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};function Vr(e){var r=Object.create(null);return function(t){return r[t]===void 0&&(r[t]=e(t)),r[t]}}var qr=/[A-Z]|^ms/g,Kr=/_EMO_([^_]+?)_([^]*?)_EMO_/g,qe=function(r){return r.charCodeAt(1)===45},ke=function(r){return r!=null&&typeof r!="boolean"},ue=Vr(function(e){return qe(e)?e:e.replace(qr,"-$&").toLowerCase()}),Re=function(r,t){switch(r){case"animation":case"animationName":if(typeof t=="string")return t.replace(Kr,function(n,a,s){return T={name:a,styles:s,next:T},a})}return Gr[r]!==1&&!qe(r)&&typeof t=="number"&&t!==0?t+"px":t};function V(e,r,t){if(t==null)return"";if(t.__emotion_styles!==void 0)return t;switch(typeof t){case"boolean":return"";case"object":{if(t.anim===1)return T={name:t.name,styles:t.styles,next:T},t.name;if(t.styles!==void 0){var n=t.next;if(n!==void 0)for(;n!==void 0;)T={name:n.name,styles:n.styles,next:T},n=n.next;var a=t.styles+";";return a}return Br(e,r,t)}case"function":{if(e!==void 0){var s=T,i=t(e);return T=s,V(e,r,i)}break}}if(r==null)return t;var c=r[t];return c!==void 0?c:t}function Br(e,r,t){var n="";if(Array.isArray(t))for(var a=0;a<t.length;a++)n+=V(e,r,t[a])+";";else for(var s in t){var i=t[s];if(typeof i!="object")r!=null&&r[i]!==void 0?n+=s+"{"+r[i]+"}":ke(i)&&(n+=ue(s)+":"+Re(s,i)+";");else if(Array.isArray(i)&&typeof i[0]=="string"&&(r==null||r[i[0]]===void 0))for(var c=0;c<i.length;c++)ke(i[c])&&(n+=ue(s)+":"+Re(s,i[c])+";");else{var o=V(e,r,i);switch(s){case"animation":case"animationName":{n+=ue(s)+":"+o+";";break}default:n+=s+"{"+o+"}"}}}return n}var Te=/label:\s*([^\s;\n{]+)\s*(;|$)/g,T,Ke=function(r,t,n){if(r.length===1&&typeof r[0]=="object"&&r[0]!==null&&r[0].styles!==void 0)return r[0];var a=!0,s="";T=void 0;var i=r[0];i==null||i.raw===void 0?(a=!1,s+=V(n,t,i)):s+=i[0];for(var c=1;c<r.length;c++)s+=V(n,t,r[c]),a&&(s+=i[c]);Te.lastIndex=0;for(var o="",v;(v=Te.exec(s))!==null;)o+="-"+v[1];var m=Dr(s)+o;return{name:m,styles:s,next:T}},Hr=function(r){return r()},Be=we["useInsertionEffect"]?we["useInsertionEffect"]:!1,rt=Be||Hr,Me=Be||j.useLayoutEffect,He=j.createContext(typeof HTMLElement<"u"?$r({key:"css"}):null),tt=He.Provider,Yr=function(r){return j.forwardRef(function(t,n){var a=j.useContext(He);return r(t,a,n)})},Zr=j.createContext({}),nt=Yr(function(e,r){var t=e.styles,n=Ke([t],void 0,j.useContext(Zr)),a=j.useRef();return Me(function(){var s=r.key+"-global",i=new r.sheet.constructor({key:s,nonce:r.sheet.nonce,container:r.sheet.container,speedy:r.sheet.isSpeedy}),c=!1,o=document.querySelector('style[data-emotion="'+s+" "+n.name+'"]');return r.sheet.tags.length&&(i.before=r.sheet.tags[0]),o!==null&&(c=!0,o.setAttribute("data-emotion",s),i.hydrate([o])),a.current=[i,c],function(){i.flush()}},[r]),Me(function(){var s=a.current,i=s[0],c=s[1];if(c){s[1]=!1;return}if(n.next!==void 0&&zr(r,n.next,!0),i.tags.length){var o=i.tags[i.tags.length-1].nextElementSibling;i.before=o,i.flush()}r.insert("",n,i,!1)},[r,n.name]),null});function Ur(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];return Ke(r)}var at=function(){var r=Ur.apply(void 0,arguments),t="animation-"+r.name;return{name:t,styles:"@keyframes "+t+"{"+r.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}};export{tt as C,nt as G,Zr as T,de as _,Qr as a,Ur as b,$r as c,et as g,Xr as h,zr as i,at as k,Wr as r,Ke as s,rt as u,Yr as w};
